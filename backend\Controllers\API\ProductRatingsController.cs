﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Services.Products.Ratings;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class ProductRatingsController : ControllerBase
    {
        private readonly IProductRatingService _ratingService;

        public ProductRatingsController(IProductRatingService ratingService)
        {
            _ratingService = ratingService;
        }

        [HttpGet("Details/{productId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetProductRatings(string productId, [FromQuery] RequestQuery query)
        {
            try
            {
                var (result, summary) = await _ratingService.GetProductReviews(productId, query);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages,
                    summary.TotalRating,
                    summary.AveragePoint
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception)
            {
                return Ok(new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpGet("{orderId}")]
        public async Task<IActionResult> GetOrderRating(string orderId)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var data = await _ratingService.GetProductRatingAsync(orderId, userZaloId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Lấy đánh giá thành công!",
                    Data = data
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception)
            {
                return Ok(new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("{orderId}")]
        public async Task<IActionResult> AddProductRating(string orderId, [FromForm] ProductRatingRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await _ratingService.AddProductRatingAsync(orderId, userZaloId, request);
                return Ok(new
                {
                    Code = 0,
                    Message = "Đánh giá thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception)
            {
                return Ok(new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }
    }
}
