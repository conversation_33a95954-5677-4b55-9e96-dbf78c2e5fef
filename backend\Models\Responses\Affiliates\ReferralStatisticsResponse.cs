namespace MiniAppCore.Models.Responses.Affiliates
{
    public class ReferralStatisticsResponse
    {
        public string UserZaloId { get; set; } = string.Empty;
        public string UserZaloName { get; set; } = string.Empty;
        public string Avatar { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public int TotalDirectReferrals { get; set; }
        public int TotalIndirectReferrals { get; set; }
        public int TotalReferrals => TotalDirectReferrals + TotalIndirectReferrals;
        public decimal TotalCommissionEarned { get; set; }
        public decimal TotalCommissionPaid { get; set; }
        public decimal PendingCommission => TotalCommissionEarned - TotalCommissionPaid;
        public int MaxDepth { get; set; }
        public Dictionary<int, int> ReferralsByLevel { get; set; } = new Dictionary<int, int>();
    }
}
