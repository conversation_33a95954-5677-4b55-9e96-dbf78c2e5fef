﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Services.Events.EventRegistrations;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class EventRegistrationsController(ILogger<EventRegistrationsController> logger, IEventRegistrationService eventService) : ControllerBase
    {
        [AllowAnonymous]
        [HttpGet("Participants/{eventId}")]
        public async Task<IActionResult> GetAll([FromQuery] RequestQuery query, string eventId)
        {
            try
            {
                eventService.IsAdmin = User?.IsInRole("ADMIN") ?? false;

                var result = await eventService.GetPaged(query, eventId);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            try
            {
                var data = await eventService.GetByIdAsync(id);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    data,
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [AllowAnonymous]
        [HttpPost("Register/{id}")]
        public async Task<IActionResult> Register(string id, [FromBody] EventRegistrationDTO dto)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId");

                if (userZaloId == null)
                {
                    return BadRequest(new { Code = 1, Message = "Vui lòng đăng ký thành viên" });
                }

                await eventService.RegisterEvent(id, userZaloId.Value, dto);

                return Ok(new
                {
                    Code = 0,
                    Message = "Đăng ký thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [AllowAnonymous]
        [HttpPost("Cancel/{id}")]
        public async Task<IActionResult> Cancel(string id)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId");

                if (userZaloId == null)
                {
                    return BadRequest(new { Code = 1, Message = "Vui lòng đăng ký thành viên" });
                }

                await eventService.Cancel(id, userZaloId.Value);

                return Ok(new
                {
                    Code = 0,
                    Message = "Hủy đăng ký thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        #region ADMIN FUNCTIONS

        [HttpPost("CheckIn/{eventId}")]
        public async Task<IActionResult> CheckIn([FromBody] CheckInData data, string eventId)
        {
            try
            {
                var result = await eventService.CheckInAsync(data.Code, eventId);

                return Ok(new
                {
                    Code = 0,
                    Message = "CheckIn thành công!",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("CheckInRange/{eventId}")]
        public async Task<IActionResult> CheckInRange([FromBody] CheckInRangeData data, string eventId)
        {
            try
            {
                var result = await eventService.CheckInMultipleAsync(data.Code, eventId);

                return Ok(new
                {
                    Code = 0,
                    Message = "CheckIn thành công!",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("CancelByCode/{eventId}")]
        public async Task<IActionResult> CancelByCode([FromBody] CheckInData data, string eventId)
        {
            try
            {
                await eventService.CancelByCode(eventId, data.Code);
                return Ok(new { Code = 0, Message = "Hủy thành công" });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("Participants/Export")]
        public async Task<IActionResult> ExportSubmissions([FromBody] string eventId)
        {
            try
            {
                var excelBytes = await eventService.ExportToExcelAsync(eventId);
                var fileName = $"event_export_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                return File(excelBytes,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    fileName);
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        #endregion
    }

    public class CheckInData
    {
        public required string Code { get; set; }
    }

    public class CheckInRangeData
    {
        public required List<string> Code { get; set; }
    }

}
