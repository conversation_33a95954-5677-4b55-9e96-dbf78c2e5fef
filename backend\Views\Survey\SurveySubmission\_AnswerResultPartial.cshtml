﻿@model MiniAppCore.Models.Responses.Surveys.QuestionResponse

<div class="question-card card">
    <div class="card-body">
        <div class="question-title d-flex justify-content-between align-items-start">
            <div class="me-2 flex-grow-1">@Html.Raw(Model.QuestionTitle)</div>
            @await Html.PartialAsync("SurveySubmission/_QuestionTypeBadgePartial", Model.Type)
            @if (Model.IsRequied)
            {
                <span class="badge bg-danger question-badge" title="Bắt buộc">
                    <i class="ri-asterisk me-1"></i> Bắt buộc
                </span>
            }
        </div>
        @if (Model.UserResponses?.Any() == true)
        {
            @foreach (var response in Model.UserResponses)
            {
                <div class="response-text position-relative ps-4">
                    <i class="bi bi-chat-quote text-muted position-absolute top-0 start-0 ms-2 mt-2"></i>
                    @response.InputValue
                </div>
            }
        }
    </div>
</div>
