﻿@using MiniAppCore.Entities.Branches
@using MiniAppCore.Enums
@using System.Globalization
@model MiniAppCore.Models.Responses.Bookings.BookingDetailResponse;

@{
    var containsBranch = User.Claims
        .Where(c => c.Type == "ViewPermission")
        .Any(c => c.Value.Contains("Branch"));

    // Thông tin chi nhánh
    var selectedBranchId = Model.Branch?.Id;
    var branches = ViewBag.Branches as List<Branch> ?? new List<Branch>();
}

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.Id" />
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Mã đặt lịch</label>
                            <input id="id" type="text" class="form-control" disabled value="@Model.Id" />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Ngày đặt lịch</label>
                            <input id="bookingDate" type="datetime-local" class="form-control" value="@Model.BookingDate.ToString("yyyy-MM-ddTHH:mm")" />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Trạng thái</label>
                            <select id="status" class="selectpicker form-control">
                                <option selected="@(Model.Status == 0)" value="0">Đang chờ</option>
                                <option selected="@(Model.Status == 1)" value="1">Xác nhận</option>
                                <option selected="@(Model.Status == 2)" value="2">Check in</option>
                                <option selected="@(Model.Status == 4)" value="4">Hoàn thành</option>
                                <option selected="@(Model.Status == 5)" value="5">Hủy</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Tên khách hàng</label>
                            <input id="memebershipName" type="text" class="form-control" value="@Model.UserZaloName" />
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Số điện thoại</label>
                            <input id="cusPhone" type="text" class="form-control" value="@Model.PhoneNumber" />
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Ghi chú</label>
                            <textarea id="notes" rows="3" class="form-control">@Model.Note</textarea>
                        </div>
                    </div>

                    <div class="col-md-12 @(containsBranch ? "" : "d-none")">
                        <div class="form-group">
                            <label for="branchId">Chi nhánh</label>
                            <div class="row align-items-center">
                                <div class="col-md-10">
                                    <select class="form-control" id="branchId" name="branchId" disabled>
                                        @foreach (var branch in branches)
                                        {
                                            <option value="@branch.Id"
                                                    selected="@(branch.Id == selectedBranchId)">
                                                @branch.Name
                                            </option>
                                        }
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-stretch">
                                    <button type="button"
                                            class="btn btn-outline-primary btn-block h-100 d-flex justify-content-center align-items-center"
                                            id="btnChangeBranch">
                                        <i class="ri-refresh-line mr-2"></i> Thay đổi
                                    </button>
                                </div>
                            </div>
                        </div>

                        @if (Model.Branch != null)
                        {
                            <div id="branchCard" class="card mt-3">
                                <div class="card-body d-flex align-items-center">
                                    <img id="branchImage"
                                         src="@Model.Branch?.Image"
                                         class="img-thumbnail rounded mr-3"
                                         style="width:80px; height:80px; object-fit:cover;"
                                         alt="Branch Image">
                                    <div>
                                        <h5 class="mb-1" id="branchName">@Model.Branch?.Name</h5>
                                        <p class="text-muted mb-0" id="branchAddress">@Model.Branch?.FullAddress</p>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            @* <p>Mã đơn hàng: <a href="/order?id=@Model.OrderId">@Model.OrderId</a></p> *@
                            <p>Chi tiết đặt lịch: </p>
                            <div class="d-flex scrollbar-thin" style="overflow-x: scroll; padding-bottom: 10px;">
                                @if (Model.BookingItems.Any())
                                {
                                    foreach (var item in Model.BookingItems)
                                    {
                                        <div class="card rounded-2 my-2 mx-2 col-3 shadow-sm">
                                            <div class="card-header text-center p-2 bg-light">
                                                <h5 class="m-0">@item.Name</h5>
                                            </div>
                                            <div class="card-body text-center p-2">
                                                <strong class="text-danger">
                                                    @item.OriginalPrice.ToString("N0", new CultureInfo("vi-VN")) ₫
                                                </strong>
                                            </div>
                                            <div class="card-body text-center p-2 d-flex gap-1 justify-content-center overflow-x-scroll scrollbar-thin">
                                                @foreach (var image in item.Images)
                                                {
                                                    <img src="~/images/logo.png" class="img-fluid rounded border p-1" style="width: 80px; height: 80px; object-fit: cover;" />
                                                }
                                            </div>
                                        </div>
                                    } 
                                }
                                else
                                {
                                    <p class="text-center">Không có dữ liệu</p>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        @* @if (string.IsNullOrEmpty(Model.Id)) *@
        @* { *@
        @*         <a asp-action="Index" asp-controller="Order" asp-route-create="true" class="btn btn-success">Tạo đơn hàng</a> *@
        @* } *@
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        if ('@(Model.Status == (short)EBooking.Canceled)' === "True") {
            // $('#status').attr('disabled', true);
        }

        const $select = $('#branchId');
        const $button = $('#btnChangeBranch');
        $select.select2();

        let isEditing = false;

        // Parse dữ liệu branches từ server
        const branchesData = @Html.Raw(Newtonsoft.Json.JsonConvert.SerializeObject(branches))

        // Toggle button click
        $button.on('click', function () {
            isEditing = !isEditing;
            $select.prop('disabled', !isEditing);

            if (isEditing) {
                // Trạng thái "Xong"
                $button
                    .html('<i class="ri-check-line mr-2"></i> Xong')
                    .removeClass('btn-outline-primary')
                    .addClass('btn-outline-success');
            } else {
                // Trạng thái ban đầu "Thay đổi"
                $button
                    .html('<i class="ri-refresh-line mr-2"></i> Thay đổi')
                    .removeClass('btn-outline-success')
                    .addClass('btn-outline-primary');
            }
        });

        // Handle khi user chọn chi nhánh mới
        $select.on('change', function () {
            const selectedId = $(this).val();
            const selectedBranch = branchesData.find(b => b.Id === selectedId);

            if (selectedBranch) {
                $('#branchImage').attr('src', selectedBranch.Image);
                $('#branchName').text(selectedBranch.Name);
                $('#branchAddress').text(selectedBranch.FullAddress);
            }

            // Reset về trạng thái ban đầu
            isEditing = false;
            $select.prop('disabled', true);
            $button
                .html('<i class="ri-refresh-line mr-2"></i> Thay đổi')
                .removeClass('btn-outline-success')
                .addClass('btn-outline-primary');
        });
    });
</script>