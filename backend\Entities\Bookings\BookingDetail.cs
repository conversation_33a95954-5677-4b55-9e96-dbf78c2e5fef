﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Bookings
{
    public class BookingDetail : BaseEntity
    {
        public long Quantity { get; set; }
        public string? Note { get; set; }
        public string? BookingId { get; set; }
        public string? BookingItemId { get; set; }

        public decimal OrginalPrice { get; set; }
        public decimal DiscountPrice { get; set; }
    }
}
