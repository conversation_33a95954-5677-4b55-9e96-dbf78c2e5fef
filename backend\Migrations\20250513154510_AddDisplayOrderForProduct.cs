﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddDisplayOrderForProduct : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "f5a6f3f71fed42569a0d5ab455034926");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "02c180315c1a49c9b3f6febfb3b89164");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "03f7d1e77d9a48129877d18b124cadf9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "05198f6674f34f4c99fd86970ccd42aa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "10b9c5cc3d524a0a9c90e3e1b9fcfe92");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "136e558715874c4cb99176b2d205e22c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1aa3926f799f49c69fcce18de58f820a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1ba8d5730c304f55a8055b90e9a20a3b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1c1648f7cded4e6a807ffd12ea2168a6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1ecc23eb61d54f22889533c13d16e444");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "253d7e92276d46b6932664d82d216551");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3a56f9dd017f43d59194373d21ce2600");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3ad0c65b89724d399427d190b7e55db4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4024fd5f767c4f61bdb66fe3509f9ca0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "43507a58519d4d56a68f33eeb31786bc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "47fea9eb900c4b58bd31aec61a22a260");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4d8a4037ff6543c682d0f02ee6441fc1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "60757f782b8341e4b92630a1a7391e8b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7b6cd2a56af14751a06e96fdebfbd573");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7ebc267b356441f28562b1766603deb2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "80b64e533851415d86e231d5b376a469");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "871b004cbbd247f3af00ff7edac3cc5c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8ec72bdb335a4cd9b9a57dac9d7c122e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "965d39f34b104a71940e8ce0aeace423");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9910174b4bcc4bffab332b119069deb5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9bc3025ae3604734b547bf7522618a1c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9cd7be1f1822427ba87455e76a9a1a97");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a91675a2859b470aa8f30ac55a3be5ef");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b34df1c70d5b4e1c827fbbb70ac4766b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b952cea0287c4658b2dff060aade4f70");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c773d86a8bc548ca8c2ba350a5353f66");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d038b9980cdf4b158073d6138bde798f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d46538406b524f1ab43d6e73a236ea0e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e56395c770b542778de23b529004755a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f6e14c5891ad48f792b3bc12fa7c490a");

            migrationBuilder.AddColumn<int>(
                name: "DisplayOrder",
                table: "Products",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "DisplayOrder",
                table: "BookingItems",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEL9lgyW8EhPnN9+bxrQE0ecGTeDoTBz57bNLqYU3wQRRbcTLa/0veNQ0E5QEswPf2A==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "247dcfe4db8342ecb76db88f30503138", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 13, 22, 45, 8, 871, DateTimeKind.Local).AddTicks(1307), "FeaturesButton", new DateTime(2025, 5, 13, 22, 45, 8, 871, DateTimeKind.Local).AddTicks(1310) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "02672e38c196412396c514ed85242d4e", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3672), true, "SurveyList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3672), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "0a6ea3cb77b847189c9ea9013ec60ed7", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3705), true, "CustomForm", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3706), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "0ef065a7dc0c4cb1b48d2f3aa1527577", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3669), true, "History", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3670), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "1e5cf7f4903e4a489f2f83d568b51cc9", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3660), true, "MembershipList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3660), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "21d838a2bf3c4bd8bf24973659b1cb19", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3701), true, "ShippingFeeConfig", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3701), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2546eb96fe0e43128fc37b2fcc6beedc", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3633), true, "Brand", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3633), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "285472479482460695f1c6495328c7e5", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3692), true, "TemplateUidList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3692), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "2ec4d869ade2407b8b572961ef2fc13d", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3685), true, "CampaignHistory", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3685), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "3a09a3960e094008aaec9237ce612bcc", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3630), true, "Category", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3631), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "3bcbd7af236147609fea006d93997bf0", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3664), true, "Rank", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3665), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "42f3bedd02f4477db4928cfd1b4449c0", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3681), true, "CampaignList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3681), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "4795ae3e1586459b9ac0576d2bca531f", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3694), true, "GeneralSetting", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3694), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "535819194e93483496bc1e7bb0bc3411", new DateTime(2025, 5, 13, 22, 45, 8, 869, DateTimeKind.Local).AddTicks(7467), true, "Overview", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(2865), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "538bf4bc1a22434e8f5eca78926ef53a", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3689), true, "EventTemplateHistory", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3690), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "5767dcedda9b4611ae63c994041361cd", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3635), true, "ProductProperty", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3636), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "6d7cb89a0f104075a23573c734479f43", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3674), true, "GameList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3674), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "6eeaebb18cb54279929abc69f03494ac", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3653), true, "DiscountList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3653), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "75b3a273e92246e5a6113baaefff7f3d", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3676), true, "History", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3677), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "82a59c99ef234a10ba87ecbc53df3f82", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3617), true, "AffiliateList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3618), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "8f3ae00df8f0418d93de3a9cf5501ed9", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3687), true, "EventTemplateList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3687), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "9ffc1cdfcbd842baafb79038ecb73b9b", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3646), true, "BookingList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3646), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "a3ebebd357d04fb39dab385fcbf2a165", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3655), true, "Promotion", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3656), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "ab44bd5b884a4414ac258ef479e42004", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3698), true, "MembershipExtendDefaults", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3699), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "b0d709fb8d8a44a984a64d56d86e586d", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3620), true, "ArticleCategory", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3620), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "b0e1263a2a0d4337b6ff5df57ec4282b", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3651), true, "InvoiceTemplate", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3651), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "b3aae582034941e69ef0c31f606078d4", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3638), true, "ProductList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3638), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "b4985bf5df234717af540c99612fcc39", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3667), true, "Tag", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3667), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "ca8bd7336d2e470ebf2877450cd02614", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3622), true, "ArticleList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3623), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "cf6cea67545b4a4a85c845e74d63b762", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3612), true, "BranchList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3614), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "d77a24af3476400fac0a7b61630e1997", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3658), true, "VoucherList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3658), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "dbe51b761e1b4f088df68fcf0138313d", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3678), true, "GamePrize", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3679), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "e3b7674875bf47a9a37aa8af73cd7721", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3643), true, "BookingItem", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3644), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "e687fcafe0364706923bf2116d21f071", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3696), true, "EnableFeatures", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3697), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "e950dbd9dba44a30af0795e68a28e017", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3648), true, "OrderList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3649), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "247dcfe4db8342ecb76db88f30503138");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "02672e38c196412396c514ed85242d4e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0a6ea3cb77b847189c9ea9013ec60ed7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0ef065a7dc0c4cb1b48d2f3aa1527577");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1e5cf7f4903e4a489f2f83d568b51cc9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "21d838a2bf3c4bd8bf24973659b1cb19");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2546eb96fe0e43128fc37b2fcc6beedc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "285472479482460695f1c6495328c7e5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2ec4d869ade2407b8b572961ef2fc13d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3a09a3960e094008aaec9237ce612bcc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3bcbd7af236147609fea006d93997bf0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "42f3bedd02f4477db4928cfd1b4449c0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4795ae3e1586459b9ac0576d2bca531f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "535819194e93483496bc1e7bb0bc3411");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "538bf4bc1a22434e8f5eca78926ef53a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5767dcedda9b4611ae63c994041361cd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6d7cb89a0f104075a23573c734479f43");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6eeaebb18cb54279929abc69f03494ac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "75b3a273e92246e5a6113baaefff7f3d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "82a59c99ef234a10ba87ecbc53df3f82");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8f3ae00df8f0418d93de3a9cf5501ed9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ffc1cdfcbd842baafb79038ecb73b9b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a3ebebd357d04fb39dab385fcbf2a165");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ab44bd5b884a4414ac258ef479e42004");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b0d709fb8d8a44a984a64d56d86e586d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b0e1263a2a0d4337b6ff5df57ec4282b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b3aae582034941e69ef0c31f606078d4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b4985bf5df234717af540c99612fcc39");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ca8bd7336d2e470ebf2877450cd02614");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cf6cea67545b4a4a85c845e74d63b762");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d77a24af3476400fac0a7b61630e1997");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dbe51b761e1b4f088df68fcf0138313d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e3b7674875bf47a9a37aa8af73cd7721");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e687fcafe0364706923bf2116d21f071");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e950dbd9dba44a30af0795e68a28e017");

            migrationBuilder.DropColumn(
                name: "DisplayOrder",
                table: "Products");

            migrationBuilder.DropColumn(
                name: "DisplayOrder",
                table: "BookingItems");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEJ4O5bMNitKXFToS7XHWO2AHZhjjc0jy6VPgTjp3mWWjwij0hp0kWt87XrUfYBv9gw==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "f5a6f3f71fed42569a0d5ab455034926", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 6, 9, 40, 42, 650, DateTimeKind.Local).AddTicks(5343), "FeaturesButton", new DateTime(2025, 5, 6, 9, 40, 42, 650, DateTimeKind.Local).AddTicks(5346) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "02c180315c1a49c9b3f6febfb3b89164", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5001), true, "GameList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5002), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "03f7d1e77d9a48129877d18b124cadf9", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5013), true, "EventTemplateList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5014), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "05198f6674f34f4c99fd86970ccd42aa", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5003), true, "History", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5004), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "10b9c5cc3d524a0a9c90e3e1b9fcfe92", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4907), true, "Brand", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4908), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "136e558715874c4cb99176b2d205e22c", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4892), true, "AffiliateList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4892), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "1aa3926f799f49c69fcce18de58f820a", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4916), true, "ProductList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4916), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "1ba8d5730c304f55a8055b90e9a20a3b", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4999), true, "SurveyList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5000), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "1c1648f7cded4e6a807ffd12ea2168a6", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4887), true, "BranchList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4888), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "1ecc23eb61d54f22889533c13d16e444", new DateTime(2025, 5, 6, 9, 40, 42, 648, DateTimeKind.Local).AddTicks(9217), true, "Overview", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(3898), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "253d7e92276d46b6932664d82d216551", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4983), true, "Promotion", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4983), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "3a56f9dd017f43d59194373d21ce2600", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4972), true, "BookingItem", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4972), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "3ad0c65b89724d399427d190b7e55db4", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5019), true, "GeneralSetting", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5020), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "4024fd5f767c4f61bdb66fe3509f9ca0", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4997), true, "History", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4997), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "43507a58519d4d56a68f33eeb31786bc", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5024), true, "MembershipExtendDefaults", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5024), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "47fea9eb900c4b58bd31aec61a22a260", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5006), true, "GamePrize", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5006), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "4d8a4037ff6543c682d0f02ee6441fc1", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4978), true, "InvoiceTemplate", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4979), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "60757f782b8341e4b92630a1a7391e8b", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4993), true, "Rank", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4993), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "7b6cd2a56af14751a06e96fdebfbd573", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4981), true, "DiscountList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4981), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "7ebc267b356441f28562b1766603deb2", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5017), true, "TemplateUidList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5018), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "80b64e533851415d86e231d5b376a469", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5027), true, "ShippingFeeConfig", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5027), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "871b004cbbd247f3af00ff7edac3cc5c", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5030), true, "CustomForm", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5030), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8ec72bdb335a4cd9b9a57dac9d7c122e", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4995), true, "Tag", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4995), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "965d39f34b104a71940e8ce0aeace423", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4896), true, "ArticleList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4896), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "9910174b4bcc4bffab332b119069deb5", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5015), true, "EventTemplateHistory", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5016), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "9bc3025ae3604734b547bf7522618a1c", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4985), true, "VoucherList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4985), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "9cd7be1f1822427ba87455e76a9a1a97", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4974), true, "BookingList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4975), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "a91675a2859b470aa8f30ac55a3be5ef", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4910), true, "ProductProperty", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4910), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "b34df1c70d5b4e1c827fbbb70ac4766b", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5021), true, "EnableFeatures", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5022), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "b952cea0287c4658b2dff060aade4f70", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4905), true, "Category", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4906), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c773d86a8bc548ca8c2ba350a5353f66", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5009), true, "CampaignList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5009), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d038b9980cdf4b158073d6138bde798f", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5011), true, "CampaignHistory", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5012), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d46538406b524f1ab43d6e73a236ea0e", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4989), true, "MembershipList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4989), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "e56395c770b542778de23b529004755a", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4976), true, "OrderList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4977), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "f6e14c5891ad48f792b3bc12fa7c490a", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4894), true, "ArticleCategory", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4894), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" }
                });
        }
    }
}
