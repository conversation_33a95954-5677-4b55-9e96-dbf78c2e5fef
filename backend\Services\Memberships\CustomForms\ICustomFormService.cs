﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.FormCustoms;
using MiniAppCore.Models.Requests.Memberships;
using MiniAppCore.Models.Responses.Memberships;

namespace MiniAppCore.Services.Memberships.CustomForms
{
    public interface ICustomFormService : IService<CustomForm>
    {
        // Task<PagedResult<FormCustomResponse>> GetPage(RequestQuery query);
        Task<FormCustomResponse> GetActiveCustomFormAsync(string formId);
        Task<FormCustomResponse> GetByIdAsync(string id, bool? includesDeactive);

        Task<int> CreateAsync(CustomFormRequest request);
        Task<int> UpdateAsync(string id, CustomFormRequest request);

    }
}
