﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.LuckyWheels;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.LuckyWheels;
using MiniAppCore.Models.Requests.GamePrizes;
using MiniAppCore.Models.Responses.Gamification.GamePrizes;

namespace MiniAppCore.Services.Gamifications.GamePrizes
{
    public class GamePrizeService(IUnitOfWork unitOfWork,
                                  IMapper mapper,
                                  IWebHostEnvironment env,
                                  IHttpContextAccessor httpContextAccessor) : Service<GamePrize>(unitOfWork), IGamePrizeService
    {
        private readonly IRepository<GamePrizeConfig> _gamePrizeConfigRepo = unitOfWork.GetRepository<GamePrizeConfig>();
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        #region GamePrize CRUD

        public override async Task<PagedResult<GamePrize>> GetPage(IRequestQuery query)
        {
            var result = await base.GetPage(query);
            result.Data.ToList().ForEach(x => x.Image = $"{hostUrl}/uploads/images/gamePrizes/{x.Image}");
            return result;
        }

        public async Task<int> CreateAsync(GamePrizeRequest gamePrizeRequest)
        {
            var gamePrize = mapper.Map<GamePrize>(gamePrizeRequest);
            if (gamePrizeRequest.Image != null)
            {
                var files = new List<IFormFile> { gamePrizeRequest.Image };
                gamePrize.Image = await ProcessUpload(files);
            }
            return await base.CreateAsync(gamePrize); ;
        }

        public async Task<int> UpdateAsync(string id, GamePrizeRequest gamePrizeRequest)
        {
            var existingGamePrize = await GetByIdAsync(id);
            if (existingGamePrize == null)
            {
                return 0;
            }
            string oldImage = existingGamePrize.Image ?? string.Empty;
            // Update properties using mapper
            mapper.Map(gamePrizeRequest, existingGamePrize);
            // Process new image if provided
            if (gamePrizeRequest.Image != null)
            {
                var files = new List<IFormFile> { gamePrizeRequest.Image };
                existingGamePrize.Image = await ProcessUpload(files);
                // Remove old image
                if (!string.IsNullOrEmpty(oldImage))
                {
                    RemoveOldImage(oldImage);
                }
            }
            return await base.UpdateAsync(existingGamePrize); ;
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var gamePrize = await GetByIdAsync(id);
            if (gamePrize == null)
            {
                return 0;
            }

            if (!string.IsNullOrEmpty(gamePrize.Image))
            {
                RemoveOldImage(gamePrize.Image);
            }

            // xóa các cấu hình của game prize này 
            var configs = _gamePrizeConfigRepo.AsQueryable().Where(x => x.GamePrizeId == id);
            _gamePrizeConfigRepo.DeleteRange(configs);

            return await base.DeleteAsync(gamePrize);
        }

        #endregion

        #region Game Prize Config (LuckyWheel,....)

        public async Task<int> DeleteGamePrizeConfigsByGameId(string gameId)
        {
            var gamePrizeConfigs = _gamePrizeConfigRepo.AsQueryable().Where(x => x.GameId == gameId);
            if (!gamePrizeConfigs.Any())
            {
                return 0;
            }
            _gamePrizeConfigRepo.DeleteRange(gamePrizeConfigs);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> CreateListGamePrizeConfigAsync(string gameId, List<GamePrizeConfigDTO> gamePrize)
        {
            var gamePrizeConfig = gamePrize.Select(x => new GamePrizeConfig()
            {
                WinRate = x.WinRate,
                Ranking = x.Ranking,
                Position = x.Position,
                Quantity = x.Quantity,
                DailyLimit = x.DailyLimit,

                GameId = gameId,
                GamePrizeId = x.GamePrizeId,
            }).ToList();
            _gamePrizeConfigRepo.AddRange(gamePrizeConfig);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<List<GamePrizeResponse>> GetGamePrizesByGameId(string gameId)
        {
            return await GetMappedPrizeResult<GamePrizeResponse>(gameId);
        }

        public async Task<List<GamePrizeDetailResponse>> GetGamePrizesDetailByGameId(string gameId)
        {
            return await GetMappedPrizeResult<GamePrizeDetailResponse>(gameId);
        }

        public async Task<List<GamePrizeConfigDTO>> GetGameConfiguredPrizesByGameId(string gameId)
        {
            return await GetMappedPrizeResult<GamePrizeConfigDTO>(gameId);
        }

        public async Task<List<ConfiguredGamePrizeDTO>> GetGamePrizesConfiguredByGameId(string gameId)
        {
            return await GetMappedPrizeResult<ConfiguredGamePrizeDTO>(gameId);
        }

        private async Task<List<T>> GetMappedPrizeResult<T>(string gameId)
        {
            var (gamePrizes, gamePrizeConfigs) = await GetGamePrizeAndConfigByGameId(gameId);
            var result = gamePrizeConfigs
                .Select(config =>
                {
                    var prize = gamePrizes.FirstOrDefault(x => x.Id == config.GamePrizeId);
                    return prize != null ? mapper.Map<T>((prize, config)) : default;
                })
                .Where(mapped => mapped != null)
                .Select(mapped => mapped!)
                .ToList()!;

            return result;
        }

        private async Task<(List<GamePrize>, List<GamePrizeConfig>)> GetGamePrizeAndConfigByGameId(string gameId)
        {
            var gamePrizeConfigs = await _gamePrizeConfigRepo.AsQueryable().AsNoTracking()
                .Where(x => x.GameId == gameId).OrderBy(x => x.Ranking).ToListAsync();

            var gamePrizeIds = gamePrizeConfigs.Select(x => x.GamePrizeId).ToList();
            var gamePrize = await GetByIdsAsync(gamePrizeIds);

            return (gamePrize.ToList(), gamePrizeConfigs);
        }

        #endregion

        #region Hanlde Image

        private async Task<string> ProcessUpload(List<IFormFile>? files)
        {
            var stringFiles = string.Empty;
            if (files != null)
            {
                var savePath = Path.Combine(env.WebRootPath, "uploads/images/gamePrizes");
                var fileResult = await FileHandler.SaveFiles(files, savePath);
                stringFiles = string.Join(",", fileResult);
            }
            return stringFiles;
        }

        private void RemoveOldImage(string listImage)
        {
            var images = listImage.Split(",").Select(x => Path.Combine(env.WebRootPath, "uploads/images/gamePrizes", x)).ToList();
            FileHandler.RemoveFiles(images);
        }

        #endregion
    }
}
