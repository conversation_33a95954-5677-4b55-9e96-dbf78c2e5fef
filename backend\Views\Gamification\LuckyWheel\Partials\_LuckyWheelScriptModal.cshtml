﻿<script>
    $(document).ready(function () {
        // Initialize Quill editor
        InitialEditor();

        // Initialize Select2
        initSelect2();

        // File input handling
        $("#imageFile").on("change", function () {
            let fileName = $(this).val().split("\\").pop();
            $(this).next(".custom-file-label").html(fileName || "Chọn file...");
            ShowPreview(this);
        });

        // Remove prize row
        $(document).on("click", ".remove-prize-btn", function () {
            $(this).closest("tr").remove();
            validateWinRates();
        });

        // Win rate validation
        $(document).on("change", ".winrate-input", function () {
            validateWinRates();
        });

        // Save button
        $("#saveButton").on("click", function () {
            if (validateForm()) {
                var id = $("#id").val() || '';
                HandleSaveOrUpdate(id);
            }
        });

        // Job type toggle functionality - REMOVED (không còn cần thiết)

        // Auto add spin points toggle
        $("#enableAutoSpinPoints").on("change", function () {
            if ($(this).val() === 'true') {
                $("#spinPointsContainer").show();
            } else {
                $("#spinPointsContainer").hide();
            }
        });
    });

    function initSelect2() {
        $('.select2-prizes').select2({
            placeholder: 'Chọn phần thưởng...',
            allowClear: false,
            minimumInputLength: 0,
            dropdownParent: $("#modal-wheel"),
            ajax: {
                url: '/api/GamePrizes',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        page: params.page || 1,
                        pageSize: 10,
                        keyword: params.term || ''
                    };
                },
                processResults: function (response, params) {
                    params.page = params.page || 1;

                    return {
                        results: response.data.map(function (prize) {
                            return {
                                id: prize.id,
                                text: prize.name,
                                image: prize.image,
                                value: prize.value,
                                type: prize.type
                            };
                        }),
                        pagination: {
                            more: params.page < response.totalPages
                        }
                    };
                },
                cache: true
            },
            templateResult: formatPrize,
            templateSelection: formatPrizeSelection
        });

        // Load pre-selected values
        $('.select2-prizes').each(function () {
            let prizeId = $(this).data('prize-id');
            if (prizeId) {
                $.ajax({
                    url: '/api/GamePrizes',
                    type: 'GET',
                    data: {
                        keyword: prizeId
                    },
                    success: function (response) {
                        if (response && response.data && response.data.length > 0) {
                            const prize = response.data[0];
                            let option = new Option(prize.name, prize.id, true, true);
                            $(this).append(option).trigger('change');
                        }
                    }.bind(this),
                    error: function () {
                        console.error('Không thể tải dữ liệu phần thưởng');
                    }
                });
            }
        });
    }

    function formatPrize(prize) {
        if (!prize.id) {
            return prize.text;
        }

        let $prize = $(`
          <div class="d-flex align-items-center">
            ${prize.image ? `<img src="${prize.image}" class="mr-2" style="width:30px; height:30px; object-fit:contain;" />` : ''}
            <div>
              <div class="font-weight-bold">${prize.text}</div>
              ${prize.value ? `<div class="small text-muted">Giá trị: ${prize.value}</div>` : ''}
            </div>
          </div>
        `);

        return $prize;
    }

    function formatPrizeSelection(prize) {
        return prize.text || 'Chọn phần thưởng...';
    }

    function addPrizeRow() {
        let newRow = `
            <tr class="prize-row">
                <td>
                    <select class="form-control select2-prizes">
                        <option></option>
                    </select>
                </td>
                <td><input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\\d)/g, '')" min="0" max="359" class="form-control position-input" value="0" /></td>
                <td><input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\\d)/g, '')" min="1" class="form-control ranking-input" value="1" /></td>
                <td><input type="number" oninput="validateReviewPoint(this, 0, 100)" step="0.01" min="0" max="100" class="form-control winrate-input" value="0" /></td>
                <td><input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\\d)/g, '')" min="0" class="form-control daily-limit-input" value="0" /></td>
                <td><input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\\d)/g, '')" min="0" class="form-control total-quantity-input" value="0" /></td>
                <td class="text-center">
                    <button type="button" class="btn btn-sm btn-danger remove-prize-btn">
                        <i class="ri-delete-bin-line fs-6 mr-0"></i>
                    </button>
                </td>
            </tr>
        `;
        $("#prizesTable tbody").append(newRow);

        // Initialize Select2 for the new row
        let newSelect = $("#prizesTable tbody tr:last-child .select2-prizes");

        // Reinitialize Select2 on the newly created element
        $('.select2-prizes').not('.select2-hidden-accessible').each(function () {
            $(this).select2({
                placeholder: 'Chọn phần thưởng...',
                allowClear: false,
                minimumInputLength: 0,
                ajax: {
                    url: '/api/GamePrizes',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            page: params.page || 1,
                            pageSize: 10,
                            keyword: params.term || ''
                        };
                    },
                    processResults: function (response, params) {
                        params.page = params.page || 1;

                        return {
                            results: response.data.map(function (prize) {
                                return {
                                    id: prize.id,
                                    text: prize.name,
                                    image: prize.image,
                                    value: prize.value,
                                    type: prize.type
                                };
                            }),
                            pagination: {
                                more: params.page < response.totalPages
                            }
                        };
                    },
                    cache: true
                },
                templateResult: formatPrize,
                templateSelection: formatPrizeSelection
            });
        });
    }

    function validateWinRates() {
        let total = 0;
        $(".winrate-input").each(function () {
            total += parseFloat($(this).val() || 0);
        });

        if (total > 100) {
            AlertResponse('Tổng tỷ lệ trúng thưởng đang vượt quá 100%. Vui lòng điều chỉnh lại.', 'warning');
            return false;
        }
        return true;
    }

    function validateForm() {
        let isValid = true;

        // Check required fields
        if (!$("#name").val()) {
            $("#name").addClass("is-invalid");
            isValid = false;
        } else {
            $("#name").removeClass("is-invalid");
        }

        if (!$("#requiredPoints").val()) {
            $("#requiredPoints").addClass("is-invalid");
            isValid = false;
        } else {
            $("#requiredPoints").removeClass("is-invalid");
        }

        if (!$("#startDate").val()) {
            $("#startDate").addClass("is-invalid");
            isValid = false;
        } else {
            $("#startDate").removeClass("is-invalid");
        }

        if (!$("#expiryDate").val()) {
            $("#expiryDate").addClass("is-invalid");
            isValid = false;
        } else {
            $("#expiryDate").removeClass("is-invalid");
        }

        // Check prizes
        if ($(".prize-row").length === 0) {
            AlertResponse('Vui lòng thêm ít nhất một phần thưởng.', 'warning');
            return false;
        }

        return isValid && validateWinRates();
    }

    function CollectGamePrizes() {
        const prizes = [];
        $(".prize-row").each(function () {
            const row = $(this);
            const prizeId = row.find(".select2-prizes").val();
            if (prizeId) {
                prizes.push({
                    gamePrizeId: prizeId,
                    position: parseInt(row.find(".position-input").val()),
                    ranking: parseInt(row.find(".ranking-input").val()),
                    winRate: parseFloat(row.find(".winrate-input").val()),
                    dailyLimit: parseInt(row.find(".daily-limit-input").val()),
                    quantity: parseInt(row.find(".total-quantity-input").val())
                });
            }
        });
        return prizes;
    }

</script>