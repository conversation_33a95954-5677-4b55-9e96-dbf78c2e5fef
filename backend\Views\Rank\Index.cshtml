﻿@using MiniAppCore.Entities.Categories
@using MiniAppCore.Entities.Settings
@model Rank
<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Hạng thành viên</h4>
                <p class="mb-0">
                    <PERSON><PERSON><PERSON> là nơi bạn quản lý các hạng thành viên của hệ thống. <br>
                    Sử dụng danh sách danh mục để mô tả lĩnh vực kinh doanh chính của từng hạng. <br>
                    <PERSON>h<PERSON>n vào tên danh mục để thêm vào danh sách hạng thành viên.
                </p>
            </div>

            <button onclick="GetFormCategory('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-category">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0; z-index: -1;"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-rank" class="data-table table mb-0 tbl-server-info"> </table>
        </div>
    </div>
</div>

<div id="modal-category" class="modal fade" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<div id="modal-pullCategory" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-pullContent" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        let removedOldImages = [];
        let newImages = [];
        let currentImages = 0;
        const catgoryDatadefault = {};

        $(document).ready(function () {
            GetListRank();
            $('#search').on('input', search);

        });

        $(document).on("click", ".btn-preview-remove", function () {
            const fullImageUrl = $(this).data("url");
            const imageUrl = fullImageUrl.split("/").pop();

            $(this).parent().remove();

            if (!removedOldImages.includes(imageUrl)) {
                removedOldImages.push(imageUrl);
            }
        });

        function ClearForm() {
            $("form").trigger("reset");
            ShowPreview();
        }

        function ShowPreview(event) {
            if (!event) return;

            const files = event.target.files;

            // Kiểm tra tất cả file phải là ảnh
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (!file.type.startsWith("image/")) {
                    AlertResponse("Chỉ được upload ảnh!", "warning");
                    event.target.value = ""; // Reset input
                    return;
                }
            }

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.readAsDataURL(file);
                    reader.onload = (e) => {
                        const previewItem = $(`
                                                            <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height: 170px;">
                                                                        <img src="${e.target.result}" class="card-img-top h-100" alt="Hình ảnh" style="object-fit:contain" />
                                                                        <span class="btn-preview-remove">x</span>
                                                                    </div>`);

                        previewItem.find('.btn-preview-remove').click(function () {
                            $(this).parent().remove();
                            const currentFiles = Array.from(event.target.files);
                            const newFiles = currentFiles.filter(f => f !== file);
                        });

                        newImages.push(file);
                        $("#preview").append(previewItem);
                    };
                }
            }
        }

        function GetListRank() {
            table = new DataTable("#list-rank", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    // const type = $("#filter-type").val();
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetPage", "Ranks")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword,
                            type: "sanpham"
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                index: data.start + index + 1, // Số thứ tự
                                name: `<div class="d-flex align-items-center">
                                                                        <img src="${item.images[0] ? item.images[0] : "/images/no-image-1.png"}" style="background-color: #f0f0f0; padding: 2px; border-radius: 8px;" class="img-fluid rounded avatar-50 mr-3" alt="image" />
                                                                        <div>
                                                                            ${item.name}
                                                                        </div>
                                                                    </div>`, // Hiển thị ảnh đại diện và tên
                                type: "Sản phẩm", // Giả sử là "Sản phẩm", có thể thay đổi tùy nhu cầu
                                description: item.description, // Mô tả
                                rankingPoint: item.rankingPoint, // Formatted ranking points
                                isActive: item.isActive ? "Đang hoạt động" : "Ngưng hoạt động", // Mô tả
                                isDefault: item.isDefault ? "Mặc định" : "-", // Mô tả
                                actions: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                                        <a onclick="GetFormCategory('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                                            <i class="ri-edit-line fs-6 mr-0"></i>
                                                                        </a>
                                                                        <a onclick="DeleteCategory('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                                            <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                                        </a>
                                                                    </div>`, // Các hành động (Xem chi tiết, Xóa)
                                hexColor: item.hexColor ? item.hexColor.toUpperCase() : "", // Màu sắc (nếu có)
                                colorPicker: `<input type="color" id="head" name="head" value="${item.hexColor}" disabled />`, // Màu sắc dưới dạng màu picker
                                id: item.id // ID của mục
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "index", className: 'text-center', width: '5%' },
                    { title: "Tên", data: "name", width: '20%' },
                    { title: "Điểm yêu cầu", data: "rankingPoint", className: 'text-center', width: '10%' },
                    { title: "Trạng thái", data: "isActive", className: 'text-center', width: '10%' },
                    { title: "Mặc định", data: "isDefault", className: 'text-center', width: '10%' },
                    { title: "Mô tả", data: "description", width: '20%' },
                    { title: "Thao tác", data: "actions", width: '10%' },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormCategory(id) {
            const url = id ? `@Url.Action("Detail", "Rank")/${id}` : "@Url.Action("Create", "Rank")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-category").modal("toggle");

                    $("#type").select2();

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function DeleteCategory(id) {
            const url = `/api/ranks/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const data = {
                id,
                name: $("#name").val()?.trim(),
                isActive: $("#isActive").val() === "true", // Trạng thái (Hoạt động/Không hoạt động)
                isDefault: $("#isDefault").val() === "true", // Mặc định (Có/Không)
                description: $("#description").val()?.trim(), // Mô tả
                rankingPoint: parseInt($("#rankingPoint").val()) || 0,
                convertRate: parseFloat($("#convertRate").val()) || 0
            }

            const formData = new FormData();

            formData.append("id", id);
            formData.append("isActive", data.isActive);
            formData.append("isDefault", data.isDefault);
            formData.append("name", data.name);
            formData.append("description", data.description);
            formData.append("rankingPoint", data.rankingPoint);
            formData.append("convertRate", data.convertRate);

            // Xử lý ảnh
            const files = $('#pics')[0].files;
            if (files.length > 0) {
                for (let i = 0; i < files.length; i++) {
                    formData.append('images', files[i]);
                }
            }

            // Nếu có ảnh đã bị xóa, cần xử lý
            if (removedOldImages.length > 0) {
                for (const removedImage of removedOldImages) {
                    formData.append('removedOldImages', removedImage);
                }
            }

            const url = id ? `/api/ranks/${id}` : '/api/ranks';
            const method = id ? 'PUT' : 'POST'

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    }
                    $("#modal-category").modal("toggle");
                },
                error: function (err) {
                    AlertResponse('Thêm mới danh mục thất bại!', 'error')
                }
            });
        }
    </script>
}
