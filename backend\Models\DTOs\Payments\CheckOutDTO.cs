﻿namespace MiniAppCore.Models.DTOs.Payments
{
    public class CheckOutDTO
    {
        public string? Desc { get; set; }
        public decimal Amount { get; set; }
        public List<Dictionary<string, string>>? Items { get; set; }
        public required ExtraData ExtraData { get; set; }
        public string? MacData { get; set; }
    }

    public class ExtraData
    {
        public string? storeName { get; set; } = "INCOM SaiGon";
        public string? storeId { get; set; } = "2152012";
        public string? orderGroupId { get; set; } = "2152012";
        public string? myTransactionId { get; set; }
        public string? notes { get; set; }
    }
}
