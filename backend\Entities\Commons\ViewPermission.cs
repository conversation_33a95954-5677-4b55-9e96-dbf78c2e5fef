﻿using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Commons
{
    public class ViewPermission : BaseEntity
    {
        [MaxLength(50)]
        public required string ViewId { get; set; } // Id là số tên của view đó

        [MaxLength(50)]
        public required string SubViewId { get; set; }

        [MaxLength(50)]
        public required string UserId { get; set; }
        public bool IsActive { get; set; } = true;
    }
}
