﻿@model MiniAppCore.Entities.Settings.Rank;
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.Id" />
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Tên hạng <span style="color:red">*</span></label>
                            <input id="name" type="text" class="form-control" value="@Model.Name" placeholder="Tên hạng... " data-errors="Vui lòng nhập tên hạng." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <!-- Adding RankingPoint field -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Điểm yêu cầu <span style="color:red">*</span></label>
                            <input id="rankingPoint" type="number" class="form-control" value="@Model.RankingPoint" placeholder="Điểm yêu cầu để đạt hạng này" min="0" required>
                            <small class="form-text text-muted">
                                Số điểm tích lũy tối thiểu để đạt hạng này
                            </small>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <!-- Adding ConvertRate field -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Tỷ lệ quy đổi <span style="color:red">*</span></label>
                            <input id="convertRate" type="number" class="form-control" value="@Model.ConvertRate" placeholder="Tỷ lệ quy đổi điểm" min="0" step="0.01" required>
                            <small class="form-text text-muted">
                                Tỷ lệ quy đổi điểm thưởng (VD: 0.5 = 50% tỷ lệ quy đổi)
                            </small>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Trạng thái</label>
                            <select id="isActive" class="form-control">
                                <option value="true" selected="@(Model.IsActive)">Hoạt động</option>
                                <option value="false" selected="@(!Model.IsActive)">Không hoạt động</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Mặc định </label>
                            <select id="isDefault" class="form-control">
                                <option value="true" selected="@(Model.IsDefault)">Có</option>
                                <option value="false" selected="@(!Model.IsDefault)">Không</option>
                            </select>
                            <small class="form-text text-muted">
                                Khi chọn "Có", các khách hàng mới đăng ký sẽ được gán vào hạng này.
                            </small>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="descr">Mô tả hạng thành viên</label>
                            <textarea id="description" class="form-control" rows="4">@Model.Description</textarea>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Ảnh</label>
                            <input type="file" id="pics" class="form-control image-file" name="pic" accept="image/*"
                                   onchange="handleImagePreview(this, { width: 0, height: 0 }, null, '#preview', {isSingleImage: true, defaultImagePath: '/images/no-image-2.jpg'})">
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div id="preview" class="row d-flex flex-nowrap" style="overflow-x: scroll;">
                            @if (!string.IsNullOrEmpty(Model.Image))
                            {
                                var images = Model.Image.Split(",").Select(x => $"/uploads/images/ranks/{x}");
                                foreach (var item in images)
                                {
                                    <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                        <img src="@item" class="card-img-top h-100" alt="Alternate Text" style="object-fit:contain" />
                                        <span class="btn-preview-remove" data-url="@item">x</span>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (User.IsInRole("ADMIN"))
        {
            <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
            <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
        }
    </div>
</div>

<script>
    $(document).ready(() => {
        currentImages = $("#preview .image-preview img").length;
    });
</script>