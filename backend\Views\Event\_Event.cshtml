﻿﻿@using MiniAppCore.Enums
@model MiniAppCore.Entities.Events.Event;

<style>
    .error-message {
        color: red;
        font-size: 12px;
        margin-top: 4px;
    }
</style>
<div class="modal-content modal-xl">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <form>
            <input type="hidden" value="@Model.Id" />
            <div class="row">
                <!-- THÔNG TIN CƠ BẢN -->
                <div class="col-12 mb-3">
                    <h6 class="text-primary">THÔNG TIN SỰ KIỆN</h6>
                    <hr>
                </div>
                <!-- Tiêu đề sự kiện -->
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Tiêu đề sự kiện <span class="text-danger">*</span></label>
                        <input id="title" type="text" class="form-control" value="@Model.Title" placeholder="Tiêu đề sự kiện..." required>
                        <span class="error-message" id="error-title"></span>
                    </div>
                </div>
                <!-- Nội dung sự kiện -->
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Nội dung sự kiện</label>
                        <div id="content" class="rounded-bottom" style="height: 150px" data-type="content">@Html.Raw(Model.Content)</div>
                        <span class="error-message" id="error-content"></span>
                    </div>
                </div>

                <!-- THỜI GIAN & ĐỊA ĐIỂM -->
                <div class="col-12 mb-3 mt-3">
                    <h6 class="text-primary">THỜI GIAN & ĐỊA ĐIỂM</h6>
                    <hr>
                </div>
                <!-- Thời gian bắt đầu -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Thời gian bắt đầu <span class="text-danger">*</span></label>
                        <input id="startDate" type="datetime-local" class="form-control" value="@Model.StartTime.ToString("yyyy-MM-ddTHH:mm")" required>
                        <span class="error-message" id="error-startDate"></span>
                    </div>
                </div>
                <!-- Thời gian kết thúc -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Thời gian kết thúc <span class="text-danger">*</span></label>
                        <input id="expiryDate" type="datetime-local" class="form-control" value="@Model.EndTime.ToString("yyyy-MM-ddTHH:mm")" required>
                        <span class="error-message" id="error-expiryDate"></span>
                    </div>
                </div>
                <!-- Hình thức tổ chức -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Hình thức tổ chức <span class="text-danger">*</span></label>
                        <select id="type" class="form-control">
                            <option value="1" selected="@(Model.Type == EEvent.Online)">Online</option>
                            <option value="2" selected="@(Model.Type == EEvent.Offline)">Offline</option>
                        </select>
                        <span class="error-message" id="error-type"></span>
                    </div>
                </div>
                <!-- Địa chỉ -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Địa chỉ <span class="text-danger">*</span></label>
                        <input id="streetLine" type="text" class="form-control" placeholder="Nhập địa chỉ cụ thể" value="@Model.Address" required />
                        <span class="error-message" id="error-streetLine"></span>
                    </div>
                </div>
                <!-- Đường dẫn tham dự -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Đường dẫn tham dự <span class="text-danger">*</span></label>
                        <input id="meetingLink" type="url" class="form-control" placeholder="Địa chỉ url tham dự Online" value="@Model.MeetingLink" required />
                        <span class="error-message" id="error-meetingLink"></span>
                    </div>
                </div>
                <!-- Đường dẫn Google Map -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Đường dẫn Google Map <span class="text-danger">*</span></label>
                        <input id="googleMapURL" type="url" class="form-control" placeholder="Địa chỉ google map tham dự Offline" value="@Model.GoogleMapURL" required />
                        <small class="text-muted">VD: http://www.google.com/maps/place/10.7769,106.7009</small>
                        <span class="error-message" id="error-googleMapURL"></span>
                    </div>
                </div>

                <!-- HÌNH ẢNH SỰ KIỆN -->
                <div class="col-12 mb-3 mt-3">
                    <h6 class="text-primary">HÌNH ẢNH SỰ KIỆN</h6>
                    <hr>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Ảnh chi tiết <small class="text-muted">(Tỉ lệ 16:9)</small></label>
                        <input id="images" type="file" class="form-control" multiple onchange="handler.showImagesPreview(this, '#carosuel-preview', '#preview-upload-images')" />
                        <div id="preview-upload-images" class="d-flex flex-wrap justify-content-center mt-2">
                            @if (ViewBag?.Images?.Count > 0)
                            {
                                foreach (var item in ViewBag.Images)
                                {
                                    <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                        <img src="@item" class="card-img-top h-100" style="object-fit:contain" />
                                        <span class="btn-preview-remove" data-url="@item">x</span>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Banner <small class="text-muted">(Tỉ lệ 16:9)</small></label>
                        <input id="bannerImage" type="file" accept="image/*" class="form-control" onchange="handler.showBannerPreview('#bannerImage', '#preview-banner')" />
                        <div id="preview-banner" class="mt-2">
                            <div class="position-relative">
                                <img style="object-fit:contain; height:150px;" src="@(!string.IsNullOrEmpty(ViewBag?.Banner) ? ViewBag?.Banner : "/images/no-image-2.jpg")" id="banner-preview-img" />
                                @if (!string.IsNullOrEmpty(ViewBag?.Banner))
                                {
                                    <span class="btn-preview-remove" data-url="@ViewBag?.Banner">x</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QUÀ TẶNG -->
                <div class="col-12 mb-3 mt-3">
                    <h6 class="text-primary">QUÀ TẶNG</h6>
                    <hr>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Chọn sản phẩm quà tặng <span class="text-danger">*</span></label>
                        <select id="gifts" class="form-control" multiple></select>
                        <span class="error-message" id="error-gifts"></span>
                    </div>
                    <div id="gift-quantity-container" class="row mt-3"></div>
                </div>

                <!-- NHÀ TÀI TRỢ -->
                <div class="col-12 mb-3 mt-3">
                    <h6 class="text-primary">NHÀ TÀI TRỢ</h6>
                    <hr>
                </div>
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Chọn nhà tài trợ <span class="text-danger">*</span></label>
                        <select id="sponsors" class="form-control" multiple></select>
                        <span class="error-message" id="error-sponsors"></span>
                    </div>
                    <div id="sponsor-tier-container" class="row mt-3"></div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="button" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag?.Button</button>
    </div>
</div>

<script>
    window.editor = createQuillEditor("#content");

    handler.initGiftSelect(@Html.Raw(Json.Serialize(ViewBag?.Gifts ?? new List<object>())));
    handler.initSponsorSelect(@Html.Raw(Json.Serialize(ViewBag?.Sponsors ?? new List<object>())));
    handler.initSponsorSelectOnChange();
</script>