﻿<!-- Modal Xem <PERSON> -->
<div id="modal-orders" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">L<PERSON>ch sử mua hàng: <span id="customer-name"></span></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-responsive rounded mb-3">
                    <table id="customer-orders" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th class="text-center">STT</th>
                                <th class="text-center">Mã đơn hàng</th>
                                <th class="text-center">Ngày đặt</th>
                                <th class="text-center">T<PERSON>ng tiền</th>
                                <th class="text-center">Trạng thái</th>
                                <th class="text-center">Thanh toán</th>
                                <th class="text-center">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="orders-data">
                            <!-- Dữ liệu đơn hàng sẽ được thêm vào đây -->
                        </tbody>
                    </table>
                </div>
                <div id="no-orders" class="alert alert-info d-none justify-content-center">
                    Khách hàng này chưa có đơn hàng nào.
                </div>
                <div id="orders-loading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Đang tải...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>