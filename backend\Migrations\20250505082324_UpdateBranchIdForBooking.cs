﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateBranchIdForBooking : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "a8551d788f004767a4c8f4799dd34f93");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "00e3474e8e1e4ab9afcdce31c6511960");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "07630c61118243b4bf6f54521afbe1ba");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0ba8ddeabd574355b3222fd7c39e506b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0c36e10aa7de4462b04d28d14d1af2d3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0d51b2374c6b47789c91c70cd31ab25a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0fe7cfe74a4f4d05b06b5e7fe9dfba11");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "114bd1ea63b448b4b3b1962dc652e516");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "17092c4955d343719e02f66baa0af616");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3e655e5332df44128fa9c7e18b31216d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "530e3c2c54ce4d6d908186e79f49e9d2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "545b1b23de344e4d95e65165b6517754");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "56f0adae7dd14c22a55870393752c168");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "63e1f8af22ae4b1ea3d6609651086d07");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "739e88e4ba3c471eba61bf4af5416c20");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "778cb268d81a46ab949e04339cd093ec");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7bc852e23daf422294addb3f183e9f0b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "89c0bd84221b497c9bc70a5d1847ef72");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8b95763ff8124027ac1adbb95a13bd81");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8c96afbba15b44b1a9fb1c577e208fb6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "98b3448a6d3f47599cfc05fc2c7706c6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a39201c5b1854fafa03164aa1b2ee424");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b0d7358392904df8a1a14dcf9b1cce41");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b42472b0066d4473b49e178da2ceffc0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bef9d0e8c70b4f7baec9e97a84e54a01");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c19ad04fde4f41699cba3edc43586e47");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c53be22b4ef34c008fe50f8667adeb46");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c55233d1766247789cbb294f4a939677");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c700f4813f6c4699aaf49c83f2233545");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d4b37ad79e634cd4ab405bbff7dceca2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d95201e9f8d44f45883e519442f3efe7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "db14cb81e53f421f923b87b74aab8df1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dbd8fa3a6a2f4a7897eaf227a88df1a9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ec626bd74b4f4d5384231ec3e72b2453");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f9f1ceefa92d4057a324f55a6f6ba599");

            migrationBuilder.AddColumn<string>(
                name: "BranchId",
                table: "Bookings",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOrJwjglg13bvwdYniHYrjhnPxDRDhaXwXKYEfvQEEfEbWlxlKwPj8rBJHpGgS4RFg==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "10526c72f3fb4e63a966a179144eee08", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 5, 15, 23, 23, 174, DateTimeKind.Local).AddTicks(5388), "FeaturesButton", new DateTime(2025, 5, 5, 15, 23, 23, 174, DateTimeKind.Local).AddTicks(5391) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "020acf3144e9478fb4d68fc3b3947387", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6641), true, "History", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6641), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "09be8118dc1f4087b84eab54f123b92b", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6529), true, "AffiliateList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6530), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "160acbc7632547268fc109e01412a53e", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6655), true, "TemplateUidList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6656), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "21e669fdff0f4e4f81372074e9856261", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6660), true, "EnableFeatures", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6660), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "27c6b84bb0d24e6992afaabfad232280", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6637), true, "SurveyList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6637), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "28a3142ee56844aa9874bcc02aa78db3", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6647), true, "CampaignHistory", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6648), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "2a4964403faf40f1991ecf3dc9a983fd", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6523), true, "BranchList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6526), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "35206e626c4742e2970d0b42cfbb8019", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6572), true, "Promotion", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6573), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "3e1393a16f0f49b29bd5c522590b7bca", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6643), true, "GamePrize", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6643), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "5386ae44aba742c8ab1ae1764db44ea1", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6662), true, "MembershipExtendDefaults", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6662), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "5d78cd6539d64b0fa727d37942c96902", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6664), true, "ShippingFeeConfig", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6664), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "6ad2ccade3b846318a18db47e3f157a5", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6574), true, "VoucherList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6575), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "6ad7b189eb0f4389a07ed849d405d37a", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6559), true, "BookingItem", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6559), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "6aef1e3b637344adb2c51198ae51c67b", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6561), true, "BookingList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6561), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "7561de03066342afa39e3daee9fa13ba", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6551), true, "Brand", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6552), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "76939c19a4ce4e4baf4498716758884c", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6553), true, "ProductProperty", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6554), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "76bf383e145f4324be4605e1a23144cb", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6668), true, "CustomForm", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6668), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "846452aa43ea4e7d9e6a257743941ab1", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6653), true, "EventTemplateHistory", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6653), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "87ced92c7e8a49c3ac4882bb47e6f026", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6539), true, "ArticleCategory", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6539), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "911aa9cb6ce14f858fa18da3e2a665d6", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6555), true, "ProductList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6556), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "9ecd3ee8e34a4ff7b268a6918bf34b38", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6549), true, "Category", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6549), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "a215737b6ac7436c987294e76fb33396", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(609), true, "Overview", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(5815), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "b92256492b2b491d823f88d3f0162b3b", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6567), true, "InvoiceTemplate", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6568), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "bd2f0f8c82f2460986f84be386dc8daf", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6541), true, "ArticleList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6541), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "c7b8e5f42df34f728f8cc4bb629847fc", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6645), true, "CampaignList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6646), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "cb5c2eabae68448b9e67274c6e6910e1", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6565), true, "OrderList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6566), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "ce082790c573444ba16c6811e9b961a7", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6639), true, "GameList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6639), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "da03c980a317415a8d065daf7ea01879", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6634), true, "History", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6635), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "e3ac48c79d5c46c8b4251c1dc5c5e6cb", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6630), true, "Tag", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6630), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "ea09a7fb0d0c4d629e9eeed294abda21", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6658), true, "GeneralSetting", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6658), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "eeec3e2ee9974b85b6d1a5b586016b0c", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6570), true, "DiscountList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6570), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "f2000bea390b40a3b8235b4657d4c898", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6577), true, "MembershipList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6577), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "f9636e302e1341a697ad3e7eb601ba66", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6649), true, "EventTemplateList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6650), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "fe765b727b6b4235ad04dd5b64a835d7", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6580), true, "Rank", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6580), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "10526c72f3fb4e63a966a179144eee08");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "020acf3144e9478fb4d68fc3b3947387");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "09be8118dc1f4087b84eab54f123b92b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "160acbc7632547268fc109e01412a53e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "21e669fdff0f4e4f81372074e9856261");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "27c6b84bb0d24e6992afaabfad232280");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "28a3142ee56844aa9874bcc02aa78db3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2a4964403faf40f1991ecf3dc9a983fd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "35206e626c4742e2970d0b42cfbb8019");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3e1393a16f0f49b29bd5c522590b7bca");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5386ae44aba742c8ab1ae1764db44ea1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5d78cd6539d64b0fa727d37942c96902");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6ad2ccade3b846318a18db47e3f157a5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6ad7b189eb0f4389a07ed849d405d37a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6aef1e3b637344adb2c51198ae51c67b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7561de03066342afa39e3daee9fa13ba");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "76939c19a4ce4e4baf4498716758884c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "76bf383e145f4324be4605e1a23144cb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "846452aa43ea4e7d9e6a257743941ab1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "87ced92c7e8a49c3ac4882bb47e6f026");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "911aa9cb6ce14f858fa18da3e2a665d6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ecd3ee8e34a4ff7b268a6918bf34b38");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a215737b6ac7436c987294e76fb33396");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b92256492b2b491d823f88d3f0162b3b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bd2f0f8c82f2460986f84be386dc8daf");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c7b8e5f42df34f728f8cc4bb629847fc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cb5c2eabae68448b9e67274c6e6910e1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ce082790c573444ba16c6811e9b961a7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "da03c980a317415a8d065daf7ea01879");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e3ac48c79d5c46c8b4251c1dc5c5e6cb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ea09a7fb0d0c4d629e9eeed294abda21");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "eeec3e2ee9974b85b6d1a5b586016b0c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f2000bea390b40a3b8235b4657d4c898");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f9636e302e1341a697ad3e7eb601ba66");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fe765b727b6b4235ad04dd5b64a835d7");

            migrationBuilder.DropColumn(
                name: "BranchId",
                table: "Bookings");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAELka/oyPZO0wSxjMsSPiicVY++bqMROgKDHGJSvG74UMOaDDu10dO3kFxy8MSFO0ag==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "a8551d788f004767a4c8f4799dd34f93", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 5, 9, 29, 56, 599, DateTimeKind.Local).AddTicks(3237), "FeaturesButton", new DateTime(2025, 5, 5, 9, 29, 56, 599, DateTimeKind.Local).AddTicks(3240) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "00e3474e8e1e4ab9afcdce31c6511960", new DateTime(2025, 5, 5, 9, 29, 56, 597, DateTimeKind.Local).AddTicks(9082), true, "Overview", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(3339), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "07630c61118243b4bf6f54521afbe1ba", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4070), true, "BookingItem", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4071), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "0ba8ddeabd574355b3222fd7c39e506b", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4108), true, "CampaignList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4108), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "0c36e10aa7de4462b04d28d14d1af2d3", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4061), true, "Category", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4061), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "0d51b2374c6b47789c91c70cd31ab25a", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4097), true, "History", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4098), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "0fe7cfe74a4f4d05b06b5e7fe9dfba11", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4063), true, "Brand", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4064), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "114bd1ea63b448b4b3b1962dc652e516", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4099), true, "SurveyList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4100), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "17092c4955d343719e02f66baa0af616", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4053), true, "ArticleList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4054), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "3e655e5332df44128fa9c7e18b31216d", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4084), true, "Promotion", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4084), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "530e3c2c54ce4d6d908186e79f49e9d2", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4051), true, "ArticleCategory", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4051), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "545b1b23de344e4d95e65165b6517754", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4101), true, "GameList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4102), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "56f0adae7dd14c22a55870393752c168", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4116), true, "EventTemplateHistory", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4116), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "63e1f8af22ae4b1ea3d6609651086d07", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4106), true, "GamePrize", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4106), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "739e88e4ba3c471eba61bf4af5416c20", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4065), true, "ProductProperty", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4066), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "778cb268d81a46ab949e04339cd093ec", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4118), true, "TemplateUidList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4118), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "7bc852e23daf422294addb3f183e9f0b", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4122), true, "EnableFeatures", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4123), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "89c0bd84221b497c9bc70a5d1847ef72", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4091), true, "Rank", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4092), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "8b95763ff8124027ac1adbb95a13bd81", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4129), true, "CustomForm", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4130), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8c96afbba15b44b1a9fb1c577e208fb6", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4079), true, "InvoiceTemplate", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4080), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "98b3448a6d3f47599cfc05fc2c7706c6", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4120), true, "GeneralSetting", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4120), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "a39201c5b1854fafa03164aa1b2ee424", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4086), true, "VoucherList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4086), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "b0d7358392904df8a1a14dcf9b1cce41", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4094), true, "Tag", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4094), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "b42472b0066d4473b49e178da2ceffc0", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4077), true, "OrderList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4078), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "bef9d0e8c70b4f7baec9e97a84e54a01", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4072), true, "BookingList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4073), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "c19ad04fde4f41699cba3edc43586e47", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4068), true, "ProductList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4068), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c53be22b4ef34c008fe50f8667adeb46", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4126), true, "ShippingFeeConfig", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4127), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "c55233d1766247789cbb294f4a939677", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4104), true, "History", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4104), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "c700f4813f6c4699aaf49c83f2233545", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4036), true, "BranchList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4038), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "d4b37ad79e634cd4ab405bbff7dceca2", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4110), true, "CampaignHistory", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4111), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d95201e9f8d44f45883e519442f3efe7", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4041), true, "AffiliateList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4042), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "db14cb81e53f421f923b87b74aab8df1", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4112), true, "EventTemplateList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4113), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "dbd8fa3a6a2f4a7897eaf227a88df1a9", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4081), true, "DiscountList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4082), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "ec626bd74b4f4d5384231ec3e72b2453", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4124), true, "MembershipExtendDefaults", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4125), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "f9f1ceefa92d4057a324f55a6f6ba599", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4088), true, "MembershipList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4088), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" }
                });
        }
    }
}
