﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableMembership : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountdownDate",
                table: "Vouchers");

            migrationBuilder.DropColumn(
                name: "Expiry",
                table: "Vouchers");

            migrationBuilder.DropColumn(
                name: "Percentage",
                table: "Vouchers");

            migrationBuilder.RenameColumn(
                name: "MaxDiscount",
                table: "Vouchers",
                newName: "MaxDiscountAmount");

            migrationBuilder.AlterColumn<long>(
                name: "PointRequired",
                table: "Vouchers",
                type: "bigint",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<long>(
                name: "ExchangeTimes",
                table: "Vouchers",
                type: "bigint",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            // 1. Xóa khóa chính trước khi thay đổi cột Id
            migrationBuilder.DropPrimaryKey(
               name: "PK_Vouchers",
               table: "Vouchers");

            migrationBuilder.AlterColumn<string>(
                name: "Id",
                table: "Vouchers",
                type: "nvarchar(32)",
                maxLength: 32,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)");

            // 3. Thêm lại khóa chính
            migrationBuilder.AddPrimaryKey(
                name: "PK_Vouchers",
                table: "Vouchers",
                column: "Id");

            migrationBuilder.AddColumn<short>(
                name: "DiscountType",
                table: "Vouchers",
                type: "smallint",
                nullable: false,
                defaultValue: (short)0);

            migrationBuilder.AddColumn<decimal>(
                name: "DiscountValue",
                table: "Vouchers",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "Vouchers",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Name",
                table: "Bookings",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "MembershipExtendDefaults",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Attribute = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MembershipExtendDefaults", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MembershipExtends",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Content = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Attribute = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserZaloId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MembershipExtends", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEHf17rAV68ZChrUBls/XF7HsHjsrtwZqlmADDLtgh09jB31Umr+9m56HIH2/wQF/Lg==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MembershipExtendDefaults");

            migrationBuilder.DropTable(
                name: "MembershipExtends");

            migrationBuilder.DropColumn(
                name: "DiscountType",
                table: "Vouchers");

            migrationBuilder.DropColumn(
                name: "DiscountValue",
                table: "Vouchers");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "Vouchers");

            migrationBuilder.DropColumn(
                name: "Name",
                table: "Bookings");

            migrationBuilder.RenameColumn(
                name: "MaxDiscountAmount",
                table: "Vouchers",
                newName: "MaxDiscount");

            migrationBuilder.AlterColumn<int>(
                name: "PointRequired",
                table: "Vouchers",
                type: "int",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AlterColumn<int>(
                name: "ExchangeTimes",
                table: "Vouchers",
                type: "int",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "bigint");

            // 1. Xóa khóa chính trước khi đổi lại kiểu dữ liệu
            migrationBuilder.DropPrimaryKey(
                name: "PK_Vouchers",
                table: "Vouchers");

            // 2. Khôi phục kiểu dữ liệu cũ
            migrationBuilder.AlterColumn<string>(
                name: "Id",
                table: "Vouchers",
                type: "nvarchar(450)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(32)",
                oldMaxLength: 32);

            // 3. Thêm lại khóa chính
            migrationBuilder.AddPrimaryKey(
                name: "PK_Vouchers",
                table: "Vouchers",
                column: "Id");

            migrationBuilder.AddColumn<int>(
                name: "CountdownDate",
                table: "Vouchers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Expiry",
                table: "Vouchers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Percentage",
                table: "Vouchers",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEBSmpdo1nkxChADeB9dxwQ4Vf7YnuMqk3UyTWPjbBD67Iu7ETGPsGehxKiEAGa+d6A==");
        }
    }
}
