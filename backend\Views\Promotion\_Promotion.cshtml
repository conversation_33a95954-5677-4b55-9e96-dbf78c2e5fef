﻿﻿@using MiniAppCore.Entities.Products
@using MiniAppCore.Entities.Offers;
@using MiniAppCore.Models.Responses.Products
@using Newtonsoft.Json
@model (Promotion promotion, List<Product> allProducts, List<string> products, List<string> gifts)
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.promotion.Id" />
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Tên chương tr<PERSON>nh khu<PERSON>ến mãi <span style="color:red">*</span></label>
                            <input id="name" type="text" class="form-control" value="@Model.promotion.Name" placeholder="Tên chương trình khuyến mãi "
                                data-errors="Vui lòng tên chương trình khuyến mãi." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Tổng giá trị phần quà <span style="color:red">*</span></label>
                            <input id="totalAmount" type="text" class="form-control" value="@(Model.promotion.TotalAmount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))" placeholder="Tổng giá trị phần quà"
                                   data-errors="Vui lòng nhập tổng giá trị phần quà" oninput="InputValidator.currency(this)" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Trạng thái hoạt động <span style="color:red">*</span></label>
                            <select id="isActive" class="selectpicker form-control" required>
                                <option value="true" selected="@(Model.promotion.IsActive)">Đang hoạt động</option>
                                <option value="false" selected="@(!Model.promotion.IsActive)">Ngưng hoạt động</option>
                            </select>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    @* <div class="col-md-12"> *@
                    @*     <div class="form-group"> *@
                    @*         <label>Quà tặng kèm <span style="color:red">*</span></label> *@
                    @*         <div id="extraGift" class="rounded-bottom" style="height: 150px" data-type="content">@Html.Raw(Model.ExtraGift)</div> *@
                    @*     </div> *@
                    @* </div> *@

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Ngày bắt đầu <span style="color:red">*</span></label>
                            <input id="startDate" type="datetime-local" data-from="true" class="form-control" value="@Model.promotion.StartDate.ToString("yyyy-MM-ddTHH:mm")" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Ngày hết hạn <span style="color:red">*</span></label>
                            <input id="expiryDate" type="datetime-local" data-to="true" class="form-control" value="@Model.promotion.ExpiryDate.AddDays(1).ToString("yyyy-MM-ddTHH:mm")" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="products">Chọn sản phẩm áp dụng <span style="color:red">*</span></label>
                            <select id="products" class="form-control" multiple>
                                @foreach (var product in Model.allProducts.Where(x => !x.IsGift).ToList())
                                {
                                    var images = (product.Images ?? "")
                                    .Split(",", StringSplitOptions.RemoveEmptyEntries)
                                    .Select((Func<string, string>)(x => $"/images/products/{x}"))
                                    .FirstOrDefault();

                                    <option value="@product.Id" data-image="/images/products/@(product.Images.Split(",")[0])" data-price="@product.Price" selected="@(Model.products.Contains(product.Id))">
                                        @product.Name
                                    </option>
                                }
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="gifts">Chọn sản phẩm quà tặng <span style="color:red">*</span></label>
                            <select id="gifts" class="form-control" multiple>
                                @foreach (var product in Model.allProducts.ToList())
                                {
                                    var images = (product.Images ?? "")
                                    .Split(",", StringSplitOptions.RemoveEmptyEntries)
                                    .Select((Func<string, string>)(x => $"/images/products/{x}"))
                                    .FirstOrDefault();

                                    <option value="@product.Id" data-image="/images/products/@(product.Images.Split(",")[0])" data-price="@product.Price" selected="@(Model.gifts.Contains(product.Id))">
                                        @product.Name
                                    </option>
                                }
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div id="gift-quantity-container" class="row"></div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="descr">Mô tả chương trình</label>
                            <textarea id="descr" class="form-control" rows="4">@Model.promotion.Description</textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.promotion.Id')">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        var giftedProducts = @Html.Raw(JsonConvert.SerializeObject(Model.gifts)) || '[]';

        function updateGiftQuantities() {
            var selectedProducts = $('#gifts').val() || [];
            var container = $('#gift-quantity-container');
            container.empty();

            console.log("Selected Products:", selectedProducts);
            console.log("Gifted Products Data:", giftedProducts);

            selectedProducts.forEach(id => {
                var $option = $(`#gifts option[value="${id}"]`);
                if ($option.length === 0) return;

                var productName = $option.text();
                var productImage = $option.data('image');
                var price = $option.data('price');
                var formattedPrice = new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);

                // Lấy số lượng từ ViewBag.GiftedProducts (nếu có\\)
                var quantity = giftedProducts[id] ? giftedProducts[id] : 1;

                var item = `
                    <div class="col-md-12">
                        <div class="card shadow-sm p-2 d-flex align-items-center flex-row">
                           <!-- <img src="${productImage}" class="rounded" style="width: 50px; height: 50px; object-fit: cover;"> -->
                            <div class="ms-3 flex-grow-1">
                                <h6 class="mb-0">${productName} - ${formattedPrice}</h6>
                            </div>
                            <input type="number" class="form-control gift-quantity" data-id="${id}" value="${quantity}" min="1" style="width: 60px; text-align: center;" disabled>
                        </div>
                    </div>
                `;

                container.append(item);
            });
        }

        function formatProduct(product) {
            if (!product.id) return product.text;
            var image = $(product.element).data('image');
            var price = $(product.element).data('price');
            var formattedPrice = new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(price);

            return $(`
                <div style="display: flex; align-items: center; gap: 8px;">
                   <!-- <img src="${image}" style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"> -->
                    <span>${product.text} - ${formattedPrice}</span>
                </div>
            `);
        }

        $('#products').select2({
            // templateResult: formatProduct,
            // templateSelection: formatProduct,
            allowClear: true,
            width: '100%'
        });

        $('#gifts').select2({
            // templateResult: formatProduct,
            // templateSelection: formatProduct,
            allowClear: true,
            width: '100%',
        }).on('change', function () {
            console.log("Gifts changed!");
            updateGiftQuantities();
        });

        updateGiftQuantities();

        // InitialEditor();



    });
</script>