﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Branches;
using MiniAppCore.Entities.Events.SponsorshipTiers;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events.Sponsors;
using MiniAppCore.Services.Branches;

namespace MiniAppCore.Services.Events.SponsorshipTiers
{
    public class SponsorshipTierService(IUnitOfWork unitOfWork, IWebHostEnvironment env, IHttpContextAccessor httpContextAccessor, IMapper mapper) : Service<SponsorshipTier>(unitOfWork), ISponsorshipTierService
    {
        private readonly string _uploadPath = "uploads/images/sponsorshiptiers";
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public async Task<PagedResult<SponsorshipTier>> GetPaged(RequestQuery query, short activeStatus)
        {
            var tiers = _repository.AsQueryable();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                tiers = tiers.Where(x => x.TierName.Contains(query.Keyword));
            }

            if (!IsAdmin)
            {
                tiers = tiers.Where(x => x.IsActive);
            }
            else
            {
                if (activeStatus == 1) tiers = tiers.Where(x => x.IsActive);
                else if (activeStatus == 2) tiers = tiers.Where(x => !x.IsActive);
            }

            var totalItems = await tiers.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);

            var listTiers = await tiers
                .OrderByDescending(t => t.CreatedDate)
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            foreach (var tier in listTiers)
            {
                if (!string.IsNullOrEmpty(tier.Image) && !tier.Image.StartsWith("http"))
                {
                    tier.Image = $"{hostUrl}/{_uploadPath}/{tier.Image}";
                }
            }

            return new PagedResult<SponsorshipTier>
            {
                Data = listTiers,
                TotalPages = totalPages
            };
        }

        public async Task<int> CreateTierAsync(SponsorshipTierDTO dto)
        {
            var tier = mapper.Map<SponsorshipTier>(dto);
            tier.Image = await HandleImageUploadAsync(dto.Image);
            return await base.CreateAsync(tier);
        }

        public async Task<int> UpdateTierAsync(string id, SponsorshipTierDTO dto)
        {
            var tier = await _repository.FindByIdAsync(id);
            if (tier == null) throw new CustomException(1, "Hạng nhà tài trợ không khả dụng!");

            mapper.Map(dto, tier);
            tier.Image = await HandleImageUploadAsync(dto.Image, tier.Image);
            return await base.UpdateAsync(tier);
        }

        public async Task<int> DeleteTierAsync(string id)
        {
            var tier = await _repository.FindByIdAsync(id);
            if (tier == null) throw new CustomException(1, "Hạng nhà tài trợ không tồn tại!");

            if (!string.IsNullOrEmpty(tier.Image))
                RemoveOldImage(tier.Image);

            return await base.DeleteAsync(tier);
        }

        public async Task<List<SponsorshipTier>> GetListByIdsAsync(IEnumerable<string> ids)
        {
            return await _repository.AsQueryable()
                .Where(s => ids.Contains(s.Id))
                .OrderByDescending(s => s.CreatedDate)
                .ToListAsync();
        }

        public async Task<SponsorshipTier?> GetById(string id)
        {
            var tier = await _repository.FindByIdAsync(id);
            if (tier == null || !tier.IsActive)
            {
                tier = await _repository.AsQueryable().Where(x => x.IsActive).FirstOrDefaultAsync();
            }
            return tier;
        }

        #region Data Process

        private async Task<string> ProcessUpload(List<IFormFile> files)
        {
            if (files == null || files.Count == 0)
                return string.Empty;

            var savePath = Path.Combine(env.WebRootPath, _uploadPath);

            // Đảm bảo thư mục tồn tại
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            var fileResult = await Helpers.FileHandler.SaveFiles(files, savePath);
            return string.Join(",", fileResult);
        }

        private void RemoveOldImage(string listImage)
        {
            var images = listImage.Split(",")
                .Select(x => Path.Combine(env.WebRootPath, _uploadPath, x))
                .ToList();

            Helpers.FileHandler.RemoveFiles(images);
        }

        private async Task<string?> HandleImageUploadAsync(IFormFile? imageFile, string? oldImage = null)
        {
            if (imageFile == null) return oldImage;

            if (!string.IsNullOrEmpty(oldImage))
                RemoveOldImage(oldImage);

            return await ProcessUpload(new List<IFormFile> { imageFile });
        }

        #endregion
    }

}
