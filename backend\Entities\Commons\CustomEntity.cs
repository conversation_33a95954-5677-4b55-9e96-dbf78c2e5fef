﻿namespace MiniAppCore.Entities.Commons
{
    public class CustomField : BaseEntity
    {
        public string? EntityName { get; set; } // EntityName varchar
        public string? FieldName { get; set; } // FieldName varchar
        public string? FieldNameDisplay { get; set; } // FieldNameDisplay varchar
        public string? DataType { get; set; } // DataType varchar
        public bool IsRequired { get; set; } // IsRequired bit

        // Navigation property
        public virtual ICollection<CustomFieldValue> CustomFieldValues { get; set; } = new List<CustomFieldValue>();
    }

    public class CustomFieldValue : BaseEntity
    {
        public string? CustomFieldID { get; set; } // CustomFieldID varchar (references CustomFields(ID))
        public string? EntityID { get; set; } // EntityID varchar
        public string? Value { get; set; } // Value nvarchar

        // Navigation property
        public virtual CustomField? CustomField { get; set; }
    }
}
