﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.DTOs.Payments;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Carts;
using MiniAppCore.Models.Requests.Orders;
using MiniAppCore.Services.Orders;

namespace MiniAppCore.Controllers.API
{
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    [Route("api/[controller]")]
    public class OrdersController(ILogger<OrdersController> logger, IConfiguration configuration, IOrderService orderService) : ControllerBase
    {
        [HttpGet("history")]
        public async Task<IActionResult> History([FromQuery] PurchaseQueryParams query)
        {
            try
            {
                if (User.IsInRole("ADMIN"))
                {
                    var result = await orderService.GetPage(query, null);
                    return Ok(new
                    {
                        Code = 0,
                        Message = "Thành công!",
                        result.Data,
                        result.TotalPages
                    });
                }

                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }

                var resultList = await orderService.GetPage(query, userZaloId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    resultList.Data,
                    resultList.TotalPages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpGet("{orderId}")]
        public async Task<IActionResult> OrderDetails(string orderId)
        {
            try
            {
                var orderDetail = await orderService.GetOrderByIdAsync(orderId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = orderDetail
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpDelete("{id}")]
        public IActionResult DeleteOrder(string id)
        {
            try
            {
                //await orderService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xoá đơn hàng thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> PlaceOrder([FromBody] OrderRequest dto)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value ?? string.Empty;

                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }

                var order = await orderService.PlacedOrder(userZaloId, dto, true);

                if (dto.PaymentMethod == 2)
                {
                    ExtraData extraData = new ExtraData()
                    {
                        myTransactionId = order.OrderId,
                        notes = dto.Note,
                        storeId = configuration["MiniAppSettings:StoreId"]?.ToString(),
                        storeName = configuration["MiniAppSettings:StoreName"]?.ToString(),
                        orderGroupId = configuration["MiniAppSettings:OrderGroupId"]?.ToString()
                    };

                    var orderDetails = await orderService.GetOrderItemsByOrderId(order.OrderId);
                    var items = orderDetails.Select(x => new Dictionary<string, string>() { { x.ProductId, (x.DiscountPrice * x.Quantity).ToString() } }).ToList();

                    CheckOutDTO checkout = new()
                    {
                        Desc = !string.IsNullOrEmpty(order.Note) ? order.Note : "Thanh toán đơn hàng!",
                        Amount = order.TotalAmount,
                        Items = items,
                        ExtraData = extraData
                    };

                    var macData = Tools.CreateMac(checkout, configuration["MiniAppSettings:PrivateKey"] ?? "");
                    checkout.MacData = macData;

                    return Ok(new { Code = 0, Message = "Thành công!", PaymentData = checkout });
                }

                return Ok(new { Code = 0, Message = "Thành công!", Order = order });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [AllowAnonymous]
        [HttpPost("Buy")]
        public async Task<IActionResult> BuyNow([FromBody] List<CartItemRequest> cartItemRequest)
        {
            try
            {
                var data = await orderService.GetBuyProductInfo(cartItemRequest);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = data
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPost("apply-voucher")]
        public async Task<IActionResult> ApplyVocher([FromBody] OrderRequest dto)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value ?? string.Empty;

                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }

                var order = await orderService.PlacedOrder(userZaloId, dto, false);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = order
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    ex.Code,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPut("Cancel")]
        public async Task<IActionResult> Cancel([FromQuery] string id, [FromQuery] string? reason = "")
        {
            try
            {
                await orderService.CancelOrder(id, reason);
                return Ok(new
                {
                    Code = 0,
                    Message = "Hủy đơn hàng thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPut("QuickUpdate")]
        public async Task<IActionResult> QuickUpate([FromBody] QuickUpdateRequest quickUpdateRequest)
        {
            try
            {
                await orderService.QuickUpdate(quickUpdateRequest);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPost("UpdateZaloOrderId/{id}")]
        public async Task<IActionResult> UpdateZaloOrderId(string id, [FromBody] UpdateZaloOrderId request)
        {
            try
            {
                await orderService.UpdateZaloOrderId(id, request.zaloOrderId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPost("UpdatePaymentChannel/{id}")]
        public async Task<IActionResult> UpdatePaymentChannel(string id, [FromBody] UpdateOrderInfo request)
        {
            try
            {
                await orderService.UpdatePaymentChannel(id, request.PaymentChannel);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPost("UpdateZaloOrderStatus/{id}")]
        public async Task<IActionResult> UpdateZaloOrderStatus(string id, [FromBody] UpdateOrderInfo request)
        {
            try
            {
                await orderService.UpdateZaloOrderStatus(id, request.PaymentMethod, request.PaymentChannel);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        #region Admin API

        [HttpPost("Create")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> CreateOrder(AdminCreateOrderRequset model)
        {
            try
            {
                await orderService.CreateAsync(model, true);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo đơn hàng thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    Errors = ex.Detail
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("Export")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> ExportMembership([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate, [FromQuery] short? orderStaus, [FromQuery] short? paymentStatus)
        {
            try
            {
                var fileContent = await orderService.ExportOrders(startDate, endDate, orderStaus, paymentStatus);
                var fileName = $"DonHang_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                Response.Headers["Content-Disposition"] = $"attachment; filename={fileName}";
                return File(fileContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN, EMPLOYEE")]
        public async Task<IActionResult> Update(string id, OrderRequest dto)
        {
            try
            {
                await orderService.UpdateAsync(id, dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật đơn hàng thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPut("update-products/{id}")]
        [Authorize(Roles = "ADMIN, EMPLOYEE")]
        public async Task<IActionResult> UpdateDetails(string id, OrderRequest dto)
        {
            try
            {
                var order = await orderService.UpdateOrderDetails(id, dto, false);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật chi tiết đơn hàng thành công!",
                    Data = new
                    {
                        order,
                        dto.Products
                    }
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        #endregion
    }
}
