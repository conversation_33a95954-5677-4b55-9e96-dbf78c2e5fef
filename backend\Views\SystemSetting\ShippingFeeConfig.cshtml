﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> sách cài đặt phí vận chuyển</h4>
            </div>
            <button type="button" class="btn btn-primary mt-2" onclick="GetFormShippingFeeConfig()">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">T<PERSON><PERSON> kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="shippingFeeConfig-table" class="table-responsive rounded mb-3">
            <table id="list-shippingFeeConfig" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-shippingFeeConfig" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-dialog-centered"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListShipingFeeConfig();
            $('#search').on('input', search);
        });

        function GetFormShippingFeeConfig(id) {
            const url = id ? `@Url.Action("UpdateShippingFeeConfig", "SystemSetting")/${id}` : "@Url.Action("CreateShippingFeeConfig", "SystemSetting")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-shippingFeeConfig").modal("toggle");
                }
            })

        }

        function GetListShipingFeeConfig() {
            table = new DataTable("#list-shippingFeeConfig", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const status = $("#filter-status").val()

                    $.ajax({
                        url: '@Url.Action("GetPageShippingFeeConfig", "SystemSettings")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            status: status
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                index: data.start + index + 1, // STT
                                orderValueRange: `${item.minOrderValue.toLocaleString()} - ${item.maxOrderValue.toLocaleString()} đ`, // Khoảng giá trị đơn hàng
                                shippingFee: `${item.shippingFee.toLocaleString()} đ`, // Phí vận chuyển
                                createdDate: FormatDate(item.createdDate), // Ngày tạo
                                actions: `<div class="d-flex align-items-center justify-content-center list-action">
                                                <a onclick="GetFormShippingFeeConfig('${item.id}')" class="badge badge-info mx-1" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                    <i class="ri-edit-line fs-6 mr-0"></i>
                                                </a>
                                                <a onclick="DeleteShippingFeeConfig('${item.id}')" class="badge bg-warning mx-1" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                    <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                </a>
                                            </div>`
                            }));
                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 'index', className: 'text-center' },
                    { title: "Khoảng giá trị đơn hàng", data: 'orderValueRange' },
                    { title: "Phí vận chuyển", data: 'shippingFee', className: 'text-center' },
                    { title: "Ngày tạo", data: 'createdDate', className: 'text-center' },
                    { title: "Thao tác", data: 'actions', className: 'text-center' },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function DeleteShippingFeeConfig(id) {
            if (id === '') return;
            const url = `/api/SystemSettings/ShippingFee/${id}`
            $("#modal-shippingFeeConfig").modal("hide");
            DeleteItem(url);
        }

        async function HandleSaveOrUpdate(id) {
            var minOrderValue = InputValidator.parseCurrency($("#minOrderValue").val());
            var maxOrderValue = InputValidator.parseCurrency($("#maxOrderValue").val());
            var shippingFee = InputValidator.parseCurrency($("#shippingFee").val());      
            if(minOrderValue > maxOrderValue){
                AlertResponse("Giá trị tối thiểu không được lớn hơn giá trị tối đa", 'error');
                return;
            }

            const data = {
                shippingFee: shippingFee,
                minOrderValue: minOrderValue,
                maxOrderValue:maxOrderValue,
            }

            const url = id === "" ? "/api/SystemSettings/ShippingFee" : "/api/SystemSettings/ShippingFee/" + id;
            const method = id === "" ? "POST" : "PUT";
            $.ajax({
                url: url,
                type: method,
                contentType: "application/json",
                data: JSON.stringify(data),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload();
                    }
                    $("#modal-shippingFeeConfig").modal("toggle");
                },
                error: function (err) {
                    AlertResponse("Đã xảy ra lỗi, vui lòng thử lại sau!", 'error')
                }
            });
        }
    </script>
}
