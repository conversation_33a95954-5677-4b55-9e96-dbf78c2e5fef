﻿namespace MiniAppCore.Enums
{
    public enum EEvent
    {
        Online = 1,
        Offline = 2
    }

    public enum EEventStatus
    {
        Upcoming = 1,   // Sắp diễn ra
        Ongoing = 2,    // <PERSON><PERSON> diễn ra
        Ended = 3       // <PERSON><PERSON> kết thúc
    }

    public enum ECheckInStatus
    {
        NotCheckIn = 1, // Chưa check-in
        CheckIn = 2,    // Đ<PERSON> check-in
        Cancel = 3      // Đã hủy
    }
}
