﻿﻿<div class="row">
    <!-- Header Section -->
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> sách chi nh<PERSON>h</h4>
                @* <p class="mb-0">
                    Trang tổng quan cung cấp cho bạn cái nhìn tổng quan về danh sách chi nhánh hiện có,<br>
                    bao gồm thông tin quan trọng và các chức năng quản lý chi nhánh.
                </p> *@
            </div>
            @if (User.IsInRole("ADMIN"))
            {
                <!-- Add Branch Button -->
                <button type="button" class="btn btn-primary mt-2" onclick="GetFormBranch()">
                    <i class="ri-add-line mr-3"></i> Thêm mới
                </button>
            }
        </div>
    </div>

    <!--<PERSON><PERSON> lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <!-- Thanh tìm kiếm -->
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên/số điện thoại">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <!-- Branch City -->
                    <div class="col-md-3">
                        <label for="city-filter" class="form-label">Tỉnh/Thành phố</label>
                        <select id="city-filter" class="form-control select2">
                            <option value="">Tất cả Tỉnh/Thành phố</option>
                        </select>
                    </div>

                    <!-- Branch District -->
                    <div class="col-md-3">
                        <label for="district-filter" class="form-label">Quận/Huyện</label>
                        <select id="district-filter" class="form-control select2">
                            <option value="">Tất cả Quận/Huyện</option>
                        </select>
                    </div>

                    <!-- Branch Ward -->
                    <div class="col-md-3">
                        <label for="ward-filter" class="form-label">Phường/Xã</label>
                        <select id="ward-filter" class="form-control select2">
                            <option value="">Tất cả Phường/Xã</option>
                        </select>
                    </div>

                    <!-- Nút lọc -->
                    <div class="col-12 d-flex justify-content-end mt-3">
                        <button type="button" class="btn btn-primary" onclick="table.ajax.reload();">
                            <i class="ri-filter-line mr-1"></i> Lọc
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" class="text-center my-4"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-branch" class="data-table table table-bordered table-hover mb-0">
            </table>
        </div>
    </div>
</div>

<div id="modal-branch" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-xl" role="document">
        <div id="modal-content" class="modal-content">
        </div>
    </div>
</div>

@section Scripts {
    <script>
        const options = {
            dropdownParent: $("#modal-content")
        }

        $(document).ready(function () {
            InitSelect2ForFilter('', '', '');
            GetListBranch();
            $('#search').on('input', search);
        });

        function GetListBranch() {
            table = new DataTable("#list-branch", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const pageSize = data.length;
                    const keyword = $("#search").val();
                    const city = $("#city-filter").val();
                    const district = $("#district-filter").val();
                    const ward = $("#ward-filter").val();

                    $.ajax({
                        url: '@Url.Action("GetPage", "Branches")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            city: city,
                            district: district,
                            ward: ward
                        },
                        success: function (response) {
                            // Sử dụng tên thuộc tính thay vì chỉ số
                            const formattedData = response.data.map((item, index) => ({
                                rowIndex: data.start + index + 1, // STT
                                name: item.name, // Tên chi nhánh
                                streetLine: item.streetLine,
                                wardName: item.wardName,
                                districtName: item.districtName,
                                provinceName: item.provinceName,
                                phoneNumber: item.phoneNumber,
                                description: item.description,
                                actions: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                                    <a onclick="GetFormBranch('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                                        <i class="ri-edit-line fs-6 mr-0"></i>
                                                                    </a>
                                                                    <a onclick="DeleteBranch('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                                        <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                                    </a>
                                                                  </div>`,
                                fullAddress: `${item.streetLine}, ${item.wardName}, ${item.districtName}, ${item.provinceName}`, // Gộp địa chỉ
                                status: item.status ? '<span class="badge bg-success">Hoạt động</span>' : '<span class="badge bg-danger">Không hoạt động</span>' // Badge status
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "rowIndex", className: 'text-center' },
                    { title: "Tên", data: "name" },
                    { title: "Địa chỉ", data: "fullAddress" },
                    { title: "Số điện thoại", data: "phoneNumber" },
                    { title: "Trạng thái", data: "status", className: 'text-center' },
                    { title: "Thao tác", data: "actions", className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormBranch(id) {
            const url = id ? `@Url.Action("Detail", "Branch")/${id}` : "@Url.Action("Create", "Branch")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-branch").modal("toggle");
                }
            })
        }

        function DeleteBranch(id) {
            const url = `/api/branches/${id}`
            DeleteItem(url);
        }

        function InitialEditor() {
            window.editor = createQuillEditor("#description");
        }

        function HandleSaveOrUpdate(id) {
            const formData = new FormData();
            $(".error-message").text("");

            // Lấy giá trị từ form
            const fields = ["name", "streetLine", "phoneNumber", "city", "district", "ward"];
            const data = fields.reduce((obj, field) => {
                obj[field] = $(`#${field}`).val()?.trim();
                return obj;
            }, {});

            // Tạo các giá trị để mapping
            data.provinceId = data.city;
            data.districtId = data.district;
            data.wardId = data.ward;

            // Lấy text của tỉnh/quận/phường
            data.provinceName = $("#city option:selected").text();
            data.districtName = $("#district option:selected").text();
            data.wardName = $("#ward option:selected").text();

            // Tạo địa chỉ đầy đủ
            if (data.streetLine && data.wardName && data.districtName && data.provinceName) {
                data.fullAddress = `${data.streetLine}, ${data.wardName}, ${data.districtName}, ${data.provinceName}`;
            }

            // Lấy các giá trị khác
            data.latitude = $("#latitude").val();
            data.longitude = $("#longitude").val();
            data.googleMapURL = $("#googleMapsLink").val();

            // Lấy giá trị thời gian
            data.openingTime = $("#openingTime").val() || "08:00";
            data.closingTime = $("#closingTime").val() || "22:00";

            // Lấy các giá trị boolean
            data.isDefault = $("#isDefault").val() === "true";
            data.status = $("#isEnable").val() === "true";
            data.isOpenHoliday = $("#isOpenHoliday").val() === "true";

            // Lấy mô tả từ editor
            data.description = window.editor.root.innerHTML;

            // Kiểm tra các trường bắt buộc
            const requiredFields = {
                name: "Tên chi nhánh là bắt buộc",
                streetLine: "Địa chỉ là bắt buộc",
                phoneNumber: "Số điện thoại là bắt buộc",
                city: "Tỉnh/Thành phố là bắt buộc",
                district: "Quận/Huyện là bắt buộc",
                ward: "Phường/Xã là bắt buộc",
                latitude: "Vĩ độ là bắt buộc. Vui lòng nhập link Google Maps hợp lệ",
                longitude: "Kinh độ là bắt buộc. Vui lòng nhập link Google Maps hợp lệ"
            };

            // Hiển thị lỗi cho từng trường
            let hasError = false;
            for (const [field, message] of Object.entries(requiredFields)) {
                if (!data[field]) {
                    $(`#error-${field}`).text(message);
                    hasError = true;
                }
            }

            if (hasError) {
                AlertResponse("Vui lòng điền đầy đủ thông tin", 'warning');
                return;
            }

            // Kiểm tra số điện thoại
            if (!/^[0-9]{10,11}$/.test(data.phoneNumber)) {
                $("#error-phoneNumber").text("Số điện thoại không hợp lệ");
                AlertResponse("Số điện thoại không hợp lệ", 'warning');
                return;
            }

            // Kiểm tra Google Maps link
            const googleMapsLink = $("#googleMapsLink").val();
            if (googleMapsLink && (!data.latitude || isNaN(parseFloat(data.latitude)) || !data.longitude || isNaN(parseFloat(data.longitude)))) {
                $("#error-googleMapsLink").text("Link không hợp lệ. Vui lòng dán một link Google Maps hợp lệ!");
                AlertResponse("Link Google Maps không hợp lệ!", 'warning');
                return;
            }

            // Kiểm tra hình ảnh
            const imageFiles = $("#image")[0].files;
            if (!id && imageFiles.length === 0) {
                AlertResponse("Vui lòng tải lên ít nhất một ảnh", 'warning');
                return;
            }

            // Thêm dữ liệu vào FormData
            Object.entries(data).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    formData.append(key, value);
                }
            });

            // Thêm files hình ảnh
            Array.from(imageFiles).forEach(file => formData.append("files", file));

            // Gửi request
            const url = id ? `/api/branches/${id}` : '/api/branches';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                        $("#modal-branch").modal("hide");
                    } else {
                        AlertResponse(response.message || "Có lỗi xảy ra", 'error');
                    }
                },
                error: function (err) {
                    console.error(err);
                    AlertResponse("Lỗi máy chủ, vui lòng thử lại sau!", 'error');
                }
            });
        }

        function InitSelect2(cityKeyword, districtKeyword, wardKeyword) {
            const citySelect = $("#city");
            const districtSelect = $("#district");
            const wardSelect = $("#ward");

            loadCities(cityKeyword);

            citySelect.on('change', function () {
                const cityId = $(this).find(":selected").data("id");
                loadDistricts(cityId, districtKeyword);
                wardSelect.empty().append('<option value="">Chọn Xã/Phường</option>');
            });

            districtSelect.on('change', function () {
                const districtId = $(this).find(":selected").data("id");
                loadWards(districtId, wardKeyword);
            });
        }

        function InitSelect2ForFilter(cityKeyword, districtKeyword, wardKeyword) {
            const citySelect = $("#city-filter");
            const districtSelect = $("#district-filter");
            const wardSelect = $("#ward-filter");

            loadCities(cityKeyword, "#city-filter");

            citySelect.on('change', function () {
                const cityId = $(this).find(":selected").data("id");
                loadDistricts(cityId, districtKeyword, "#district-filter");
                wardSelect.empty().append('<option value="">Chọn Xã/Phường</option>');
            });

            districtSelect.on('change', function () {
                const districtId = $(this).find(":selected").data("id");
                loadWards(districtId, wardKeyword, "#ward-filter");
            });
        }

        function loadCities(keyword, id = "#city") {
            console.log(id);
            const citySelect = $(id);
            $.ajax({
                url: "/api/ZaloHelperApi/GetCities?keyword=",
                method: 'GET',
                dataType: 'json',
                success: function (response) {
                    citySelect.empty().append('<option value="">Chọn Tỉnh/Thành phố</option>');
                    $.each(response.data, function (index, province) {
                        if (keyword === province.name || keyword === province.id) {
                            citySelect.append(`<option value="${province.id}" data-id="${province.id}" selected>${province.name}</option>`);
                        } else {
                            citySelect.append(`<option value="${province.id}" data-id="${province.id}">${province.name}</option>`);
                        }
                    });
                    console.log(keyword)
                    if (keyword) {
                        citySelect.val(keyword).trigger("change");
                    }

                    if (id.includes('filter')) {
                        citySelect.select2();
                    } else {
                        citySelect.select2(options);
                    }
                },
                error: function (error) {
                    console.error('Error loading cities:', error);
                }
            });
        }

        function loadDistricts(cityId, keyword, id = "#district") {
            const districtSelect = $(id);
            districtSelect.empty().append('<option value="">Chọn Quận/Huyện</option>');

            if (cityId) {
                $.ajax({
                    url: `/api/ZaloHelperApi/GetDistricts?cityId=${cityId}&keyword=`,
                    method: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        $.each(response.data, function (index, district) {
                            if (keyword === district.name || keyword === district.id) {
                                districtSelect.append(`<option value="${district.id}" data-id="${district.id}" selected>${district.name}</option>`);
                            } else {
                                districtSelect.append(`<option value="${district.id}" data-id="${district.id}">${district.name}</option>`);
                            }
                        });

                        if (id.includes('filter')) {
                            districtSelect.select2().trigger("change");
                        } else {
                            districtSelect.select2(options).trigger("change");
                        }
                    },
                    error: function (error) {
                        console.error('Error loading districts:', error);
                    }
                });
            }
        }

        function loadWards(districtId, keyword, id = "#ward") {
            const wardSelect = $(id);
            wardSelect.empty().append('<option value="">Chọn Xã/Phường</option>'); // Clear previous selections

            if (districtId) {
                $.ajax({
                    url: `/api/ZaloHelperApi/GetWards?districtId=${districtId}&keyword=`,
                    method: 'GET',
                    dataType: 'json',
                    success: function (response) {
                        $.each(response.data, function (index, ward) {
                            if (keyword === ward.name || keyword === ward.id) {
                                wardSelect.append(`<option value="${ward.id}" data-id="${ward.id}" selected>${ward.name}</option>`);
                            } else {
                                wardSelect.append(`<option value="${ward.id}" data-id="${ward.id}">${ward.name}</option>`);
                            }
                        });

                        if (id.includes('filter')) {
                            wardSelect.select2();
                        } else {
                            wardSelect.select2(options);
                        }
                    },
                    error: function (error) {
                        console.error('Error loading wards:', error);
                    }
                });
            }
        }

    </script>
}
