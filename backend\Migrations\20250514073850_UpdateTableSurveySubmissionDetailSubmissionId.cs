﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableSurveySubmissionDetailSubmissionId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "247dcfe4db8342ecb76db88f30503138");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "02672e38c196412396c514ed85242d4e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0a6ea3cb77b847189c9ea9013ec60ed7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0ef065a7dc0c4cb1b48d2f3aa1527577");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1e5cf7f4903e4a489f2f83d568b51cc9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "21d838a2bf3c4bd8bf24973659b1cb19");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2546eb96fe0e43128fc37b2fcc6beedc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "285472479482460695f1c6495328c7e5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2ec4d869ade2407b8b572961ef2fc13d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3a09a3960e094008aaec9237ce612bcc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3bcbd7af236147609fea006d93997bf0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "42f3bedd02f4477db4928cfd1b4449c0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4795ae3e1586459b9ac0576d2bca531f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "535819194e93483496bc1e7bb0bc3411");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "538bf4bc1a22434e8f5eca78926ef53a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5767dcedda9b4611ae63c994041361cd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6d7cb89a0f104075a23573c734479f43");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6eeaebb18cb54279929abc69f03494ac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "75b3a273e92246e5a6113baaefff7f3d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "82a59c99ef234a10ba87ecbc53df3f82");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8f3ae00df8f0418d93de3a9cf5501ed9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ffc1cdfcbd842baafb79038ecb73b9b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a3ebebd357d04fb39dab385fcbf2a165");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ab44bd5b884a4414ac258ef479e42004");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b0d709fb8d8a44a984a64d56d86e586d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b0e1263a2a0d4337b6ff5df57ec4282b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b3aae582034941e69ef0c31f606078d4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b4985bf5df234717af540c99612fcc39");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ca8bd7336d2e470ebf2877450cd02614");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cf6cea67545b4a4a85c845e74d63b762");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d77a24af3476400fac0a7b61630e1997");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dbe51b761e1b4f088df68fcf0138313d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e3b7674875bf47a9a37aa8af73cd7721");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e687fcafe0364706923bf2116d21f071");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e950dbd9dba44a30af0795e68a28e017");

            migrationBuilder.AddColumn<string>(
                name: "SubmissionId",
                table: "SurveySubmissionDetails",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEKPJlENgen/pqI7bldPBbL144Qz5wvf/KQ3KlzZIX02S9otl3aRF7nS0IXvcCQtFmw==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "c0f03acf6c6a4532b0521833de5611fb", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(7684), "FeaturesButton", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(7688) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "050ce0ac4bfa428a899673a188cf9564", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(616), true, "GameList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(616), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "0704b3e405bc4a3a80a91d1e2096c385", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(542), true, "Promotion", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(543), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "0a617a21ad454ce5984b504aa2ad093e", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(546), true, "VoucherList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(546), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "0d0aac6d1c994a2999e0c1978f0f2b37", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(624), true, "GamePrize", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(624), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "0d4ae8118cb44188bee06e84dd414d4b", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(491), true, "ArticleList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(492), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "121272c7d60d49539d8006539a2d34a6", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(555), true, "Rank", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(555), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "12d329bb8fa146cbb838c01dcb96ccb0", new DateTime(2025, 5, 14, 14, 38, 46, 686, DateTimeKind.Local).AddTicks(2784), true, "Overview", new DateTime(2025, 5, 14, 14, 38, 46, 686, DateTimeKind.Local).AddTicks(9810), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "14dc21725cd94d6b85936bd79b8f5f1c", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(654), true, "MembershipExtendDefaults", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(655), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2cc506faa3084a27bef9fe6d7733b53b", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(559), true, "Tag", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(559), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "2f654980583a4971b75192d99e4ab7d1", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(483), true, "AffiliateList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(483), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "511d5b240f1d4a6f9623cf48346cd9d8", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(651), true, "EnableFeatures", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(651), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "518fd7d216bc4e32b18d623fc15062d4", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(563), true, "History", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(563), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "5596c9c7a60b44679746527f8126df1b", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(643), true, "TemplateUidList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(644), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "664cfe60885745ab98cc035a1b9db861", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(504), true, "Category", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(504), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "667b611ea0c94932b92ec834f7cc07d6", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(534), true, "InvoiceTemplate", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(534), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "75d9c11f1e214dd6a6e38339ba8f1032", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(513), true, "ProductProperty", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(513), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "7922926f9495478bba727124868001a3", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(620), true, "History", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(620), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "83729961fb254b2c85a4b6e09edce637", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(664), true, "CustomForm", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(665), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8756d7fded514827b34e6ce3199f1993", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(550), true, "MembershipList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(550), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "93c87391b27b438ebb43329a111d149d", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(516), true, "ProductList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(517), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "9ad17a1668c8448ba452b88d3366aef8", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(627), true, "CampaignList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(628), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "9fe71f506f7f4121bb7414e9d3869c9c", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(539), true, "DiscountList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(539), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "b14ba72c9cdc473f937063e02c5712fd", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(527), true, "BookingList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(527), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "b1740b50787043a79768fa47e7530224", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(530), true, "OrderList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(531), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "b1ae743ca8fd4cd6824bb3eda80a638d", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(611), true, "SurveyList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(612), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "bb662e0088fd42249bc952abfe388662", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(658), true, "ShippingFeeConfig", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(658), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "bc1e1dcd981e44c28b77c0d07944239a", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(523), true, "BookingItem", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(523), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "c5b6742f3d454e41bc73a9a44d2c6130", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(508), true, "Brand", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(509), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c835bf7cae3b40bdadc2ec483f2d0e19", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(640), true, "EventTemplateHistory", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(640), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "cbe4e961b93344d18c432faaaf299479", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(632), true, "CampaignHistory", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(633), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d29c5d96d2b24e8aa9ccacb148ba3643", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(647), true, "GeneralSetting", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(647), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "db3527865c8f40939e408cc83cf13f3f", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(476), true, "BranchList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(477), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "e35e41342eb7434dac2068350b9a6493", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(487), true, "ArticleCategory", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(487), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "f5ee9443dd11469fb43f71c16665413f", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(636), true, "EventTemplateList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(636), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "c0f03acf6c6a4532b0521833de5611fb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "050ce0ac4bfa428a899673a188cf9564");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0704b3e405bc4a3a80a91d1e2096c385");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0a617a21ad454ce5984b504aa2ad093e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0d0aac6d1c994a2999e0c1978f0f2b37");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0d4ae8118cb44188bee06e84dd414d4b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "121272c7d60d49539d8006539a2d34a6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "12d329bb8fa146cbb838c01dcb96ccb0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "14dc21725cd94d6b85936bd79b8f5f1c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2cc506faa3084a27bef9fe6d7733b53b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2f654980583a4971b75192d99e4ab7d1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "511d5b240f1d4a6f9623cf48346cd9d8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "518fd7d216bc4e32b18d623fc15062d4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5596c9c7a60b44679746527f8126df1b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "664cfe60885745ab98cc035a1b9db861");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "667b611ea0c94932b92ec834f7cc07d6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "75d9c11f1e214dd6a6e38339ba8f1032");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7922926f9495478bba727124868001a3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "83729961fb254b2c85a4b6e09edce637");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8756d7fded514827b34e6ce3199f1993");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "93c87391b27b438ebb43329a111d149d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ad17a1668c8448ba452b88d3366aef8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9fe71f506f7f4121bb7414e9d3869c9c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b14ba72c9cdc473f937063e02c5712fd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b1740b50787043a79768fa47e7530224");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b1ae743ca8fd4cd6824bb3eda80a638d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bb662e0088fd42249bc952abfe388662");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bc1e1dcd981e44c28b77c0d07944239a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c5b6742f3d454e41bc73a9a44d2c6130");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c835bf7cae3b40bdadc2ec483f2d0e19");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cbe4e961b93344d18c432faaaf299479");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d29c5d96d2b24e8aa9ccacb148ba3643");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "db3527865c8f40939e408cc83cf13f3f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e35e41342eb7434dac2068350b9a6493");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f5ee9443dd11469fb43f71c16665413f");

            migrationBuilder.DropColumn(
                name: "SubmissionId",
                table: "SurveySubmissionDetails");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEL9lgyW8EhPnN9+bxrQE0ecGTeDoTBz57bNLqYU3wQRRbcTLa/0veNQ0E5QEswPf2A==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "247dcfe4db8342ecb76db88f30503138", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 13, 22, 45, 8, 871, DateTimeKind.Local).AddTicks(1307), "FeaturesButton", new DateTime(2025, 5, 13, 22, 45, 8, 871, DateTimeKind.Local).AddTicks(1310) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "02672e38c196412396c514ed85242d4e", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3672), true, "SurveyList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3672), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "0a6ea3cb77b847189c9ea9013ec60ed7", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3705), true, "CustomForm", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3706), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "0ef065a7dc0c4cb1b48d2f3aa1527577", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3669), true, "History", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3670), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "1e5cf7f4903e4a489f2f83d568b51cc9", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3660), true, "MembershipList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3660), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "21d838a2bf3c4bd8bf24973659b1cb19", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3701), true, "ShippingFeeConfig", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3701), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2546eb96fe0e43128fc37b2fcc6beedc", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3633), true, "Brand", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3633), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "285472479482460695f1c6495328c7e5", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3692), true, "TemplateUidList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3692), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "2ec4d869ade2407b8b572961ef2fc13d", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3685), true, "CampaignHistory", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3685), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "3a09a3960e094008aaec9237ce612bcc", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3630), true, "Category", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3631), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "3bcbd7af236147609fea006d93997bf0", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3664), true, "Rank", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3665), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "42f3bedd02f4477db4928cfd1b4449c0", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3681), true, "CampaignList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3681), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "4795ae3e1586459b9ac0576d2bca531f", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3694), true, "GeneralSetting", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3694), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "535819194e93483496bc1e7bb0bc3411", new DateTime(2025, 5, 13, 22, 45, 8, 869, DateTimeKind.Local).AddTicks(7467), true, "Overview", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(2865), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "538bf4bc1a22434e8f5eca78926ef53a", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3689), true, "EventTemplateHistory", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3690), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "5767dcedda9b4611ae63c994041361cd", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3635), true, "ProductProperty", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3636), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "6d7cb89a0f104075a23573c734479f43", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3674), true, "GameList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3674), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "6eeaebb18cb54279929abc69f03494ac", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3653), true, "DiscountList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3653), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "75b3a273e92246e5a6113baaefff7f3d", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3676), true, "History", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3677), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "82a59c99ef234a10ba87ecbc53df3f82", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3617), true, "AffiliateList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3618), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "8f3ae00df8f0418d93de3a9cf5501ed9", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3687), true, "EventTemplateList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3687), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "9ffc1cdfcbd842baafb79038ecb73b9b", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3646), true, "BookingList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3646), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "a3ebebd357d04fb39dab385fcbf2a165", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3655), true, "Promotion", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3656), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "ab44bd5b884a4414ac258ef479e42004", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3698), true, "MembershipExtendDefaults", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3699), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "b0d709fb8d8a44a984a64d56d86e586d", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3620), true, "ArticleCategory", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3620), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "b0e1263a2a0d4337b6ff5df57ec4282b", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3651), true, "InvoiceTemplate", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3651), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "b3aae582034941e69ef0c31f606078d4", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3638), true, "ProductList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3638), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "b4985bf5df234717af540c99612fcc39", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3667), true, "Tag", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3667), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "ca8bd7336d2e470ebf2877450cd02614", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3622), true, "ArticleList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3623), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "cf6cea67545b4a4a85c845e74d63b762", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3612), true, "BranchList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3614), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "d77a24af3476400fac0a7b61630e1997", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3658), true, "VoucherList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3658), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "dbe51b761e1b4f088df68fcf0138313d", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3678), true, "GamePrize", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3679), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "e3b7674875bf47a9a37aa8af73cd7721", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3643), true, "BookingItem", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3644), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "e687fcafe0364706923bf2116d21f071", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3696), true, "EnableFeatures", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3697), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "e950dbd9dba44a30af0795e68a28e017", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3648), true, "OrderList", new DateTime(2025, 5, 13, 22, 45, 8, 870, DateTimeKind.Local).AddTicks(3649), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" }
                });
        }
    }
}
