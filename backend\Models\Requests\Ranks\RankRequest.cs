﻿namespace MiniAppCore.Models.Requests.Ranks
{
    public class RankRequest
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public long RankingPoint { get; set; }
        public float ConvertRate { get; set; }

        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }

        public List<IFormFile> Images { get; set; } = new();
        public List<string> RemovedOldImages { get; set; } = new();
    }
}
