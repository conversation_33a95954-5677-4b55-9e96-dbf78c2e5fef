﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddTableActionButtonConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "61d24dfb7f7744e6b8549fab15b21fb5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0cca2b4a3fce47d6afa05c7675a103cf");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2907b32a65304330a0bbc1196c260c2e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "29b5c200b76640bba81e76c6045c78d2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "325c7f3117d048139f407e658e9cca45");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "38b4b2bbe61d49178064b4bfc6dceab3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3b5510a94fd249718c4a281c2920bce2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3f36583eba0847939435881ad90bf5df");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "47f80fb69bcf468dbf9043d0a9fc0506");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "48600721a1ff4f19859cfdc06a072129");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4a53bbc292a14c96b510e37277fc6626");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "507fe437720647f493a01d45f7e02f85");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "54ca1114c4024dd0bfdf399fe7e95a8a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "66e702f285314cf386ce3822b01edfe5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "702521c8146140a19b26ccc6f09f3852");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "74f24a5675564e759072073b1c6b6852");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7cb4295f7f44427fa592645ffe641937");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "99abbfcea5d1433bbd2f5300ec614006");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a7cd3d664d9747369aae3981f3a92b32");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a8646f84fd7a40259db6f9c99cf62372");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "aba359bb53d840b19ae19106dbed45ea");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ad37da3de60f4596a4b58e3c5abeebb7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b217b5a5c3394bb0a8989c357aa0e312");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bdd7d66dcfb64ac99a07db063158ae9e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bf2e331792154a36a9f38388a8cd1702");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c0a35ef04243413dbc63a3432a4d99fd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c21819588a2049e09818da2c7d5529ee");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c21b88ad05b34a01995f3f04be65b7cf");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ce9e432031e94604905d2b3c9a60733e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d8a8dcf3d8f44242916604a96fe8154c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dc38bfbbd7fd4e4b8e22e318cc89f043");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e217d285d9ec46a9a1a5512148b0a6fa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e9f6faa165664d26845166072371d38a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ed965853cd3d47b9b015b0915af2dd3d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fa7cbd3db1b74e4ea41133b4d5f5f543");

            migrationBuilder.AlterColumn<string>(
                name: "SectionId",
                table: "SurveyQuestions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "QuestionId",
                table: "SurveyLikertQuestions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36,
                oldNullable: true);

            migrationBuilder.CreateTable(
                name: "ActionButtonConfigs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Image = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Category = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SortOrder = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ActionButtonConfigs", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEEcPQmzFlWDpwrqX6lEhyOMoXtIf4+AS0K8oO20Uufiznuvnjo05H+eFiIxb5Nq+gg==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "7669df895d73456a8bf134cc0a900349", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 4, 0, 8, 58, 490, DateTimeKind.Local).AddTicks(4396), "FeaturesButton", new DateTime(2025, 5, 4, 0, 8, 58, 490, DateTimeKind.Local).AddTicks(4408) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "0599752851f84af29f30a8e6b11c4fce", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(1013), true, "Overview", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5216), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "1807f86b67be40d6b7d46d24e900192f", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6034), true, "History", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6034), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "193952b325b84fbc9f9374dd825981a1", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5972), true, "History", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5972), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "1a5759ac54ad495595699824c8b6bc6a", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5939), true, "Brand", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5940), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "1f603528c4944ce288efb2ce7610520c", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5964), true, "MembershipList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5965), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "1f67946195b44a96a8c564565a3cab9b", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5977), true, "GameList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5978), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "1f6862a8d2914eb3b74cab5d60ce2a20", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6041), true, "CampaignHistory", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6041), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "2cc201acd93c4e349b4f9464b59bbca6", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6045), true, "EventTemplateHistory", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6046), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "2e0067561ec545078867d553bcdc85c3", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5974), true, "SurveyList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5974), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "30170463c05e452dbea50566e8eb06f0", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6047), true, "TemplateUidList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6048), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "33e89d68c08449c4826177e9b52bf83f", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5970), true, "Tag", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5970), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "37764e17602e41d6a57c4b35abf3fd32", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5958), true, "DiscountList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5958), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "3d917cd4d5a54c71a9b62fc632d951ac", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6055), true, "MembershipExtendDefaults", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6056), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "3db5a4f9e19c4cc4be3b6a7ce7317a3d", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6039), true, "CampaignList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6039), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "455579a5c01b44cf8ab8936df9167f81", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6051), true, "GeneralSetting", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6051), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "4b64cdd8f5d74173864fbef4daaf9a0e", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5953), true, "InvoiceTemplate", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5954), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "5b03d93687ec4a6f8bbf1afcb167da35", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5962), true, "VoucherList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5962), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "6371743da95b42dc888d0c3acb922203", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6058), true, "ShippingFeeConfig", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6058), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "65c2d9d8a4994ed29b8f233ff493f7fc", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6037), true, "GamePrize", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6037), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "70e5b6c7d49c4bac99e48c50a3a1010d", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5967), true, "Rank", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5968), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "71a30ffa4fed47349497c24436869e45", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5937), true, "Category", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5937), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "935826b1c670497680801c440b2e0e64", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5915), true, "BranchList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5917), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "9ae8946f0b3849adbb354a52ec7a59ee", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6043), true, "EventTemplateList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6043), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "aea3de85262847258417fff19d68b632", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6053), true, "EnableFeatures", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6054), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "b11b5701cde1431198be3c65ff83d601", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5943), true, "ProductList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5944), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "bb03f9cf14114ce9a9e62024ed628460", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5947), true, "BookingItem", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5947), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "c385950b121b4a9cb6f5ac890492876a", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5951), true, "OrderList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5951), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "ca0479e1920c47399bfdb5d4f64eda60", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5941), true, "ProductProperty", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5942), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "d4e9267c5086404dbd8dd64e7628a1b9", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5921), true, "AffiliateList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5922), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "d7c083b5bb1f4b22b3efbcb59db6bded", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5949), true, "BookingList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5949), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "d8028fdc47a64a9298a37fa493a94073", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5960), true, "Promotion", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5960), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "d8856b7e15cb4ab0a52c64bff8b314ad", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5926), true, "ArticleList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5926), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "f3fcc5063ed9409f83d9be0a78276404", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5924), true, "ArticleCategory", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5924), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "f53477a08c1144bebae4415036cfda73", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6061), true, "CustomForm", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6062), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ActionButtonConfigs");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "7669df895d73456a8bf134cc0a900349");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0599752851f84af29f30a8e6b11c4fce");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1807f86b67be40d6b7d46d24e900192f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "193952b325b84fbc9f9374dd825981a1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1a5759ac54ad495595699824c8b6bc6a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1f603528c4944ce288efb2ce7610520c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1f67946195b44a96a8c564565a3cab9b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1f6862a8d2914eb3b74cab5d60ce2a20");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2cc201acd93c4e349b4f9464b59bbca6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2e0067561ec545078867d553bcdc85c3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "30170463c05e452dbea50566e8eb06f0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "33e89d68c08449c4826177e9b52bf83f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "37764e17602e41d6a57c4b35abf3fd32");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3d917cd4d5a54c71a9b62fc632d951ac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3db5a4f9e19c4cc4be3b6a7ce7317a3d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "455579a5c01b44cf8ab8936df9167f81");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4b64cdd8f5d74173864fbef4daaf9a0e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5b03d93687ec4a6f8bbf1afcb167da35");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6371743da95b42dc888d0c3acb922203");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "65c2d9d8a4994ed29b8f233ff493f7fc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "70e5b6c7d49c4bac99e48c50a3a1010d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "71a30ffa4fed47349497c24436869e45");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "935826b1c670497680801c440b2e0e64");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ae8946f0b3849adbb354a52ec7a59ee");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "aea3de85262847258417fff19d68b632");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b11b5701cde1431198be3c65ff83d601");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bb03f9cf14114ce9a9e62024ed628460");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c385950b121b4a9cb6f5ac890492876a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ca0479e1920c47399bfdb5d4f64eda60");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d4e9267c5086404dbd8dd64e7628a1b9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d7c083b5bb1f4b22b3efbcb59db6bded");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d8028fdc47a64a9298a37fa493a94073");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d8856b7e15cb4ab0a52c64bff8b314ad");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f3fcc5063ed9409f83d9be0a78276404");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f53477a08c1144bebae4415036cfda73");

            migrationBuilder.AlterColumn<string>(
                name: "SectionId",
                table: "SurveyQuestions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.AlterColumn<string>(
                name: "QuestionId",
                table: "SurveyLikertQuestions",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(36)",
                oldMaxLength: 36);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEFnD0yM5e66kPysRwAlpjx/ZHAIShPCRw96+LpNa9vxlo5AqclqDRQ3mfQL9YArwNw==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "61d24dfb7f7744e6b8549fab15b21fb5", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 28, 16, 7, 18, 66, DateTimeKind.Local).AddTicks(9154), "FeaturesButton", new DateTime(2025, 4, 28, 16, 7, 18, 66, DateTimeKind.Local).AddTicks(9157) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "0cca2b4a3fce47d6afa05c7675a103cf", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9952), true, "CustomForm", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9952), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2907b32a65304330a0bbc1196c260c2e", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9941), true, "GeneralSetting", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9941), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "29b5c200b76640bba81e76c6045c78d2", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9923), true, "GameList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9923), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "325c7f3117d048139f407e658e9cca45", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9918), true, "History", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9919), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "38b4b2bbe61d49178064b4bfc6dceab3", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9921), true, "SurveyList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9921), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "3b5510a94fd249718c4a281c2920bce2", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9883), true, "Brand", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9884), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "3f36583eba0847939435881ad90bf5df", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9896), true, "BookingList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9896), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "47f80fb69bcf468dbf9043d0a9fc0506", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9914), true, "Rank", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9914), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "48600721a1ff4f19859cfdc06a072129", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9929), true, "CampaignList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9929), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "4a53bbc292a14c96b510e37277fc6626", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9907), true, "VoucherList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9907), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "507fe437720647f493a01d45f7e02f85", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9869), true, "ArticleCategory", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9870), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "54ca1114c4024dd0bfdf399fe7e95a8a", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9933), true, "CampaignHistory", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9933), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "66e702f285314cf386ce3822b01edfe5", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(4916), true, "Overview", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9092), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "702521c8146140a19b26ccc6f09f3852", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9893), true, "BookingItem", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9894), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "74f24a5675564e759072073b1c6b6852", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9935), true, "EventTemplateList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9935), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "7cb4295f7f44427fa592645ffe641937", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9948), true, "ShippingFeeConfig", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9948), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "99abbfcea5d1433bbd2f5300ec614006", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9814), true, "AffiliateList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9815), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "a7cd3d664d9747369aae3981f3a92b32", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9888), true, "ProductList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9888), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "a8646f84fd7a40259db6f9c99cf62372", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9939), true, "TemplateUidList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9939), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "aba359bb53d840b19ae19106dbed45ea", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9945), true, "MembershipExtendDefaults", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9946), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "ad37da3de60f4596a4b58e3c5abeebb7", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9927), true, "GamePrize", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9927), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "b217b5a5c3394bb0a8989c357aa0e312", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9937), true, "EventTemplateHistory", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9937), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "bdd7d66dcfb64ac99a07db063158ae9e", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9898), true, "OrderList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9898), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "bf2e331792154a36a9f38388a8cd1702", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9925), true, "History", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9925), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "c0a35ef04243413dbc63a3432a4d99fd", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9905), true, "Promotion", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9905), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "c21819588a2049e09818da2c7d5529ee", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9809), true, "BranchList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9811), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "c21b88ad05b34a01995f3f04be65b7cf", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9900), true, "InvoiceTemplate", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9901), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "ce9e432031e94604905d2b3c9a60733e", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9943), true, "EnableFeatures", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9944), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "d8a8dcf3d8f44242916604a96fe8154c", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9903), true, "DiscountList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9903), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "dc38bfbbd7fd4e4b8e22e318cc89f043", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9916), true, "Tag", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9917), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "e217d285d9ec46a9a1a5512148b0a6fa", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9909), true, "MembershipList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9910), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "e9f6faa165664d26845166072371d38a", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9885), true, "ProductProperty", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9886), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "ed965853cd3d47b9b015b0915af2dd3d", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9872), true, "ArticleList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9872), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "fa7cbd3db1b74e4ea41133b4d5f5f543", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9881), true, "Category", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9881), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" }
                });
        }
    }
}
