﻿using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Surveys
{
    public class SurveySubmissionDetail : BaseEntity
    {
        [MaxLength(36)]
        public string? SurveySubmissionId { get; set; }

        [MaxLength(36)]
        public string? SurveyQuestionId { get; set; }

        [MaxLength(36)]
        public string? SurveyLikertQuestionId { get; set; }

        [MaxLength(36)]
        public string? SurveyAnswerId { get; set; }

        public string? InputValue { get; set; }
    }
}
