﻿<style>
    .spinner {
        z-index: 0 !important;
    }
</style>

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> loại sản phẩm</h4>
                <p class="mb-0">
                    Điền phân loại hàng nếu sản phẩm có màu sắc kích thước khác nhau để khách hàng dễ lựa chọn.
                </p>
            </div>
            <button onclick="GetFormProperty('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-property">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>
    <!--<PERSON><PERSON> lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên/mô tả">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0; z-index: -1;"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-properties" class="data-table table mb-0 tbl-server-info"> </table>
        </div>
    </div>
</div>

<div id="modal-property" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListProperty();

            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#search').on('input', search);
        });

        function GetListProperty() {
            table = new DataTable("#list-properties", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetPageProperty", "Products")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order?.[0]?.dir || 'asc',
                            keyword: keyword,
                        },
                        success: function (response) {
                            if (response.code !== 0) {
                                console.error("Lỗi từ server:", response.message);
                                return;
                            }

                            const formattedData = response.data.map((item, index) => ({
                                index: data.start + index + 1, // Số thứ tự
                                name: `
                                        <div class="d-flex align-items-center">
                                            <div>
                                                ${item.name}
                                                <p class="mb-0"><small>${item.description}</small></p>
                                            </div>
                                        </div>
                                    `,
                                description: item.description,
                                actions: `
                                        <div class="d-flex align-items-center justify-content-evenly list-action">
                                            <a onclick="GetFormProperty('${item.id}')" class="badge badge-info"
                                               data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                <i class="ri-edit-line fs-6"></i>
                                            </a>
                                            <a onclick="DeleteProperty('${item.id}')" class="badge bg-warning"
                                               data-toggle="tooltip" data-placement="top" title="Xóa">
                                                <i class="ri-delete-bin-line fs-6"></i>
                                            </a>
                                        </div>
                                    `
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || response.data.length,
                                    data: formattedData
                                });
                            }, 400);
                        },
                        error: function (xhr, status, error) {
                            console.error("AJAX error:", status, error);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "index", className: 'text-center' },
                    { title: "Tên", data: "name" },
                    { title: "Mô tả", data: "description" },
                    { title: "Thao tác", data: "actions" },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('light light-data');
        }

        function GetFormProperty(id) {
            const url = id ? `@Url.Action("Detail", "ProductProperty")/${id}` : "@Url.Action("Create", "ProductProperty")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-property").modal("toggle");

                    $("#type").select2();

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function DeleteProperty(id) {
            const url = `/api/Products/Properties/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const name = $("#name").val()?.trim();
            const description = $("#descr").val()?.trim();
            const isMultipleChoice = $("#isMultipleChoice").val() === "true";
            const maxSelection = parseInt($("#maxSelection").val());

            const options = [];

            $("#optionsTableBody tr").each(function () {
                const value = $(this).find(".option-value").text().trim();
                const propertyItemId = $(this).data("property-item-id");

                options.push({ PropertyValueId: propertyItemId || "", Value: value });
            });

            const data = {
                PropertyId: id,
                Name: name,
                Description: description,
                IsMultipleChoice: isMultipleChoice,
                MaxSelection: maxSelection,
                Options: options
            };

            const url = id ? `/api/Products/Properties/${id}` : "/api/Products/Properties";
            const method = id ? "PUT" : "POST";

            $.ajax({
                url: url,
                type: method,
                contentType: "application/json",
                data: JSON.stringify(data),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, "success");
                        table.ajax.reload(null, false);
                    }
                    $("#modal-property").modal("toggle");
                },
                error: function (err) {
                    AlertResponse("Lưu thuộc tính thất bại!", "error");
                }
            });
        }
    </script>
}
