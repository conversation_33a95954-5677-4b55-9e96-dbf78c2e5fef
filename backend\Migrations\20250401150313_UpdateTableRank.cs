﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableRank : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CreadtedDate",
                table: "Ranks",
                newName: "CreatedDate");

            // Xóa cột Id cũ (int, identity)
            migrationBuilder.DropPrimaryKey(name: "PK_Ranks", table: "Ranks");
            migrationBuilder.DropColumn(name: "Id", table: "Ranks");

            // Tạo lại cột Id kiểu nvarchar(32) + <PERSON><PERSON><PERSON> trị mặc định là NEWID() (GUID)
            migrationBuilder.AddColumn<string>(
                name: "Id",
                table: "Ranks",
                type: "nvarchar(32)",
                maxLength: 32,
                nullable: false);

            // 4️⃣ Đặt lại khóa ch<PERSON>h trên cột Id mới
            migrationBuilder.AddPrimaryKey(name: "PK_Ranks", table: "Ranks", column: "Id");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEB52FCBXGzI8JcGgopfxYYUMpigD283ZB8j73TBrNxD1dZ4IZQLftBRXFVPp6NXcgA==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "CreatedDate",
                table: "Ranks",
                newName: "CreadtedDate");

            // Khi rollback, xóa cột Id (nvarchar)
            migrationBuilder.DropPrimaryKey(name: "PK_Ranks", table: "Ranks");
            migrationBuilder.DropColumn(name: "Id", table: "Ranks");

            // Tạo lại cột Id kiểu nvarchar(32) + Giá trị mặc định là NEWID() (GUID)
            migrationBuilder.AddColumn<string>(
                name: "Id",
                table: "Ranks",
                type: "nvarchar(32)",
                maxLength: 32,
                nullable: false);

            // 4️⃣ Đặt lại khóa chính trên cột Id mới
            migrationBuilder.AddPrimaryKey(name: "PK_Ranks", table: "Ranks", column: "Id");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEHG9rZqhJFuNw9/NeWvUKiVJT4kWlSf625gdkFvbEbqd3lL+mxvD1RAJ/FZswe45og==");
        }
    }
}
