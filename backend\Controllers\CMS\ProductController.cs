﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Products;
using MiniAppCore.Models.DTOs.Properties;
using MiniAppCore.Models.Responses.Products;
using MiniAppCore.Services.Branches;
using MiniAppCore.Services.Products;
using MiniAppCore.Services.Products.Ratings;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class ProductController(IProductService productService, IProductRatingService ratingService, IBranchService branchService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }


        [HttpGet("Product/PullProduct")]
        public IActionResult PullProduct()
        {
            return PartialView("_FormPullProduct");
        }

        [HttpGet("Product/Create")]
        public IActionResult Create()
        {
            var product = new ProductDetailResponse()
            {
                Id = string.Empty,
                Name = string.Empty,
                Description = string.Empty,
                OriginalPrice = 0,
                DisplayOrder = 1
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới sản phẩm";
            ViewBag.SelectedProperties = new List<PropertyDTO>();
            ViewBag.Variants = new List<VariantDTO>();

            return PartialView("_Product", product);
        }

        [HttpGet("Product/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Title = "Cập nhật sản phẩm";
            ViewBag.Button = "Cập nhật";
            ViewBag.SelectedBranch = await branchService.GetBranchesByProductId(id);
            var result = await productService.GetProductDetailAsync(id);
            return PartialView("_Product", result);
        }

        [HttpGet("Product/Ratings/Page")]
        public async Task<IActionResult> RatingsPage(string productId, int page = 1)
        {
            var query = new RequestQuery { Page = page, PageSize = 5 };
            var (result, summary) = await ratingService.GetProductReviews(productId, query, true);

            ViewBag.HasMore = page < result.TotalPages;

            return PartialView("Partials/_ProductRatingItems", result.Data);
        }

        [HttpGet("Product/Ratings")]
        public IActionResult Ratings(string productId)
        {
            ViewBag.Title = "Đánh giá sản phẩm";

            return PartialView("_ProductRating", productId);
        }

        [HttpPost("Product/Review/ToggleShow")]
        public async Task<IActionResult> ToggleReviewShow(string id, bool isShow)
        {
            try
            {
                var result = await ratingService.UpdateReviewShown(id, isShow);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thành công"
                });
            }
            catch (CustomException ex)
            {
                return Ok(new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception)
            {
                return Ok(new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }
    }
}
