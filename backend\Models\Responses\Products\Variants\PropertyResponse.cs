﻿namespace MiniAppCore.Models.Responses.Products.Variants
{
    public class PropertyResponse
    {
        public string? PropertyId { get; set; }
        public string? PropertyName { get; set; }
        public string? Description { get; set; }

        public int MaxSelection { get; set; }
        public bool IsMultipleChoice { get; set; }

        public List<PropertyValueResponse> PropertyValue { get; set; } = new List<PropertyValueResponse>();
    }

    public class PropertyValueResponse
    {
        public string? PropertyValue { get; set; }
        public string? PropertyValueId { get; set; }
    }
}
