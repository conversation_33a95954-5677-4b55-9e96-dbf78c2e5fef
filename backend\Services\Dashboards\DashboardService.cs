﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Database;
using MiniAppCore.Entities.Dashboards;
using MiniAppCore.Enums;

namespace MiniAppCore.Services.Dashboards
{
    public class DashboardService : IDashboardService
    {
        private readonly ApplicationDbContext _context;

        public DashboardService(ApplicationDbContext context)
        {
            _context = context;
        }

        #region Statistics Old
        public async Task<int> TotalOrdersInRange(DateTime startDate, DateTime endDate, string branchId = null)
        {
            var query = _context.Orders.Where(o => o.CreatedDate >= startDate && o.CreatedDate <= endDate && o.OrderStatus != EOrder.Cancelled);

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     query = query.Where(o => o.BranchId == branchId);
            // }

            return await query.CountAsync();
        }

        public async Task<decimal> TotalRevenueInRange(DateTime startDate, DateTime endDate, string branchId = null)
        {
            var query = _context.Orders.Where(o => o.CreatedDate >= startDate && o.CreatedDate <= endDate && o.OrderStatus != EOrder.Cancelled);

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     query = query.Where(o => o.BranchId == branchId);
            // }

            return await query.SumAsync(o => o.Total);
        }

        public async Task<List<ChartDataPoint>> ChartRevenueInRange(DateTime startDate, DateTime endDate, string type, string branchId = null)
        {
            var query = _context.Orders.Where(o => o.CreatedDate >= startDate && o.CreatedDate <= endDate && o.OrderStatus != EOrder.Cancelled);

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     query = query.Where(o => o.BranchId == branchId);
            // }

            var result = new List<ChartDataPoint>();

            switch (type.ToLower())
            {
                case "daily":
                    var dailyData = await query
                        .GroupBy(o => new { o.CreatedDate.Date })
                        .Select(g => new
                        {
                            Date = g.Key.Date,
                            Value = g.Sum(o => o.Total)
                        })
                        .ToListAsync();

                    result = dailyData
                        .OrderBy(item => item.Date)
                        .Select(item => new ChartDataPoint
                        {
                            Label = item.Date.ToString("dd/MM/yyyy"),
                            Value = item.Value
                        })
                        .ToList();
                    break;

                case "monthly":
                    var monthlyData = await query
                        .GroupBy(o => new { Year = o.CreatedDate.Year, Month = o.CreatedDate.Month })
                        .Select(g => new
                        {
                            Year = g.Key.Year,
                            Month = g.Key.Month,
                            Value = g.Sum(o => o.Total)
                        })
                        .ToListAsync();

                    result = monthlyData
                        .OrderBy(item => item.Year)
                        .ThenBy(item => item.Month)
                        .Select(item => new ChartDataPoint
                        {
                            Label = $"{item.Month}/{item.Year}",
                            Value = item.Value
                        })
                        .ToList();
                    break;

                case "yearly":
                    var yearlyData = await query
                        .GroupBy(o => o.CreatedDate.Year)
                        .Select(g => new
                        {
                            Year = g.Key,
                            Value = g.Sum(o => o.Total)
                        })
                        .ToListAsync();

                    result = yearlyData
                        .OrderBy(item => item.Year)
                        .Select(item => new ChartDataPoint
                        {
                            Label = item.Year.ToString(),
                            Value = item.Value
                        })
                        .ToList();
                    break;
            }

            return result;
        }

        public async Task<DailyOrdersRevenueDto> GetOrdersAndRevenue(DateTime startDate, DateTime endDate, string branchId = null)
        {
            var query = _context.Orders.Where(o => o.CreatedDate >= startDate && o.CreatedDate <= endDate && o.OrderStatus != EOrder.Cancelled);

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     query = query.Where(o => o.BranchId == branchId);
            // }

            var orderCount = await query.CountAsync();
            var revenue = await query.SumAsync(o => o.Total);

            return new DailyOrdersRevenueDto
            {
                OrderCount = orderCount,
                Revenue = revenue
            };
        }

        public async Task<int> GetBookingsCount(DateTime startDate, DateTime endDate, string branchId = null)
        {
            var query = _context.Bookings.Where(b => b.CreatedDate >= startDate && b.CreatedDate <= endDate);

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     query = query.Where(b => b.BranchId == branchId);
            // }

            return await query.CountAsync();
        }

        public async Task<int> GetNewCustomersCount(DateTime startDate, DateTime endDate, string branchId = null)
        {
            // Chỉ cần đếm số lượng thành viên mới trong khoảng thời gian
            var newCustomerCount = await _context.Memberships
                .Where(m => m.CreatedDate >= startDate && m.CreatedDate <= endDate)
                .CountAsync();

            return newCustomerCount;
        }

        public async Task<List<ServiceBookingStatsDto>> GetServiceBookingStats(DateTime startDate, DateTime endDate, string branchId = null)
        {
            // Lấy các booking trong khoảng thời gian
            var bookings = await _context.Bookings
                .Where(b => b.CreatedDate >= startDate && b.CreatedDate <= endDate)
                .ToListAsync();

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     bookings = bookings.Where(b => b.BranchId == branchId).ToList();
            // }

            // Lấy ID của các booking hợp lệ
            var bookingIds = bookings.Select(b => b.Id).ToList();

            // Lấy booking details liên quan đến các booking trên
            var bookingDetails = await _context.BookingDetails
                .Where(bd => bookingIds.Contains(bd.BookingId))
                .ToListAsync();

            // Lấy tất cả services để join theo ID
            var services = await _context.BookingItems.ToListAsync();

            // Nhóm theo serviceId và tính số lượng booking
            var serviceStats = bookingDetails
                .GroupBy(bd => bd.BookingItemId)
                .Select(g => new
                {
                    ServiceId = g.Key,
                    BookingCount = g.Count()
                })
                .ToList();

            // Join với services để lấy tên và loại bỏ những dịch vụ không tìm thấy
            var result = serviceStats
                .Select(s => new
                {
                    s.ServiceId,
                    Service = services.FirstOrDefault(svc => svc.Id == s.ServiceId),
                    s.BookingCount
                })
                .Where(x => x.Service != null) // Loại bỏ những dịch vụ không tìm thấy
                .Select(s => new ServiceBookingStatsDto
                {
                    ServiceId = s.ServiceId,
                    ServiceName = s.Service.Name, // Không cần null check nữa vì đã lọc ở trên
                    BookingCount = s.BookingCount
                })
                .OrderByDescending(s => s.BookingCount)
                .ToList();

            return result;
        }

        public async Task<List<ProductSalesStatsDto>> GetProductSalesStats(DateTime startDate, DateTime endDate, string branchId = null)
        {
            // Lấy các order trong khoảng thời gian
            var orders = await _context.Orders
                .Where(o => o.CreatedDate >= startDate && o.CreatedDate <= endDate && o.OrderStatus != EOrder.Cancelled)
                .ToListAsync();

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     orders = orders.Where(o => o.BranchId == branchId).ToList();
            // }

            // Lấy ID của các order hợp lệ
            var orderIds = orders.Select(o => o.Id).ToList();

            // Lấy order details liên quan đến các order trên
            var orderDetails = await _context.OrderDetails
                .Where(od => orderIds.Contains(od.OrderId))
                .ToListAsync();

            // Lấy tất cả products để join theo ID
            var products = await _context.Products.ToListAsync();

            // Nhóm theo productId và tính số lượng và doanh thu
            var productStats = orderDetails
                .GroupBy(od => od.ProductId)
                .Select(g => new
                {
                    ProductId = g.Key,
                    Quantity = g.Sum(od => od.Quantity),
                    Revenue = g.Sum(od => od.TotalPrice)
                })
                .ToList();

            // Join với products để lấy tên và loại bỏ những sản phẩm không tìm thấy
            var result = productStats
                .Select(p => new
                {
                    ProductId = p.ProductId,
                    Product = products.FirstOrDefault(prd => prd.Id == p.ProductId),
                    Quantity = p.Quantity,
                    Revenue = p.Revenue
                })
                .Where(x => x.Product != null) // Loại bỏ những sản phẩm không tìm thấy
                .Select(p => new ProductSalesStatsDto
                {
                    ProductId = p.ProductId,
                    ProductName = p.Product.Name, // Không cần null check nữa vì đã lọc ở trên
                    Quantity = p.Quantity,
                    Revenue = p.Revenue
                })
                .OrderByDescending(p => p.Revenue)
                .ToList();

            return result;
        }

        public async Task<List<ChartDataPoint>> GetDailyRevenueChart(DateTime startDate, DateTime endDate, string branchId = null)
        {
            var query = _context.Orders
                .Where(o => o.CreatedDate >= startDate && o.CreatedDate <= endDate && o.OrderStatus != EOrder.Cancelled);

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     query = query.Where(o => o.BranchId == branchId);
            // }

            // Lấy dữ liệu thực tế - chỉ lấy ngày có doanh thu
            var revenueData = await query
                .GroupBy(o => o.CreatedDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Value = g.Sum(o => o.Total)
                })
                .ToListAsync();

            // Sau khi lấy dữ liệu từ database, thực hiện việc chuyển đổi DateTime thành chuỗi ở phía client
            return revenueData
                .Select(item => new ChartDataPoint
                {
                    Label = item.Date.ToString("dd/MM/yyyy"),
                    Value = item.Value
                })
                .OrderBy(item => item.Label) // Đã có cùng định dạng dd/MM/yyyy nên có thể sắp xếp như chuỗi
                .ToList();
        }

        public async Task<List<ChartDataPoint>> GetDailyOrdersChart(DateTime startDate, DateTime endDate, string branchId = null)
        {
            var query = _context.Orders
                .Where(o => o.CreatedDate >= startDate && o.CreatedDate <= endDate && o.OrderStatus != EOrder.Cancelled);

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     query = query.Where(o => o.BranchId == branchId);
            // }

            // Lấy dữ liệu thực tế - chỉ lấy ngày có đơn hàng
            var orderData = await query
                .GroupBy(o => o.CreatedDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Count = g.Count()
                })
                .ToListAsync();

            // Xử lý phía client
            return orderData
                .OrderBy(item => item.Date) // Sắp xếp theo ngày
                .Select(item => new ChartDataPoint
                {
                    Label = item.Date.ToString("dd/MM/yyyy"),
                    Value = item.Count
                })
                .ToList();
        }

        public async Task<List<ChartDataPoint>> GetDailyBookingsChart(DateTime startDate, DateTime endDate, string branchId = null)
        {
            var query = _context.Bookings.Where(b => b.CreatedDate >= startDate && b.CreatedDate <= endDate);

            // Tạm thời ẩn branchId
            // if (!string.IsNullOrEmpty(branchId))
            // {
            //     query = query.Where(b => b.BranchId == branchId);
            // }

            // Lấy dữ liệu thực tế - chỉ lấy ngày có booking
            var bookingData = await query
                .GroupBy(b => b.CreatedDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Count = g.Count()
                })
                .ToListAsync();

            // Xử lý phía client
            return bookingData
                .OrderBy(item => item.Date) // Sắp xếp theo ngày
                .Select(item => new ChartDataPoint
                {
                    Label = item.Date.ToString("dd/MM/yyyy"),
                    Value = item.Count
                })
                .ToList();
        }

        #endregion

        #region Statistics New

        public async Task<IEnumerable<StatisticBooking>> GetBookingStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            return await _context.Set<StatisticBooking>()
                .Where(b => b.UpdatedDate >= startDate && b.UpdatedDate <= endDate)
                .OrderBy(b => b.UpdatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<StatisticBooking>> GetBookingStatisticsByStatusAsync(DateOnly startDate, DateOnly endDate, string status)
        {
            return await _context.Set<StatisticBooking>()
                .Where(b => b.UpdatedDate >= startDate && b.UpdatedDate <= endDate && b.BookingStatus == status)
                .OrderBy(b => b.UpdatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<StatisticMembership>> GetMembershipStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            return await _context.Set<StatisticMembership>()
                .Where(m => m.UpdatedDate >= startDate && m.UpdatedDate <= endDate)
                .OrderBy(m => m.UpdatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<StatisticOrder>> GetOrderStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            return await _context.Set<StatisticOrder>()
                .Where(o => o.UpdatedDate >= startDate && o.UpdatedDate <= endDate)
                .OrderBy(o => o.UpdatedDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<StatisticOrder>> GetOrderStatisticsByStatusAsync(DateOnly startDate, DateOnly endDate, string status)
        {
            return await _context.Set<StatisticOrder>()
                .Where(o => o.UpdatedDate >= startDate && o.UpdatedDate <= endDate && o.OrderStatus == status)
                .OrderBy(o => o.UpdatedDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalRevenueFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            return await _context.Set<StatisticOrder>()
                .Where(o => o.UpdatedDate >= startDate && o.UpdatedDate <= endDate)
                .SumAsync(o => o.Revenue);
        }

        public async Task<IEnumerable<StatisticTopProducts>> GetTopProductsStatisticsAsync(DateOnly startDate, DateOnly endDate, int limit = 5)
        {
            // Get the latest updated date within the range to ensure we're getting the most current statistics
            var latestDate = await _context.Set<StatisticTopProducts>()
                .Where(p => p.UpdatedDate >= startDate && p.UpdatedDate <= endDate)
                .OrderByDescending(p => p.UpdatedDate)
                .Select(p => p.UpdatedDate)
                .FirstOrDefaultAsync();

            if (latestDate == default(DateOnly))
                return new List<StatisticTopProducts>();

            // Get the top products from the latest date
            return await _context.Set<StatisticTopProducts>()
                .Where(p => p.UpdatedDate == latestDate)
                .OrderByDescending(p => p.Revenue)
                .ThenByDescending(p => p.Sold)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<IEnumerable<StatisticTopBookingItem>> GetTopBookingItemsStatisticsAsync(DateOnly startDate, DateOnly endDate, int limit = 5)
        {
            // Get the latest updated date within the range to ensure we're getting the most current statistics
            var latestDate = await _context.Set<StatisticTopBookingItem>()
                .Where(b => b.UpdatedDate >= startDate && b.UpdatedDate <= endDate)
                .OrderByDescending(b => b.UpdatedDate)
                .Select(b => b.UpdatedDate)
                .FirstOrDefaultAsync();

            if (latestDate == default(DateOnly))
                return new List<StatisticTopBookingItem>();

            // Get the top booking items from the latest date
            return await _context.Set<StatisticTopBookingItem>()
                .Where(b => b.UpdatedDate == latestDate)
                .OrderByDescending(b => b.Revenue)
                .ThenByDescending(b => b.Sold)
                .Take(limit)
                .ToListAsync();
        }

        public async Task<IEnumerable<StatisticVoucher>> GetVoucherStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            return await _context.Set<StatisticVoucher>()
                .Where(v => v.UpdatedDate >= startDate && v.UpdatedDate <= endDate)
                .OrderBy(v => v.UpdatedDate)
                .ToListAsync();
        }

        public async Task<DashboardSummaryDto> GetDashboardSummaryAsync(DateOnly startDate, DateOnly endDate)
        {
            // Get order statistics
            var orderStats = await GetOrderStatisticsAsync(startDate, endDate);

            // Get booking statistics
            var bookingStats = await GetBookingStatisticsAsync(startDate, endDate);

            // Get membership statistics
            var membershipStats = await GetMembershipStatisticsAsync(startDate, endDate);

            // Get top products
            var topProducts = await GetTopProductsStatisticsAsync(startDate, endDate);

            // Get top booking items
            var topBookingItems = await GetTopBookingItemsStatisticsAsync(startDate, endDate);

            // Create charts
            var revenueChartData = await GetRevenueChartFromStatisticsAsync(startDate, endDate);

            var ordersChartData = await GetOrderChartFromStatisticsAsync(startDate, endDate);
            var ordersCountChartData = await GetOrderCountChartFromStatisticsAsync(startDate, endDate);
            var ordersRevenueChartData = await GetOrderRevenueChartFromStatisticsAsync(startDate, endDate);

            var bookingsChartData = await GetBookingChartFromStatisticsAsync(startDate, endDate);
            var bookingsCountChartData = await GetBookingCountChartFromStatisticsAsync(startDate, endDate);
            var bookingsRevenueChartData = await GetBookingRevenueChartFromStatisticsAsync(startDate, endDate);

            var membershipsChartData = await GetMemberhsipsChartFromStatisticsAsync(startDate, endDate);

            // Calculate totals
            int totalOrders = orderStats.Sum(o => (int)o.OrderCount);
            decimal totalRevenue = orderStats.Sum(o => o.Revenue);
            int totalBookings = bookingStats.Sum(b => (int)b.BookingCount);
            int newMemberships = membershipStats.Sum(m => (int)m.JoinCount);

            return new DashboardSummaryDto
            {
                TotalOrders = totalOrders,
                TotalRevenue = totalRevenue,
                TotalBookings = totalBookings,
                NewMemberships = newMemberships,
                NewMembershipChart = membershipsChartData,

                RevenueChart = revenueChartData,

                OrdersChart = ordersChartData,
                OrdersCountChart = ordersCountChartData,
                OrdersRevenueChart = ordersRevenueChartData,

                BookingsChart = bookingsChartData,
                BookingsCountChart = bookingsCountChartData,
                BookingsRevenueChart = bookingsRevenueChartData,

                TopProducts = topProducts.ToList(),
                TopBookingItems = topBookingItems.ToList()
            };
        }

        public async Task<List<ChartDataPoint>> GetRevenueChartFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            var orderStats = await _context.Set<StatisticOrder>()
                .Where(o => o.UpdatedDate >= startDate && o.UpdatedDate <= endDate)
                .OrderBy(o => o.UpdatedDate)
                .ToListAsync();

            return orderStats
                .GroupBy(o => o.UpdatedDate)
                .Select(g => new ChartDataPoint
                {
                    Label = g.Key.ToString("dd/MM/yyyy"),
                    Value = g.Sum(o => o.Revenue)
                })
                .ToList();
        }

        #region Memberhsip statistic 
        public async Task<List<ChartDataPoint>> GetMemberhsipsChartFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            var orderStats = await _context.Set<StatisticMembership>()
                .Where(o => o.UpdatedDate >= startDate && o.UpdatedDate <= endDate)
                .OrderBy(o => o.UpdatedDate)
                .ToListAsync();

            return orderStats
                .GroupBy(o => o.UpdatedDate)
                .Select(g => new ChartDataPoint
                {
                    Label = g.Key.ToString("dd/MM/yyyy"),
                    Value = g.Sum(o => o.JoinCount)
                })
                .ToList();
        }

        #endregion

        #region Order statistic

        public async Task<List<ChartDataPoint>> GetOrderChartFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            var orderStats = await _context.Set<StatisticOrder>()
                .Where(o => o.UpdatedDate >= startDate && o.UpdatedDate <= endDate)
                .OrderBy(o => o.UpdatedDate)
                .ToListAsync();

            return orderStats
                .GroupBy(o => o.UpdatedDate)
                .Select(g => new ChartDataPoint
                {
                    Label = g.Key.ToString("dd/MM/yyyy"),
                    Value = g.Sum(o => o.OrderCount)
                })
                .ToList();
        }

        public async Task<List<ChartDataPoint>> GetOrderCountChartFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            var orderStats = await _context.Set<StatisticOrder>()
                .Where(o => o.UpdatedDate >= startDate && o.UpdatedDate <= endDate)
                .OrderBy(o => o.UpdatedDate)
                .ToListAsync();

            return orderStats
                .GroupBy(o => o.OrderStatus)
                .Select(g => new ChartDataPoint
                {
                    Label = g.Key,
                    Value = g.Sum(o => o.OrderCount)
                })
                .ToList();
        }

        public async Task<List<ChartDataPoint>> GetOrderRevenueChartFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            var orderStats = await _context.Set<StatisticOrder>()
                .Where(o => o.UpdatedDate >= startDate && o.UpdatedDate <= endDate)
                .OrderBy(o => o.UpdatedDate)
                .ToListAsync();

            return orderStats
                .GroupBy(o => o.OrderStatus)
                .Select(g => new ChartDataPoint
                {
                    Label = g.Key,
                    Value = g.Sum(o => o.Revenue)
                })
                .ToList();
        }

        #endregion

        #region Booking statistic

        public async Task<List<ChartDataPoint>> GetBookingChartFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            var bookingStats = await _context.Set<StatisticBooking>()
                .Where(b => b.UpdatedDate >= startDate && b.UpdatedDate <= endDate)
                .OrderBy(b => b.UpdatedDate)
                .ToListAsync();

            return bookingStats
                .GroupBy(b => b.UpdatedDate)
                .Select(g => new ChartDataPoint
                {
                    Label = g.Key.ToString("dd/MM/yyyy"),
                    Value = g.Sum(b => b.BookingCount)
                })
                .ToList();
        }

        public async Task<List<ChartDataPoint>> GetBookingCountChartFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            var bookingStats = await _context.Set<StatisticBooking>()
                .Where(b => b.UpdatedDate >= startDate && b.UpdatedDate <= endDate)
                .OrderBy(b => b.UpdatedDate)
                .ToListAsync();

            return bookingStats
                .GroupBy(b => b.BookingStatus)
                .Select(g => new ChartDataPoint
                {
                    Label = g.Key,
                    Value = g.Sum(b => b.BookingCount)
                })
                .ToList();
        }

        public async Task<List<ChartDataPoint>> GetBookingRevenueChartFromStatisticsAsync(DateOnly startDate, DateOnly endDate)
        {
            var bookingStats = await _context.Set<StatisticBooking>()
                .Where(b => b.UpdatedDate >= startDate && b.UpdatedDate <= endDate)
                .OrderBy(b => b.UpdatedDate)
                .ToListAsync();

            return bookingStats
                .GroupBy(b => b.BookingStatus)
                .Select(g => new ChartDataPoint
                {
                    Label = g.Key,
                    Value = g.Sum(b => b.BookingCount)
                })
                .ToList();
        }

        #endregion 

        #endregion
    }
}
