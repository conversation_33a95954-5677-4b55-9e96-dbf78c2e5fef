﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.FormCustoms;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Memberships;
using MiniAppCore.Models.Responses.Memberships;

namespace MiniAppCore.Services.Memberships.CustomForms
{
    public class CustomFormService(IUnitOfWork unitOfWork, IMapper mapper) : Service<CustomForm>(unitOfWork), ICustomFormService
    {
        private readonly IRepository<CustomFormAttribute> _customFormAttributeRepository = unitOfWork.GetRepository<CustomFormAttribute>();

        public async Task<PagedResult<FormCustomResponse>> GetPage(RequestQuery query)
        {
            // Lấy tất cả các form
            var forms = await base.GetPage(query);
            var result = new List<FormCustomResponse>();

            var formIds = forms.Data.Select(f => f.Id).ToList();
            var inputFields = await _customFormAttributeRepository.AsQueryable()
                .Where(a => formIds.Contains(a.CustomFormId))
                .ToListAsync();

            foreach (var form in forms.Data)
            {
                // Lấy thuộc tính của từng form
                var attributes = inputFields.Where(a => a.CustomFormId == form.Id).ToList();
                // Map form sang response
                var formResponse = mapper.Map<FormCustomResponse>(form);
                // Map các thuộc tính sang InputFields
                formResponse.InputFields = mapper.Map<List<ExtendInfoFormResponse>>(attributes);
                result.Add(formResponse);
            }

            return new PagedResult<FormCustomResponse>()
            {
                Data = result,
                TotalPages = forms.TotalPages,
            };
        }

        public async Task<FormCustomResponse> GetActiveCustomFormAsync(string formId)
        {
            var form = await _repository.AsQueryable()
                .Where(f => f.IsActive && f.Id == formId)
                .Select(x => x.Id)
                .FirstOrDefaultAsync();
            if (string.IsNullOrEmpty(form))
            {
                return default;
            }
            return await GetByIdAsync(form, false);
        }

        public async Task<FormCustomResponse> GetByIdAsync(string id, bool? includesDeactive)
        {
            // Lấy form theo ID
            var form = await base.GetByIdAsync(id);
            if (form == null) return default;

            // Lấy các thuộc tính của form
            var attributesQuery = _customFormAttributeRepository.AsQueryable().Where(a => a.CustomFormId == id);
            if (includesDeactive.HasValue && !includesDeactive.Value)
            {
                attributesQuery = attributesQuery.Where(a => a.IsActive);
            }

            var attributes = await attributesQuery.OrderBy(x => x.DislayOrder).ToListAsync();
            // Map sang response
            var formResponse = mapper.Map<FormCustomResponse>(form);
            formResponse.InputFields = attributes.Select(x => new ExtendInfoFormResponse()
            {
                Id = x.Id,
                Attribute = x.Attribute,
                Type = x.Type,
                IsActive = x.IsActive,
                DisplayOrder = x.DislayOrder,
                DefaultValue = x.DefaultValue,
                AttributeName = x.AttributeLabel,
                Min = x.Min,
                Max = x.Max,
                Options = !string.IsNullOrEmpty(x.AttributeValue) && !string.IsNullOrEmpty(x.Type) && x.Type.Equals("option", StringComparison.OrdinalIgnoreCase)
                ? x.AttributeValue.Split(",")
                   .Select(opt => new KeyValuePair<string, string>(opt.Trim(), opt.Trim()))
                   .ToList()
                : new List<KeyValuePair<string, string>>()
            }).ToList(); // mapper.Map<List<ExtendInfoFormResponse>>(attributes);

            return formResponse;
        }

        public async Task<int> CreateAsync(CustomFormRequest request)
        {
            // Map từ request sang form
            var form = mapper.Map<CustomForm>(request);
            if (form.IsActive)
            {
                // await DeactivateOtherActiveFormsAsync();
            }

            // Thêm các thuộc tính của form
            if (request.CustomFormAttributes != null && request.CustomFormAttributes.Any())
            {
                foreach (var attrRequest in request.CustomFormAttributes)
                {
                    var attribute = mapper.Map<CustomFormAttribute>(attrRequest);
                    attribute.CustomFormId = form.Id;
                    _customFormAttributeRepository.Add(attribute);
                }
            }
            return await base.CreateAsync(form);
        }

        public async Task<int> UpdateAsync(string id, CustomFormRequest request)
        {
            // Kiểm tra form tồn tại
            var existingForm = await base.GetByIdAsync(id);
            if (existingForm == null)
            {
                return 0;
            }

            if (request.IsActive)
            {
                // await DeactivateOtherActiveFormsAsync();
            }

            // Map từ request sang entity hiện có
            mapper.Map(request, existingForm);
            // Thêm các thuộc tính mới
            if (request.CustomFormAttributes != null && request.CustomFormAttributes.Any())
            {
                // Xóa tất cả các thuộc tính hiện tại
                var existingAttributes = await _customFormAttributeRepository.AsQueryable()
                    .Where(a => a.CustomFormId == id)
                    .ToListAsync();

                _customFormAttributeRepository.DeleteRange(existingAttributes);

                foreach (var attrRequest in request.CustomFormAttributes)
                {
                    var attribute = mapper.Map<CustomFormAttribute>(attrRequest);
                    attribute.CustomFormId = existingForm.Id;

                    _customFormAttributeRepository.Add(attribute);
                }
            }
            return await base.UpdateAsync(existingForm);
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            // Kiểm tra form tồn tại
            var form = await base.GetByIdAsync(id);
            if (form == null)
            {
                throw new CustomException(200, $"Không tìm thấy form với ID {id}");
            }

            var existingAttributes = await _customFormAttributeRepository.AsQueryable()
                .Where(a => a.CustomFormId == id)
                .ToListAsync();
            _customFormAttributeRepository.DeleteRange(existingAttributes);
            // Xóa form
            return await base.DeleteAsync(form);
        }

        private async Task DeactivateOtherActiveFormsAsync(string? excludeFormId = null)
        {
            var activeForms = await _repository.AsQueryable()
                .Where(f => f.IsActive && (excludeFormId == null || f.Id != excludeFormId))
                .ToListAsync();
            if (activeForms.Any())
            {
                activeForms.ForEach(f => f.IsActive = false);
                _repository.UpdateRange(activeForms);
            }
        }
    }
}
