using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Services.Gamifications.LuckyWheels;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "ADMIN, SUPER_ADMIN")]
    public class LuckyWheelJobsController(
        ILuckyWheelJobManagementService jobManagementService,
        ILogger<LuckyWheelJobsController> logger) : ControllerBase
    {
        /// <summary>
        /// Trigger manual job để cộng điểm cho tất cả users của một LuckyWheel
        /// </summary>
        [HttpPost("{luckyWheelId}/trigger-auto-add-points")]
        public IActionResult TriggerAutoAddSpinPoints(string luckyWheelId)
        {
            try
            {
                jobManagementService.EnqueueAutoAddSpinPointsJob(luckyWheelId);

                return Ok(new
                {
                    Code = 0,
                    Message = "Background job đã được khởi tạo để cộng điểm cho tất cả users",
                    LuckyWheelId = luckyWheelId
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error triggering auto add spin points job for LuckyWheel {LuckyWheelId}", luckyWheelId);
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Có lỗi xảy ra khi khởi tạo background job"
                });
            }
        }

        /// <summary>
        /// Trigger manual job để cộng điểm cho một user cụ thể
        /// </summary>
        [HttpPost("{luckyWheelId}/users/{userZaloId}/add-points")]
        public IActionResult TriggerAddPointsToUser(string luckyWheelId, string userZaloId)
        {
            try
            {
                jobManagementService.EnqueueAddSpinPointsToUserJob(luckyWheelId, userZaloId);

                return Ok(new
                {
                    Code = 0,
                    Message = "Background job đã được khởi tạo để cộng điểm cho user",
                    LuckyWheelId = luckyWheelId,
                    UserZaloId = userZaloId
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error triggering add points job for LuckyWheel {LuckyWheelId}, User {UserZaloId}",
                    luckyWheelId, userZaloId);
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Có lỗi xảy ra khi khởi tạo background job"
                });
            }
        }

        /// <summary>
        /// Khởi tạo lại tất cả auto add points jobs
        /// </summary>
        [HttpPost("reinitialize-all-jobs")]
        public async Task<IActionResult> ReinitializeAllJobs()
        {
            try
            {
                await jobManagementService.InitializeAllAutoSpinPointsJobs();

                return Ok(new
                {
                    Code = 0,
                    Message = "Đã khởi tạo lại tất cả auto add points jobs"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error reinitializing all jobs");
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Có lỗi xảy ra khi khởi tạo lại jobs"
                });
            }
        }
    }
}
