﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Products
{
    public class Product : BaseEntity
    {
        [MaxLength(500)]
        public required string Name { get; set; }
        public string? Description { get; set; }
        public decimal Price { get; set; }
        public string? Images { get; set; }
        public EProduct Status { get; set; } // 1: InStock, 2: OutOfStock, 3: Discontinued

        public bool IsGift { get; set; } = false; // "True" là quà tặng
        public bool IsGlobalProduct { get; set; } = false; // "True" nếu thuộc tất cả chi nhánh

        public int LikeCount { get; set; }
        public int ViewCount { get; set; }
        public int BoughtCount { get; set; }
        public int ReviewCount { get; set; }
        public float ReviewPoint { get; set; }

        public int DisplayOrder { get; set; } = 0; // Thứ tự hiển thị trong danh sách sản phẩm
    }
}
