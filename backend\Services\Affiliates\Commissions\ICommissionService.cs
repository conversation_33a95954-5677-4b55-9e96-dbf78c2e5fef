﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Affiliates;
using MiniAppCore.Models.Responses.Affiliates;

namespace MiniAppCore.Services.Affiliates.Commissions
{
    public interface ICommissionService : IService<CommissionTransaction>
    {
        Task CalculateAndSaveCommissionForOrder(string orderId);
        Task<List<CommissionTransactionResponse>> GetUserCommissionTransactions(string userZaloId);
        Task<List<CommissionTransactionResponse>> GetReferredHistoryCommissions(string userZaloId, string referredUserZaloId);

        // Thêm các phương thức mới
        Task<(List<CommissionRecipientResponse> data, int count)> GetCommissionRecipients(int page, int pageSize, string? keyword, string? status, DateTime? startDate, DateTime? endDate);
        Task<(List<CommissionTransaction> data, int count)> GetTransactionsByReferrerId(int page, int pageSize, string referrerZaloId, DateTime? startDate, DateTime? endDate);
        Task<bool> UpdateTransactionStatus(string transactionId, bool status);
        Task<bool> PayAllCommissions(string referrerZaloId);
        Task<byte[]> ExportCommissionsToExcel(DateTime startDate, DateTime endDate);
        Task<bool> CalculateCommissions(DateTime? startDate, DateTime? endDate);


        // cấu hình 
        Task<decimal> GetCommissionConfigAsync();
        Task<int> AddOrUpdateCommissionRateAsync(decimal percentage);
    }
}
