﻿using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.GamePrizes;
using MiniAppCore.Services.Gamifications.GamePrizes;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    public class GamePrizesController(ILogger<GamePrizesController> logger, IGamePrizeService gamePrizeService) : ControllerBase
    {
        [HttpGet]
        public async Task<IActionResult> GetPage([FromQuery] RequestQuery query)
        {
            try
            {
                var result = await gamePrizeService.GetPage(query);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500, new { ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateGamePrize([FromForm] GamePrizeRequest model)
        {
            try
            {
                await gamePrizeService.CreateAsync(model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo mới phần thưởng thành công",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500, new { ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateGamePrize(string id, [FromForm] GamePrizeRequest model)
        {
            try
            {
                await gamePrizeService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật phần thưởng thành công.",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500, new { ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteGamePrize(string id)
        {
            try
            {
                await gamePrizeService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xoá phần thưởng thành công.",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500, new { ex.Message });
            }
        }
    }
}
