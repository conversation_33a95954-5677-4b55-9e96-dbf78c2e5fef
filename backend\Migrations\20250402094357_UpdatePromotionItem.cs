﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePromotionItem : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_PromotionItem",
                table: "PromotionItem");

            migrationBuilder.RenameTable(
                name: "PromotionItem",
                newName: "PromotionItems");

            migrationBuilder.AlterColumn<string>(
                name: "UserZaloId",
                table: "MembershipExtends",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsGift",
                table: "PromotionItems",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddPrimaryKey(
                name: "PK_PromotionItems",
                table: "PromotionItems",
                column: "Id");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAELAGSLuTYaswEUFmHL8oucH2huBuFULnk1HPQwaqinchf/EO9FXQsWXjSvtNLDdiMQ==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_PromotionItems",
                table: "PromotionItems");

            migrationBuilder.DropColumn(
                name: "IsGift",
                table: "PromotionItems");

            migrationBuilder.RenameTable(
                name: "PromotionItems",
                newName: "PromotionItem");

            migrationBuilder.AlterColumn<string>(
                name: "UserZaloId",
                table: "MembershipExtends",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddPrimaryKey(
                name: "PK_PromotionItem",
                table: "PromotionItem",
                column: "Id");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEB52FCBXGzI8JcGgopfxYYUMpigD283ZB8j73TBrNxD1dZ4IZQLftBRXFVPp6NXcgA==");
        }
    }
}
