using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Models.Requests.Affiliates
{
    public class CreateCommissionRateRequest
    {
        [Required]
        [Range(1, 100)]
        public int Level { get; set; }

        [Required]
        [Range(0.001, 100)]
        public decimal Rate { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime EffectiveDate { get; set; } = DateTime.Now;
    }

    public class UpdateCommissionRateRequest
    {
        [Required]
        [Range(1, 100)]
        public int Level { get; set; }

        [Required]
        [Range(0.001, 100)]
        public decimal Rate { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime EffectiveDate { get; set; } = DateTime.Now;
    }
}
