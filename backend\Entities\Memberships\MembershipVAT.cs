﻿using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Memberships
{
    public class MembershipVAT : BaseEntity
    {
        public required string UserZaloId { get; set; }
        [MaxLength(200)]
        public string? OwnerName { get; set; }
        [MaxLength(100)]
        public string? Email { get; set; }
        [MaxLength(100)]
        public required string TaxCode { get; set; }
        [MaxLength(50)]
        public required string Type { get; set; } = "Personal";
        [MaxLength(200)]
        public required string StreetLine { get; set; }
        [MaxLength(50)]
        public required string WardId { get; set; }
        [MaxLength(50)]
        public required string DistrictId { get; set; }
        [MaxLength(50)]
        public required string ProvinceId { get; set; }
        [MaxLength(350)]
        public required string FullAddress { get; set; }
        public bool IsDefault { get; set; } = true;

    }
}
