﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Services.Surveys.Sections;

namespace MiniAppCore.Services.Surveys
{
    public class SurveyService(IUnitOfWork unitOfWork,
                               IMapper mapper,
                               ISurveySectionService surveySectionService) : Service<Survey>(unitOfWork), ISurveyService
    {
        private readonly IRepository<SurveySubmission> _surveySumissionRepo = unitOfWork.GetRepository<SurveySubmission>();

        public async Task<PagedResult<Survey>> GetPage(IRequestQuery query, string? visitorId, short? status)
        {
            var now = DateTime.Now;
            var surveys = _repository.AsQueryable().AsNoTracking();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                surveys = surveys.Where(x => !string.IsNullOrEmpty(x.Title) && x.Title.ToLower().Contains(query.Keyword.ToLower()));
            }

            // Apply status and date filtering
            if (status.HasValue)
            {
                if (status.Value == (short)ESurvey.Closed)
                {
                    surveys = surveys.Where(x => x.Status == ESurvey.Closed || x.EndDate < now);
                }
                else
                {
                    surveys = surveys.Where(x => x.Status == ESurvey.Opening || (x.StartedDate <= now && x.EndDate > now));

                    // Apply visitor completion status filtering if visitor ID provided
                    if (!string.IsNullOrEmpty(visitorId))
                    {
                        var completedSurveyQuery = _surveySumissionRepo.AsQueryable()
                                                  .Where(x => x.UserZaloId == visitorId)
                                                  .Select(x => x.SurveyId);

                        // Filter by completion status
                        surveys = status.Value == (short)ESurvey.Completed
                            ? surveys.Where(x => completedSurveyQuery.Contains(x.Id))
                            : surveys.Where(x => !completedSurveyQuery.Contains(x.Id));
                    }
                    else
                    {
                        surveys = surveys.Where(x => x.Status == (ESurvey)status.Value);
                    }
                }
            }

            // Get total count for pagination
            var totalItems = await surveys.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);

            // Apply pagination and retrieve results
            var items = await surveys
                .OrderBy(x => x.DisplayOrder)
                .Skip(query.Skip)
                .Take(query.PageSize)
                .ToListAsync();

            // Update status for display if visitor ID was provided
            if (!string.IsNullOrEmpty(visitorId))
            {
                var completedSurveyIds = await _surveySumissionRepo.AsQueryable()
                    .Where(x => x.UserZaloId == visitorId)
                    .Select(x => x.SurveyId!)
                    .ToListAsync();

                if (completedSurveyIds.Any())
                {
                    foreach (var survey in items)
                    {
                        if (completedSurveyIds.Contains(survey.Id))
                        {
                            survey.Status = ESurvey.Completed;
                        }
                        else
                        {
                            survey.Status = survey.EndDate < now ? ESurvey.Closed : ESurvey.Opening;
                        }
                    }
                }
            }

            return new PagedResult<Survey>
            {
                Data = items,
                TotalPages = totalPages
            };
        }

        public async Task<SurveyResponse> GetSurveyByIdAsync(string id)
        {
            // Get survey
            var survey = await GetByIdAsync(id);
            if (survey == null)
            {
                throw new CustomException(200, "Không thể tìm thấy bài khảo sát này!");
            }

            // Validate survey status
            var now = DateTime.Now;
            if (survey.Status == ESurvey.Closed)
            {
                throw new CustomException(200, "Khảo sát đã đóng!");
            }
            if (survey.StartedDate > now)
            {
                throw new CustomException(200, "Khảo sát chưa mở!");
            }
            if (survey.EndDate < now)
            {
                throw new CustomException(200, "Khảo sát đã hết hạn!");
            }
            var sections = await surveySectionService.GetSectionsBySurveyIdAsync(survey.Id);
            // Create response with sections
            return new SurveyResponse
            {
                SurveyId = id,
                ListSection = sections,
                TotalSection = sections.Count
            };
        }

        public async Task<int> CreateSurveyAsync(SurveyDTO request)
        {
            var survey = mapper.Map<Survey>(request);
            survey.Id = Guid.NewGuid().ToString("N");
            _repository.Add(survey);
            request.Sections.ForEach(x => x.SurveyId = survey.Id);
            // Handle sections for survey
            await surveySectionService.CreateOrUpdateSectionsAsync(request.Sections);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> UpdateSurveyAsync(string id, SurveyDTO request)
        {
            // Find the existing survey
            var existingSurvey = await GetByIdAsync(id)
                ?? throw new CustomException(404, "Không tìm thấy khảo sát");

            // Lấy danh sách sections hiện có để giữ lại thông tin tiêu đề nếu cần
            var existingSections = await surveySectionService.GetSectionDTOsBySurveyIdAsync(id);

            // Map giữa ID và tiêu đề section cũ để dễ tra cứu
            var existingSectionTitles = existingSections
                .Where(s => !string.IsNullOrEmpty(s.Id) && !string.IsNullOrEmpty(s.TitleSection))
                .ToDictionary(s => s.Id!, s => s.TitleSection);

            // Kiểm tra và giữ lại tiêu đề cũ nếu tiêu đề mới trống
            foreach (var section in request.Sections)
            {
                if (string.IsNullOrEmpty(section.TitleSection) &&
                    !string.IsNullOrEmpty(section.Id) &&
                    existingSectionTitles.TryGetValue(section.Id, out var oldTitle))
                {
                    section.TitleSection = oldTitle;
                }
            }

            // Update survey properties
            existingSurvey.Title = request.Title;
            existingSurvey.Status = (ESurvey)request.Status;
            existingSurvey.DisplayOrder = request.DisplayOrder;
            existingSurvey.IsDisplay = request.IsDisplay;
            existingSurvey.StartedDate = request.StartedDate;
            existingSurvey.EndDate = request.EndDate;
            existingSurvey.UpdatedDate = DateTime.Now;

            _repository.Update(existingSurvey);

            // Handle sections for survey
            await surveySectionService.CreateOrUpdateSectionsAsync(request.Sections);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<SurveyDTO?> GetSurveyDTOByIdAsync(string id)
        {
            // Get survey
            var survey = await GetByIdAsync(id);
            if (survey == null)
            {
                return null;
            }

            // Map base survey properties
            var surveyDto = mapper.Map<SurveyDTO>(survey);
            surveyDto.Sections = await surveySectionService.GetSectionDTOsBySurveyIdAsync(id);
            return surveyDto;
        }
    }
}
