﻿using System.ComponentModel.DataAnnotations.Schema;
using MiniAppCore.Enums;

namespace MiniAppCore.Models.DTOs.Events
{
    public class EventDTO
    {
        public string? Title { get; set; }
        public string? Content { get; set; }

        public IFormFile? Banner { get; set; }
        public List<IFormFile>? Images { get; set; }

        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        public EEvent Type { get; set; }
        public string? MeetingLink { get; set; }
        public string? GoogleMapURL { get; set; }

        public string? Address { get; set; }

        public bool IsActive { get; set; }

        public EEventStatus Status { get; set; }

        // Nhận từ form
        public string? GiftData { get; set; }
        public string? SponsorData { get; set; }

        // Tự parse
        [NotMapped]
        public List<EventGiftDTO>? Gifts => !string.IsNullOrEmpty(GiftData)
            ? System.Text.Json.JsonSerializer.Deserialize<List<EventGiftDTO>>(GiftData, new System.Text.Json.JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            })
            : new();

        [NotMapped]
        public List<EventSponsorDTO>? Sponsors => !string.IsNullOrEmpty(SponsorData)
            ? System.Text.Json.JsonSerializer.Deserialize<List<EventSponsorDTO>>(SponsorData, new System.Text.Json.JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            })
            : new();
    }

    public class EventGiftDTO
    {
        public string? ProductId { get; set; }
        public int Quantity { get; set; }
    }

    public class EventSponsorDTO
    {
        public string? SponsorId { get; set; }
        public string? TierId { get; set; }
    }
}
