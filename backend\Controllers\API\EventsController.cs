﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Services.Events;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class EventsController(ILogger<EventsController> logger, IEventService eventService) : ControllerBase
    {
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] RequestQuery query, [FromQuery] short status, [FromQuery] short activeStatus, [FromQuery] string? sponsorId)
        {
            try
            {
                eventService.IsAdmin = User?.IsInRole("ADMIN") ?? false;

                var userZaloId = User?.Claims.FirstOrDefault(c => c.Type == "UserZaloId");

                var result = await eventService.GetPaged(query, userZaloId?.Value ?? "", sponsorId ?? "", status, activeStatus);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            try
            {
                var userZaloId = User?.Claims.FirstOrDefault(c => c.Type == "UserZaloId");

                var data = await eventService.GetDetailAsync(id, userZaloId?.Value ?? "");

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    data,
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        #region ADMIN FUNCTIONS

        [HttpPost]
        public async Task<IActionResult> Create([FromForm] EventDTO request)
        {
            try
            {
                await eventService.CreateEventAsync(request);

                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo sự kiện thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Create(string id, [FromForm] EventDTO request)
        {
            try
            {
                await eventService.UpdateEventAsync(id, request);

                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật sự kiện thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                await eventService.DeleteEventAsync(id);

                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa sự kiện thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        #endregion
    }
}
