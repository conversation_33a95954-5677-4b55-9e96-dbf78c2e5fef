﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Models.DTOs.Surveys;

namespace MiniAppCore.Services.Surveys.Answers
{
    public class SurveyAnswerService(IUnitOfWork unitOfWork, IMapper mapper) : Service<SurveyAnswer>(unitOfWork), ISurveyAnswerService
    {
        private readonly IMapper _mapper = mapper;

        public async Task<List<T>> GetAnswersResponseByQuestionIds<T>(List<string> questionIds)
        {
            if (questionIds?.Any() != true)
                return new List<T>();

            var dataLoader = new AnswerDataLoader<T>(_repository, _mapper);
            return await dataLoader.LoadAnswersByQuestionIds(questionIds);
        }

        public async Task<int> DeleteAnswersByQuestionIdsAsync(List<string> questionIds)
        {
            if (questionIds?.Any() != true)
                return 0;

            var processor = new AnswerProcessor(_repository);
            return await processor.DeleteAnswersByQuestionIds(questionIds, unitOfWork);
        }

        public async Task<int> CreateOrUpdateAnswersAsync(List<SurveyAnswerDTO> answers)
        {
            if (answers?.Any() != true)
                return 0;

            // Group by question ID first for better organization
            var answersByQuestion = answers
                .Where(a => !string.IsNullOrEmpty(a.QuestionId))
                .GroupBy(a => a.QuestionId!)
                .ToDictionary(g => g.Key, g => g.ToList());

            return await CreateOrUpdateAnswersAsync(answersByQuestion);
        }

        public async Task<int> CreateOrUpdateAnswersAsync(Dictionary<string, List<SurveyAnswerDTO>> answersByQuestion)
        {
            if (answersByQuestion?.Any() != true)
                return 0;

            var processor = new AnswerProcessor(_repository);
            return await processor.ProcessAnswers(answersByQuestion, unitOfWork);
        }

        #region Helper Classes

        private class AnswerDataLoader<T>
        {
            private readonly IRepository<SurveyAnswer> _answerRepository;
            private readonly IMapper _mapper;

            public AnswerDataLoader(IRepository<SurveyAnswer> answerRepository, IMapper mapper)
            {
                _answerRepository = answerRepository;
                _mapper = mapper;
            }

            public async Task<List<T>> LoadAnswersByQuestionIds(List<string> questionIds)
            {
                try
                {
                    var options = await _answerRepository.AsQueryable()
                        .Where(a => a.SurveyQuestionId != null && questionIds.Contains(a.SurveyQuestionId))
                        .OrderBy(a => a.DisplayOrder)
                        .ToListAsync();

                    return _mapper.Map<List<T>>(options);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error loading answers: " + ex.Message);
                    return new List<T>();
                }
            }
        }

        private class AnswerProcessor
        {
            private readonly IRepository<SurveyAnswer> _answerRepository;

            public AnswerProcessor(IRepository<SurveyAnswer> answerRepository)
            {
                _answerRepository = answerRepository;
            }

            public async Task<int> DeleteAnswersByQuestionIds(List<string> questionIds, IUnitOfWork unitOfWork)
            {
                var answersToDelete = await _answerRepository.AsQueryable()
                    .Where(a => a.SurveyQuestionId != null && questionIds.Contains(a.SurveyQuestionId))
                    .ToListAsync();

                if (!answersToDelete.Any())
                    return 0;

                _answerRepository.DeleteRange(answersToDelete);
                return await unitOfWork.SaveChangesAsync();
            }

            public async Task<int> ProcessAnswers(
                Dictionary<string, List<SurveyAnswerDTO>> answersByQuestion,
                IUnitOfWork unitOfWork)
            {
                var allNewAnswers = new List<SurveyAnswer>();
                var allUpdatedAnswers = new List<SurveyAnswer>();

                // Get all existing answers for these questions in one query
                var questionIds = answersByQuestion.Keys.ToList();
                var existingAnswersMap = await LoadExistingAnswersMap(questionIds);

                foreach (var questionId in questionIds)
                {
                    var answersForQuestion = await ProcessAnswersForQuestion(
                        questionId,
                        answersByQuestion[questionId],
                        existingAnswersMap.GetValueOrDefault(questionId, new Dictionary<string, SurveyAnswer>()));

                    allNewAnswers.AddRange(answersForQuestion.NewAnswers);
                    allUpdatedAnswers.AddRange(answersForQuestion.UpdatedAnswers);
                }

                // Perform batch operations
                if (allNewAnswers.Any())
                    _answerRepository.AddRange(allNewAnswers);

                if (allUpdatedAnswers.Any())
                    _answerRepository.UpdateRange(allUpdatedAnswers);

                return await unitOfWork.SaveChangesAsync();
            }

            private async Task<Dictionary<string, Dictionary<string, SurveyAnswer>>> LoadExistingAnswersMap(List<string> questionIds)
            {
                return await _answerRepository.AsQueryable()
                    .Where(a => a.SurveyQuestionId != null && questionIds.Contains(a.SurveyQuestionId))
                    .GroupBy(a => a.SurveyQuestionId)
                    .ToDictionaryAsync(
                        g => g.Key!,
                        g => g.ToDictionary(a => a.Id, a => a)
                    );
            }

            private async Task<(List<SurveyAnswer> NewAnswers, List<SurveyAnswer> UpdatedAnswers)> ProcessAnswersForQuestion(
                string questionId,
                List<SurveyAnswerDTO> questionAnswers,
                Dictionary<string, SurveyAnswer> existingAnswers)
            {
                var newAnswers = new List<SurveyAnswer>();
                var updatedAnswers = new List<SurveyAnswer>();
                var idsToKeep = new HashSet<string>();

                foreach (var answerDto in questionAnswers)
                {
                    bool isNewAnswer = string.IsNullOrEmpty(answerDto.Id) || answerDto.Id.StartsWith("temp_");

                    if (isNewAnswer)
                    {
                        // New answer
                        var newAnswer = new SurveyAnswer
                        {
                            Id = Guid.NewGuid().ToString("N"),
                            SurveyQuestionId = questionId,
                            Key = answerDto.Key,
                            Value = answerDto.Value,
                            IsInput = answerDto.IsInput,
                            DisplayOrder = answerDto.DisplayOrder
                        };

                        newAnswers.Add(newAnswer);
                        idsToKeep.Add(newAnswer.Id);
                    }
                    else
                    {
                        idsToKeep.Add(answerDto.Id);

                        if (existingAnswers.TryGetValue(answerDto.Id, out var existingAnswer))
                        {
                            // Update existing answer
                            existingAnswer.Key = answerDto.Key;
                            existingAnswer.Value = answerDto.Value;
                            existingAnswer.IsInput = answerDto.IsInput;
                            existingAnswer.DisplayOrder = answerDto.DisplayOrder;

                            updatedAnswers.Add(existingAnswer);
                        }
                        else
                        {
                            // Answer ID not found, create a new one
                            newAnswers.Add(new SurveyAnswer
                            {
                                Id = Guid.NewGuid().ToString("N"),
                                SurveyQuestionId = questionId,
                                Key = answerDto.Key,
                                Value = answerDto.Value,
                                IsInput = answerDto.IsInput,
                                DisplayOrder = answerDto.DisplayOrder
                            });
                        }
                    }
                }

                // Delete answers that weren't in the update
                var answersToDelete = existingAnswers.Values
                    .Where(a => !idsToKeep.Contains(a.Id))
                    .ToList();

                if (answersToDelete.Any())
                {
                    _answerRepository.DeleteRange(answersToDelete);
                }

                return (newAnswers, updatedAnswers);
            }
        }

        #endregion
    }
}
