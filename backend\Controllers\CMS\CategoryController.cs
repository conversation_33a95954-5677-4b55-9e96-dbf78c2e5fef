﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Services.Categories;
using MiniAppCore.Services.SystemSettings;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class CategoryController(ICategoryService categoryService, ISystemSettingService systemSettingService) : Controller
    {


        public async Task<IActionResult> Index()
        {
            var categories = await categoryService.GetAllAsync();
            var categoryIds = (await systemSettingService.GetCategoryHomeAsync()).Select(x => x.Id).ToList();

            return View((categoryIds, categories));
        }

        [HttpGet("Category/PullCategory")]
        public IActionResult PullCategory()
        {
            return PartialView("_FormPullCategory");
        }

        [HttpGet("Category/Create")]
        public IActionResult Create()
        {
            var category = new Category()
            {
                IsGlobal = true,
                Id = string.Empty,
                Name = string.Empty,
                Type = string.Empty,
                Description = string.Empty,
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới danh mục";
            var listCategoryChild = new List<CategoryChild>();
            return PartialView("_Category", (category, listCategoryChild));
        }

        [HttpGet("Category/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            var result = await categoryService.GetByIdAsync(id);
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật danh mục";
            var listCategoryChild = await categoryService.GetListCategoryChild(id);
            return PartialView("_Category", (result, listCategoryChild));
        }
    }
}
