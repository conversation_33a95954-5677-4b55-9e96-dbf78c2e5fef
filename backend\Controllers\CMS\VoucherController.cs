﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Models.Responses.Vouchers;
using MiniAppCore.Services.Offers.Vouchers;
using MiniAppCore.Services.Products;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class VoucherController(IVoucherService voucherService, IProductService productService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("Voucher/Gift")]
        public async Task<IActionResult> FormGiftVoucher()
        {
            return PartialView("_GiftVoucher");
        }

        [HttpGet("Voucher/Create")]
        public async Task<IActionResult> Create()
        {
            var voucher = new VoucherDetailResponse()
            {
                Id = string.Empty,
                Code = string.Empty,
                Name = string.Empty,
                Description = string.Empty,
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(1),
                ExpiryDate = DateTime.Now.AddDays(2),
                IsAllProducts = true,
            };

            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới voucher";
            ViewBag.Products = await productService.GetAllAsync();
            return PartialView("_Voucher", voucher);
        }

        [HttpGet("Voucher/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Chi tiết voucher";
            var voucher = await voucherService.GetVoucherDetailResponseByIdAsync(id);
            ViewBag.Products = await productService.GetAllAsync();
            return PartialView("_Voucher", voucher);
        }

        [HttpGet("Voucher/ImportTemplateExcel")]
        public async Task<IActionResult> ImportTemplateExcel()
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("VoucherTemplate");

            // Tạo header
            worksheet.Cells[1, 1].Value = "Name";
            worksheet.Cells[1, 2].Value = "Code";
            worksheet.Cells[1, 3].Value = "Description";
            worksheet.Cells[1, 4].Value = "DiscountValue";
            worksheet.Cells[1, 5].Value = "PointRequired";
            worksheet.Cells[1, 6].Value = "StartDate (dd/MM/yyyy)";
            worksheet.Cells[1, 7].Value = "EndDate (dd/MM/yyyy)";
            worksheet.Cells[1, 8].Value = "ExpiryDate (dd/MM/yyyy)";
            worksheet.Cells[1, 9].Value = "DiscountType (Percentage | FixedAmount)";
            worksheet.Cells[1, 10].Value = "Quantity";
            worksheet.Cells[1, 11].Value = "ExchangeTimes";

            // Định dạng header
            using (var range = worksheet.Cells[1, 1, 1, 11])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }

            // Dòng mẫu (optional)
            worksheet.Cells[2, 1].Value = "Voucher Sinh Nhật";
            worksheet.Cells[2, 2].Value = "SN2025";
            worksheet.Cells[2, 3].Value = "Giảm 50% cho đơn hàng sinh nhật";
            worksheet.Cells[2, 4].Value = 50;
            worksheet.Cells[2, 5].Value = 100;
            worksheet.Cells[2, 6].Value = DateTime.Now.ToString("dd/MM/yyyy");
            worksheet.Cells[2, 7].Value = DateTime.Now.AddDays(7).ToString("dd/MM/yyyy");
            worksheet.Cells[2, 8].Value = DateTime.Now.AddDays(10).ToString("dd/MM/yyyy");
            worksheet.Cells[2, 9].Value = "Percentage";
            worksheet.Cells[2, 10].Value = 10;
            worksheet.Cells[2, 11].Value = 1;

            // Dòng mẫu 2
            worksheet.Cells[3, 1].Value = "Voucher Giảm Tiền Mặt";
            worksheet.Cells[3, 2].Value = "CASH100";
            worksheet.Cells[3, 3].Value = "Giảm 100.000 cho đơn từ 500K";
            worksheet.Cells[3, 4].Value = 100000;
            worksheet.Cells[3, 5].Value = 200;
            worksheet.Cells[3, 6].Value = DateTime.Now.ToString("dd/MM/yyyy");
            worksheet.Cells[3, 7].Value = DateTime.Now.AddDays(5).ToString("dd/MM/yyyy");
            worksheet.Cells[3, 8].Value = DateTime.Now.AddDays(8).ToString("dd/MM/yyyy");
            worksheet.Cells[3, 9].Value = "FixedAmount";
            worksheet.Cells[3, 10].Value = 5;
            worksheet.Cells[3, 11].Value = 2;

            worksheet.Cells.AutoFitColumns();
            worksheet.Cells[1, 1, 3, 11].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            var stream = new MemoryStream();
            await package.SaveAsAsync(stream);
            stream.Position = 0;

            var fileName = $"Import_Voucher_Template_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
            var contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

            return File(stream, contentType, fileName);
        }

    }
}
