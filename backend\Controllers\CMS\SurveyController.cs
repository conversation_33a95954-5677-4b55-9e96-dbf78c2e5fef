﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Services.Surveys;
using MiniAppCore.Services.Surveys.UserSurveys;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class SurveyController(ISurveyService surveyService, ISurveySubmissionService submissionService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        public IActionResult History()
        {
            return View();
        }

        public IActionResult Create()
        {
            // Khởi tạo mẫu khảo sát trống
            var model = new SurveyDTO
            {
                Id = string.Empty,
                Status = (short)Enums.ESurvey.Opening,
                StartedDate = DateTime.Now,
                EndDate = DateTime.Now.AddDays(7),
                DisplayOrder = 1,
                IsDisplay = true,
                Sections = new List<SurveySectionDTO>()
            };

            return View("_Survey", model);
        }

        public async Task<IActionResult> Detail(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return RedirectToAction("Index");
            }

            var survey = await surveyService.GetSurveyDTOByIdAsync(id);
            if (survey == null)
            {
                return RedirectToAction("Create");
            }

            ViewData["Title"] = "Chỉnh sửa khảo sát";
            return View("_Survey", survey);
        }

        public async Task<IActionResult> SubmissionDetail(string id)
        {
            var surveyResult = await submissionService.GetSurveyResultAsync(id);
            return PartialView("_SurveySubmission", surveyResult);
        }

        #region Components 

        // Partial Views Actions
        [HttpGet]
        public IActionResult SectionPartial(string? id = null, string? surveyId = null, string? title = null, byte displayOrder = 1)
        {
            var section = new SurveySectionDTO
            {
                Id = id ?? string.Empty,
                SurveyId = surveyId,
                TitleSection = title ?? "Phần mới",
                DisplayOrder = displayOrder,
                Questions = new List<SurveyQuestionDTO>()
            };

            return PartialView("_SectionPartial", section);
        }

        [HttpGet]
        public IActionResult QuestionPartial(string? id = null, string? sectionId = null, string type = "paragraph", string title = "", bool isRequired = false, short displayOrder = 1)
        {
            var question = new SurveyQuestionDTO
            {
                Id = id ?? "temp_question_" + DateTime.Now.Ticks,
                SectionId = sectionId,
                Type = type,
                QuestionTitle = title,
                IsRequired = isRequired,
                DisplayOrder = displayOrder,
                Answers = new List<SurveyAnswerDTO>()
            };

            // Khởi tạo các câu trả lời mặc định dựa trên loại câu hỏi
            if (type == "multiChoice" || type == "singleChoice" || type == "dropDown")
            {
                var timestamp = DateTime.Now.Ticks;
                question.Answers.Add(new SurveyAnswerDTO
                {
                    Id = "temp_answer_" + timestamp,
                    QuestionId = question.Id,
                    Key = "option_1",
                    Value = "Lựa chọn 1",
                    IsInput = false,
                    DisplayOrder = 1
                });
            }
            else if (type == "likert")
            {
                // Likert không có answers trực tiếp, sẽ xử lý ở client
            }

            return PartialView("_QuestionPartial", question);
        }

        [HttpGet]
        public IActionResult OptionPartial(string questionId, string? id = null, string value = "Lựa chọn mới", bool isInput = false, short displayOrder = 1)
        {
            var option = new SurveyAnswerDTO
            {
                Id = id ?? "temp_option_" + DateTime.Now.Ticks,
                QuestionId = questionId,
                Key = $"option_{displayOrder}",
                Value = value,
                IsInput = isInput,
                DisplayOrder = displayOrder
            };

            return PartialView("_AnswerPartial", option);
        }

        [HttpGet]
        public IActionResult EditQuestionModal(string id)
        {
            // Chỉ trả về template, thông tin sẽ được điền bởi JS
            return PartialView("_EditQuestionModal");
        }

        #endregion
    }
}
