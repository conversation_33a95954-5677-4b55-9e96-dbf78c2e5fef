﻿@model MiniAppCore.Entities.LuckyWheels.GamePrize
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Tên Giải Thưởng <span style="color:red">*</span></label>
                            <input id="name" type="text" class="form-control" value="@Model?.Name" placeholder="Tên giải thưởng..." data-error-message="Vui lòng nhập tên giải thưởng." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Giá trị giải thưởng <span style="color:red">*</span></label>
                            <input id="value" type="text" class="form-control" value="@Model?.Value" placeholder="Nhập giá trị..." data-error-message="Vui lòng nhập giá trị." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Mô tả <span style="color:red">*</span></label>
                            <textarea id="description" class="form-control" rows="4" placeholder="Mô tả giải thưởng..." data-error-message="Vui lòng nhập mô tả."
                                required>@Model?.Description</textarea>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Loại phần thưởng <span style="color:red">*</span></label>
                            <select id="prizeType" class="form-control">
                                <option value="0" selected="@(Model?.Type == MiniAppCore.Enums.EPrizeType.None)">Không trúng thưởng</option>
                                <option value="1" selected="@(Model?.Type == MiniAppCore.Enums.EPrizeType.Voucher)">Voucher</option>
                                <option value="2" selected="@(Model?.Type == MiniAppCore.Enums.EPrizeType.Product)">Sản phẩm</option>
                                <option value="3" selected="@(Model?.Type == MiniAppCore.Enums.EPrizeType.Cash)">Tiền mặt</option>
                                <option value="4" selected="@(Model?.Type == MiniAppCore.Enums.EPrizeType.Point)">Điểm</option>
                                <option value="99" selected="@(Model?.Type == MiniAppCore.Enums.EPrizeType.Other)">Khác</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12" id="voucherSection" style="display: @(Model?.Type == MiniAppCore.Enums.EPrizeType.Voucher ? "block" : "none")">
                        <div class="form-group">
                            <label>Voucher <span style="color:red">*</span></label>
                            <select id="vouchers" class="selectpicker form-control">
                                <option value="">--- Chọn voucher --- </option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Thông tin bổ sung</label>
                            <textarea id="metadata" class="form-control" rows="2" placeholder="Thông tin bổ sung...">@Model?.Metadata</textarea>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Hình Ảnh <span style="color:red">*</span></label>
                            <input type="file" id="image" class="form-control image-file" name="image" accept="image/*" data-error-message="Vui lòng tải lên hình ảnh."
                                @(string.IsNullOrEmpty(Model?.Image) ? "required" : "")>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div id="preview" class="row d-flex flex-nowrap" style="overflow-x: scroll; min-height: 250px;">
                            @if (!string.IsNullOrEmpty(Model?.Image))
                            {
                                <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                    <img src="@Model.Image" class="card-img-top h-100" alt="Hình ảnh" style="object-fit:contain" />
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (string.IsNullOrEmpty(Model?.Id))
        {
            <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
        }
        <button type="button" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model?.Id')">@ViewBag.Button</button>
    </div>
</div>
<script>
    $(document).ready(() => {
        $("#image").on("change", ShowPreview);
        GetVouchers('#vouchers', '@Model?.ReferenceId');
    });
</script>
