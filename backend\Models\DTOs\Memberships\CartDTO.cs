﻿namespace MiniAppCore.Models.DTOs
{
    public class CartDTO
    {
        public string? ProductId { get; set; }
        public string? UserId { get; set; }
        public string? ProductName { get; set; }
        public string? Images { get; set; }
        public long Quantity { get; set; }
        public decimal Price { get; set; }

        public string? Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;

    }
}
