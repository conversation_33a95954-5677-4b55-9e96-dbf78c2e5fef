<script>
    // Entity Selector Scripts
    $(document).ready(function () {
        initializeEntitySelector();
    });

    function initializeEntitySelector() {
        const entitySelector = $('#entitySelector');
        const configureBtn = $('#configureEntityBtn');
        const viewBtn = $('#viewEntityBtn');
        const entityStats = $('#entityStats');

        entitySelector.on('change', function () {
            const selectedEntity = $(this).val();
            updateEntityButtons(selectedEntity);

            if (selectedEntity) {
                loadEntityData(selectedEntity);
            } else {
                clearEntityData();
            }
        });

        configureBtn.on('click', function () {
            const selectedEntity = entitySelector.val();
            if (selectedEntity) {
                openEntityConfiguration(selectedEntity);
            }
        });

        viewBtn.on('click', function () {
            scrollToEntityOverview();
        });

        // Auto-select if there's a pre-selected entity
        const initialEntity = entitySelector.val();
        if (initialEntity) {
            updateEntityButtons(initialEntity);
            loadEntityData(initialEntity);
        }
    }

    function updateEntityButtons(selectedEntity) {
        const hasEntity = selectedEntity && selectedEntity.length > 0;
        $('#configureEntityBtn').prop('disabled', !hasEntity);
        $('#viewEntityBtn').prop('disabled', !hasEntity);
        $('#entityStats').fadeToggle(hasEntity);
    }

    function loadEntityData(entityName) {
        showLoadingState();

        // Use a single endpoint to get all necessary data (overview HTML and stats)
        $.get('@Url.Action("GetEntityData", "CustomEntity")', { entityName: entityName })
            .done(function (response) {
                if (response.success) {
                    $('#entityFieldsContainer').html(response.overviewHtml);
                    updateEntityStats(response.stats);
                } else {
                    showErrorState(response.message || 'Không thể tải dữ liệu entity.');
                }
            })
            .fail(function () {
                showErrorState('Lỗi khi tải thông tin fields. Vui lòng thử lại.');
            });
    }

    function showLoadingState() {
        $('#entityFieldsContainer').html(`
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Đang tải...</span>
                </div>
                <p class="mt-2 text-muted">Đang tải thông tin fields...</p>
            </div>
        `);
    }

    function showErrorState(message) {
        $('#entityFieldsContainer').html(`
            <div class="alert alert-warning text-center">
                <i class="ri-error-warning-line me-2"></i>${message}
            </div>
        `);
    }

    function updateEntityStats(stats) {
        if (!stats) return;
        $('#totalFields').text(stats.totalFields || 0);
        $('#requiredFields').text(stats.requiredFields || 0);
        $('#optionalFields').text(stats.optionalFields || 0);
        $('#lastUpdated').text(stats.lastUpdated || '-');
    }

    function clearEntityData() {
        $('#entityFieldsContainer').html(`
            <div class="text-center py-5" id="selectEntityPrompt">
                <div class="mb-3"><i class="ri-arrow-up-line text-muted" style="font-size: 3rem;"></i></div>
                <h5 class="text-muted">Chọn Entity ở trên để bắt đầu</h5>
                <p class="text-muted mb-0">Bạn sẽ thấy danh sách custom fields và các tùy chọn cấu hình tại đây.</p>
            </div>
        `);
        updateEntityStats(null); // Clear stats
    }

    function openEntityConfiguration(entityName) {
        $.get('@Url.Action("ConfigureEntity", "CustomEntity")', { entityName: entityName })
            .done(function (data) {
                $('#modalContent').html(data);
                $('#customFieldModal').modal('show');
            })
            .fail(function () {
                // Assuming you have a global alert function
                if (typeof AlertResponse === 'function') {
                    AlertResponse('Không thể tải form cấu hình. Vui lòng thử lại.', 'error');
                } else {
                    alert('Không thể tải form cấu hình. Vui lòng thử lại.');
                }
            });
    }

    function scrollToEntityOverview() {
        const container = $('#entityFieldsContainer');
        if (container.length) {
            $('html, body').animate({
                scrollTop: container.offset().top - 80 // Adjust offset for better positioning
            }, 500);
        }
    }
</script>
