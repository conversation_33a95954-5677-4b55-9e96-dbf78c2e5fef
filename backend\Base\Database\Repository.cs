﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Interface;

namespace MiniAppCore.Base.Database
{
    public class Repository<T>(ApplicationDbContext context) : IRepository<T> where T : class
    {
        #region "READ"

        public IQueryable<T> AsQueryable()
        {
            return context.Set<T>().AsQueryable();
        }

        public async Task<T?> FindByIdAsync(string id)
        {
            return await context.Set<T>().FindAsync(id);
        }

        public async Task<IEnumerable<T>> FindByIdsAsync(IEnumerable<string> ids)
        {
            if (ids == null || !ids.Any())
            {
                return Enumerable.Empty<T>();
            }

            return await context.Set<T>()
                .AsNoTracking()
                .Where(e => ids.Contains(EF.Property<string>(e, "Id")))
                .ToListAsync();
        }

        #endregion

        #region "CREATE"

        public void Add(T entity)
        {
            context.Set<T>().Add(entity);
            //return context.SaveChangesAsync();
        }

        public void AddRange(IEnumerable<T> entities)
        {
            context.Set<T>().AddRange(entities);
            //return await context.SaveChangesAsync();
        }

        #endregion

        #region "UPDATE"

        public void Update(T entity)
        {
            context.Set<T>().Update(entity);
            //return await context.SaveChangesAsync();
        }

        public void UpdateRange(IEnumerable<T> entities)
        {
            context.Set<T>().UpdateRange(entities);
            //return await context.SaveChangesAsync();
        }
        #endregion

        #region "DELETE"

        public void Delete(T entity)
        {
            context.Set<T>().Remove(entity);
            //await context.SaveChangesAsync();
        }

        public void DeleteRange(IEnumerable<T> entities)
        {
            context.Set<T>().RemoveRange(entities);
            //await context.SaveChangesAsync();
        }

        public async Task DeleteByIdAsync(string id)
        {
            var item = await FindByIdAsync(id);
            if (item != null)
            {
                context.Set<T>().Remove(item);
            }
            //await context.SaveChangesAsync();
        }

        #endregion
    }
}
