﻿@using MiniAppCore.Entities.Commons
@using MiniAppCore.Entities.Memberships
@using System.Text.Json
@using Newtonsoft.Json
@model (Membership membership, List<MembershipExtendDefault> lstMbExDf, List<MembershipExtend> lstMbEx, List<string> selectedTags )
@{
    var tags = (List<Tag>)ViewBag.Tags;
    var addresses = ViewBag.Addresses as List<MembershipAddress>;
    var listVAT = ViewBag.MembershipVAT as List<MembershipVAT>;
}

<div class="modal-content modal-xl">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <form>
            <div class="row">
                <input type="hidden" value="@Model.membership.UserZaloId" id="UserZaloId" />

                <!-- THÔNG TIN CƠ BẢN -->
                <div class="col-md-12 mb-3">
                    <h5 class="border-bottom pb-2">Thông tin cơ bản</h5>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Tên <span style="color:red">*</span></label>
                                <input id="displayName" type="text" class="form-control" placeholder="Nhập tên" value="@Model.membership.UserZaloName" required />
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Số điện thoại <span style="color:red">*</span></label>
                                <input id="phoneNumber" type="text" class="form-control" placeholder="Nhập số điện thoại" value="@Model.membership.PhoneNumber" required />
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Sinh nhật</label>
                                <input id="dob" type="date" class="form-control" required value="@Model.membership.DateOfBirth?.ToString("yyyy-MM-dd")" />
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Nghề nghiệp</label>
                                <input id="job" type="text" class="form-control" placeholder="Nhập nghề nghiệp" value="@Model.membership.Job" required />
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Hạng</label>
                                <select class="form-control" id="rank" disabled>
                                    @if (ViewBag.Ranks != null)
                                    {
                                        foreach (MiniAppCore.Entities.Settings.Rank rank in ViewBag.Ranks)
                                        {
                                            if (Model.membership.RankingId == rank.Id)
                                            {
                                                <option value="@rank.Id" selected>@rank.Name</option>
                                            }
                                            else
                                            {
                                                <option value="@rank.Id">@rank.Name</option>
                                            }
                                        }
                                    }
                                    else
                                    {
                                        <option value="">Không có dữ liệu</option>
                                    }
                                </select>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- THÔNG TIN ĐIỂM VÀ TÀI KHOẢN -->
                <div class="col-md-12 mb-3">
                    <h5 class="border-bottom pb-2">Thông tin điểm và tài khoản</h5>
                    <div class="row">
                        <div class="col-md-5">
                            <div class="form-group">
                                <label>Điểm vòng quay</label>
                                <input id="spinPoint"
                                       type="text"
                                       class="form-control"
                                       placeholder="Nhập điểm vòng quay"
                                       value="@Model.membership.SpinPoint.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN"))"
                                       oninput="InputValidator.currency(this)"
                                       min="0"
                                       required />
                            </div>

                            <div class="form-group">
                                <label>Điểm tích lũy</label>
                                <input id="rankingPoint"
                                       type="text"
                                       class="form-control"
                                       placeholder="Nhập điểm tích lũy"
                                       value="@Model.membership.RankingPoint.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN"))"
                                       oninput="InputValidator.currency(this)"
                                       min="0"
                                       required />
                            </div>

                            <div class="form-group">
                                <label>Điểm tiêu dùng</label>
                                <input id="usingPoint"
                                       type="text"
                                       class="form-control"
                                       placeholder="Nhập điểm tiêu dùng"
                                       value="@Model.membership.UsingPoint.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN"))"
                                       oninput="InputValidator.currency(this)"
                                       min="0"
                                       required />
                            </div>
                        </div>

                        <div class="col-md-5">
                            <div class="form-group" hidden>
                                <label>Hoa hồng</label>
                                <input id="commissionTotal" type="text" class="form-control" value="@ViewBag.CommissionTotal" disabled />
                            </div>
                            <div class="form-group">
                                <label>Mã giới thiệu</label>
                                <input id="referralCode" type="text" class="form-control" value="@Model.membership.ReferralCode" disabled />
                            </div>
                            <div class="form-group">
                                <label>Liên kết giới thiệu</label>
                                <input id="link" type="text" class="form-control" value="@ViewBag.ZaloUrl" disabled />
                            </div>
                        </div>

                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Mã QR</label>
                                <div id="qrcode">
                                    @if (!string.IsNullOrEmpty(ViewBag.QrCodeBase64))
                                    {
                                        <img src="data:image/png;base64,@ViewBag.QrCodeBase64" alt="Mã QR" style="width: 150px; height: 150px;" />
                                    }
                                    else
                                    {
                                        <p>Chưa có mã QR</p>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- THÔNG TIN PHÂN LOẠI -->
                <div class="col-md-12 mb-3">
                    <h5 class="border-bottom pb-2">Thông tin phân loại</h5>
                    <div class="form-group">
                        <label>Thẻ phân loại </label>
                        <select id="tags" class="form-control" multiple>
                            @foreach (var item in tags)
                            {
                                <option value="@item.Id" selected="@(Model.selectedTags.Contains(item.Id))">@item.Name</option>
                            }
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Ghi chú</label>
                        <input id="note" type="text" class="form-control" placeholder="Nhập ghi chú" value="" />
                    </div>
                </div>

                <!-- THÔNG TIN ĐỊA CHỈ -->
                <div class="col-md-12 mb-3">
                    <h5 class="border-bottom pb-2">Thông tin địa chỉ</h5>
                    <div class="form-group">
                        <div class="row">
                            @if (addresses != null && addresses.Any())
                            {
                                @foreach (var address in addresses)
                                {
                                    <div class="col-md-6 col-lg-4 mb-3 mt-2">
                                        <div class="card shadow-sm">
                                            <div class="card-body">
                                                <h5 class="card-title">@address.Name | @address.PhoneNumber</h5>
                                                <p class="card-text">
                                                    @address.FullAddress
                                                </p>
                                                @if (!string.IsNullOrEmpty(address.Note))
                                                {
                                                    <p class="text-muted"><strong>Ghi chú:</strong> @address.Note</p>
                                                }
                                                @if (address.IsDefault)
                                                {
                                                    <span class="badge bg-success">Mặc định</span>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="col-12">
                                    <p class="text-muted text-center">Chưa có địa chỉ nào.</p>
                                </div>
                            }
                        </div>
                    </div>
                </div>


                <!-- THÔNG TIN HÓA ĐƠN -->
                <div class="col-md-12 mb-3">
                    <h5 class="border-bottom pb-2">Thông tin hóa đơn</h5>
                    <div class="row">
                        <!-- CỘT DOANH NGHIỆP -->
                        <div class="col-md-6 mt-2">
                            <h6 class="mb-3">
                                Doanh nghiệp
                            </h6>
                            @if (listVAT != null && listVAT.Any(v => v.Type == "Company"))
                            {
                                @foreach (var vat in listVAT.Where(v => v.Type == "Company"))
                                {
                                    <div class="mb-3">
                                        <div class="border rounded shadow-sm p-3 h-100 bg-white">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="fw-bold">@vat.OwnerName</span>
                                                @if (vat.IsDefault)
                                                {
                                                    <span class="badge bg-success">Mặc định</span>
                                                }
                                            </div>
                                            <div class="mb-1">
                                                <span class="text-muted fw-semibold">Mã số thuế:</span>
                                                <span>@vat.TaxCode</span>
                                            </div>
                                            <div class="mb-1">
                                                <span class="text-muted fw-semibold">Địa chỉ:</span>
                                                <span>@vat.FullAddress</span>
                                            </div>
                                            <div class="mb-1">
                                                <span class="text-muted fw-semibold">Email nhận hóa đơn:</span>
                                                <span>@vat.Email</span>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-muted text-center mb-3">Chưa có hóa đơn doanh nghiệp.</div>
                            }
                        </div>
                        <!-- CỘT CÁ NHÂN -->
                        <div class="col-md-6 mt-2">
                            <h6 class="mb-3">
                                Cá nhân
                            </h6>
                            @if (listVAT != null && listVAT.Any(v => v.Type == "Personal"))
                            {
                                @foreach (var vat in listVAT.Where(v => v.Type == "Personal"))
                                {
                                    <div class="mb-3">
                                        <div class="border rounded shadow-sm p-3 h-100 bg-white">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="fw-bold">@vat.OwnerName</span>
                                                @if (vat.IsDefault)
                                                {
                                                    <span class="badge bg-success">Mặc định</span>
                                                }
                                            </div>
                                            <div class="mb-1">
                                                <span class="text-muted fw-semibold">Mã số thuế:</span>
                                                <span>@vat.TaxCode</span>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-muted text-center mb-3">Chưa có hóa đơn cá nhân.</div>
                            }
                        </div>
                    </div>
                </div>

                <!-- THÔNG TIN MỞ RỘNG -->
                <div class="col-md-12 mb-3">
                    <h5 class="border-bottom pb-2">Thông tin mở rộng</h5>
                    <div class="form-group">
                        <label>Thông tin mặc định</label>
                    </div>
                    <div id="membership-extend-default-div">
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <label>Thông tin tùy chọn</label>
                        <div class="input-group">
                            <input id="optionValue" type="text" class="form-control" placeholder="Nhập tên trường">
                            <button type="button" id="addOptionButton" class="btn btn-success" onclick="addOption()">Thêm</button>
                        </div>
                    </div>
                    <div id="membership-extend-div">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (string.IsNullOrEmpty(Model.membership.Id))
        {
            <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
        }
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.membership.Id')">@ViewBag.Button</button>
    </div>
</div>

<script>
    var lstMbEx = @Html.Raw(JsonConvert.SerializeObject(Model.lstMbEx))
        $(document).ready(function () {
            $("#tags").select2();
            renderMembershipExtend();
        });

    var renderMembershipExtend = () => {
        $("#membership-extend-div").html("");
        if (lstMbEx?.length > 0) {
            $("#membership-extend-div").append(`
            <div class="col-md-12">
                <div class="row">
                    <div class="col-md-4">
                        <strong>Trường dữ liệu</strong>
                    </div>
                    <div class="col-md-6">
                        <strong>Giá trị dữ liệu</strong>
                    </div>
                    <div class="col-md-2">
                        <strong>Thao tác</strong>
                    </div>
                </div>
            </div>
            `);

            lstMbEx.map((item, index) => {
                $("#membership-extend-div").append(`
                 <div id="membership-extend-${item.Id}" class="row" style="padding:5px">
                    <input id="Id_${item.Id}" value="${item.Id}" hidden/>
                    <input id="UserZaloId_${item.Id}" value="${item.UserZaloId}" hidden/>
                    <input id="CreatedDate_${item.Id}" value="${item.CreatedDate}" hidden/>
                    <input id="UpdatedDate_${item.Id}" value="${item.UpdatedDate}" hidden/>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="Attribute_${item.Id}" value="${item.Attribute}" readonly />
                    </div>
                    <div class="col-md-6">
                        <input id="Content_${item.Id}" type="text" class="form-control" value="${item.Content}" placeholder="Vui lòng nhập giá trị" required />
                    </div>
                    <div class="col-md-2">
                        <a onclick="removeOption('${item.Id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                            <i class="ri-delete-bin-line fs-6 mr-0"></i>
                        </a>
                    </div>
                 </div>`)
            })
        }
        else {
            $("#membership-extend-div").append(`
             <div class="row" style="margin-top:5px">
                <div class="col-12">
                    <p class="text-muted text-center">Chưa có thông tin tùy chọn nào.</p>
                </div>
             </div>`)
        }
    }

    function addOption() {
        if (!$("#optionValue").val()) {
            return;
        }

        var obj = {
            Id: lstMbEx.length + "_1",
            Attribute: removeVietnameseAndSpace($("#optionValue").val()),
            Content: "",
            UserZaloId: $("#UserZaloId").val(),
            CreatedDate: moment().format(),
            UpdatedDate: moment().format(),
        }
        lstMbEx.push(obj);
        renderMembershipExtend()
        $("#optionValue").val("")
    }

    function removeOption(id) {
        lstMbEx = lstMbEx.filter(item => item.Id != id);
        renderMembershipExtend()
    }
</script>