﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Enums;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Carts;
using MiniAppCore.Models.Requests.Orders;
using MiniAppCore.Models.Responses.Orders;
using MiniAppCore.Models.Responses.Orders.Carts;

namespace MiniAppCore.Services.Orders
{
    public interface IOrderService : IService<Order>
    {
        Task<int> CancelOrder(string id, string? reason);
        Task<OrderResponse> CreateAsync(AdminCreateOrderRequset dto, bool? isSave);
        Task<OrderResponse> PlacedOrder(string userZaloId, OrderRequest dto, bool? isSave);

        Task<OrderDetailResponse> GetOrderByIdAsync(string orderId);
        Task<IEnumerable<OrderDetail>> GetOrderItemsByOrderId(string orderId);
        Task<IEnumerable<CartItemResponse>> GetBuyProductInfo(List<CartItemRequest> cartItemRequest);
        Task<PagedResult<OrderResponse>> GetPage(PurchaseQueryParams queryParams, string? userZaloId);

        Task<int> QuickUpdate(QuickUpdateRequest model);
        Task<int> UpdateAsync(string id, OrderRequest dto);
        Task<int> UpdateZaloOrderId(string id, string zaloOrderId);
        Task<Order> UpdatePaymentChannel(string id, string channel);
        Task<Order> UpdateOrderDetails(string id, OrderRequest dto, bool? isSave);
        Task<Order> UpdateZaloOrderStatus(string zaloOrderId, int method, string channel);
        Task<int> UpdateStatusAsync(string id, string? paymentMethod, EOrder? orderStatus = null, EPayment? paymentStatus = null);

        Task<byte[]> ExportOrders(DateTime? startDate, DateTime? endDate, short? orderStatus, short? paymentStatus);
    }
}