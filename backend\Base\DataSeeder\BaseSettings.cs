﻿using MiniAppCore.Base.Database;
using MiniAppCore.Models.DTOs.SystemSettings;
using Newtonsoft.Json;

namespace MiniAppCore.Base.DataSeeder
{
    public class BaseSettings
    {

        /*
        public static void Seed(ModelBuilder builder)
        {
            var featuresButton = new List<FeatureButtonDto>
            {
                new FeatureButtonDto { key = "ChatOAButton", value = true },
                new FeatureButtonDto { key = "BranchButton", value = true },
                new FeatureButtonDto { key = "HotLineButton", value = true },
                new FeatureButtonDto { key = "LuckyWheelButton", value = true },
                new FeatureButtonDto { key = "MembershipRankButton", value = true },
                new FeatureButtonDto { key = "BookingButton", value = true },
                new FeatureButtonDto { key = "SurveyButton", value = false },
                new FeatureButtonDto { key = "RattingButton", value = false }
            };

            var common = new Entities.Commons.Common
            {
                Name = "FeaturesButton",
                Content = JsonConvert.SerializeObject(featuresButton)
            };

            builder.Entity<Entities.Commons.Common>().HasData(common);
        }
        */

        public static async Task SeedAsync(ApplicationDbContext context)
        {
            const string name = "FeaturesButton";
            const string cmsRef = "CMSRef";

            if (!context.Commons.Any(c => c.Name == name))
            {
                var featuresButton = new List<FeatureButtonDto>
                {
                    new FeatureButtonDto { key = "ChatOAButton", value = true },
                    new FeatureButtonDto { key = "BranchButton", value = true },
                    new FeatureButtonDto { key = "HotLineButton", value = true },
                    new FeatureButtonDto { key = "LuckyWheelButton", value = true },
                    new FeatureButtonDto { key = "MembershipRankButton", value = true },
                    new FeatureButtonDto { key = "BookingButton", value = true },
                    new FeatureButtonDto { key = "SurveyButton", value = false },
                    new FeatureButtonDto { key = "RattingButton", value = false },
                    new FeatureButtonDto { key = "EventButton", value = false }
                };

                context.Commons.Add(new Entities.Commons.Common
                {
                    Name = name,
                    Content = JsonConvert.SerializeObject(featuresButton)
                });

                await context.SaveChangesAsync();
            }

            if (!context.Commons.Any(c => c.Name == cmsRef))
            {
                var featuresButton = new List<CMSRefDto>
                {
                    new CMSRefDto { key = "BrandName", value = "COMPANY_NAME" },
                    new CMSRefDto { key = "BrandLogo", value = "LOGO_IMAGE" },
                    new CMSRefDto { key = "BrandFavicon", value = "LOGO_IMAGE" },
                };

                context.Commons.Add(new Entities.Commons.Common
                {
                    Name = cmsRef,
                    Content = JsonConvert.SerializeObject(featuresButton)
                });

                await context.SaveChangesAsync();
            }
        }
    }
}
