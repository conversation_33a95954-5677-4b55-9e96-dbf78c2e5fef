﻿@using MiniAppCore.Models.DTOs.SystemSettings
@model (string linkPaymentGuide, OmniAccountDTO omniAccount, EnterpriseInfomationDTO enterpriseInfomation, OaInfoDTO oaInfo)
<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Tham số cài đặt hệ thống</h4>
                <p class="mb-0">
                    Danh sách các tham số cài đặt hệ thống sẽ giúp bạn dễ dàng thiết lập và quản lý các cài đặt quan trọng. <br>
                    Mỗi tham số có thể điều chỉnh các yếu tố của hệ thống để tối ưu hóa hiệu suất và đảm bảo hoạt động ổn định. <br>
                    H<PERSON><PERSON> sử dụng các thông tin này để cài đặt hệ thống một cách hiệu quả và phù hợp với yêu cầu của bạn.
                </p>
            </div>
        </div>
    </div>
    <div class="row col-lg-12">
        <!-- Account Omni -->
        <div class="col-12">
            <div class="shadow-none">
                <div class="modal-content shadow-lg p-3 mb-2 rounded border-none">
                    <div class="modal-body">
                        <div class="col-12 form-group">
                            <div class="shadow-none">
                                <div class="modal-content shadow-lg p-3 rounded border-none">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Thông tin tài khoản omni</h5>
                                    </div>
                                    <div class="modal-body">
                                        <div class="form-group">
                                            <label>Môi trường</label>
                                            <input type="text" class="form-control" data-default="@Model.omniAccount.EnvUrl" value="@Model.omniAccount.EnvUrl" id="envUrlInput" />
                                        </div>
                                        <div class="form-group">
                                            <label>Username</label>
                                            <input type="text" class="form-control" name="username" data-default="@Model.omniAccount.Username" value="@Model.omniAccount.Username" id="usernameInput" />
                                        </div>
                                        <div class="form-group">
                                            <label>Mật khẩu</label>
                                            <input type="password" class="form-control" name="password" data-default="@Model.omniAccount.Password" value="@Model.omniAccount.Password" id="passwordInput" />
                                        </div>
                                    </div>
                                    <div class="d-flex my-1 justify-content-end px-3">
                                        <button class="mx-1 btn btn-primary" onclick="HandleSaveChangeAccount()">Lưu thay đổi</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Oa Token -->
        <div class="col-12">
            <div class="shadow-none">
                <div class="modal-content shadow-lg p-3 mb-2 rounded border-none">
                    <div class="modal-body">
                        <div class="col-12 form-group">
                            <div class="shadow-none">
                                <div class="modal-content shadow-lg p-3 rounded border-none">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Thông tin Token OA</h5>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row mx-auto">
                                            <div class="col-6">
                                                <div class="form-group">
                                                    <label for="accessToken" id="enableEdit" style="cursor:pointer; user-select: none;">Access Token</label>
                                                    <input type="text" class="form-control" id="accessToken" value="@Model.oaInfo.AccessToken" placeholder="Nhập Access Token" disabled>
                                                </div>

                                                <div class="form-group">
                                                    <label for="refreshToken">Refresh token</label>
                                                    <input type="text" class="form-control" id="refreshToken" value="@Model.oaInfo.RefreshToken" placeholder="Nhập Refresh token" disabled>
                                                </div>

                                                <div class="row">
                                                    <button id="updateToken" class="btn btn-primary col-2 mx-2" disabled>Cập nhật token</button>
                                                    <button id="btnRefreshToken" class="btn btn-danger col-2" disabled>Refresh Token</button>
                                                </div>
                                            </div>

                                            <div class="col-6">
                                                <div class="form-group">
                                                    <label for="appId">AppId</label>
                                                    <input type="text" class="form-control" id="appId" placeholder="Nhập AppId" value="@Model.oaInfo.AppId">
                                                </div>

                                                <div class="form-group">
                                                    <label for="secretKey">Secret key</label>
                                                    <input type="text" class="form-control" id="secretKey" placeholder="Nhập Secret key" value="@Model.oaInfo.SecretKey">
                                                </div>

                                                <div class="form-group">
                                                    <button id="updateInfo" class="btn btn-primary">Cập nhật thông tin</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enterprise Information -->
        <div class="col-12" style="margin-bottom: 100px;">
            <div class="shadow-none">
                <div class="modal-content shadow-lg p-3 mb-5 rounded border-none">
                    <div class="modal-body">
                        <div class="col-12 form-group" style="margin-bottom: 100px;">
                            <div class="shadow-none">
                                <div class="modal-content shadow-lg p-3 rounded border-none">
                                    <div class="modal-header">
                                        <h5 class="modal-title">Thông tin doanh nghiệp</h5>
                                    </div>
                                    <div class="modal-body">
                                        <div class="form-group">
                                            <label>Link hướng dẫn thanh toán</label>
                                            <input type="text" class="form-control" data-default="@Model.enterpriseInfomation.PaymentInstruction" value="@Model.enterpriseInfomation.PaymentInstruction" id="linkPaymentGuide" />
                                        </div>

                                        <div class="form-group">
                                            <label>Thông tin đơn vị kinh doanh</label>
                                            <div id="businessInfoInput" class="rounded-bottom"
                                                 data-default="@Model.enterpriseInfomation.BusinessInformation"
                                                 style="height: 200px; overflow-y: auto;">
                                                @Html.Raw(Model.enterpriseInfomation.BusinessInformation)
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label>Chính sách hoa hồng</label>
                                            <div id="policyInput" class="rounded-bottom"
                                                 data-default="@Model.enterpriseInfomation.Policy"
                                                 style="height: 200px; overflow-y: auto;">
                                                @Html.Raw(Model.enterpriseInfomation.Policy)
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label>Mã doanh nghiệp</label>
                                            <input type="text" class="form-control"
                                                   data-default="@Model.enterpriseInfomation.BusinessCode"
                                                   value="@Model.enterpriseInfomation.BusinessCode"
                                                   id="businessCodeInput" />
                                        </div>

                                        <div class="form-group">
                                            <label>Địa chỉ</label>
                                            <input type="text" class="form-control"
                                                   data-default="@Model.enterpriseInfomation.Address"
                                                   value="@Model.enterpriseInfomation.Address"
                                                   id="addressInput" />
                                        </div>

                                        <div class="form-group">
                                            <label>Email</label>
                                            <input type="email" class="form-control"
                                                   data-default="@Model.enterpriseInfomation.Email"
                                                   value="@Model.enterpriseInfomation.Email"
                                                   id="emailInput" />
                                        </div>

                                        <div class="form-group">
                                            <label>Hotline</label>
                                            <input type="tel" class="form-control"
                                                   data-default="@Model.enterpriseInfomation.Hotline"
                                                   value="@Model.enterpriseInfomation.Hotline"
                                                   id="hotlineInput" />
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex my-1 justify-content-end px-3">
                        <button class="mx-1 btn btn-secondary" hidden>Hủy</button>
                        <button class="mx-1 btn btn-primary" onclick="HandleSaveChangeAbout()">Lưu thay đổi</button>
                    </div>
                </div>
            </div>
        </div>
    
    </div>
</div>

@section Scripts {
    <script>
	    const editors = {};
        $(document).ready(function () {
            InitialEditor("policyInput");
            InitialEditor("businessInfoInput");
            SetupInputChangeHandling();
        });

        function SetupInputChangeHandling() {

                $('.modal-body input').each(function () {
                    const $input = $(this);
                    $input.on('input', function () {
                        const $modal = $(this).closest('.modal-content');
                        const saveButton = $modal.find('.saveButton');
                        const cancelButton = $modal.find('.cancelButton');

                        saveButton.removeAttr('hidden');
                        cancelButton.removeAttr('hidden');
                    });
                });

                // Hủy và khôi phục giá trị mặc định
                $('.cancelButton').on('click', function () {
                    const $modal = $(this).closest('.modal-content');
                    $modal.find('input').each(function () {
                        const $input = $(this);
                        $input.val($input.attr('data-default'));
                        $input.trigger('input');
                    });

                    $modal.find('select').each(function () {
                        const $input = $(this);
                        $input.val($input.attr('data-default'));
                        $input.trigger('change');
                    });

                    const $policyInput = $modal.find('#policyInput .ql-editor');
                    const defaultContent = $modal.find('#policyInput').attr('data-default');
                    if (defaultContent) {
                        $policyInput.html(defaultContent);
                        $policyInput.trigger('input');
                    }

                    $modal.find('.saveButton, .cancelButton').attr('hidden', true);
                });
            }

        function InitialEditor(selector) {
             const quill = createQuillEditor(`#${selector}`);
             editors[selector] = quill;
        }

        function HandleSaveChangeAccount() {
                const envUrl = $('#envUrlInput').val()?.trim();
                const username = $('#usernameInput').val()?.trim();
                const password = $('#passwordInput').val()?.trim();

                if (!username || !password) {
                    AlertResponse("Vui lòng nhập đầy đủ thông tin tài khoản và mật khẩu.", "warning");
                    return;
                }

                const data = {
                    envUrl: envUrl,
                    username: username,
                    password: password
                };

                $.ajax({
                    url: '@Url.Action("CreateOrUpdateOmniAccount", "SystemSettings")',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function (response) {
                        if (response.code === 0) {
                            $('#usernameInput').attr('data-default', username);
                            $('#passwordInput').attr('data-default', password);

                            $('.cancelButton').attr('hidden', true);
                            $('.saveButton').attr('hidden', true);
                            AlertResponse(response.message, "success");
                        } else {
                            AlertResponse("Cập nhật thất bại! Vui lòng thử lại!", "warning");
                        }
                    },
                    error: function (xhr, status, error) {
                        AlertResponse("Đã xảy ra lỗi khi gọi API.", "error");
                    }
                });
            }

        function HandleSaveChangeAbout() {
                const data = {
                    email: $('#emailInput')?.val()?.trim(),
                    address: $('#addressInput')?.val()?.trim(),
                    hotline: $('#hotlineInput')?.val()?.trim(),
                    // phoneNumber: $('#phoneNumberInput')?.val()?.trim(),
                    businessCode: $('#businessCodeInput')?.val()?.trim(),
                    paymentInstruction: $('#linkPaymentGuide')?.val()?.trim(),
                    
                    policy: editors["policyInput"]?.root?.innerHTML?.trim(),
                    businessInformation: editors["businessInfoInput"]?.root?.innerHTML?.trim(),
                };

                $.ajax({
                    url: '@Url.Action("CreateOrUpdateEnterpriseInformation", "SystemSettings")',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(data),
                    success: function (response) {
                        if (response.code === 0) {
                            // Cập nhật lại giá trị data-default sau khi lưu thành công
                            $('#linkPaymentGuide').attr('data-default', data.paymentInstruction);
                            $('#businessCodeInput').attr('data-default', data.businessCode);
                            $('#addressInput').attr('data-default', data.address);
                            $('#emailInput').attr('data-default', data.email);
                            // $('#phoneNumberInput').attr('data-default', data.phoneNumber);
                            $('#hotlineInput').attr('data-default', data.hotline);

                            // Cập nhật data-default của editor
                            $('#businessInfoInput').attr('data-default', data.businessInformation);
                            $('#policyInput').attr('data-default', data.policy);

                            $('.cancelButton, .saveButton').attr('hidden', true);
                            AlertResponse(response.message, "success");
                        } else {
                            AlertResponse("Cập nhật thất bại! Vui lòng thử lại!", "warning");
                        }
                    },
                    error: function () {
                        AlertResponse("Đã xảy ra lỗi khi gọi API.", "error");
                    }
                });
            }           
    </script>

    <script>
        $(document).ready(function() {
            $("#updateToken").on('click',async () => {
                 try{
                    const credentials = {
                        accessToken: $("#accessToken").val(),
                        refreshToken: $("#refreshToken").val()
                    };

                    // 5️⃣ Gửi request đến API
                    const response = await $.ajax({
                        url: '@Url.Action("UpdateToken", "SystemSettings")',
                        type: 'POST',
                        contentType: "application/json",
                        data: JSON.stringify(credentials)
                    });

                    // 6️⃣ Xử lý phản hồi từ API
                    if (response.code === 0) {
                        AlertResponse(response.message, "success");
                    } else {
                        AlertResponse(response.message, "error");
                    }
                }catch(err){
                    AlertResponse(err.message, "error");
                }
            })

            $("#updateInfo").on('click',async () => {
                try{
                    const credentials = {
                        appId: $("#appId").val(),
                        secretKey: $("#secretKey").val(),
                    };

                    // 5️⃣ Gửi request đến API
                    const response = await $.ajax({
                        url: '@Url.Action("UpdateApp", "SystemSettings")',
                        type: 'POST',
                        contentType: "application/json",
                        data: JSON.stringify(credentials)
                    });

                    // 6️⃣ Xử lý phản hồi từ API
                    if (response.code === 0) {
                        AlertResponse(response.message, "success");
                    } else {
                        AlertResponse(response.message, "error");
                    }
                }catch(err){
                    AlertResponse(err.message, "error");
                }
            });

            $("#btnRefreshToken").on('click', function () {
                try{
                    $.ajax({
                        url: '/api/Helpers/RefreshToken', // Đường dẫn API
                        type: 'GET',
                        success: function (result) {
                            if (result && result.code === 0) {
                                // Lấy AccessToken và RefreshToken từ response
                                var accessToken = result.data.accessToken;
                                var refreshToken = result.data.refreshToken;

                                // Set lại giá trị vào input
                                $('#accessToken').val(accessToken);
                                $('#refreshToken').val(refreshToken);

                                alert('Refresh token thành công!');
                            } else {
                                alert('Có lỗi xảy ra: ' + (result.Message || 'Không rõ lỗi'));
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error('Lỗi gọi API:', error);
                            alert('Lỗi khi gọi API: ' + error);
                        }
                    });
                }catch(err){
                    console.log(err)
                }
            });

            let clickCount = 0; // Biến đếm số lần click
            let isEditable = false;
            $("#enableEdit").on('click', function () {
                clickCount++;
                if (clickCount >= 10) {
                    isEditable = !isEditable;
                    clickCount = 0;
                    $('#accessToken, #refreshToken, #updateToken, #btnRefreshToken').prop('disabled', !isEditable);
                }
            });
        });
    </script>
}
