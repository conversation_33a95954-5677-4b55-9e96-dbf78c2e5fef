﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Services.Surveys.Answers;

namespace MiniAppCore.Services.Surveys.Questions
{
    public class SurveyQuestionService(IUnitOfWork unitOfWork, ISurveyAnswerService surveyAnswerService) : Service<SurveyQuestion>(unitOfWork), ISurveyQuestionService
    {
        private readonly IRepository<SurveyLikertQuestion> _likertQuestionRepository = unitOfWork.GetRepository<SurveyLikertQuestion>();

        public async Task<int> CreateOrUpdateQuestionsAsync(List<SurveyQuestionDTO> questions)
        {
            if (questions == null || !questions.Any())
                return 0;

            var processor = new QuestionProcessor(this, _repository, _likertQuestionRepository, surveyAnswerService);
            return await processor.ProcessQuestions(questions);
        }

        public async Task<int> CreateOrUpdateQuestionsAsync(Dictionary<string, List<SurveyQuestionDTO>> sectionQuestionsMap)
        {
            if (sectionQuestionsMap == null || !sectionQuestionsMap.Any())
                return 0;

            var processor = new QuestionProcessor(this, _repository, _likertQuestionRepository, surveyAnswerService);
            return await processor.ProcessQuestionsBySection(sectionQuestionsMap);
        }

        public async Task<Dictionary<string, List<QuestionResponse>>> GetQuestionsMapBySectionIds(List<string> sectionIds)
        {
            if (sectionIds == null || !sectionIds.Any())
                return new Dictionary<string, List<QuestionResponse>>();

            var dataLoader = new QuestionDataLoader(_repository, _likertQuestionRepository, surveyAnswerService);
            return await dataLoader.LoadQuestionsResponseMap(sectionIds);
        }

        public async Task<Dictionary<string, List<SurveyQuestionDTO>>> GetQuestionDTOsMapBySectionIds(List<string> sectionIds)
        {
            if (sectionIds == null || !sectionIds.Any())
                return new Dictionary<string, List<SurveyQuestionDTO>>();

            var dataLoader = new QuestionDataLoader(_repository, _likertQuestionRepository, surveyAnswerService);
            return await dataLoader.LoadQuestionDTOsMap(sectionIds);
        }

        #region Helper Classes

        private class QuestionProcessor
        {
            private readonly SurveyQuestionService _service;
            private readonly IRepository<SurveyQuestion> _questionRepository;
            private readonly IRepository<SurveyLikertQuestion> _likertQuestionRepository;
            private readonly ISurveyAnswerService _answerService;

            public QuestionProcessor(
                SurveyQuestionService service,
                IRepository<SurveyQuestion> questionRepository,
                IRepository<SurveyLikertQuestion> likertQuestionRepository,
                ISurveyAnswerService answerService)
            {
                _service = service;
                _questionRepository = questionRepository;
                _likertQuestionRepository = likertQuestionRepository;
                _answerService = answerService;
            }

            public async Task<int> ProcessQuestions(List<SurveyQuestionDTO> questions)
            {
                var questionsBySection = questions
                    .GroupBy(q => q.SectionId)
                    .ToDictionary(g => g.Key ?? string.Empty, g => g.ToList());

                return await ProcessQuestionsBySection(questionsBySection);
            }

            public async Task<int> ProcessQuestionsBySection(Dictionary<string, List<SurveyQuestionDTO>> sectionQuestionsMap)
            {
                var processedQuestionIds = new List<string>();
                var answersToProcess = new Dictionary<string, List<SurveyAnswerDTO>>();
                var likertProcessor = new LikertQuestionProcessor(_likertQuestionRepository);

                // Fetch existing questions for all sections in one query
                var sectionIds = sectionQuestionsMap.Keys.ToList();
                var existingQuestions = await _questionRepository.AsQueryable()
                    .Where(q => q.SurveySectionId != null && sectionIds.Contains(q.SurveySectionId))
                    .ToDictionaryAsync(q => q.Id, q => q);

                // Process questions for each section
                foreach (var sectionId in sectionQuestionsMap.Keys)
                {
                    var questions = sectionQuestionsMap[sectionId];

                    foreach (var questionDto in questions)
                    {
                        var question = await ProcessSingleQuestion(questionDto, sectionId, existingQuestions);
                        processedQuestionIds.Add(question.Id);

                        // Collect answers for batch processing
                        if (questionDto.Answers?.Any() == true)
                        {
                            foreach (var answer in questionDto.Answers)
                            {
                                answer.QuestionId = question.Id;
                            }

                            answersToProcess[question.Id] = questionDto.Answers;
                        }

                        // Process likert questions if applicable
                        if (questionDto.Type?.ToLower() == "likert" && questionDto.LikertQuestions?.Any() == true)
                        {
                            await likertProcessor.ProcessLikertQuestions(question.Id, questionDto.LikertQuestions);
                        }
                    }
                }

                // Find and delete questions that were not in the update
                await DeleteRemovedQuestions(sectionIds, processedQuestionIds);

                // Process all collected answers
                if (answersToProcess.Any())
                {
                    await _answerService.CreateOrUpdateAnswersAsync(answersToProcess);
                }

                return await _service.unitOfWork.SaveChangesAsync();
            }

            private async Task<SurveyQuestion> ProcessSingleQuestion(
                SurveyQuestionDTO questionDto,
                string sectionId,
                Dictionary<string, SurveyQuestion> existingQuestions)
            {
                bool isNewQuestion = string.IsNullOrEmpty(questionDto.Id) || questionDto.Id.StartsWith("temp_");
                SurveyQuestion question;

                if (isNewQuestion)
                {
                    // Create new question
                    question = new SurveyQuestion
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        SurveySectionId = sectionId,
                        Type = questionDto.Type,
                        QuestionTitle = questionDto.QuestionTitle,
                        IsRequired = questionDto.IsRequired,
                        DisplayOrder = questionDto.DisplayOrder
                    };

                    _questionRepository.Add(question);
                }
                else if (existingQuestions.TryGetValue(questionDto.Id, out question))
                {
                    // Update existing question
                    question.Type = questionDto.Type;
                    question.QuestionTitle = questionDto.QuestionTitle;
                    question.IsRequired = questionDto.IsRequired;
                    question.DisplayOrder = questionDto.DisplayOrder;

                    _questionRepository.Update(question);
                }
                else
                {
                    // Create new question with a generated ID
                    question = new SurveyQuestion
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        SurveySectionId = sectionId,
                        Type = questionDto.Type,
                        QuestionTitle = questionDto.QuestionTitle,
                        IsRequired = questionDto.IsRequired,
                        DisplayOrder = questionDto.DisplayOrder
                    };

                    _questionRepository.Add(question);
                }

                return question;
            }

            private async Task DeleteRemovedQuestions(List<string> sectionIds, List<string> processedQuestionIds)
            {
                // Lấy tất cả câu hỏi từ tất cả các section trong danh sách
                var existingQuestionsBySections = await _questionRepository.AsQueryable()
                    .Where(q => q.SurveySectionId != null && sectionIds.Contains(q.SurveySectionId))
                    .ToListAsync();

                // Xác định các câu hỏi cần xóa - những câu không có trong danh sách đã xử lý
                var questionsToDelete = existingQuestionsBySections
                    .Where(q => !processedQuestionIds.Contains(q.Id))
                    .ToList();

                if (questionsToDelete.Any())
                {
                    // Get IDs of questions to delete for cascading deletions
                    var questionIdsToDelete = questionsToDelete.Select(q => q.Id).ToList();

                    // Xóa các câu hỏi likert liên quan
                    var likertQuestionsToDelete = await _likertQuestionRepository.AsQueryable()
                        .Where(lq => questionIdsToDelete.Contains(lq.SurveyQuestionId))
                        .ToListAsync();

                    if (likertQuestionsToDelete.Any())
                    {
                        _likertQuestionRepository.DeleteRange(likertQuestionsToDelete);
                    }

                    // Xóa các câu trả lời liên quan
                    await _answerService.DeleteAnswersByQuestionIdsAsync(questionIdsToDelete);

                    // Xóa các câu hỏi
                    _questionRepository.DeleteRange(questionsToDelete);
                }
            }
        }

        private class LikertQuestionProcessor
        {
            private readonly IRepository<SurveyLikertQuestion> _likertQuestionRepository;

            public LikertQuestionProcessor(IRepository<SurveyLikertQuestion> likertQuestionRepository)
            {
                _likertQuestionRepository = likertQuestionRepository;
            }

            public async Task ProcessLikertQuestions(string questionId, List<SurveyLikertQuestionDTO> likertQuestions)
            {
                var existingLikertQuestions = await _likertQuestionRepository.AsQueryable()
                    .Where(lq => lq.SurveyQuestionId == questionId)
                    .ToDictionaryAsync(lq => lq.Id, lq => lq);

                var newLikertQuestions = new List<SurveyLikertQuestion>();
                var likertQuestionIdsToKeep = new HashSet<string>();

                foreach (var likertQuestion in likertQuestions)
                {
                    bool isNewLikertQuestion = string.IsNullOrEmpty(likertQuestion.QuestionLikertId) ||
                                               likertQuestion.QuestionLikertId.StartsWith("temp_");

                    if (isNewLikertQuestion)
                    {
                        // Create new
                        newLikertQuestions.Add(new SurveyLikertQuestion
                        {
                            Id = Guid.NewGuid().ToString("N"),
                            SurveyQuestionId = questionId,
                            QuestionLikertTitle = likertQuestion.QuestionLikertTitle,
                            OptionCount = likertQuestion.OptionCount,
                            DisplayOrder = (short)likertQuestions.IndexOf(likertQuestion)
                        });
                    }
                    else if (existingLikertQuestions.TryGetValue(likertQuestion.QuestionLikertId, out var existing))
                    {
                        // Update existing
                        existing.QuestionLikertTitle = likertQuestion.QuestionLikertTitle;
                        existing.OptionCount = likertQuestion.OptionCount;
                        existing.DisplayOrder = (short)likertQuestions.IndexOf(likertQuestion);

                        _likertQuestionRepository.Update(existing);
                        likertQuestionIdsToKeep.Add(likertQuestion.QuestionLikertId);
                    }
                    else
                    {
                        // ID provided but not found
                        newLikertQuestions.Add(new SurveyLikertQuestion
                        {
                            Id = Guid.NewGuid().ToString("N"),
                            SurveyQuestionId = questionId,
                            QuestionLikertTitle = likertQuestion.QuestionLikertTitle,
                            OptionCount = likertQuestion.OptionCount,
                            DisplayOrder = (short)likertQuestions.IndexOf(likertQuestion)
                        });
                    }
                }

                // Add new likert questions in batch
                if (newLikertQuestions.Any())
                {
                    _likertQuestionRepository.AddRange(newLikertQuestions);
                }

                // Delete likert questions that aren't in the updated list
                var likertQuestionsToDelete = existingLikertQuestions.Values
                    .Where(lq => !likertQuestionIdsToKeep.Contains(lq.Id))
                    .ToList();

                if (likertQuestionsToDelete.Any())
                {
                    _likertQuestionRepository.DeleteRange(likertQuestionsToDelete);
                }
            }
        }

        private class QuestionDataLoader
        {
            private readonly IRepository<SurveyQuestion> _questionRepository;
            private readonly IRepository<SurveyLikertQuestion> _likertQuestionRepository;
            private readonly ISurveyAnswerService _answerService;

            public QuestionDataLoader(
                IRepository<SurveyQuestion> questionRepository,
                IRepository<SurveyLikertQuestion> likertQuestionRepository,
                ISurveyAnswerService answerService)
            {
                _questionRepository = questionRepository;
                _likertQuestionRepository = likertQuestionRepository;
                _answerService = answerService;
            }

            public async Task<Dictionary<string, List<QuestionResponse>>> LoadQuestionsResponseMap(List<string> sectionIds)
            {
                // Get all questions for all sections in one query
                var questions = await _questionRepository.AsQueryable()
                    .Where(q => sectionIds.Contains(q.SurveySectionId))
                    .OrderBy(q => q.DisplayOrder)
                    .ToListAsync();

                if (!questions.Any())
                    return sectionIds.ToDictionary(id => id, _ => new List<QuestionResponse>());

                // Get all question IDs
                var questionIds = questions.Select(q => q.Id).ToList();

                // Get all answers in one query
                var allAnswers = await _answerService.GetAnswersResponseByQuestionIds<AnswerResponse>(questionIds);
                var answersMap = allAnswers.GroupBy(a => a.QuestionId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // Get likert questions for relevant question types
                var likertQuestionsMap = await LoadLikertQuestionsMap<QuestionLikertResponse>(
                    questions.Where(q => q.Type?.ToLower() == "likert")
                            .Select(q => q.Id)
                            .ToList());

                // Build responses grouped by section ID
                return questions
                    .GroupBy(q => q.SurveySectionId)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Select(question => new QuestionResponse
                        {
                            QuestionId = question.Id,
                            Type = question.Type,
                            QuestionTitle = question.QuestionTitle,
                            IsRequied = question.IsRequired,
                            ListOption = answersMap.TryGetValue(question.Id, out var options) ? options : new List<AnswerResponse>(),
                            DisplayOrder = question.DisplayOrder,
                            ListQuestionLikert = question.Type?.ToLower() == "likert" && likertQuestionsMap.TryGetValue(question.Id, out var likertQuestions)
                                ? likertQuestions
                                : new List<QuestionLikertResponse>()
                        }).ToList()
                    );
            }

            public async Task<Dictionary<string, List<SurveyQuestionDTO>>> LoadQuestionDTOsMap(List<string> sectionIds)
            {
                // Get all questions for all sections in one query
                var questions = await _questionRepository.AsQueryable()
                    .Where(q => sectionIds.Contains(q.SurveySectionId))
                    .OrderBy(q => q.DisplayOrder)
                    .ToListAsync();

                if (!questions.Any())
                    return sectionIds.ToDictionary(id => id, _ => new List<SurveyQuestionDTO>());

                // Get all answers in one query
                var questionIds = questions.Select(q => q.Id).ToList();
                var allAnswers = await _answerService.GetAnswersResponseByQuestionIds<SurveyAnswerDTO>(questionIds);
                var answersMap = allAnswers.GroupBy(a => a.QuestionId!)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // Get likert questions for relevant question types
                var likertQuestionsMap = await LoadLikertQuestionsMap<SurveyLikertQuestionDTO>(
                    questions.Where(q => q.Type?.ToLower() == "likert")
                            .Select(q => q.Id)
                            .ToList());

                // Build response DTOs grouped by section ID
                return questions
                    .GroupBy(q => q.SurveySectionId)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Select(question => new SurveyQuestionDTO
                        {
                            Id = question.Id,
                            SectionId = question.SurveySectionId,
                            Type = question.Type,
                            QuestionTitle = question.QuestionTitle,
                            IsRequired = question.IsRequired,
                            DisplayOrder = question.DisplayOrder,
                            Answers = answersMap.TryGetValue(question.Id, out var answers) ? answers : new List<SurveyAnswerDTO>(),
                            LikertQuestions = question.Type?.ToLower() == "likert" && likertQuestionsMap.TryGetValue(question.Id, out var likertQuestions)
                                ? likertQuestions
                                : new List<SurveyLikertQuestionDTO>()
                        }).ToList()
                    );
            }

            private async Task<Dictionary<string, List<T>>> LoadLikertQuestionsMap<T>(List<string> likertQuestionIds)
                where T : class, new()
            {
                if (!likertQuestionIds.Any())
                    return new Dictionary<string, List<T>>();

                var likertQuestions = await _likertQuestionRepository.AsQueryable()
                    .Where(lq => likertQuestionIds.Contains(lq.SurveyQuestionId))
                    .OrderBy(lq => lq.DisplayOrder)
                    .ToListAsync();

                // Map to appropriate type based on T
                if (typeof(T) == typeof(QuestionLikertResponse))
                {
                    return likertQuestions
                        .GroupBy(lq => lq.SurveyQuestionId)
                        .ToDictionary(
                            g => g.Key,
                            g => g.Select(lq => new QuestionLikertResponse
                            {
                                QuestionLikertId = lq.Id,
                                QuestionLikertTitle = lq.QuestionLikertTitle,
                                OptionCount = lq.OptionCount,
                            }).Cast<T>().ToList()
                        );
                }
                else if (typeof(T) == typeof(SurveyLikertQuestionDTO))
                {
                    return likertQuestions
                        .GroupBy(lq => lq.SurveyQuestionId)
                        .ToDictionary(
                            g => g.Key,
                            g => g.Select(lq => new SurveyLikertQuestionDTO
                            {
                                QuestionLikertId = lq.Id,
                                QuestionLikertTitle = lq.QuestionLikertTitle,
                                OptionCount = lq.OptionCount
                            }).Cast<T>().ToList()
                        );
                }

                return new Dictionary<string, List<T>>();
            }
        }

        #endregion
    }
}

