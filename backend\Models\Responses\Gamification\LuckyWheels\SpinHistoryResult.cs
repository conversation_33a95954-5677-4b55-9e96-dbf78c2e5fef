﻿using MiniAppCore.Enums;

namespace MiniAppCore.Models.Responses.Gamification.LuckyWheels
{
    public class SpinHistoryResult
    {
        public string? HistoryId { get; set; }
        public string? Avatar { get; set; }
        public string? UserZaloId { get; set; }
        public string? UserZaloName { get; set; }

        public bool HasWon { get; set; }
        public bool IsClaimed { get; set; }
        public string? PrizeId { set; get; }
        public string? PrizeName { get; set; }
        public string? PrizeImage { get; set; }
        public float? PrizeValue { get; set; }

        public DateTime WonDate { get; set; }
        public DateTime CreatedDate { get; set; }

        public EPrizeType Type { get; set; }
        public ETransaction RequestStatus { get; set; }

        private bool? _isAdmin;
        public bool? IsAdmin
        {
            set
            {
                _isAdmin = value;
            }
        }

        private string? _phoneNumber;
        public string? PhoneNumber
        {
            get
            {
                if (string.IsNullOrEmpty(_phoneNumber))
                {
                    return string.Empty;
                }
                return _isAdmin.HasValue && _isAdmin.Value
                        ? _phoneNumber
                        : new string('*', _phoneNumber.Length - 3) + _phoneNumber.Substring(_phoneNumber.Length - 3);
            }
            set
            {
                _phoneNumber = value;
            }
        }
    }
}
