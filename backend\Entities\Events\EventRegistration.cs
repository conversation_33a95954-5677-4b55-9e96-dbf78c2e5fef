﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;

namespace MiniAppCore.Entities.Events
{
    public class EventRegistration : BaseEntity
    {
        public required string UserZaloId { get; set; }
        public required string EventId { get; set; }

        public string? Name { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }

        public ECheckInStatus IsCheckIn { get; set; } = ECheckInStatus.NotCheckIn;
        public required string CheckInCode { get; set; }
    }
}
