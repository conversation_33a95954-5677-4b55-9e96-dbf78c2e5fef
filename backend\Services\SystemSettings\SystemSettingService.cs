﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.SystemSettings;
using MiniAppCore.Models.Responses.Categories;
using Newtonsoft.Json;

namespace MiniAppCore.Services.SystemSettings
{
    public class SystemSettingService(IUnitOfWork unitOfWork) : ISystemSettingService
    {
        private readonly IRepository<Common> _commonRepository = unitOfWork.GetRepository<Common>();
        private readonly IRepository<MembershipExtend> _membershipExtenRepo = unitOfWork.GetRepository<MembershipExtend>();
        private readonly IRepository<MembershipExtendDefault> _membershipExDfRep = unitOfWork.GetRepository<MembershipExtendDefault>();

        #region Omni Account

        public async Task<OmniAccountDTO> GetOmniAccountAsync()
        {
            try
            {
                var account = await _commonRepository.AsQueryable()
                .SingleOrDefaultAsync(x => x.Name == "OmniAccount");

                return string.IsNullOrEmpty(account?.Content)
                    ? new OmniAccountDTO()
                    : JsonConvert.DeserializeObject<OmniAccountDTO>(account.Content) ?? new OmniAccountDTO();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return new OmniAccountDTO();
            }
        }

        public async Task<int> AddOrUpdateAccountOmniAsync(OmniAccountDTO omniAccount)
        {
            var account = await _commonRepository.AsQueryable().FirstOrDefaultAsync(x => x.Name == "OmniAccount");
            if (account == null)
            {
                account = new Common
                {
                    Name = "OmniAccount",
                    Content = JsonConvert.SerializeObject(omniAccount)
                };
                _commonRepository.Add(account);
            }
            else
            {
                account.Content = JsonConvert.SerializeObject(omniAccount);
                _commonRepository.Update(account);
            }
            return await unitOfWork.SaveChangesAsync();
        }

        #endregion

        #region Category Home

        public async Task<List<CategoryResponse>> GetCategoryHomeAsync()
        {
            try
            {
                var categoryConfig = await _commonRepository.AsQueryable().SingleOrDefaultAsync(x => x.Name == "CategoryHome");
                var categoryIds = string.IsNullOrEmpty(categoryConfig?.Content) ? new List<string>() : categoryConfig.Content.Split(",").ToList();
                var categories = await unitOfWork.GetRepository<Category>().AsQueryable().Where(x => categoryIds.Contains(x.Id)).ToListAsync();

                var sortedCategories = categories.OrderBy(c => categoryIds.IndexOf(c.Id)).ToList();

                var items = sortedCategories.Select(p => new CategoryResponse
                {
                    Id = p.Id,
                    Name = p.Name,
                    HexColor = p.HexColor,
                    Description = p.Description,
                    Images = new List<string>()
                }).ToList();

                return items;
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return new List<CategoryResponse>();
            }
        }

        public async Task<int> AddOrUpdateCategoryHomeAsync(List<string> categoryIds)
        {
            var categories = await _commonRepository.AsQueryable().FirstOrDefaultAsync(x => x.Name == "CategoryHome");
            if (categories == null)
            {
                categories = new Common
                {
                    Name = "CategoryHome",
                    Content = string.Join(",", categoryIds)
                };
                _commonRepository.Add(categories);
            }
            else
            {
                categories.Content = string.Join(",", categoryIds);
                _commonRepository.Update(categories);
            }
            return await unitOfWork.SaveChangesAsync();
        }

        #endregion

        #region Link Payment Guide

        public async Task<string> GetLinkPaymentGuidAsync()
        {
            return (await _commonRepository.AsQueryable().SingleOrDefaultAsync(x => x.Name == "LinkPayment"))?.Content ?? string.Empty;
        }

        public async Task<int> AddOrUpdateLinkPaymentGuideAsync(string linkPaymentGuide)
        {
            var common = await _commonRepository.AsQueryable().FirstOrDefaultAsync(x => x.Name == "LinkPaymentGuide");
            if (common == null)
            {
                common = new Common
                {
                    Name = "LinkPaymentGuide",
                    Content = JsonConvert.SerializeObject(linkPaymentGuide)
                };
                _commonRepository.Add(common);
            }
            else
            {
                common.Content = JsonConvert.SerializeObject(linkPaymentGuide);
                _commonRepository.Update(common);
            }
            return await unitOfWork.SaveChangesAsync();
        }

        #endregion

        #region Enterprise Infomation

        public async Task<EnterpriseInfomationDTO> GetEnterpriseInformationAsync()
        {
            try
            {
                var enterpriseInfo = await _commonRepository.AsQueryable()
                               .SingleOrDefaultAsync(x => x.Name == "EnterpiseInfomation");

                return string.IsNullOrEmpty(enterpriseInfo?.Content)
                    ? new EnterpriseInfomationDTO()
                    : JsonConvert.DeserializeObject<EnterpriseInfomationDTO>(enterpriseInfo.Content) ?? new EnterpriseInfomationDTO();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
                return new EnterpriseInfomationDTO();
            }
        }

        public async Task<int> AddOrUpdateEnterpriseInformationAsync(EnterpriseInfomationDTO enterpriseInfomation)
        {
            var common = await _commonRepository.AsQueryable().FirstOrDefaultAsync(x => x.Name == "EnterpiseInfomation");
            if (common == null)
            {
                common = new Common
                {
                    Name = "EnterpiseInfomation",
                    Content = JsonConvert.SerializeObject(enterpriseInfomation)
                };
                _commonRepository.Add(common);
            }
            else
            {
                common.Content = JsonConvert.SerializeObject(enterpriseInfomation);
                _commonRepository.Update(common);
            }
            return await unitOfWork.SaveChangesAsync();
        }

        #endregion

        #region Membership Extend Default

        public async Task<int> CreateMembershipExtendDefault(MembershipExtendDefault data)
        {
            data.Id = Guid.NewGuid().ToString("N");
            _membershipExDfRep.Add(data);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> UpdateMembershipExtendDefault(MembershipExtendDefault data)
        {
            var dtUp = await _membershipExDfRep.FindByIdAsync(data.Id);
            if (dtUp == null)
            {
                return 0;
            }

            dtUp.Attribute = data.Attribute;
            dtUp.Content = data.Content;
            dtUp.Type = data.Type;
            dtUp.Min = data.Min;
            dtUp.Max = data.Max;
            dtUp.AttributeName = data.AttributeName;
            dtUp.IsActive = data.IsActive;

            dtUp.UpdatedDate = DateTime.Now;
            _membershipExDfRep.Update(dtUp);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<PagedResult<MembershipExtendDefault>> GetPageMemberShipExtendDefault(RequestQuery query)
        {
            var rs = new PagedResult<MembershipExtendDefault>();
            try
            {
                var list = _membershipExDfRep.AsQueryable();
                if (!string.IsNullOrEmpty(query.Keyword))
                {
                    list = list.Where(x =>
                    (x.Attribute.Contains(query.Keyword)) ||
                    (x.Content.Contains(query.Keyword))
                      );
                }
                rs.Data = await list.Skip((query.Page - 1) * query.PageSize).Take(query.PageSize).ToListAsync();
                var totalItems = await list.CountAsync();
                var totalPages = (int)Math.Ceiling(totalItems / (double)query.PageSize);
                rs.TotalPages = totalPages;
            }
            catch (Exception)
            {
                rs.Data = new List<MembershipExtendDefault>();
                rs.TotalPages = 0;
            }
            return rs;
        }

        public async Task<MembershipExtendDefault?> GetMembershipExtendDefaultById(string id)
        {
            return await _membershipExDfRep.FindByIdAsync(id);
        }

        public async Task<int> DeleteMembershipExtendDefault(string id)
        {
            var dt = await _membershipExDfRep.FindByIdAsync(id);
            if (dt == null)
            {
                return 0;
            }
            _membershipExDfRep.Delete(dt);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<List<string>> GetAttributeFromExtendInfo()
        {
            return await _membershipExtenRepo.AsQueryable().Select(x => x.Attribute!).Distinct().ToListAsync();
        }

        #endregion

        #region Enable/Disable Feature Button   

        public async Task<List<FeatureButtonDto>> GetFeaturesButton()
        {
            var buttons = await _commonRepository.AsQueryable()
                       .SingleOrDefaultAsync(x => x.Name == "FeaturesButton");

            // If Content is empty or null, return an empty list.
            if (string.IsNullOrEmpty(buttons?.Content))
            {
                return new List<FeatureButtonDto>();
            }

            // Deserialize the Content field.
            return JsonConvert.DeserializeObject<List<FeatureButtonDto>>(buttons.Content) ??
                   new List<FeatureButtonDto>();

            // Ex:
            // [
            //   {
            //      "key": "LuckyWheelButton",
            //      "value": true
            //   },
            //   {
            //      "key": "ChatOAButton",
            //      "value": true
            //   },
            // ]
        }

        #endregion
    }
}
