﻿using MiniAppCore.Base.Database;
using MiniAppCore.Base.Dependencies.Cache;
using MiniAppCore.Base.Dependencies.Storage;
using MiniAppCore.Base.Dependencies.Storage.DTOs;
using MiniAppCore.Base.Interface;

namespace MiniAppCore.Base.Common
{
    public static class Injector
    {
        /// <summary>
        /// Đăng ký các dependency vào IServiceCollection để sử dụng Dependency Injection (DI).
        /// </summary>
        /// <param name="services">Đối tượng IServiceCollection để đăng ký các service.</param>
        public static void RegisterDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            // Tự động đăng ký tất cả các service có tên kết thúc bằng "Service".
            var serviceTypes = typeof(Program).Assembly.GetTypes()
                .Where(t => t.IsClass && !t.IsAbstract && t.Name.EndsWith("Service"))
                .ToList();

            foreach (var implementationType in serviceTypes)
            {
                // Tìm interface có tên bắt đầu bằng "I" và khớp với tên của implementation class.
                var interfaceType = implementationType.GetInterface($"I{implementationType.Name}");

                // Nếu interface tồn tại, đăng ký nó vào DI container với phạm vi Scoped.
                if (interfaceType != null)
                {
                    services.AddScoped(interfaceType, implementationType);
                }
            }
            // Đăng ký UnitOfWork theo phạm vi (Scoped).
            services.AddScoped<IUnitOfWork, UnitOfWork>();
            services.AddSingleton<ICacheService, CacheService>();

            services.Configure<StorageOptions>(configuration.GetSection("Storage"));
            services.AddScoped<IStorageService, StorageService>();

        }

    }
}
