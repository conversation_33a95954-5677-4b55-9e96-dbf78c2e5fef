﻿using MiniAppCore.Entities.Categories;

namespace MiniAppCore.Models.Responses.Categories
{
    public class CategoryResponse
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? HexColor { get; set; }
        public string? Description { get; set; }
        public DateTime CreatedDate { get; set; }
        public int OrderPriority { get; set; } = 1;
        public List<string> Images { get; set; } = new();
        public List<CategoryChild> listChild { get; set; } = new List<CategoryChild> { };
    }
}
