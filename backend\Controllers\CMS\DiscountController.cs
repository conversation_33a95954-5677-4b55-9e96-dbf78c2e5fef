﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Models.Responses.Products;
using MiniAppCore.Services.Offers.Discounts;
using MiniAppCore.Services.Products;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class DiscountController(IDiscountService discountService, IProductService productService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }
        [HttpGet("Discount/Create")]
        public async Task<IActionResult> Create()
        {
            var discount = new Discount()
            {
                Id = string.Empty,
                Name = string.Empty,
                Description = string.Empty,
            };
            ViewBag.Title = "Thêm mới chương trình giảm giá";
            ViewBag.Button = "Lưu";
            ViewBag.Products = await productService.GetAllAsync();
            return PartialView("_Discount", (discount, new List<ProductItemResponse>()));
        }

        [HttpGet("Discount/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Title = "Cập nhật chương trình giảm giá";
            ViewBag.Button = "Cập nhật";

            var result = await discountService.GetByIdAsync(id);
            var appliedProducts = await discountService.GetDiscountItems(id);

            ViewBag.Products = await productService.GetAllAsync();

            return PartialView("_Discount", (result, appliedProducts.ToList()));
        }
    }
}
