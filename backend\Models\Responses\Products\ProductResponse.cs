﻿using MiniAppCore.Enums;

namespace MiniAppCore.Models.Responses.Products
{
    public class ProductResponse
    {
        public required string Id { get; set; }
        public string? Name { get; set; }
        public decimal OriginalPrice { get; set; }
        public decimal DiscountPrice { get; set; }

        public bool IsGift { get; set; }
        public bool IsGlobalProduct { get; set; }

        public int LikeCount { get; set; }
        public int ViewCount { get; set; }
        public int BoughtCount { get; set; }
        public int ReviewCount { get; set; }
        public float ReviewPoint { get; set; }

        public EProduct Status { get; set; }

        public List<string> Images { get; set; } = new();
        public bool IsDiscounted => DiscountPrice < OriginalPrice;

        public short DiscountType { get; set; } // 1. giam %, 2. truc tiep
        public decimal DiscountValue
        {
            get
            {
                if (OriginalPrice <= 0 || DiscountPrice <= 0)
                    return 0;

                return DiscountType switch
                {
                    1 => Math.Round((1 - DiscountPrice / OriginalPrice) * 100, 2), // Giảm theo %
                    2 => Math.Round(OriginalPrice - DiscountPrice, 2), // Giảm trực tiếp
                    _ => 0
                };
            }
        }

        public int DisplayOrder { get; set; }
    }

    public class ProductItemResponse
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public decimal Price { get; set; }
        public List<string> Images { get; set; } = new();
    }
}
