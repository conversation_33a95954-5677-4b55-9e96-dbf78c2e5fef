﻿using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Affiliates;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Requests.Affiliates;
using MiniAppCore.Services.Affiliates.CommissionRates;

namespace MiniAppCore.Controllers.API
{
    [Route("api/[controller]")]
    [ApiController]
    public class AffiliatesController : ControllerBase
    {
        private readonly ILogger<AffiliatesController> _logger;
        private readonly ICommissionRateService _commissionRateService;

        public AffiliatesController(ILogger<AffiliatesController> logger, ICommissionRateService commissionRateService)
        {
            _logger = logger;
            _commissionRateService = commissionRateService;
        }

        // GET: api/affiliates/commission-rates
        [HttpGet("commission-rates")]
        public async Task<IActionResult> GetCommissionRates([FromQuery] int pagesize = 100)
        {
            _logger.LogInformation("Retrieving commission rates");

            try
            {
                var rates = await _commissionRateService.GetAllAsync();
                return Ok(new
                {
                    code = 0,
                    message = "Success",
                    data = rates
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving commission rates");
                return StatusCode(500, new { code = 500, message = "Internal server error" });
            }
        }

        // GET: api/affiliates/commission-rates/{id}
        [HttpGet("commission-rates/{id}")]
        public async Task<IActionResult> GetCommissionRate(string id)
        {
            _logger.LogInformation($"Retrieving commission rate with ID: {id}");

            try
            {
                var rate = await _commissionRateService.GetByIdAsync(id);
                if (rate == null)
                {
                    return NotFound(new { code = 404, message = "Commission rate not found" });
                }

                return Ok(new
                {
                    code = 0,
                    message = "Success",
                    data = rate
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving commission rate with ID: {id}");
                return StatusCode(500, new { code = 500, message = "Internal server error" });
            }
        }

        // POST: api/affiliates/commission-rates
        [HttpPost("commission-rates")]
        public async Task<IActionResult> CreateCommissionRate([FromBody] CreateCommissionRateRequest request)
        {
            _logger.LogInformation("Creating new commission rate");

            if (!ModelState.IsValid)
            {
                return BadRequest(new { code = 400, message = "Invalid input data" });
            }

            try
            {
                var model = new AffiliateCommissionRate
                {
                    Level = request.Level,
                    Rate = request.Rate,
                    IsActive = request.IsActive,
                    EffectiveDate = request.EffectiveDate
                };

                await _commissionRateService.CreateAsync(model);
                return Ok(new
                {
                    code = 0,
                    message = "Commission rate created successfully",
                    data = model
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating commission rate");
                return StatusCode(500, new { code = 500, message = "Internal server error" });
            }
        }

        // PUT: api/affiliates/commission-rates/{id}
        [HttpPut("commission-rates/{id}")]
        public async Task<IActionResult> UpdateCommissionRate(string id, [FromBody] UpdateCommissionRateRequest request)
        {
            _logger.LogInformation($"Updating commission rate {id}");

            if (!ModelState.IsValid)
            {
                return BadRequest(new { code = 400, message = "Invalid input data" });
            }

            try
            {
                var model = new AffiliateCommissionRate
                {
                    Level = request.Level,
                    Rate = request.Rate,
                    IsActive = request.IsActive,
                    EffectiveDate = request.EffectiveDate
                };

                await _commissionRateService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Commission rate updated successfully",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating commission rate with ID: {id}");
                return StatusCode(500, new { code = 500, message = "Internal server error" });
            }
        }

        // DELETE: api/affiliates/commission-rates/{id}
        [HttpDelete("commission-rates/{id}")]
        public async Task<IActionResult> DeleteCommissionRate(string id)
        {
            _logger.LogInformation($"Deleting commission rate with ID: {id}");
            try
            {
                var rate = await _commissionRateService.GetByIdAsync(id);
                if (rate == null)
                {
                    return NotFound(new { code = 404, message = "Commission rate not found" });
                }

                await _commissionRateService.DeleteAsync(rate);
                return Ok(new
                {
                    code = 0,
                    message = "Commission rate deleted successfully"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting commission rate with ID: {id}");
                return StatusCode(500, new { code = 500, message = "Internal server error" });
            }
        }

        // GET: api/affiliates/commission-rates/active
        [HttpGet("commission-rates/active")]
        public async Task<IActionResult> GetActiveCommissionRates()
        {
            _logger.LogInformation("Retrieving active commission rates");

            try
            {
                var rates = await _commissionRateService.GetActiveRatesAsync();
                return Ok(new
                {
                    code = 0,
                    message = "Success",
                    data = rates
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active commission rates");
                return StatusCode(500, new { code = 500, message = "Internal server error" });
            }
        }
    }
}
