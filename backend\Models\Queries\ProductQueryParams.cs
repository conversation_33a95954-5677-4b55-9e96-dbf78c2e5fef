﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Enums;
using MiniAppCore.Models.Common;

namespace MiniAppCore.Models.Queries
{
    public class ProductQueryParams : RequestQuery, IRequestQuery
    {
        public bool? IsGift { get; set; }
        public string? BranchId { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public string? CategoryId { get; set; }
        public string? CategoryChildId { get; set; }

        public List<string> BranchIds { get; set; } = new();
        public List<string> CategoryIds { get; set; } = new();

        public List<EProduct> StockStatus { get; set; } = new();

        public bool IsOrderByCreatedDate { get; set; } = false;
    }
}
