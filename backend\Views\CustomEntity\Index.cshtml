@model List<MiniAppCore.Entities.Commons.CustomField>
@{
    ViewData["Title"] = "Quản lý Custom Fields";
    var availableEntities = ViewBag.AvailableEntities as List<string> ?? new List<string>();
    var selectedEntity = ViewBag.SelectedEntity as string;
}

<div class="row g-4">
    <!-- Left Sidebar for Entity Selection -->
    <div class="col-lg-3">
        <div class="card sticky-top" style="top: 20px;">
            <div class="card-header">
                <h5 class="mb-0"><i class="ri-database-2-line me-2"></i>Entities</h5>
            </div>
            <div class="list-group list-group-flush" id="entity-list">
                <a href="@Url.Action("Index")" class="list-group-item list-group-item-action @(string.IsNullOrEmpty(selectedEntity) ? "active" : "")" data-entity-name="">
                    <i class="ri-table-fill me-2"></i>Tất cả Entities
                </a>
                @foreach (var entity in availableEntities)
                {
                    <a href="@Url.Action("Index", new { entityName = entity })" class="list-group-item list-group-item-action @(entity == selectedEntity ? "active" : "")" data-entity-name="@entity">
                        <i class="ri-arrow-right-s-fill me-2"></i>@entity
                    </a>
                }
            </div>
        </div>
    </div>

    <!-- Right Content Area for Fields -->
    <div class="col-lg-9">
        <div class="card">
            <div class="card-body">
                <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-4">
                    <div>
                        <h4 class="mb-1">
                            Custom Fields: <span class="text-primary fw-bold">@(selectedEntity ?? "Tất cả")</span>
                        </h4>
                        <p class="text-muted mb-0">Quản lý các trường dữ liệu tùy chỉnh cho entity đã chọn.</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button onclick="openCreateModal()" type="button" class="btn btn-primary">
                            <i class="ri-add-line me-1"></i> Thêm Field Mới
                        </button>
                        <button onclick="showHelpModal()" type="button" class="btn btn-outline-secondary">
                            <i class="ri-question-line me-1"></i> Trợ giúp
                        </button>
                    </div>
                </div>

                <!-- Filters Section -->
                <div class="row align-items-end mb-3 g-3">
                    <div class="col-md-5">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="ri-search-line"></i></span>
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên field, loại dữ liệu...">
                        </div>
                    </div>
                    <div class="col-md-4 @(string.IsNullOrEmpty(selectedEntity) ? "" : "d-none")" id="entity-filter-container">
                        <label for="entityFilter" class="form-label">Lọc theo Entity</label>
                        <select id="entityFilter" class="form-select">
                            <option value="">Tất cả Entities</option>
                            @foreach (var entity in availableEntities)
                            {
                                <option value="@entity">@entity</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button id="btn-refresh" type="button" class="btn btn-light w-100 border">
                            <i class="ri-refresh-line me-1"></i>Tải lại
                        </button>
                    </div>
                </div>

                <!-- Loading Spinner -->
                <div id="spinner" class="text-center py-5" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>

                <!-- Table -->
                <div class="table-responsive">
                    <table id="customFieldsTable" class="table table-hover align-middle w-100"></table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Create/Edit and Configuration -->
<div id="customFieldModal" class="modal fade" tabindex="-1" aria-labelledby="customFieldModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div id="modal-dialog" class="modal-dialog modal-lg">
        <div id="modalContent">
            <!-- AJAX Content will be loaded here -->
            <div class="modal-content">
                <div class="modal-body text-center p-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Đang tải nội dung...</p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("Scripts/_IndexScripts")
    <script>
        // Helper function to open the create modal, can be called from anywhere
        function openCreateModal() {
            const entityName = $('#entity-list a.active').data('entity-name') || '';
            var createUrl = '@Url.Action("Create", "CustomEntity")';

            console.log('Opening create modal for entity:', entityName);

            $.get(createUrl, { entityName: entityName })
                .done(function (data) {
                    console.log('Create form loaded successfully');
                    $('#modalContent').html(data);
                    $('#modal-dialog').removeClass('modal-xl').addClass('modal-lg');
                    $('#customFieldModal').modal('show');
                })
                .fail(function (xhr) {
                    console.error('Create form load failed:', xhr);
                    const message = xhr.responseJSON?.message || 'Không thể tải form. Vui lòng thử lại.';
                    if (typeof AlertResponse === 'function') {
                        AlertResponse(message, 'error');
                    } else {
                        alert(message);
                    }
                });
        }

        // Placeholder for a help modal
        function showHelpModal() {
            if (typeof AlertResponse === 'function') {
                AlertResponse('Chức năng trợ giúp sắp ra mắt!', 'info');
            } else {
                alert('Chức năng trợ giúp sắp ra mắt!');
            }
        }

        // Function to open entity configuration modal
        function openEntityConfiguration(entityName) {
            $.get('@Url.Action("ConfigureEntity", "CustomEntity")', { entityName: entityName })
                .done(function (data) {
                    $('#modalContent').html(data);
                    $('#modal-dialog').removeClass('modal-lg').addClass('modal-xl');
                    $('#customFieldModal').modal('show');
                })
                .fail(function (xhr) {
                    const message = xhr.responseJSON?.message || 'Không thể tải form cấu hình. Vui lòng thử lại.';
                    if (typeof AlertResponse === 'function') {
                        AlertResponse(message, 'error');
                    } else {
                        alert(message);
                    }
                    console.error('Configuration load failed:', xhr);
                });
        }
    </script>
}
