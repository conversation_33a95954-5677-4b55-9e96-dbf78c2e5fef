using MediatR;
using MiniAppCore.Features.CustomEntity.Handlers;

namespace MiniAppCore.Helpers
{
    public static class CustomFieldExtensions
    {
        public static async Task<Dictionary<string, string>> GetCustomFieldValuesAsync(
            this IMediator mediator, string entityId)
        {
            return await mediator.Send(new GetCustomFieldValuesAsMapQuery { EntityId = entityId });
        }

        public static async Task SaveCustomFieldsAsync(
            this IMediator mediator, string entityId, IFormCollection form)
        {
            var customFieldValues = new Dictionary<string, string>();

            foreach (var key in form.Keys)
            {
                if (key.StartsWith("CustomFields[") && key.EndsWith("]"))
                {
                    var fieldId = key.Substring("CustomFields[".Length, key.Length - "CustomFields[".Length - 1);
                    var value = form[key].ToString();
                    customFieldValues[fieldId] = value;
                }
            }

            if (customFieldValues.Any())
            {
                await mediator.Send(new SaveCustomFieldValuesCommand
                {
                    EntityId = entityId,
                    Values = customFieldValues
                });
            }
        }

        public static async Task<(bool IsValid, List<string> Errors)> ValidateCustomFieldsAsync(
            this IMediator mediator, string entityName, IFormCollection form)
        {
            var customFieldValues = new Dictionary<string, string>();

            foreach (var key in form.Keys)
            {
                if (key.StartsWith("CustomFields[") && key.EndsWith("]"))
                {
                    var fieldId = key.Substring("CustomFields[".Length, key.Length - "CustomFields[".Length - 1);
                    var value = form[key].ToString();
                    customFieldValues[fieldId] = value;
                }
            }

            return await mediator.Send(new ValidateCustomFieldsQuery
            {
                EntityName = entityName,
                Values = customFieldValues
            });
        }

        // Backward compatibility - deprecated
        [Obsolete("Use ValidateCustomFieldsAsync that returns detailed errors")]
        public static async Task<bool> ValidateCustomFieldsAsyncOld(
            this IMediator mediator, string entityName, IFormCollection form)
        {
            var (isValid, _) = await ValidateCustomFieldsAsync(mediator, entityName, form);
            return isValid;
        }
    }
}
