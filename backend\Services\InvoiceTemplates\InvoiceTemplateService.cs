﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.InvoiceTemplates;

namespace MiniAppCore.Services.InvoiceTemplates
{
    public class InvoiceTemplateService(IUnitOfWork unitOfWork, IMapper mapper) : Service<InvoiceTemplate>(unitOfWork), IInvoiceTemplateService
    {
        public override async Task<PagedResult<InvoiceTemplate>> GetPage(IRequestQuery query)
        {
            var invoicteTemplateQuery = _repository.AsQueryable();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                string keyword = query.Keyword;
                invoicteTemplateQuery = invoicteTemplateQuery.Where(b => b.Name.Contains(keyword));
            }

            var totalItems = await invoicteTemplateQuery.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)query.PageSize);
            var items = await invoicteTemplateQuery.OrderByDescending(x => x.CreatedDate)
                                                   .Skip(query.Skip)
                                                   .Take(query.PageSize)
                                                   .ToListAsync();
            return new PagedResult<InvoiceTemplate>()
            {
                Data = items,
                TotalPages = totalPages,
            };
        }

        public async Task<int> CreateAsync(InvoiceTemplateDTO dto)
        {
            var template = mapper.Map<InvoiceTemplate>(dto);

            if (template.IsDefault)
            {
                var defaultTemplate = await _repository.AsQueryable().Where(x => x.IsDefault).ToListAsync();
                defaultTemplate.ForEach(x => x.IsDefault = false);
                _repository.UpdateRange(defaultTemplate);
            }
            return await base.CreateAsync(template);
        }

        public async Task<int> UpdateAsync(string id, InvoiceTemplateDTO dto)
        {
            var template = await GetByIdAsync(id);
            if (template == null)
            {
                throw new CustomException(200, "Không tìm thấy mẫu hóa đơn!");
            }
            if (dto.IsDefault)
            {
                var defaultTemplate = await _repository.AsQueryable().Where(x => x.IsDefault).ToListAsync();
                defaultTemplate.ForEach(x => x.IsDefault = false);
                _repository.UpdateRange(defaultTemplate);
            }
            mapper.Map(dto, template);
            return await base.UpdateAsync(template);
        }

        public async Task<InvoiceTemplate?> GetDefaultTemplate()
        {
            return await _repository.AsQueryable().FirstOrDefaultAsync(x => x.IsDefault);
        }
    }
}
