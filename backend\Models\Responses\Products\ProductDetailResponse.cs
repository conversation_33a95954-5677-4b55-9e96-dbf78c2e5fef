﻿using MiniAppCore.Entities.Categories;
using MiniAppCore.Models.Responses.Categories;

namespace MiniAppCore.Models.Responses.Products
{
    public class ProductDetailResponse : ProductResponse
    {
        public string? Description { get; set; }
        public List<ProductResponse> Gifts { get; set; } = new();
        public List<CategoryResponse> Categories { get; set; } = new();
        public List<CategoryChild> CategorieChilds { get; set; } = new();
        public List<ProductVariantResponse> Options { get; set; } = new();
        public List<ProductVariantDetailResponse> Variants { get; set; } = new();
    }
}
