using MediatR;
using Microsoft.Extensions.Logging;

namespace MiniAppCore.Features.LuckyWheel.Commands
{
    public class AutoAddPointsCommand : IRequest<int>
    {
        public required string LuckyWheelId { get; set; }
    }

    /// <summary>
    /// Handler để xử lý auto add points cho tất cả memberships
    /// <PERSON><PERSON><PERSON><PERSON> sử dụng bởi background jobs (Hangfire)
    /// </summary>
    public class AutoAddPointsHandler(IMediator mediator, ILogger<AutoAddPointsHandler> logger) : IRequestHandler<AutoAddPointsCommand, int>
    {
        public async Task<int> Handle(AutoAddPointsCommand request, CancellationToken cancellationToken)
        {
            try
            {
                logger.LogInformation("Processing AutoAddPointsCommand for LuckyWheel {LuckyWheelId}", request.LuckyWheelId);

                // Sử dụng command mới để cộng điểm cho tất cả memberships
                var addPointsCommand = new AddSpinPointsToMembershipsCommand
                {
                    LuckyWheelId = request.LuckyWheelId,
                    TargetUserZaloId = null // null = cộng cho tất cả
                };

                var result = await mediator.Send(addPointsCommand, cancellationToken);

                logger.LogInformation("AutoAddPointsCommand completed for LuckyWheel {LuckyWheelId}. Updated {UpdatedCount} memberships",
                    request.LuckyWheelId, result);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing AutoAddPointsCommand for LuckyWheel {LuckyWheelId}", request.LuckyWheelId);
                throw;
            }
        }
    }
}
