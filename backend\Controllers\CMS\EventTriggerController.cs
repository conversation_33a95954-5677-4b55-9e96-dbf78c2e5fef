﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Entities.Notifications;
using MiniAppCore.Entities.Notifications.Templates;
using MiniAppCore.Models;
using MiniAppCore.Models.DTOs.SystemSettings;
using MiniAppCore.Models.Requests.OmniTools;
using MiniAppCore.Services.OmniTool.Omni;
using Newtonsoft.Json;
using System.Threading.Tasks;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class EventTriggerController(IUnitOfWork unitOfWork, IOmniService omniService) : Controller
    {
        private readonly IRepository<OmniTemplate> _omniTemplateRepository = unitOfWork.GetRepository<OmniTemplate>();
        private readonly IRepository<EventTriggerSetting> _eventTriggerRepository = unitOfWork.GetRepository<EventTriggerSetting>();
        private readonly IRepository<Entities.ETM.ZaloTemplateUid> _zaloTemplateUidRepository = unitOfWork.GetRepository<Entities.ETM.ZaloTemplateUid>();

        public IActionResult Index()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> GetEventTriggers(int page = 1, int pageSize = 10, string keyword = "")
        {
            try
            {
                var query = _eventTriggerRepository.AsQueryable();

                // Search filter
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(x => x.EventName.Contains(keyword) || x.ReferenceId.Contains(keyword));
                }

                var totalRecords = await query.CountAsync();
                var data = await query
                    .OrderByDescending(x => x.CreatedDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .Select(x => new
                    {
                        id = x.Id,
                        eventName = x.EventName,
                        type = x.Type == 1 ? "Zalo UID" : x.Type == 2 ? "Omni ZNS" : "Email",
                        referenceId = x.ReferenceId,
                        recipients = x.Recipients,
                        isActive = x.IsActive,
                        createdDate = x.CreatedDate
                    })
                    .ToListAsync();

                return Json(new
                {
                    recordsTotal = totalRecords,
                    recordsFiltered = totalRecords,
                    data = data
                });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> LoadForm(string id = "")
        {
            var ets = !string.IsNullOrWhiteSpace(id)
                        ? await _eventTriggerRepository.AsQueryable()
                                .FirstOrDefaultAsync(x => x.Id == id)
                        : new EventTriggerSetting { Recipients = "", IsActive = true, Id = "" };

            return PartialView("Partials/_EventTriggerModal",ets);
        }

        [HttpPost]
        public async Task<IActionResult> Save(EventTriggerSetting model, string templateData)
        {
            try
            {
                //if (!ModelState.IsValid)
                //{
                //    return Json(new { success = false, message = "Dữ liệu không hợp lệ!" });
                //}

                EventTriggerSetting? entity;
                string referenceId = model.ReferenceId;

                if (string.IsNullOrEmpty(model.Id))
                {
                    // Create new
                    entity = new EventTriggerSetting
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        EventName = model.EventName,
                        Type = model.Type,
                        Conditions = model.Conditions,
                        Recipients = model.Recipients,
                        IsActive = model.IsActive,
                        CreatedDate = DateTime.Now
                    };

                    // Xử lý tạo template tương ứng và lấy ReferenceId
                    referenceId = await CreateOrUpdateTemplate(model.Type, templateData, null);
                    entity.ReferenceId = referenceId;

                    _eventTriggerRepository.Add(entity);
                }
                else
                {
                    // Update existing
                    entity = await _eventTriggerRepository.FindByIdAsync(model.Id);
                    if (entity == null)
                    {
                        return Json(new { success = false, message = "Không tìm thấy bản ghi!" });
                    }

                    entity.EventName = model.EventName;
                    entity.Type = model.Type;
                    entity.Conditions = model.Conditions;
                    entity.Recipients = model.Recipients;
                    entity.IsActive = model.IsActive;
                    entity.UpdatedDate = DateTime.Now;

                    // Cập nhật template tương ứng
                    referenceId = await CreateOrUpdateTemplate(model.Type, templateData, entity.ReferenceId);
                    entity.ReferenceId = referenceId;

                    _eventTriggerRepository.Update(entity);
                }

                var result = await unitOfWork.SaveChangesAsync();
                return Json(new
                {
                    success = result > 0,
                    message = result > 0 ? "Lưu thành công!" : "Không thể lưu!"
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Có lỗi xảy ra: {ex.Message}" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetTemplate(int type)
        {
            var listName = new List<Dictionary<string, string>>();
            if (type == 1)
            {
                var uidTemplates = await _zaloTemplateUidRepository.AsQueryable().ToListAsync();
                foreach (var item in uidTemplates)
                {
                    listName.Add(new Dictionary<string, string>()
                    {
                        {"id", item.Id },
                        {"name", $"{item.Name}" }
                    });
                }
            }

            if (type == 2)
            {
                var omniTemplates = await omniService.GetAllOwnedTemplate();
                foreach (var item in omniTemplates.ListTemp)
                {
                    listName.Add(new Dictionary<string, string>()
                    {
                        {"id", item.TemplateCode },
                        { "name", item.TemplateCode }
                    });
                }
            }

            return Ok(listName);
        }

        [HttpGet]
        public async Task<IActionResult> GetTemplateReferrence(int type, string? templateCode, string referrenceId = "")
        {
            var tableParams = new List<MappingParams>();
            // table params UID
            if (type == 1)
            {

                var zaloUidReference = new Entities.ETM.ZaloTemplateConfig() { 
                    TemplateId=string.Empty, 
                    Recipients = string.Empty, 
                    TemplateMapping = string.Empty 
                };
                var listParams = new List<MappingParams>();
                if (!string.IsNullOrEmpty(referrenceId))
                {
                    zaloUidReference = await unitOfWork.GetRepository<Entities.ETM.ZaloTemplateConfig>().FindByIdAsync(referrenceId);
                    if(zaloUidReference != null)
                    {
                        listParams = JsonConvert.DeserializeObject<List<MappingParams>>(zaloUidReference.TemplateMapping);
                    }
                }

                ViewBag.Templates = await _zaloTemplateUidRepository.AsQueryable().Select(x => new { x.Id, x.Name }).ToListAsync();
                return PartialView("Partials/_ZaloConfigPartial", (template: zaloUidReference, tableParams: listParams));
            }

            // table params ZNS
            var omniTemplates = await omniService.GetAllOwnedTemplate();
            var referrenceOmni = new OmniTemplate()
            {
                PhoneNumber = string.Empty,
                RoutingRule = string.Empty,
                TemplateCode = string.Empty,
                TemplateMapping = string.Empty
            };
            if (!string.IsNullOrEmpty(referrenceId))
            {
                referrenceOmni = await _omniTemplateRepository.AsQueryable().FirstOrDefaultAsync(x => x.Id == referrenceId);
                if (referrenceOmni != null && !string.IsNullOrEmpty(referrenceOmni.TemplateMapping))
                {
                    tableParams = JsonConvert.DeserializeObject<List<MappingParams>>(referrenceOmni.TemplateMapping);
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(templateCode))
                {
                    var accountOmni = omniTemplates.ListTemp.FirstOrDefault(x => x.TemplateCode == templateCode);
                }
            }

            ViewBag.Templates = omniTemplates.ListTemp;
            return PartialView($"Partials/_IncomConfigPartial", (referrenceOmni, tableParams));
        }

        [HttpGet]
        public async Task<IActionResult> GetTableParams(int type, string id)
        {
            var tableParams = new List<MappingParams>();

            // table params UID
            if (type == 1)
            {
                var templateUID = await _zaloTemplateUidRepository.AsQueryable().FirstOrDefaultAsync(x => x.Id == id);
                if (templateUID != null && !string.IsNullOrEmpty(templateUID.ListParams))
                {
                    tableParams = templateUID.ListParams.Split(",").Select(x => new MappingParams()
                    {
                        ParamName = x.Trim(),
                        MappingColumnName = string.Empty,
                    }).ToList();
                }
            }

            if (type == 2)
            {
                var omniTemplates = await omniService.GetAllOwnedTemplate();
                if (!string.IsNullOrEmpty(id))
                {
                    var accountOmni = omniTemplates.ListTemp.FirstOrDefault(x => x.TemplateCode == id);
                    tableParams = accountOmni?.ParamsFormat?.Select(x => new MappingParams
                    {
                        ParamName = x.Key,
                        MappingColumnName = string.Empty,
                    }).ToList() ?? new List<MappingParams>();
                }
            }

            return PartialView($"Partials/_TemplateParams", tableParams);
        }

        [HttpPost]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var exist = await _eventTriggerRepository.FindByIdAsync(id);
                if (exist == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy bản ghi!" });
                }

                // Xóa template tương ứng
                if (!string.IsNullOrEmpty(exist.ReferenceId))
                {
                    await DeleteRelatedTemplate(exist.Type, exist.ReferenceId);
                }

                await _eventTriggerRepository.DeleteByIdAsync(id);
                var result = await unitOfWork.SaveChangesAsync();
                return Json(new { success = result > 0, message = result > 0 ? "Xóa thành công!" : "Không thể xóa!" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Có lỗi xảy ra: {ex.Message}" });
            }
        }

        private async Task DeleteRelatedTemplate(int type, string referenceId)
        {
            if (type == 1) // Zalo UID
            {
                await _zaloTemplateUidRepository.DeleteByIdAsync(referenceId);
            }
            else if (type == 2) // Omni Template
            {
                await _omniTemplateRepository.DeleteByIdAsync(referenceId);
            }
        }

        private async Task<string> CreateNewOmniTemplate(OmniTemplateConfig config)
        {
            var template = new OmniTemplate
            {
                Id = Guid.NewGuid().ToString("N"),
                PhoneNumber = config.PhoneNumber,
                RoutingRule = string.Join(",", config.RoutingRules ?? new List<string>()),
                TemplateCode = config.TemplateCode,
                TemplateMapping = JsonConvert.SerializeObject(config.TemplateMapping ?? new List<MappingParams>()),
                CreatedDate = DateTime.Now
            };

            _omniTemplateRepository.Add(template);
            await Task.CompletedTask;
            return template.Id;
        }

        private async Task<string> CreateNewZaloTemplateConfig(ZaloUidConfigRequest zaloUidConfigRequest)
        {
            var zaloUidCofig = new Entities.ETM.ZaloTemplateConfig
            {
                Recipients = string.Join(",", zaloUidConfigRequest.Recipients),
                TemplateId = zaloUidConfigRequest.ZaloTemplateUid,
                TemplateMapping = zaloUidConfigRequest.ParamsConfig.Any() ? JsonConvert.SerializeObject(zaloUidConfigRequest.ParamsConfig) : string.Empty
            };

            unitOfWork.GetRepository<Entities.ETM.ZaloTemplateConfig>().Add(zaloUidCofig);
            await unitOfWork.SaveChangesAsync();
            return zaloUidCofig.Id;
        }

        private async Task<string> CreateOrUpdateTemplate(int type, string templateData, string? existingReferenceId)
        {
            if (type == 1) // Zalo UID
            {
                var templateConfig = JsonConvert.DeserializeObject<ZaloUidConfigRequest>(templateData);
                if (templateConfig == null) throw new ArgumentNullException(nameof(templateConfig));

                Entities.ETM.ZaloTemplateConfig? zaloUidConfig;
                if (!string.IsNullOrEmpty(existingReferenceId))
                {
                    // Update existing
                    zaloUidConfig = await unitOfWork.GetRepository<Entities.ETM.ZaloTemplateConfig>()
                                                    .FindByIdAsync(existingReferenceId);
                    if (zaloUidConfig != null)
                    {
                        zaloUidConfig.TemplateId = templateConfig.ZaloTemplateUid;
                        zaloUidConfig.TemplateMapping = templateConfig.ParamsConfig.Any() 
                                                            ? JsonConvert.SerializeObject(templateConfig.ParamsConfig) 
                                                            : string.Empty;
                        zaloUidConfig.Recipients = string.Join(",", templateConfig.Recipients);
                        zaloUidConfig.UpdatedDate = DateTime.Now;
                        unitOfWork.GetRepository<Entities.ETM.ZaloTemplateConfig>().Update(zaloUidConfig);
                    }
                    else
                    {
                        return await CreateNewZaloTemplateConfig(templateConfig);
                    }
                }
                else
                {
                    return await CreateNewZaloTemplateConfig(templateConfig);
                }

                return zaloUidConfig.Id;
            }
            else if (type == 2) // Omni Template
            {
                var templateConfig = JsonConvert.DeserializeObject<OmniTemplateConfig>(templateData);

                OmniTemplate? template;
                if (!string.IsNullOrEmpty(existingReferenceId))
                {
                    // Update existing
                    template = await _omniTemplateRepository.FindByIdAsync(existingReferenceId);
                    if (template != null)
                    {
                        template.PhoneNumber = templateConfig.PhoneNumber;
                        template.RoutingRule = string.Join(",", templateConfig.RoutingRules ?? new List<string>());
                        template.TemplateCode = templateConfig.TemplateCode;
                        template.TemplateMapping = JsonConvert.SerializeObject(templateConfig.TemplateMapping ?? new List<MappingParams>());
                        template.UpdatedDate = DateTime.Now;
                        _omniTemplateRepository.Update(template);
                    }
                    else
                    {
                        return await CreateNewOmniTemplate(templateConfig);
                    }
                }
                else
                {
                    return await CreateNewOmniTemplate(templateConfig);
                }

                return template.Id;
            }
            else
            {

            }
            return string.Empty; // Invalid type, handle as needed
        }

        #region History

        public IActionResult History()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> GetEventLogs(int draw, int start, int length, string keyword = "", string type = "", string resultCode = "", DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var logRepository = unitOfWork.GetRepository<EventTriggerLog>();
                var query = logRepository.AsQueryable();

                // Search filters
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(x => x.Message.Contains(keyword) || x.Recipient.Contains(keyword));
                }

                if (!string.IsNullOrEmpty(type))
                {
                    query = query.Where(x => x.Type == type);
                }

                if (!string.IsNullOrEmpty(resultCode))
                {
                    query = query.Where(x => x.ResultCode == resultCode);
                }

                if (fromDate.HasValue)
                {
                    query = query.Where(x => x.CreatedDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(x => x.CreatedDate <= toDate.Value.AddDays(1));
                }

                var totalRecords = await query.CountAsync();

                // Apply pagination
                var data = await query
                    .OrderByDescending(x => x.CreatedDate)
                    .Skip(start)
                    .Take(length)
                    .Select(x => new
                    {
                        id = x.Id,
                        type = x.Type,
                        message = x.Message,
                        recipient = x.Recipient,
                        resultCode = x.ResultCode,
                        createdDate = x.CreatedDate,
                        metadata = x.Metadata
                    })
                    .ToListAsync();

                return Json(new
                {
                    Draw = draw,
                    RecordsTotal = totalRecords,
                    RecordsFiltered = totalRecords,
                    Data = data
                });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetDashboardStats(string keyword = "", string type = "", string resultCode = "", DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var logRepository = unitOfWork.GetRepository<EventTriggerLog>();
                var query = logRepository.AsQueryable();

                // Apply same filters as GetEventLogs
                if (!string.IsNullOrEmpty(keyword))
                {
                    query = query.Where(x => x.Message.Contains(keyword) || x.Recipient.Contains(keyword));
                }

                if (!string.IsNullOrEmpty(type))
                {
                    query = query.Where(x => x.Type == type);
                }

                if (!string.IsNullOrEmpty(resultCode))
                {
                    query = query.Where(x => x.ResultCode == resultCode);
                }

                if (fromDate.HasValue)
                {
                    query = query.Where(x => x.CreatedDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(x => x.CreatedDate <= toDate.Value.AddDays(1));
                }

                var totalSent = await query.CountAsync();
                var successCount = await query.Where(x => x.ResultCode == "200" || x.ResultCode == "0").CountAsync();
                var errorCount = totalSent - successCount;

                var typeStats = await query
                    .GroupBy(x => x.Type)
                    .Select(g => new { Type = g.Key, Count = g.Count() })
                    .ToListAsync();

                var dailyStats = await query
                    .Where(x => x.CreatedDate >= DateTime.Now.AddDays(-7))
                    .GroupBy(x => x.CreatedDate.Date)
                    .Select(g => new { Date = g.Key, Count = g.Count() })
                    .OrderBy(x => x.Date)
                    .ToListAsync();

                return Json(new
                {
                    TotalSent = totalSent,
                    SuccessCount = successCount,
                    ErrorCount = errorCount,
                    successRate = totalSent > 0 ? Math.Round((double)successCount / totalSent * 100, 2) : 0,
                    TypeStats = typeStats,
                    DailyStats = dailyStats
                });
            }
            catch (Exception ex)
            {
                return Json(new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> ViewLogDetail(string id)
        {
            try
            {
                var logRepository = unitOfWork.GetRepository<EventTriggerLog>();
                var log = await logRepository.FindByIdAsync(id);

                if (log == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy log!" });
                }

                return PartialView("Partials/_LogDetailModal", log);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        #endregion

        #region Omni Account Configuration
        private readonly IRepository<Common> _commonRepository = unitOfWork.GetRepository<Common>();
        private const string OMNI_ACCOUNT_KEY = "OMNI_ACCOUNT_CONFIG";

        [HttpGet]
        public IActionResult OmniAccount()
        {
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> GetOmniAccountConfig()
        {
            try
            {
                var accountConfig = await _commonRepository.AsQueryable()
                    .FirstOrDefaultAsync(x => x.Name == OMNI_ACCOUNT_KEY);

                OmniAccountDTO omniAccount = new OmniAccountDTO();

                if (accountConfig != null && !string.IsNullOrEmpty(accountConfig.Content))
                {
                    omniAccount = JsonConvert.DeserializeObject<OmniAccountDTO>(accountConfig.Content) ?? new OmniAccountDTO();
                }

                var model = new
                {
                    omniAccount = omniAccount,
                    isConfigured = accountConfig != null
                };

                return Json(new { success = true, data = model });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveOmniAccountConfig(string envUrl, string username, string password)
        {
            try
            {
                if (string.IsNullOrEmpty(envUrl) || string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    return Json(new { success = false, message = "Vui lòng nhập đầy đủ thông tin!" });
                }

                var omniAccount = new OmniAccountDTO
                {
                    EnvUrl = envUrl,
                    Username = username,
                    Password = password
                };

                var accountJson = JsonConvert.SerializeObject(omniAccount);

                var existingConfig = await _commonRepository.AsQueryable()
                    .FirstOrDefaultAsync(x => x.Name == OMNI_ACCOUNT_KEY);

                if (existingConfig == null)
                {
                    // Create new
                    var newConfig = new Common
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        Name = OMNI_ACCOUNT_KEY,
                        Content = accountJson,
                        CreatedDate = DateTime.Now
                    };
                    _commonRepository.Add(newConfig);
                }
                else
                {
                    // Update existing
                    existingConfig.Content = accountJson;
                    existingConfig.UpdatedDate = DateTime.Now;
                    _commonRepository.Update(existingConfig);
                }

                var result = await unitOfWork.SaveChangesAsync();
                return Json(new
                {
                    success = result > 0,
                    message = result > 0 ? "Lưu cấu hình thành công!" : "Không thể lưu cấu hình!"
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Có lỗi xảy ra: {ex.Message}" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> TestOmniConnection()
        {
            try
            {
                var omniAccount = await GetOmniAccountFromConfig();
                if (omniAccount == null)
                {
                    return Json(new { success = false, message = "Chưa cấu hình tài khoản Omni!" });
                }

                // Test connection by getting templates
                var templates = await omniService.GetAllOwnedTemplate(omniAccount);

                return Json(new
                {
                    success = true,
                    message = "Kết nối thành công!",
                    templateCount = templates.ListTemp?.Count ?? 0
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = $"Kết nối thất bại: {ex.Message}" });
            }
        }

        private async Task<OmniAccountDTO?> GetOmniAccountFromConfig()
        {
            var accountConfig = await _commonRepository.AsQueryable()
                .FirstOrDefaultAsync(x => x.Name == OMNI_ACCOUNT_KEY);

            if (accountConfig == null || string.IsNullOrEmpty(accountConfig.Content))
                return null;

            return JsonConvert.DeserializeObject<OmniAccountDTO>(accountConfig.Content);
        }

        #endregion
    }
}
