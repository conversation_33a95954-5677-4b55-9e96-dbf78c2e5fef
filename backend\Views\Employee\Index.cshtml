﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> sách nhân viên</h4>
                <p class="mb-0">
                    Trang tổng quan cung cấp cho bạn cái nhìn tổng quan về danh sách người dùng có quyền truy cập vào dữ liệu quan trọng nhất,<br>
                    chức năng và điều khiển.
                </p>
            </div>
            <button type="button" class="btn btn-primary mt-2" onclick="GetFormEmployee()"><i class="ri-add-line mr-3"></i>Thêm người dùng</button>
        </div>
    </div>

    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center mb-4">
            <div class="col-3">
                <input id="search" type="text" class="form-control" placeholder="Tìm kiếm" />
                <button class="btn btn-search">
                    <i class="ri-search-line"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-employee" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-employee" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<div id="modal-ref" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-dialog-centered">
        <div class="modal-content modal-xl">
            <div class="modal-header">
                <h5 class="modal-title">Người giới thiệu</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="tree-container" class="container-fluid"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                @* <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
                <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('')">Thêm mới</button> *@
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListMembership();

            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#search').on('input', search);
        });

        function GetListMembership() {
            table = new DataTable("#list-employee", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const type = $("#filter-type").val();
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetAllEmployee", "Employees")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword,
                        },
                        success: function (response) {
                            console.log(response)
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: `<div class="d-flex align-items-center">
                                                    <img src="/images/user/11.png" class="img-fluid rounded avatar-50 mr-3" alt="image" />
                                                    <div>
                                                        ${item.displayName}
                                                        <p class="mb-0"><small>${item.phoneNumber}</small></p>
                                                    </div>
                                                 </div>`,
                                2: item.gender == 0 ? "Nam" : "Nữ",
                                3: item.email,
                                4: item.roleName,
                                5: `<div class="">
                                                    <a onclick="GetFormEmployee('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                        <i class="ri-edit-line fs-6 mr-0"></i>
                                                    </a>
                                               <!-- <a onclick="ViewRef('${item.id}')" class="badge bg-success" data-toggle="tooltip" data-placement="top" title="Xem người giới thiệu">
                                                        <i class="ri-team-line mr-0"></i>
                                                    </a> -->
                                                    <a onclick="DeleteEmployee('${item.id}')" class="badge bg-danger" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                        <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                    </a>
                                                </div>`
                            }));

                            const { pageNumber, pageSize, filteredItems, totalItems } = response;
                            const pagination = filteredItems > pageSize ? filteredItems : (filteredItems < pageSize ? 1 : totalItems);

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: pagination,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Thông tin nhân viên", data: 1 },
                    { title: "Giới tính", data: 2 },
                    { title: "Email", data: 3 },
                    { title: "Vai trò", data: 4 },
                    { title: "Thao tác", data: 5, className: "text-center" }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormEmployee(id) {
            const url = id ? `/User/Employee/${id}` : "@Url.Action("CreateEmployee", "User")"

            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-employee").modal("toggle");
                }
            })
        }

        function DeleteEmployee(id) {
            const url = `/api/employees/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const branchIds = $("#branches").val();
            if (!branchIds) {
                AlertResponse("Vui lòng chọn ít nhất 1 chi nhánh!", "warning");
                return;
            }

            const email = $("#email").val()?.trim();
            const phoneNumber = $("#phoneNumber").val()?.trim();
            const displayName = $("#displayName").val()?.trim();
            const password = $("#password").val()?.trim();
            const roleName = $("#role").val()?.trim().toUpperCase();
            const roleIds = $("#roles").val() || [];

            if (!validateEmail(email)) {
                AlertResponse("Email không hợp lệ!");
                return;
            }

            if (!phoneNumber || phoneNumber.length < 10) {
                AlertResponse("Số điện thoại không hợp lệ!");
                return;
            }

            if (!id && !password) { // Nếu đang tạo mới thì bắt buộc có mật khẩu
                AlertResponse("Vui lòng nhập mật khẩu!");
                return;
            }

            const data = {
                email,
                phoneNumber,
                displayName,
                password,
                roleName,
                roleIds,
                branchIds: [branchIds],
                isChangePassword: window.isChangePassword
            };

            // Xác định URL và phương thức
            const url = id ? `/api/employees/${id}` : '/api/employees';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                    }
                    $("#modal-employee").modal("toggle");
                },
                error: function () {
                    AlertResponse("Lỗi máy chủ", 'error');
                }
            });
        }

        function ViewRef(id) {
            $.ajax({
                url: `/api/memberships/tree/${id}`,
                type: 'GET',
                dataType: 'json',
                success: function (response) {
                    $("#tree-container").html(createTree(response.data));
                    $("#modal-ref").modal('toggle');
                }
            });

            function createTree(data) {
                if (!data) return '';

                const hasChildren = data.refChild && data.refChild.length > 0;
                const collapseId = `collapse-${data.parentId}`;

                let html = `<li>
                                <a href="#${collapseId}" class="${hasChildren ? 'collapsed' : ''}" data-bs-toggle="collapse" aria-expanded="false">
                                <span class="ml-4">${data.displayName} (${data.phoneNumber})</span>
                            </a>`;

                if (hasChildren) {
                    html += `<ul id="${collapseId}" class="iq-submenu collapse" data-bs-parent="#${collapseId}">`;
                    for (let child of data.refChild) {
                        html += createTree(child);
                    }
                    html += '</ul>';
                }

                html += '</li>';

                return html;
            }
        }
    </script>
}
