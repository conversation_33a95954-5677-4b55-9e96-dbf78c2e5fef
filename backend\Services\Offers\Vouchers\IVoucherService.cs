﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Offers.Vouchers;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Enums;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Models.Responses.Vouchers;

namespace MiniAppCore.Services.Offers.Vouchers
{
    public interface IVoucherService : IService<Voucher>
    {
        Task<int> Redeem(string UserZaloId, List<string> voucherIds);

        Task<VoucherResponse> GetVoucherByIdAsync(string id);
        Task<VoucherDetailResponse> GetVoucherDetailResponseByIdAsync(string id);

        Task<PagedResult<VoucherResponse>> GetPage(VoucherQueryParams queryParams, bool? isActive = null, bool? isExchange = null, string? userZaloId = null);

        Task<int> CreateAsync(VoucherRequest model);
        Task<int> UpdateAsync(string id, VoucherRequest model);
        Task<int> UpdateMembershipVoucherByOrderId(string orderId, string userZaloId, EOffers status);
        Task<IEnumerable<string>> SendGift(List<string> userZaloIds, List<string> vouchersIds);
        Task<IEnumerable<string>> SendGiftByTags(List<string> tagIds, List<string> voucherIds);
        Task<IEnumerable<string>> SendGiftWithOwnershipLimit(List<string> userZaloIds, List<string> voucherIds);

        // Tính toán voucher
        decimal ApplyOrderVouchers(Order order, List<Voucher> vouchers, List<OrderVoucher> appliedVoucher);
        decimal ApplyShippingVouchers(Order order, List<Voucher> vouchers, List<OrderVoucher> appliedVoucher);
        decimal ApplyProductVouchers(OrderDetail detail, List<Voucher> vouchers, List<OrderVoucher> appliedVoucher);

        Task<IEnumerable<Voucher>> GetVoucherByCode(List<string> codes);
        Task<(List<Voucher> productVouchers, List<Voucher> shippingVouchers, List<Voucher> orderVouchers)> CategorizeVouchers(List<string> voucherCodes, string userZaloId);
        Task<Dictionary<string, List<string>>> GetAppliedProductsForVouchersAsync(List<Voucher> productVouchers, List<string> productIds);

        Task<byte[]> ExportVouchersToExcelAsync(VoucherQueryParams queryParams = null);
        Task<(int importCount, List<string> messages)> ImportVouchersAsync(IFormFile file);
    }
}