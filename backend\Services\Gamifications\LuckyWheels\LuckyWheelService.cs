﻿using AutoMapper;
using Hangfire;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.LuckyWheels;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Features.LuckyWheel.Commands;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.LuckyWheels;
using MiniAppCore.Models.Requests.LuckyWheels;
using MiniAppCore.Models.Responses.Gamification.GamePrizes;
using MiniAppCore.Models.Responses.Gamification.LuckyWheels;
using MiniAppCore.Services.Gamifications.GamePrizes;
using MiniAppCore.Services.Memberships;

namespace MiniAppCore.Services.Gamifications.LuckyWheels
{
    public class LuckyWheelService(IUnitOfWork unitOfWork,
                                   IMapper mapper,
                                   IWebHostEnvironment env,
                                   IConfiguration configuration,
                                   IGamePrizeService gamePrizeService,
                                   IMembershipService membershipService,
                                   ILuckyWheelJobManagementService jobManagementService) : Service<Entities.LuckyWheels.LuckyWheel>(unitOfWork), ILuckyWheelService
    {
        private readonly Random _random = new Random();
        private readonly IRepository<SpinHistory> _spinHistoryRepo = unitOfWork.GetRepository<SpinHistory>();
        private readonly IRepository<Membership> _membershipRepo = unitOfWork.GetRepository<Membership>();

        #region Play lucky wheel & Claim reward

        public async Task<ClaimRewardResponse> GetClaimStatusReward(string userZaloId, string historyId)
        {
            var history = await _spinHistoryRepo.AsQueryable().FirstOrDefaultAsync(x => historyId == x.Id && x.UserZaloId == userZaloId);
            if (history == null)
            {
                throw new CustomException(200, $"Không tìm thấy lịch sử quay thưởng với ID: {historyId}");
            }
            if (string.IsNullOrEmpty(history.PrizeId))
            {
                throw new CustomException(200, $"Người chơi không trúng thưởng.");
            }
            var gamePrize = await unitOfWork.GetRepository<GamePrize>().FindByIdAsync(history.PrizeId);
            return mapper.Map<ClaimRewardResponse>((gamePrize, history));
        }

        public async Task<(Membership, SpinHistory, ClaimRewardResponse)> GetClaimStatusReward(string historyId)
        {
            var history = await _spinHistoryRepo.AsQueryable().FirstOrDefaultAsync(x => historyId == x.Id);
            if (history == null)
            {
                throw new CustomException(200, $"Không tìm thấy lịch sử quay thưởng với ID: {historyId}");
            }
            if (string.IsNullOrEmpty(history.PrizeId))
            {
                throw new CustomException(200, $"Người chơi không trúng thưởng.");
            }
            var membership = await membershipService.GetByUserZaloId(history.UserZaloId);
            if (membership == null)
            {
                return default;
            }
            var gamePrize = await unitOfWork.GetRepository<GamePrize>().FindByIdAsync(history.PrizeId);
            var claimRewardResponse = mapper.Map<ClaimRewardResponse>((gamePrize, history));
            return (membership, history, claimRewardResponse);
        }

        public async Task<int> UpdateClaimHistoryStatus(string historyId, ETransaction status)
        {
            var history = await _spinHistoryRepo.AsQueryable().FirstOrDefaultAsync(x => historyId == x.Id);
            if (history == null)
            {
                throw new CustomException(200, $"Không tìm thấy lịch sử quay thưởng với ID: {historyId}");
            }
            if (string.IsNullOrEmpty(history.PrizeId))
            {
                throw new CustomException(200, $"Người chơi không trúng thưởng.");
            }
            history.ClaimStatatus = status;
            return await unitOfWork.SaveChangesAsync();

        }

        public async Task<ClaimRewardResponse> RegisterClaimReward(string userZaloId, string historyId, RegisterRewardRequest registerRequest)
        {
            var history = await _spinHistoryRepo.AsQueryable().FirstOrDefaultAsync(x => x.UserZaloId == userZaloId && historyId == x.Id);
            if (history == null)
            {
                throw new CustomException(200, $"Không tìm thấy lịch sử quay thưởng với ID: {historyId}");
            }

            if (string.IsNullOrEmpty(history.PrizeId))
            {
                throw new CustomException(200, $"Người chơi không trúng thưởng.");
            }

            history.Note = registerRequest.Note;
            history.RecipientName = registerRequest.Name;
            history.PhoneNumber = registerRequest.PhoneNumber;
            history.RecipientAddress = registerRequest.Address;
            history.ClaimStatatus = Enums.ETransaction.Pending;
            await unitOfWork.SaveChangesAsync();

            var gamePrize = await gamePrizeService.GetByIdAsync(history.PrizeId);
            return mapper.Map<ClaimRewardResponse>((gamePrize, history));
        }

        public async Task<SpinResultResponse> PlayLuckyWheel(string userZaloId, string luckyWheelId, bool isTest)
        {
            var membership = await membershipService.GetByUserZaloId(userZaloId);
            if (membership == null)
            {
                throw new CustomException(200, $"Không tìm thấy khách hàng với Zalo Id: {userZaloId}");
            }

            var luckyWheel = await GetByIdAsync(luckyWheelId);
            if (luckyWheel == null)
            {
                throw new CustomException(200, $"Chương trình chưa bắt đầu hoặc đã kết thúc.");
            }

            ConfiguredGamePrizeDTO? prizeWon = null;
            if (CheckUserSpinCondition(membership, isTest)) // (await CheckUserSpinCondition(membership, isTest))
            {
                return await HandleSpinResult(userZaloId, luckyWheelId, prizeWon, isTest);
            }

            var gamePrizes = await gamePrizeService.GetGamePrizesConfiguredByGameId(luckyWheelId);
            var totalWinrate = gamePrizes.DistinctBy(x => x.Id).Sum(x => x.WinRate);
            var randomizedPrizes = new List<ConfiguredGamePrizeDTO?>();

            // xử lí random phần thưởng
            foreach (var prize in gamePrizes)
            {
                for (int i = 1; i <= prize.WinRate; i++)
                {
                    randomizedPrizes.Add(prize);
                }
            }

            for (int i = 1; i <= 100 - totalWinrate; i++)
            {
                randomizedPrizes.Add(null);
            }

            var skipper = _random.Next(0, randomizedPrizes.Count());
            var shuffle = randomizedPrizes.OrderBy(_ => Guid.NewGuid()).ToList();
            prizeWon = shuffle.Skip(skipper).Take(1).FirstOrDefault();
            var result = await HandleSpinResult(userZaloId, luckyWheelId, prizeWon, isTest);

            membership.SpinPoint -= luckyWheel.RequiredPoints;
            await membershipService.UpdateAsync(membership);

            return result;
        }

        private bool CheckUserSpinCondition(Membership membership, bool isTest)
        {
            if (isTest) return false;
            var userZaloId = membership.UserZaloId;
            var forceLose = configuration.GetSection("GameSettings:ForceLose").Get<bool>();

            // số lần trúng thưởng của người chơi đó trong tuần. Tối đã 2 lần 1 tuần
            //var countWeek = await _spinHistoryRepo.AsQueryable().AsNoTracking()
            //                                 .Where(x => x.UserZaloId == userZaloId
            //                                   && !string.IsNullOrEmpty(x.LuckyPrizeId)
            //                                   && x.CreatedDate >= startOfWeek
            //                                   && x.CreatedDate < endOfWeek)
            //                                 .Take(3) // chỉ cần quan tâm tới 2 kết quả
            //                                 .CountAsync();

            // kiểm tra OA có chính xác ko
            // var common = await _commonRepository.AsQueryable().FirstOrDefaultAsync(x => x.Type == "AccessToken");
            // var checkUserZaloIdByOa = await ValidationUserZalo.CheckUserZaloIdByOA(common?.Content, membership?.UserZaloIdByOA);

            return forceLose; // || countWeek >= 2);  || !checkUserZaloIdByOa);
        }

        private async Task<SpinResultResponse> HandleSpinResult(string userZaloId, string luckyWheelId, ConfiguredGamePrizeDTO? prizeWon, bool isTest)
        {
            var spinResult = new SpinResultResponse();

            if (prizeWon != null)
            {
                spinResult.Id = prizeWon.Id;
                spinResult.Name = prizeWon.Name;
                spinResult.Type = prizeWon.Type;
                spinResult.ImageUrl = prizeWon.ImageUrl;
                spinResult.HasWon = prizeWon.Type != Enums.EPrizeType.None;

                // Nếu có phần thưởng và không phải chế độ test => cập nhật số lượng còn lại
                if (!isTest && prizeWon.Quantity > 0)
                {
                    var prizeEntity = await unitOfWork.GetRepository<GamePrizeConfig>().FindByIdAsync(prizeWon.Id);
                    if (prizeEntity != null && prizeEntity.Quantity > 0)
                    {
                        prizeEntity.Quantity--;
                        unitOfWork.GetRepository<GamePrizeConfig>().Update(prizeEntity);
                    }
                }
            }
            else
            {
                spinResult.Id = "645200ce8d8f4384a8347bb493689e65";
                spinResult.HasWon = false;
                spinResult.Name = "Chúc bạn may mắn lần sau";
                spinResult.ImageUrl = "lose.png";
                spinResult.Type = EPrizeType.None;
            }

            if (!isTest)
            {
                var spinHistory = new SpinHistory
                {
                    UserZaloId = userZaloId,
                    LuckyWheelId = luckyWheelId,
                    PrizeId = prizeWon?.Id,
                    IsWon = spinResult.HasWon,
                    PrizeType = prizeWon?.Type ?? 0,
                };

                _spinHistoryRepo.Add(spinHistory);
                await unitOfWork.SaveChangesAsync();
            }

            return spinResult;
        }

        #endregion

        #region CRUD 

        public async Task<int> CreateAsync(LuckyWheelDTO dto)
        {
            // Sử dụng mapper để chuyển từ DTO sang entity
            var luckyWheel = mapper.Map<Entities.LuckyWheels.LuckyWheel>(dto);

            // Xử lý hình ảnh nếu có
            if (dto.Image != null)
            {
                // Sử dụng phương thức ProcessUpload đã có
                var files = new List<IFormFile> { dto.Image };
                luckyWheel.Image = await ProcessUpload(files);
            }

            // Thêm LuckyWheel vào DB
            _repository.Add(luckyWheel);

            // Thêm GamePrizeConfig nếu có
            if (dto.GamePrizes?.Any() == true)
            {
                // Sử dụng mapper để chuyển từ DTO sang entity
                var prizeConfigs = mapper.Map<List<GamePrizeConfigDTO>>(dto.GamePrizes);
                await gamePrizeService.CreateListGamePrizeConfigAsync(luckyWheel.Id, prizeConfigs);
            }

            var result = await unitOfWork.SaveChangesAsync();

            // Thiết lập job Hangfire cho auto add spin points nếu được bật
            if (result > 0 && luckyWheel.EnableAutoSpinPoints)
            {
                jobManagementService.ManageAutoSpinPointsJob(luckyWheel.Id, true);
            }

            return result;
        }

        public async Task<int> UpdateAsync(string id, LuckyWheelDTO dto)
        {
            // 1. Lấy LuckyWheel cần cập nhật
            var luckyWheel = await _repository.FindByIdAsync(id);
            if (luckyWheel == null)
                throw new CustomException($"Không tìm thấy vòng quay với ID: {id}");

            // Lưu trạng thái cũ để so sánh
            var oldEnableAutoSpinPoints = luckyWheel.EnableAutoSpinPoints;

            // 2. Cập nhật thông tin LuckyWheel sử dụng mapper
            mapper.Map(dto, luckyWheel);

            // 3. Cập nhật hình ảnh nếu có
            if (dto.Image != null)
            {
                // Xóa ảnh cũ nếu có
                if (!string.IsNullOrEmpty(luckyWheel.Image))
                {
                    RemoveOldImage(luckyWheel.Image);
                }

                // Upload ảnh mới
                var files = new List<IFormFile> { dto.Image };
                luckyWheel.Image = await ProcessUpload(files);
            }

            // 4. Cập nhật LuckyWheel trong DB
            _repository.Update(luckyWheel);

            // 5. Cập nhật GamePrizeConfig
            if (dto.GamePrizes?.Any() == true)
            {
                // Xóa các config cũ
                await gamePrizeService.DeleteGamePrizeConfigsByGameId(id);

                // Thêm các config mới
                var prizeConfigs = mapper.Map<List<GamePrizeConfigDTO>>(dto.GamePrizes);
                await gamePrizeService.CreateListGamePrizeConfigAsync(luckyWheel.Id, prizeConfigs);
            }

            var result = await unitOfWork.SaveChangesAsync();

            // 6. Quản lý job Hangfire cho auto add spin points
            if (result > 0)
            {
                // Nếu có thay đổi về EnableAutoSpinPoints
                if (oldEnableAutoSpinPoints != luckyWheel.EnableAutoSpinPoints)
                {
                    jobManagementService.ManageAutoSpinPointsJob(luckyWheel.Id, luckyWheel.EnableAutoSpinPoints);
                }
            }

            return result;
        }

        public async Task<LuckyWheelDTO> GetLuckyWheelDetailById(string luckyWheelId)
        {
            var luckyWheel = await _repository.FindByIdAsync(luckyWheelId);
            if (luckyWheel == null)
            {
                throw new CustomException(200, $"Không tìm thấy vòng quay với ID: {luckyWheelId}");
            }

            var prizeConfigs = await gamePrizeService.GetGameConfiguredPrizesByGameId(luckyWheelId);

            // Sử dụng mapper để chuyển đổi
            var dto = mapper.Map<LuckyWheelDTO>(luckyWheel);
            dto.GamePrizes = prizeConfigs; // mapper.Map<List<GamePrizeConfigDTO>>(prizeConfigs);
            dto.ImageUrl = luckyWheel.Image; // Gán URL hình ảnh cho DTO

            return dto;
        }

        public async Task<LuckyWheelResponse> GetActiveLuckyWheel(string userZaloId, string luckyWheelId)
        {
            var membership = await membershipService.GetByUserZaloId(userZaloId);
            if (membership == null)
            {
                throw new CustomException(200, $"Không tìm thấy người dùng có Zalo Id: {userZaloId}");
            }

            var now = DateTime.Now;
            var luckyWheel = await _repository.AsQueryable()
                .Where(x => x.Id == luckyWheelId && x.IsActive && x.StartDate <= now && x.ExpiryDate >= now)
                .FirstOrDefaultAsync();

            if (luckyWheel == null)
            {
                throw new CustomException(404, $"Không tìm thấy vòng quay hoạt động với ID: {luckyWheelId}");
            }

            var prizeConfigs = await gamePrizeService.GetGamePrizesByGameId(luckyWheelId);

            // Sử dụng mapper để chuyển đổi
            var response = mapper.Map<LuckyWheelResponse>(luckyWheel);
            response.Prizes = mapper.Map<List<GamePrizeResponse>>(prizeConfigs);
            response.RemainingSpins = luckyWheel.RequiredPoints == 0 ? 0 : (long)Math.Ceiling((double)membership.SpinPoint / luckyWheel.RequiredPoints);

            return response;
        }

        public async Task<PagedResult<SpinHistoryResult>> GetSpinHistory(RequestQuery query, bool isAdmin, string? userZaloId, bool? resultType, short? type, DateTime? startDate, DateTime? endDate, string? luckyWheelId)
        {
            var gamePrizeRepo = unitOfWork.GetRepository<GamePrize>().AsQueryable().AsNoTracking();
            var membershipRepo = unitOfWork.GetRepository<Membership>().AsQueryable().AsNoTracking();
            var spinHistories = _spinHistoryRepo.AsQueryable().AsNoTracking();

            // Lọc theo kết quả thắng/thua
            if (resultType.HasValue)
            {
                spinHistories = resultType.Value
                    ? spinHistories.Where(x => !string.IsNullOrEmpty(x.PrizeId) && x.PrizeType != EPrizeType.None)
                    : spinHistories.Where(x => string.IsNullOrEmpty(x.PrizeId) && x.PrizeType != EPrizeType.None);
            }

            // Lọc theo loại giải thưởng
            if (type.HasValue)
            {
                var filteredPrizeIds = gamePrizeRepo
                    .Where(p => p.Type == (Enums.EPrizeType)type)
                    .Select(p => p.Id);

                spinHistories = spinHistories
                    .Where(spin => filteredPrizeIds.Contains(spin.PrizeId));
            }

            // Lọc theo từ khóa tìm kiếm trên Membership
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                var keyword = query.Keyword;

                var filteredMemberships = membershipRepo
                    .Where(m =>
                        m.UserZaloId.Contains(keyword) ||
                        m.PhoneNumber.Contains(keyword) ||
                        m.UserZaloName.Contains(keyword))
                    .Select(m => m.UserZaloId);

                spinHistories = spinHistories
                    .Where(spin => filteredMemberships.Contains(spin.UserZaloId));
            }

            if (!string.IsNullOrEmpty(userZaloId))
            {
                spinHistories = spinHistories
                    .Where(spin => spin.UserZaloId == userZaloId);
            }

            // Lọc theo ngày
            if (startDate.HasValue)
            {
                spinHistories = spinHistories.Where(spin => spin.CreatedDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                spinHistories = spinHistories.Where(spin => spin.CreatedDate <= endDate.Value);
            }

            if (!string.IsNullOrEmpty(luckyWheelId))
            {
                spinHistories = spinHistories.Where(spin => spin.LuckyWheelId == luckyWheelId);
            }

            var totalRecords = await spinHistories.CountAsync();
            var totalPages = (int)Math.Ceiling(totalRecords / (double)query.PageSize);

            var memberships = membershipRepo.AsQueryable()
                                            .AsNoTracking()
                                            .Where(x => spinHistories.Select(sp => sp.UserZaloId).Contains(x.UserZaloId));
            // Dữ liệu trả về
            var items = await spinHistories
                .OrderByDescending(spin => spin.CreatedDate)
                .Skip(query.Skip)
                .Take(query.PageSize)
                .Select(spin => new SpinHistoryResult
                {
                    HistoryId = spin.Id,
                    UserZaloId = spin.UserZaloId,
                    Avatar = memberships
                        .Where(m => m.UserZaloId == spin.UserZaloId)
                        .Select(m => m.Avatar)
                        .FirstOrDefault(),
                    PhoneNumber = memberships
                        .Where(m => m.UserZaloId == spin.UserZaloId)
                        .Select(m => m.PhoneNumber)
                        .FirstOrDefault(),
                    UserZaloName = memberships
                        .Where(m => m.UserZaloId == spin.UserZaloId)
                        .Select(m => m.UserZaloName)
                        .FirstOrDefault(),

                    Type = spin.PrizeType,
                    PrizeId = spin.PrizeId ?? "645200ce8d8f4384a8347bb493689e65",
                    PrizeName = gamePrizeRepo
                        .Where(p => p.Id == spin.PrizeId)
                        .Select(p => p.Name)
                        .FirstOrDefault() ?? "Chúc bạn may mắn lần sau",
                    IsAdmin = isAdmin,
                    HasWon = spin.PrizeType != EPrizeType.None,
                    CreatedDate = spin.CreatedDate,
                    RequestStatus = spin.ClaimStatatus,
                    WonDate = spin.CreatedDate
                })
                .ToListAsync();

            return new PagedResult<SpinHistoryResult>
            {
                Data = items,
                TotalPages = totalPages
            };
        }

        #endregion

        #region Handle Image

        private async Task<string> ProcessUpload(List<IFormFile>? files)
        {
            var stringFiles = string.Empty;
            if (files != null && files.Any())
            {
                var savePath = Path.Combine(env.WebRootPath, "uploads/images/luckyWheels");
                var fileResult = await FileHandler.SaveFiles(files, savePath);
                stringFiles = string.Join(",", fileResult);
            }
            return stringFiles;
        }

        private void RemoveOldImage(string listImage)
        {
            if (!string.IsNullOrEmpty(listImage))
            {
                var images = listImage.Split(",").Select(x => Path.Combine(env.WebRootPath, "uploads/images/luckyWheels", x)).ToList();
                FileHandler.RemoveFiles(images);
            }
        }

        #endregion

        #region Override Methods

        public override async Task<int> DeleteByIdAsync(string id)
        {
            // Xóa job Hangfire trước khi xóa LuckyWheel
            jobManagementService.ManageAutoSpinPointsJob(id, false);

            return await base.DeleteByIdAsync(id);
        }

        #endregion
    }
}
