@using Newtonsoft.Json

<script>
    // =================== HandleViewOrders=====================

    function ViewOrders(customerId, phoneNumber) {
        // Reset modal content
        $("#orders-loading").show();
        $("#customer-orders").addClass("d-none");
        $("#no-orders").addClass("d-none");

        // Show modal
        $("#modal-orders").modal("show");

        // Set customer info in modal
        $("#customer-name").text(phoneNumber);

        // Fetch orders data
        $.ajax({
            url: "/api/Orders/by-phone",
            method: "GET",
            data: { phoneNumber: phoneNumber },
            success: function (response) {
                $("#orders-loading").hide();
                $("#customer-orders").removeClass("d-none");

                if (response && response.data && response.data.length > 0) {
                    renderOrdersTable(response.data);
                } else {
                    $("#customer-orders").addClass("d-none");
                    $("#no-orders").removeClass("d-none");
                }
            },
            error: function (error) {
                $("#orders-loading").hide();
                $("#customer-orders").addClass("d-none");
                $("#no-orders").removeClass("d-none").text("Đã xảy ra lỗi khi tải dữ liệu đơn hàng.");
                console.error("Error loading orders:", error);
            }
        });
    }

    // Hàm render bảng đơn hàng
    function renderOrdersTable(orders) {
        const tableBody = $("#orders-data");
        tableBody.empty();

        orders.forEach((order, index) => {
            // Tạo row cho bảng
            const row = `
                    <tr>
                        <td class="text-center">${index + 1}</td>
                        <td class="text-center">${order.orderId}</td>
                        <td class="text-center">${FormatDateTime(order.createdDate)}</td>
                        <td class="text-center">${order.totalAmount ? order.totalAmount?.toLocaleString("vi-VN") : "0"} VNĐ</td>
                        <td class="text-center">${orderStatusMapping[order.orderStatus] || "Unknown"}</td>
                        <td class="text-center">${paymentStatusMapping[order.paymentStatus] || "Unknown"}</td>
                        <td class="text-center">
                            <a href="/Order?id=${order.orderId}" class="badge badge-info" target="_blank" data-toggle="tooltip" title="Xem chi tiết">
                                <i class="ri-eye-line mr-0"></i>
                            </a>
                        </td>
                    </tr>
                `;

            tableBody.append(row);
        });
    }
</script>
