﻿namespace MiniAppCore.Models.Responses.Orders
{
    public class OrderResponse
    {
        public bool IsRating { get; set; }

        public int OrderStatus { get; set; }
        public int PaymentStatus { get; set; }

        public required string OrderId { get; set; }
        public string? Note { get; set; }
        public string? Address { get; set; }
        public string? NameReciver { get; set; } //Tên người nhận
        public string? PhoneReciver { get; set; } //sđt người nhận
        public decimal ShippingFee { get; set; }
        public string? PaymentMethod { get; set; }

        public decimal TotalAmount { get; set; } // Tổng tiền đơn hàng
        public decimal DiscountAmount { get; set; } // Tổng số tiền đã giảm

        public string? UserZaloId { get; set; }
        public string? PhoneNumber { get; set; } // Số điện thoại người đặt
        public string? UserZaloName { get; set; } //Tên người đặt

        public DateTime CreatedDate { get; set; }

        public decimal SubTotalAmount => TotalAmount + DiscountAmount - ShippingFee; // Tổng tiền hàng (không bao gồm phí vận chuyển và giảm giá)
    }
}
