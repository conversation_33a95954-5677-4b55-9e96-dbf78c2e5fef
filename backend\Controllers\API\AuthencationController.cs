﻿using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Exceptions;
using MiniAppCore.Services.Authencation;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthencationController(ILogger<AuthencationController> logger, IAuthencationService authencationService, IConfiguration configuration) : ControllerBase
    {
        [HttpPost("miniapp-auth")]
        public async Task<IActionResult> MiniAppAuthencation([FromForm] string UserZaloId, [FromForm] string phoneNumber)
        {
            try
            {
                var accessToken = await authencationService.LoginMiniApp(phoneNumber, UserZaloId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thanh cong!",
                    Token = accessToken
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("cms-auth")]
        public async Task<IActionResult> CmsAuthencation([FromForm] string username, [FromForm] string password)
        {
            try
            {
                var claims = await authencationService.GetClaimsCmsAsync(username, password);
                var accessToken = JwtGenerator.GenerateJwtToken(configuration, claims); ;

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Token = accessToken
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }
    }
}
