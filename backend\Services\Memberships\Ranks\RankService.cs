﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Settings;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Ranks;
using MiniAppCore.Models.Responses.Memberships;
using MiniAppCore.Models.Responses.Ranks;

namespace MiniAppCore.Services.Memberships.Ranks
{
    public class RankService(IUnitOfWork unitOfWork, IMapper mapper, IWebHostEnvironment env, IHttpContextAccessor httpContextAccessor, IMembershipService membershipService) : Service<Rank>(unitOfWork), IRankService
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public async Task<int> CreateAsync(RankRequest request)
        {
            var rank = mapper.Map<Rank>(request);
            rank.Description = request.Description;
            if (request.Description == "_")
            {
                rank.Description = "";
            }

            if (request.IsDefault)
            {
                await EnsureSingleDefaultAsync();
            }

            if (request.Images.Any())
            {
                rank.Image = await ProcessUpload(request.Images);
            }
            return await base.CreateAsync(rank);
        }

        public async Task<int> UpdateAsync(string id, RankRequest model)
        {
            var rank = await GetByIdAsync(id);
            if (rank == null)
            {
                throw new CustomException(200, "Category not found.");
            }

            bool wasDefault = rank.IsDefault;
            mapper.Map(model, rank);

            if (model.IsDefault && !wasDefault)
            {
                await EnsureSingleDefaultAsync(rank.Id);
            }

            if (model.Images.Any())
            {
                string newFiles = await ProcessUpload(model.Images);

                if (!string.IsNullOrEmpty(newFiles))
                {
                    if (!string.IsNullOrEmpty(rank.Image))
                    {
                        var existingImages = rank.Image.Split(",").ToList();
                        var newImageFiles = newFiles.Split(",").ToList();

                        existingImages.AddRange(newImageFiles);

                        rank.Image = string.Join(",", existingImages.Distinct());
                    }
                    else
                    {
                        rank.Image = newFiles;
                    }
                }
            }

            if (model.RemovedOldImages.Any())
            {
                RemoveOldImage(string.Join(",", model.RemovedOldImages));

                if (!string.IsNullOrEmpty(rank.Image))
                {
                    var remainingImages = rank.Image.Split(",")
                    .Where(image => !model.RemovedOldImages.Contains(image))
                    .ToList();
                    rank.Image = string.Join(",", remainingImages);
                }
            }

            return await base.UpdateAsync(rank);
        }

        public async Task<int> UpdateMembershipRank(string userZaloId)
        {
            var membership = await membershipService.GetByUserZaloId(userZaloId);
            if (membership == null)
            {
                return 0;
            }

            var updatedRank = await _repository.AsQueryable().FirstOrDefaultAsync(x => x.RankingPoint >= membership.RankingPoint);
            if (updatedRank != null)
            {
                membership.RankingId = updatedRank.Id;
            }
            return await membershipService.UpdateAsync(membership);
        }

        public Task<Rank?> GetRankDefault()
        {
            return _repository.AsQueryable().FirstOrDefaultAsync(x => x.IsDefault);
        }

        public async Task<PagedResult<RankResponse>> GetPage(RequestQuery query)
        {
            var categories = _repository.AsQueryable();
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                categories = categories.Where(x => x.Name.Contains(query.Keyword));
            }

            var totalItems = await categories.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);
            var listCategories = await categories.OrderBy(x => x.CreatedDate).Skip(query.Skip).Take(query.PageSize).ToListAsync();

            // Ánh xạ dữ liệu và xử lý Images
            var items = listCategories.Select(p => new RankResponse
            {
                Id = p.Id,
                Name = p.Name,
                IsActive = p.IsActive,
                IsDefault = p.IsDefault,
                Description = p.Description,
                RankingPoint = p.RankingPoint,
                Images = string.IsNullOrEmpty(p.Image)
                    ? new List<string>()
                    : p.Image.Split(',').Select(x => $"{hostUrl}/uploads/images/ranks/{x}").ToList()
            }).ToList();

            return new PagedResult<RankResponse>()
            {
                Data = items,
                TotalPages = totalPages
            };
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var existingRanks = await GetByIdAsync(id);
            if (existingRanks == null)
            {
                throw new CustomException(200, "Category not found");
            }

            // remove image
            if (!string.IsNullOrEmpty(existingRanks.Image))
            {
                RemoveOldImage(existingRanks.Image);
            }

            return await base.DeleteAsync(existingRanks);
        }

        public async Task<MembershipRankResponse?> GetMembershipRankResponseAsync(string rankId, long currentPoints)
        {
            // Lấy hạng thành viên hiện tại từ cơ sở dữ liệu
            var currentRank = await GetByIdAsync(rankId);
            if (currentRank == null)
            {
                return null; // Không tìm thấy hạng nào cho user
            }

            // Lấy hạng liền trước currentRank
            var beforeRank = await _repository.AsQueryable()
                .Where(r => r.RankingPoint < currentRank.RankingPoint)
                .OrderByDescending(r => r.RankingPoint)
                .FirstOrDefaultAsync();

            long beforeRankPoint = beforeRank?.RankingPoint ?? 0;

            // Tính toán hạng kế tiếp
            var nextRank = await _repository.AsQueryable()
                .Where(r => r.RankingPoint > currentPoints)
                .OrderBy(r => r.RankingPoint)
                .FirstOrDefaultAsync();

            // Nếu không có hạng tiếp theo (user đã đạt hạng cao nhất)
            bool isMaxRank = false;
            if (nextRank == null)
            {
                nextRank = currentRank;
                isMaxRank = true;
            }

            // Kiểm tra để tránh chia cho 0 và các tình huống không hợp lệ
            long pointsToNextRank = isMaxRank ? 0 : Math.Max(0, nextRank.RankingPoint - currentPoints);

            // Tính toán tỷ lệ tiến độ
            float progressPercent = 0;
            if (!isMaxRank && nextRank.RankingPoint != currentRank.RankingPoint)
            {
                // Tránh chia cho 0 nếu điểm của hạng hiện tại và hạng tiếp theo bằng nhau
                progressPercent = (float)(currentPoints - currentRank.RankingPoint) /
                                  (nextRank.RankingPoint - currentRank.RankingPoint) * 100;
            }
            else if (currentPoints >= nextRank.RankingPoint)
            {
                // Nếu người dùng đã đạt hạng tiếp theo, trả về 100%
                progressPercent = 100;
            }

            // Làm tròn tiến độ và ép kiểu về int
            int roundedProgressPercent = (int)Math.Round(progressPercent);

            // Đảm bảo tỷ lệ tiến độ không vượt quá 100% hoặc dưới 0%
            roundedProgressPercent = Math.Clamp(roundedProgressPercent, 0, 100);

            // Tạo response
            var response = new MembershipRankResponse
            {
                Id = currentRank.Id,
                Name = currentRank.Name,
                CurrentPoint = currentPoints,
                NextRankName = isMaxRank ? "Đã đạt hạng cao nhất" : nextRank.Name,
                NextRankPoint = nextRank.RankingPoint,
                BeforeRankPoint = beforeRankPoint,
                PointsToNextRank = pointsToNextRank,
                ProgressPercent = roundedProgressPercent,
                Background = string.IsNullOrEmpty(currentRank.Image)
                    ? string.Empty
                    : currentRank.Image.Split(',').Select(x => $"{hostUrl}/uploads/images/ranks/{x}").FirstOrDefault()
            };

            return response;
        }

        private async Task EnsureSingleDefaultAsync(string currentRankId = null)
        {
            var allRanks = await base.GetAllAsync(); // hoặc dùng repository.GetAll()

            foreach (var item in allRanks)
            {
                if (item.IsDefault && item.Id != currentRankId)
                {
                    item.IsDefault = false;
                    await base.UpdateAsync(item);
                }
            }
        }

        #region Image hanlder

        private async Task<string> ProcessUpload(List<IFormFile> files)
        {
            var stringFiles = string.Empty;
            if (files != null)
            {
                var savePath = Path.Combine(env.WebRootPath, "uploads/images/ranks");
                var fileResult = await FileHandler.SaveFiles(files, savePath);
                stringFiles = string.Join(",", fileResult);
            }
            return stringFiles;
        }

        private void RemoveOldImage(string listImage)
        {
            var images = listImage.Split(",").Select(x => Path.Combine(env.WebRootPath, "uploads/images/ranks", x)).ToList();
            FileHandler.RemoveFiles(images);
        }

        #endregion
    }
}
