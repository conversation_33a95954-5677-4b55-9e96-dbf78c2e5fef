﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.LuckyWheels;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Enums;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.LuckyWheels;
using MiniAppCore.Models.Requests.LuckyWheels;
using MiniAppCore.Models.Responses.Gamification.LuckyWheels;

namespace MiniAppCore.Services.Gamifications.LuckyWheels
{
    public interface ILuckyWheelService : IService<Entities.LuckyWheels.LuckyWheel>
    {
        Task<int> CreateAsync(LuckyWheelDTO dto);
        Task<int> UpdateAsync(string id, LuckyWheelDTO dto);

        Task<LuckyWheelDTO> GetLuckyWheelDetailById(string luckyWheelId);
        Task<LuckyWheelResponse> GetActiveLuckyWheel(string userZaloId, string luckyWheelId);

        Task<SpinResultResponse> PlayLuckyWheel(string userZaloId, string luckyWheelId, bool isTest);
        Task<PagedResult<SpinHistoryResult>> GetSpinHistory(RequestQuery query, bool isAdmin, string? userZaloId, bool? resultType, short? type, DateTime? startDate, DateTime? endDate, string? luckyWheelId);

        Task<int> UpdateClaimHistoryStatus(string historyId, ETransaction status);
        Task<(Membership, SpinHistory, ClaimRewardResponse)> GetClaimStatusReward(string historyId);
        Task<ClaimRewardResponse> GetClaimStatusReward(string userZaloId, string historyId);
        Task<ClaimRewardResponse> RegisterClaimReward(string userZaloId, string historyId, RegisterRewardRequest registerRequest);
    }
}
