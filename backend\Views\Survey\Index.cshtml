﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> s<PERSON>ch kh<PERSON>o sát</h4>
            </div>
            <a href="@Url.Action("Create", "Survey")" class="btn btn-primary mt-2">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </a>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">T<PERSON><PERSON> kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-3">
                        <label for="filter-status">Trạng thái</label>
                        <select id="filter-status" class="form-control">
                            <option value="">Tất cả</option>
                            <option value="1">Đang mở</option>
                            <option value="3">Đã đóng</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="survey-table" class="table-responsive rounded mb-3">
            <table id="list-survey" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-survey" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade" style="overflow-y:scroll;">
    <div id="modal-content" class="modal-dialog modal-xl" style="width: 90vw"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListSurvey();

            $('#search').on('input', () => {
                table.ajax.reload();
            });
            $('#filter-status').on('change', function () {
                table.ajax.reload();
            });
        });

        function GetListSurvey() {
            table = new DataTable("#list-survey", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const status = $("#filter-status").val()

                    $.ajax({
                        url: '@Url.Action("GetPage", "Surveys")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            status: status
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                rowNum: data.start + index + 1,
                                title: item.title,
                                description: item.description || '',
                                startDate: item.startedDate ? FormatDate(item.startedDate) : '',
                                endDate: item.endDate ? FormatDate(item.endDate) : '',
                                status: GetStatusBadge(item.status === 1 ? 'active' : 'inactive'),
                                createdDate: FormatDate(item.createdDate),
                                actions: `<div class="d-flex align-items-center justify-content-center list-action">
                                            <a href="@Url.Action("Detail", "Survey")/${item.id}" class="badge badge-info mx-1" data-toggle="tooltip" data-placement="top" title="Chỉnh sửa">
                                                <i class="ri-edit-line fs-6 mr-0"></i>
                                            </a>
                                            <a id="btn-export-${item.id}" onclick="ExportSurveyToExcel('${item.id}')" class="badge bg-success mx-1" data-toggle="tooltip" data-placement="top" title="Xuất Excel">
                                                <i class="ri-download-2-line fs-6 mr-0"></i>
                                            </a>
                                            <a onclick="DeleteSurvey('${item.id}')" class="badge bg-warning mx-1" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                            </a>
                                        </div>`,
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "rowNum", className: 'text-center' },
                    { title: "Tiêu đề", data: "title" },
                    { title: "Mô tả", data: "description" },
                    { title: "Ngày bắt đầu", data: "startDate" },
                    { title: "Ngày kết thúc", data: "endDate" },
                    { title: "Trạng thái", data: "status", className: 'text-center' },
                    { title: "Ngày tạo", data: "createdDate" },
                    { title: "Thao tác", data: "actions", className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetStatusBadge(status) {
            if (status === 'active') {
                return '<span class="badge bg-success">Đang hoạt động</span>';
            } else {
                return '<span class="badge bg-secondary">Không hoạt động</span>';
            }
        }

        function DeleteSurvey(id) {
            if (id === '') return;
            const url = `/api/Surveys/${id}`
            DeleteItem(url);
        }

        function ExportSurveyToExcel(surveyId) {
            if (!surveyId) return;

            const exportBtn = $(`#btn-export-${surveyId}`);
            const originalIcon = exportBtn.html();

            exportBtn.html('<span class="spinner-border text-light spinner-border-sm" role="status" aria-hidden="true"></span>');
            exportBtn.prop("disabled", true);

            $.ajax({
                url: '/api/Surveys/Submissions/Export',
                method: 'POST',
                data: JSON.stringify([surveyId]),
                contentType: 'application/json',
                xhrFields: {
                    responseType: 'blob'
                },
                success: async function (blob, status, xhr) {
                    const disposition = xhr.getResponseHeader('Content-Disposition');
                    let filename = 'survey_export.xlsx';

                    if (disposition) {
                        const match = disposition.match(/filename\*\=UTF-8''(.+)/);
                        if (match && match[1]) {
                            filename = decodeURIComponent(match[1].trim());
                        }
                    }

                    const downloadUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(downloadUrl);
                },
                error: function (xhr) {
                    AlertResponse("Xuất Excel thất bại. Vui lòng thử lại.", "error");
                },
                complete: function () {
                    exportBtn.html(originalIcon);
                    exportBtn.prop("disabled", false);
                }
            });
        }
    </script>
}
