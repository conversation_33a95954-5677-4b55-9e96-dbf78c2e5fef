﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Dependencies.Storage;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Entities.Products;
using MiniAppCore.Entities.Products.ProductInfo;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Categories;
using MiniAppCore.Models.Responses.Categories;

namespace MiniAppCore.Services.Categories
{
    public class CategoryService(IUnitOfWork unitOfWork, IMapper mapper, IHttpContextAccessor httpContextAccessor, IStorageService storageService) : OrderedEntityService<Category>(unitOfWork), ICategoryService
    {
        private readonly IRepository<CategoryChild> _categoryChildRep = unitOfWork.GetRepository<CategoryChild>();
        protected override string OrderColumnName => "OrderPriority";

        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public async Task<int> CreateAsync(CategoryRequest dto)
        {
            var category = mapper.Map<Category>(dto);

            await unitOfWork.BeginTransactionAsync();

            try
            {
                await base.PrepareInsertFirstAsync(category);

                category.Description = dto.Description;
                if (dto.Description == "_")
                {
                    category.Description = "";
                }
                if (dto.Images.Any())
                {
                    category.Images = await ProcessUpload(dto.Images);
                }
                //var rs = await base.CreateAsync(category);

                _repository.Add(category);
                await AddOrRemoveCategoryChild(category.Id, dto.listChild);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public async Task<int> UpdateAsync(string id, CategoryRequest dto)
        {
            var category = await GetByIdAsync(id);
            if (category == null)
            {
                throw new CustomException(200, "Category not found.");
            }

            await unitOfWork.BeginTransactionAsync();

            try
            {
                int currentOrder = category.OrderPriority;

                mapper.Map(dto, category);

                await base.ReorderAsync(category, currentOrder, category.OrderPriority);

                if (dto.Images.Any())
                {
                    string newFiles = await ProcessUpload(dto.Images);

                    if (!string.IsNullOrEmpty(newFiles))
                    {
                        if (!string.IsNullOrEmpty(category.Images))
                        {
                            var existingImages = category.Images.Split(",").ToList();
                            var newImageFiles = newFiles.Split(",").ToList();

                            existingImages.AddRange(newImageFiles);

                            category.Images = string.Join(",", existingImages.Distinct());
                        }
                        else
                        {
                            category.Images = newFiles;
                        }
                    }
                }

                if (dto.RemovedOldImages.Any())
                {
                    await RemoveOldImage(string.Join(",", dto.RemovedOldImages));

                    if (!string.IsNullOrEmpty(category.Images))
                    {
                        var remainingImages = category.Images.Split(",")
                        .Where(image => !dto.RemovedOldImages.Contains(image))
                        .ToList();
                        category.Images = string.Join(",", remainingImages);
                    }
                }

                _repository.Update(category);
                await AddOrRemoveCategoryChild(category.Id, dto.listChild);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public async Task<PagedResult<CategoryResponse>> GetPage(CategoryQueryParams query)
        {
            var categories = _repository.AsQueryable();
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                categories = categories.Where(x => x.Name.Contains(query.Keyword));
            }

            var totalItems = await categories.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);
            var listCategories = await categories.OrderBy(x => x.OrderPriority).ThenByDescending(x => x.CreatedDate).Skip(query.Skip).Take(query.PageSize).ToListAsync();

            // Ánh xạ dữ liệu và xử lý Images
            var items = listCategories.Select(p => new CategoryResponse
            {
                Id = p.Id,
                Name = p.Name,
                HexColor = p.HexColor,
                Description = p.Description,
                OrderPriority = p.OrderPriority,
                Images = string.IsNullOrEmpty(p.Images)
                    ? new List<string>()
                    : p.Images.Split(',').Select(x => $"{hostUrl}/uploads/images/categories/{x}").ToList(),
                listChild = GetListCategoryChild(p.Id).Result
            }).ToList();

            return new PagedResult<CategoryResponse>()
            {
                Data = items,
                TotalPages = totalPages
            };
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var existingCategory = await GetByIdAsync(id);
            if (existingCategory == null)
            {
                throw new CustomException(200, "Category not found");
            }

            await unitOfWork.BeginTransactionAsync();
            try
            {
                // remove image
                if (!string.IsNullOrEmpty(existingCategory.Images))
                {
                    await RemoveOldImage(existingCategory.Images);
                }

                await base.ReorderAfterDeleteAsync(existingCategory.OrderPriority);
                await AddOrRemoveCategoryChild(id, new List<CategoryChild>());

                _repository.Delete(existingCategory);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public async Task<List<CategoryResponse>> GetCategoriesByProductId(string productId)
        {
            var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();

            // Get all category IDs associated with this product
            var categoryIds = await productCategoryRepo.AsQueryable()
                .Where(pc => pc.ProductId == productId)
                .Select(pc => pc.CategoryId)
                .ToListAsync();

            if (!categoryIds.Any())
            {
                return new List<CategoryResponse>();
            }

            // Get the actual category objects
            var categories = await _repository.AsQueryable()
                .Where(c => categoryIds.Contains(c.Id))
                .ToListAsync();

            // Map to response objects
            var result = categories.Select(c => new CategoryResponse
            {
                Id = c.Id,
                Name = c.Name,
                HexColor = c.HexColor,
                Description = c.Description,
                Images = string.IsNullOrEmpty(c.Images)
                    ? new List<string>()
                    : c.Images.Split(',').Select(x => $"{hostUrl}/uploads/images/categories/{x}").ToList()
            }).ToList();

            return result;
        }

        public async Task<int> AssignProductToCategories(string productId, List<string> categoryIds)
        {
            // Get the repository for ProductCategory
            var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();

            // Get existing product-category relationships for this product
            var existingCategories = await productCategoryRepo.AsQueryable()
                .Where(pc => pc.ProductId == productId)
                .ToListAsync();

            // Identify categories to remove (exists in DB but not in the new list)
            var categoriesToRemove = existingCategories
                .Where(pc => !categoryIds.Contains(pc.CategoryId))
                .ToList();

            // Identify categories to add (exists in the new list but not in DB)
            var existingCategoryIds = existingCategories.Select(pc => pc.CategoryId).ToList();
            var categoriesToAdd = categoryIds
                .Where(categoryId => !existingCategoryIds.Contains(categoryId))
                .Select(categoryId => new ProductCategory
                {
                    ProductId = productId,
                    CategoryId = categoryId
                })
                .ToList();

            // Remove categories that are no longer associated
            if (categoriesToRemove.Any())
            {
                productCategoryRepo.DeleteRange(categoriesToRemove);
            }

            // Add new category associations
            if (categoriesToAdd.Any())
            {
                productCategoryRepo.AddRange(categoriesToAdd);
            }

            // Save changes and return the total number of affected records
            //await unitOfWork.SaveChangesAsync(); // Commit sau

            return categoriesToRemove.Count + categoriesToAdd.Count;
        }

        public async Task<int> RemoveAllCategoriesFromProduct(string productId)
        {
            var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();

            // Get all category associations for this product
            var productCategories = await productCategoryRepo.AsQueryable()
                .Where(pc => pc.ProductId == productId)
                .ToListAsync();

            if (!productCategories.Any())
            {
                return 0;
            }

            // Remove all associations
            productCategoryRepo.DeleteRange(productCategories);
            //await unitOfWork.SaveChangesAsync();

            return productCategories.Count;
        }

        public async Task<int> RemoveProductFromCategories(string productId, List<string> categoryIds)
        {
            if (categoryIds == null || !categoryIds.Any())
            {
                return 0;
            }

            var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();

            // Get specific category associations to remove
            var productCategoriesToRemove = await productCategoryRepo.AsQueryable()
                .Where(pc => pc.ProductId == productId && categoryIds.Contains(pc.CategoryId))
                .ToListAsync();

            if (!productCategoriesToRemove.Any())
            {
                return 0;
            }

            // Remove the specified associations
            productCategoryRepo.DeleteRange(productCategoriesToRemove);
            await unitOfWork.SaveChangesAsync();

            return productCategoriesToRemove.Count;
        }

        public async Task<int> QuickUpdate(List<string> categoryIds)
        {
            var categories = await _repository.AsQueryable().Where(x => categoryIds.Contains(x.Id)).ToListAsync();
            if (!categories.Any()) throw new CustomException(1, "Không tìm thấy danh mục nào!");

            _repository.DeleteRange(categories);

            return await unitOfWork.SaveChangesAsync();
        }

        #region Image hanlder

        private async Task<string> ProcessUpload(List<IFormFile> files)
        {
            if (files == null || !files.Any())
            {
                return string.Empty;
            }
            string subFolder = "images/categories";
            var results = await storageService.SaveFilesAsync(files, subFolder);
            return string.Join(",", results.Select(r => r.FileName));
        }

        private async Task RemoveOldImage(string listImage)
        {
            if (string.IsNullOrWhiteSpace(listImage)) return;
            var fileNames = listImage.Split(',', StringSplitOptions.RemoveEmptyEntries);
            foreach (var name in fileNames)
            {
                // relative path tính theo pattern {UploadFolder}/{subFolder}/{fileName}
                var rel = Path.Combine("uploads", "images/categories", name).Replace("\\", "/");
                await storageService.DeleteFileAsync(rel);
            }
        }

        #endregion


        #region category child

        public async Task AddOrRemoveCategoryChild(string categoryId, List<CategoryChild> categoryChilds)
        {
            var lstCt = await _categoryChildRep.AsQueryable().Where(x => x.CategoryId == categoryId).ToListAsync();
            if (categoryChilds.Count == 0)
            {

                _categoryChildRep.DeleteRange(lstCt);
            }
            else
            {
                var lstCategoryChildIdNew = categoryChilds.Select(x => x.Id).ToList();
                var lstCategoryChildOld = lstCt.Select(x => x.Id).ToList();
                var lstCtChildRemove = lstCategoryChildOld.Except(lstCategoryChildIdNew).ToList();
                //Xóa
                if (lstCtChildRemove.Count > 0)
                {
                    var lstRemove = lstCt.Where(x => lstCtChildRemove.Contains(x.Id)).ToList();
                    _categoryChildRep.DeleteRange(lstRemove);
                }
                //Thêm mới
                var lstCtChildIdAdd = lstCategoryChildIdNew.Except(lstCategoryChildOld).ToList();
                if (lstCtChildIdAdd.Count > 0)
                {
                    var CategoryChildAdd = categoryChilds.Where(x => lstCtChildIdAdd.Contains(x.Id)).Select(x => new CategoryChild
                    {
                        Name = x.Name,
                        Description = x.Description,
                        CategoryId = categoryId,
                        OrderPriority = x.OrderPriority,
                    }).ToList();

                    _categoryChildRep.AddRange(CategoryChildAdd);
                }
                //Cập nhật
                var lstCategoryChildIdIntersect = lstCategoryChildIdNew.Intersect(lstCategoryChildOld).ToList();
                if (lstCategoryChildIdIntersect.Count > 0)
                {
                    var categoryChildUpdate = categoryChilds.Where(x => lstCategoryChildIdIntersect.Contains(x.Id)).ToList();
                    foreach (var item in categoryChildUpdate)
                    {
                        var dt = await _categoryChildRep.FindByIdAsync(item.Id);
                        if (dt != null)
                        {
                            dt.Name = item.Name;
                            dt.Description = item.Description;
                            dt.OrderPriority = item.OrderPriority;
                            _categoryChildRep.Update(dt);
                        }
                    }
                }


            }
            //int rs = await unitOfWork.SaveChangesAsync();
            //return rs;
        }

        public async Task<List<CategoryChild>> GetListCategoryChild(string categoryId)
        {
            return await _categoryChildRep.AsQueryable().Where(x => x.CategoryId == categoryId).OrderBy(x => x.OrderPriority).ToListAsync();
        }

        public async Task<int> RemoveCategoryChildFromProduct(string productId)
        {
            var productCategoryChildRepo = unitOfWork.GetRepository<ProductCategoryChild>();

            // Get specific category associations to remove
            var productCategoriesToRemove = await productCategoryChildRepo.AsQueryable()
                .Where(pc => pc.ProductId == productId)
                .ToListAsync();

            if (!productCategoriesToRemove.Any())
            {
                return 0;
            }

            // Remove the specified associations
            productCategoryChildRepo.DeleteRange(productCategoriesToRemove);
            //await unitOfWork.SaveChangesAsync();

            return productCategoriesToRemove.Count;
        }

        public async Task<int> AddOrUpdateCategoryChildByProduct(string productId, List<string> categoryChildIds)
        {
            // Get the repository for ProductCategory
            var productCategoryRepo = unitOfWork.GetRepository<ProductCategoryChild>();

            // Get existing product-category relationships for this product
            var existingCategories = await productCategoryRepo.AsQueryable()
                .Where(pc => pc.ProductId == productId)
                .ToListAsync();

            // Identify categories to remove (exists in DB but not in the new list)
            var categoriesToRemove = existingCategories
                .Where(pc => !categoryChildIds.Contains(pc.CategoryChildId))
                .ToList();

            // Identify categories to add (exists in the new list but not in DB)
            var existingCategoryIds = existingCategories.Select(pc => pc.CategoryChildId).ToList();
            var categoriesToAdd = categoryChildIds
                .Where(categoryChildId => !existingCategoryIds.Contains(categoryChildId))
                .Select(categoryChildId => new ProductCategoryChild
                {
                    ProductId = productId,
                    CategoryChildId = categoryChildId
                })
                .ToList();

            // Remove categories that are no longer associated
            if (categoriesToRemove.Any())
            {
                productCategoryRepo.DeleteRange(categoriesToRemove);
            }

            // Add new category associations
            if (categoriesToAdd.Any())
            {
                productCategoryRepo.AddRange(categoriesToAdd);
            }

            // Save changes and return the total number of affected records
            //await unitOfWork.SaveChangesAsync(); // Commit sau

            return categoriesToRemove.Count + categoriesToAdd.Count;
        }


        #endregion
    }



}
