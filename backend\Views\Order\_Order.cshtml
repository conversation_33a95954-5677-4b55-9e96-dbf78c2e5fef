﻿@using MiniAppCore.Enums
@using MiniAppCore.Models.Responses.Orders
@using Newtonsoft.Json
@using System.Globalization
@using Newtonsoft.Json.Serialization
@model MiniAppCore.Models.Responses.Orders.OrderDetailResponse;
@{
    var settings = new JsonSerializerSettings
    {
        ContractResolver = new CamelCasePropertyNamesContractResolver()
    };
    var json = Html.Raw(JsonConvert.SerializeObject(Model.OrderDetails, settings) ?? "[]");
}

<style>
    #order-detail__search__result.show {
        display: block;
        z-index: 1050;
        width: 100%;
        border: 1px solid #ddd;
        border-radius: 0.25rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .product-qty-edit {
        cursor: pointer;
        background-color: #f8f9fa;
        padding: 0.2rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: bold;
    }

    .product-qty-edit:hover {
        background-color: #e9ecef;
    }
</style>

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        @* <div class="card-body" style="@((Model.OrderStatus == (short)EOrder.Completed) ? "pointer-events: none;" : ""))"> *@
        <div class="card-body">
            <form data-toggle="validator">
                @* <input type="hidden" value="" id="bookingId" /> *@
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Mã đơn hàng</label>
                            <input id="id" type="text" class="form-control" disabled value="@Model.OrderId.ToUpper()" />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Ngày mua hàng</label>
                            <input id="orderDate" type="datetime-local" class="form-control" value="@Model.CreatedDate.ToString("yyyy-MM-ddTHH:mm")" disabled />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Tên khách hàng</label>
                            <input id="memebershipName" type="text" class="form-control" value="@Model.UserZaloName" disabled />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Số điện thoại</label>
                            <input id="cusPhone" type="text" class="form-control" value="@Model.PhoneNumber" disabled />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Địa chỉ vận chuyển</label>
                            <input id="deliveryAddress" rows="1" class="form-control" value="@Model.Address" />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Điểm sử dụng</label>
                            <input type="text" class="form-control" disabled value="@Model.PointUsage" />
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>
                                Thông tin xuất hóa đơn
                                @if (Model.Vat != null)
                                {
                                    if (Model.Vat.Type == "Company")
                                    {
                                        <span class="badge bg-primary ms-2">Doanh nghiệp</span>
                                    }
                                    else if (Model.Vat.Type == "Personal")
                                    {
                                        <span class="badge bg-success ms-2">Cá nhân</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary ms-2">Khác</span>
                                    }   
                                }
                            </label>
                            <div class="table-responsive">
                                <table class="table table-bordered align-middle mb-0">
                                    <tbody>
                                        @if (Model.Vat != null)
                                        {
                                            if (Model.Vat.Type == "Personal")
                                            {
                                                <tr>
                                                    <td class="fw-bold" style="width: 25%;">Họ và tên</td>
                                                    <td>@Model.Vat.OwnerName</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Mã số thuế</td>
                                                    <td>@Model.Vat.TaxCode</td>
                                                </tr>
                                            }
                                            else if (Model.Vat.Type == "Company")
                                            {
                                                <tr>
                                                    <td class="fw-bold" style="width: 25%;">Tên Doanh nghiệp</td>
                                                    <td>@Model.Vat.OwnerName</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Mã số thuế</td>
                                                    <td>@Model.Vat.TaxCode</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Địa chỉ</td>
                                                    <td>@Model.Vat.FullAddress</td>
                                                </tr>
                                                <tr>
                                                    <td class="fw-bold">Email nhận hóa đơn</td>
                                                    <td>@Model.Vat.Email</td>
                                                </tr>
                                            }
                                            else
                                            {
                                                <tr>
                                                    <td colspan="2" class="text-center">Loại hóa đơn không xác định.</td>
                                                </tr>
                                            }
                                        }
                                        else
                                        {
                                            <tr>
                                                <td colspan="2" class="text-center">Chưa có thông tin xuất hóa đơn.</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Vouchers áp dụng</label>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Mã voucher</th>
                                            <th>Tên voucher</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if (Model.Vouchers.Any())
                                        {
                                            @foreach (var voucher in Model.Vouchers)
                                            {
                                                <tr>
                                                    <td>@voucher.VoucherCode</td>
                                                    <td>@voucher.VoucherName</td>
                                                </tr>
                                            }
                                        }
                                        else
                                        {
                                            <tr>
                                                <td colspan="2" class="text-center">Không có voucher nào được áp dụng</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Trạng thái thanh toán</label>
                            <select id="paymentStatus" class="selectpicker form-control">
                                <option value="@((short)EPayment.Paid)" selected="@((short)EPayment.Paid == Model.PaymentStatus)">Đã thanh toán</option>
                                <option value="@((short)EPayment.Unpaid)" selected="@((short)EPayment.Unpaid == Model.PaymentStatus)">Chưa thanh toán</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Phương thức thanh toán</label>
                            <select id="paymentMethod" class="selectpicker form-control">
                                <option value="1" selected="@(Model.PaymentMethod == "1")">Tiền mặt</option>
                                <option value="2" selected="@(Model.PaymentMethod != "1")">Chuyển khoản</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Trạng thái đơn hàng</label>
                            <select id="orderStatus" class="selectpicker form-control" onchange="OrderDetail.handleOrderStatusChange(this)">
                                <option value="@((short)EOrder.Pending)" selected="@(Model.OrderStatus == (short)EOrder.Pending)">Chờ xác nhận</option> <!-- Pending -->
                                <option value="@((short)EOrder.Confirmed)" selected="@(Model.OrderStatus == (short)EOrder.Confirmed)">Đã xác nhận</option> <!-- Confirmed -->
                                <option value="@((short)EOrder.Completed)" selected="@(Model.OrderStatus == (short)EOrder.Completed)">Đã hoàn thành</option> <!-- Completed -->
                                <option value="@((short)EOrder.Cancelled)" selected="@(Model.OrderStatus == (short)EOrder.Cancelled)">Đã hủy</option> <!-- Cancelled -->
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Ghi chú</label>
                            <textarea id="notes" rows="3" class="form-control">@Model.Note</textarea>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <div class="d-flex align-items-center justify-content-between py-2">
                                <label class="font-weight-bold mb-0">Chi tiết đơn hàng</label>
                                @if (!(Model.OrderStatus == (short)EOrder.Completed || Model.OrderStatus == (short)EOrder.Cancelled))
                                {
                                    <div class="col-md-8" hidden>
                                        <!-- Phần input tìm kiếm sản phẩm -->
                                        <div class="input-group position-relative">
                                            <input id="order-detail-query" class="form-control" placeholder="Tìm kiếm và thêm sản phẩm..." oninput="OrderDetail.searchProducts(this.value)" />
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-primary" onclick="event.preventDefault(); orderDetailSearch(event)">
                                                    <i class="ri-search-line"></i>
                                                </button>
                                            </div>
                                            <div id="order-detail__search__result" class="dropdown-menu w-100 position-absolute" style="max-height: 300px; overflow-y: auto; top: 38px; left: 0;">
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                            <div id="order-details" class="d-flex flex-column" style="overflow-y: scroll">

                                @if (Model != null && Model.OrderDetails.Any())
                                {
                                    foreach (OrderItemResponse item in Model.OrderDetails)
                                    {
                                        <div class="shadow-sm p-3 mb-4 bg-white rounded">
                                            <div class="d-flex px-4 align-items-center justify-content-between">
                                                <div class="col-5 d-flex align-items-center">
                                                    <img src="@(item.Images.Count > 0 ? item.Images[0] : "/images/product/01.png")" class="img-fluid rounded avatar-50 mr-3" alt="image">
                                                    <div class="col-8 flex flex-column justify-content-center">
                                                        <p class="my-0 font-weight-bold"> @item.ProductName </p>
                                                        <p class="my-0 note-text"> Ghi chú: @item.Note </p>
                                                        @if (item.PropertyValues.Any())
                                                        {
                                                            <p class="my-0 small">Phân loại: @string.Join(", ", item.PropertyValues)</p>
                                                        }
                                                    </div>
                                                </div>
                                                <div class="text-right">
                                                    @if (item.IsDiscounted)
                                                    {
                                                        <p class="my-0 text-muted"><del>@item.OriginalPrice.ToString("N0", new CultureInfo("vi-VN"))đ</del></p>
                                                    }
                                                    <p class="my-0">
                                                        @item.DiscountPrice.ToString("N0", new CultureInfo("vi-VN"))đ x <span style="cursor:pointer"
                                                            onclick="makeEditable(this, '@item.ProductId')">@item.Quantity</span>
                                                    </p>
                                                    <p class="my-0 font-weight-bold">
                                                        @((item.DiscountPrice * @item.Quantity).ToString("N0", new CultureInfo("vi-VN"))) đ
                                                    </p>
                                                </div>
                                                @if (!(Model.OrderStatus == (short)EOrder.Completed))
                                                {
                                                    <span hidden class="btn btn-badge btn-danger" style="height: 40px; cursor: pointer" onclick="removeProductOrderDetail('@item.ProductId')">
                                                        Xóa
                                                    </span>
                                                }
                                            </div>

                                            @if (item.Gifts.Any())
                                            {
                                                <div class="mt-2 px-4">
                                                    <p class="font-weight-bold text-success mb-2">Quà tặng kèm:</p>
                                                    @foreach (var gift in item.Gifts)
                                                    {
                                                        <div class="d-flex align-items-center mb-2 ml-4 border-left border-success pl-2">
                                                            <img src="@(gift.Images.Count > 0 ? gift.Images[0] : "/images/product/01.png")" class="img-fluid rounded avatar-30 mr-2" alt="gift">
                                                            <div>
                                                                <p class="my-0">@gift.ProductName x @gift.Quantity</p>
                                                                @if (gift.PropertyValues.Any())
                                                                {
                                                                    <p class="my-0 small">Thuộc tính: @string.Join(", ", gift.PropertyValues)</p>
                                                                }
                                                            </div>
                                                        </div>
                                                    }
                                                </div>
                                            }
                                        </div>
                                    }
                                }
                                else
                                {
                                    <div class="card">Không có dữ liệu</div>
                                }

                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 d-flex flex-end">
                        <div class="col-8"></div>
                        <div class="col-4">
                            <table class="w-100">
                                <tr>
                                    <td>Tạm tính:</td>
                                    <td id="order_tamtinh" class="text-end">@(Model.SubTotalAmount.ToString("N0", new CultureInfo("vi-VN"))) đ</td>
                                </tr>

                                <tr>
                                    <td>Giảm giá: </td>
                                    <td id="order_discount" class="text-end">@Model.DiscountAmount.ToString("N0", new CultureInfo("vi-VN")) đ</td>
                                </tr>

                                <tr>
                                    <td>Vận chuyển: </td>
                                    <td id="order_discount" class="text-end">@Model.ShippingFee.ToString("N0", new CultureInfo("vi-VN")) đ</td>
                                </tr>

                                <tr>
                                    <td>Tổng: </td>
                                    <td id="order_total" class="text-end">@Model.TotalAmount.ToString("N0", new CultureInfo("vi-VN")) đ</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button id="submitButton" type="submit" class="btn btn-primary" onclick="OrderDetail.handleSaveOrUpdate('@Model.OrderId')">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        // Set global context immediately
        window.currentOrderContext = 'update';

        // Set order status values
        $('#orderStatus').val("@Model.OrderStatus");
        $('#paymentStatus').val("@Model.PaymentStatus");

        // Initialize OrderDetail module with data
        if (typeof OrderDetail !== 'undefined') {
            OrderDetail.init('@Model.OrderId', @json);
        }

        // Handle status changes
        $('#orderStatus').on('change', function () {
            OrderDetail.handleOrderStatusChange(this);
        });
    });
</script>