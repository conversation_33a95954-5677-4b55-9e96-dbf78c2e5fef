﻿@using MiniAppCore.Enums
@using MiniAppCore.Models.Responses.BookingItem
@model BookingItemResponse;

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body" style="@(User.IsInRole("EMPLOYEE") ? "pointer-events: none;" : "")">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.Id" />
                <div class="row">
                    <div class="col-md-10">
                        <div class="form-group">
                            <label>Tên dịch vụ <span style="color:red">*</span></label>
                            <input id="name" type="text" class="form-control" value="@Model.Name" placeholder="Ví dụ: T<PERSON> vấn sức khoẻ, v.v" data-error-message="<PERSON>ui lòng nhập tên sản phẩm." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Thứ tự hiển thị</label>
                            <input id="displayOrder"
                                   type="text"
                                   min="1"
                                   class="form-control"
                                   value="@(Model.DisplayOrder.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Nhập thứ tự hiển thị."
                                   data-error-message="Vui lòng nhập thứ tự hiển thị"
                                   oninput="InputValidator.currency(this)"
                                   required>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Giá bán <span style="color:red">*</span></label>
                            <input id="price" type="text" min="0" class="form-control"
                                   value="@(Model.OriginalPrice.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))" placeholder="Nhập giá bán."
                                   oninput="InputValidator.currency(this)" data-error-message="Vui lòng nhập giá bán." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Lượt bán</label>
                            <input id="boughtCount"
                                   type="text"
                                   min="0"
                                   class="form-control"
                                   value="@(Model.BoughtCount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Nhập lượt bán."
                                   data-error-message="Vui lòng nhập lượt bán."
                                   oninput="InputValidator.currency(this)"
                                   required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3" hidden>
                        <div class="form-group">
                            <label>Lượt thích</label>
                            <input id="likeCount"
                                   type="text"
                                   min="0"
                                   class="form-control"
                                   value="@(Model.LikeCount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Nhập lượt thích."
                                   data-error-message="Vui lòng nhập lượt thích."
                                   oninput="InputValidator.currency(this)"
                                   required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Lượt đánh giá</label>
                            <input id="reviewCount"
                                   type="text"
                                   min="0"
                                   class="form-control"
                                   value="@(Model.ReviewCount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Nhập lượt đánh giá."
                                   data-error-message="Vui lòng nhập lượt đánh giá"
                                   oninput="InputValidator.currency(this)"
                                   required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Điểm đánh giá</label>
                            <input id="reviewPoint" type="number" min="0" class="form-control" max="5" value="@Model.ReviewPoint" placeholder="Nhập điểm đánh giá."
                                data-error-message="Vui lòng nhập điểm đánh giá." value="@Model.ReviewPoint" oninput="validateReviewPoint(this, 0, 5)" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Trạng thái hoạt động</label>
                            <select id="status" class="form-control" required>
                                <option value="@((short)EProduct.InStock)" selected="@(Model.Status == (short)EProduct.InStock)">Hoạt động</option>
                                @* <option value="@EProduct.OutOfStock" selected="@(Model.Status == EProduct.OutOfStock)">Hết hàng</option> *@
                                <option value="@((short)EProduct.Discontinued)" selected="@(Model.Status == (short)EProduct.Discontinued)">Ngưng hoạt động</option>
                            </select>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Mô tả dịch vụ</label>
                            <div id="description" class="rounded-bottom" style="height: 250px" data-type="content">@Html.Raw(Model.Description)</div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Ảnh<span style=" color:red">*</span></label>
                            <input type="file" id="pics" class="form-control image-file" name="pic" accept="image/*" onchange="ShowPreview(event)" multiple>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div id="preview" class="row d-flex flex-nowrap" style="overflow-x: scroll; min-height: 250px;">
                            @foreach (var item in Model.Images)
                            {
                                <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                    <img src="@item" class="card-img-top h-100" alt="Alternate Text" style="object-fit:contain" />
                                    <span class="btn-preview-remove" data-url="@item">x</span>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(() => {
        InitialEditor();
    });
</script>