﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Queries;
using MiniAppCore.Services.Bookings;

namespace MiniAppCore.Controllers.API
{

    [ApiController()]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class BookingsController(ILogger<BookingsController> logger, IBookingService bookingService, IConfiguration configuration) : ControllerBase
    {
        [HttpGet]
        public async Task<IActionResult> GetPage([FromQuery] PurchaseQueryParams query)
        {
            try
            {
                if (User.IsInRole("ADMIN"))
                {
                    var adminResult = await bookingService.GetPaged(query);
                    return Ok(new
                    {
                        Code = 0,
                        Message = "Thành công!",
                        adminResult.Data,
                        adminResult.TotalPages
                    });
                }

                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await bookingService.GetPaged(query, userZaloId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetBookingDetail(string id)
        {
            try
            {
                var booking = await bookingService.GetBookingDetailResponseAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = booking
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Booking(Models.Requests.Bookings.BookingRequest dto)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value;
                if (string.IsNullOrWhiteSpace(userZaloId))
                {
                    return Unauthorized();
                }
                // Get minimum time difference from configuration (default to 1 hour if not specified)
                int minHoursAhead = configuration.GetValue<int>("BookingSettings:MinHoursAhead", 1);
                DateTime minAllowedBookingTime = DateTime.Now.AddHours(minHoursAhead);
                if (dto.BookingDate <= minAllowedBookingTime)
                {
                    return StatusCode(200, new
                    {
                        Code = 1,
                        Message = $"Ngày đặt lịch không hợp lệ! Vui lòng đặt lịch trước ít nhất {minHoursAhead} giờ."
                    });
                }
                var bookingId = await bookingService.CreateAsync(userZaloId, dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Đặt lịch thành công!",
                    bookingId
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("cancel/{id}")]
        public async Task<IActionResult> Cancel(string id, [FromQuery] string? reason)
        {
            try
            {
                await bookingService.UpdateStatusBooking(id, Enums.EBooking.Canceled, reason);
                return Ok(new
                {
                    Code = 0,
                    Message = "Hủy đặt lịch thành công!",
                });
            }
            catch (CustomException ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("checkin/{id}")]
        public async Task<IActionResult> CheckIn(string id)
        {
            try
            {
                await bookingService.UpdateStatusBooking(id, Enums.EBooking.CheckIn);
                return Ok(new
                {
                    Code = 0,
                    Message = "Check in thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("confirm/{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> Confirm(string id)
        {
            try
            {
                await bookingService.UpdateStatusBooking(id, Enums.EBooking.Confirmed);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xác nhận đặt lịch thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("complete/{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> Complete(string id)
        {
            try
            {
                await bookingService.UpdateStatusBooking(id, Enums.EBooking.Completed);
                return Ok(new
                {
                    Code = 0,
                    Message = "Hoàn thành đặt lịch!",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> Update(string id, Models.Requests.Bookings.BookingRequest dto)
        {
            try
            {
                await bookingService.UpdateAsync(id, dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật đặt lịch thành công!"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("Export")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> ExportMembership([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate, [FromQuery] short? bookingStatus)
        {
            try
            {
                var fileContent = await bookingService.ExportBookings(startDate, endDate, bookingStatus);
                var fileName = $"DatLich_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                Response.Headers["Content-Disposition"] = $"attachment; filename={fileName}";
                return File(fileContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

    }
}
