﻿namespace MiniAppCore.Models.DTOs.Properties
{
    public class PropertyDTO
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public int MaxSelection { get; set; }
        public bool IsMultipleChoice { get; set; }
        public List<PropertyValueDTO> Options { get; set; } = new();
    }

    public class PropertyValueDTO
    {
        public string? Value { get; set; }
        public string? PropertyValueId { get; set; }
    }
}
