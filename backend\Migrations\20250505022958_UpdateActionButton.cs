﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateActionButton : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "7669df895d73456a8bf134cc0a900349");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0599752851f84af29f30a8e6b11c4fce");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1807f86b67be40d6b7d46d24e900192f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "193952b325b84fbc9f9374dd825981a1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1a5759ac54ad495595699824c8b6bc6a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1f603528c4944ce288efb2ce7610520c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1f67946195b44a96a8c564565a3cab9b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1f6862a8d2914eb3b74cab5d60ce2a20");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2cc201acd93c4e349b4f9464b59bbca6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2e0067561ec545078867d553bcdc85c3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "30170463c05e452dbea50566e8eb06f0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "33e89d68c08449c4826177e9b52bf83f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "37764e17602e41d6a57c4b35abf3fd32");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3d917cd4d5a54c71a9b62fc632d951ac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3db5a4f9e19c4cc4be3b6a7ce7317a3d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "455579a5c01b44cf8ab8936df9167f81");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4b64cdd8f5d74173864fbef4daaf9a0e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5b03d93687ec4a6f8bbf1afcb167da35");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6371743da95b42dc888d0c3acb922203");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "65c2d9d8a4994ed29b8f233ff493f7fc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "70e5b6c7d49c4bac99e48c50a3a1010d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "71a30ffa4fed47349497c24436869e45");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "935826b1c670497680801c440b2e0e64");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ae8946f0b3849adbb354a52ec7a59ee");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "aea3de85262847258417fff19d68b632");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b11b5701cde1431198be3c65ff83d601");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bb03f9cf14114ce9a9e62024ed628460");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c385950b121b4a9cb6f5ac890492876a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ca0479e1920c47399bfdb5d4f64eda60");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d4e9267c5086404dbd8dd64e7628a1b9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d7c083b5bb1f4b22b3efbcb59db6bded");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d8028fdc47a64a9298a37fa493a94073");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d8856b7e15cb4ab0a52c64bff8b314ad");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f3fcc5063ed9409f83d9be0a78276404");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f53477a08c1144bebae4415036cfda73");

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "ActionButtonConfigs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAELka/oyPZO0wSxjMsSPiicVY++bqMROgKDHGJSvG74UMOaDDu10dO3kFxy8MSFO0ag==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "a8551d788f004767a4c8f4799dd34f93", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 5, 9, 29, 56, 599, DateTimeKind.Local).AddTicks(3237), "FeaturesButton", new DateTime(2025, 5, 5, 9, 29, 56, 599, DateTimeKind.Local).AddTicks(3240) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "00e3474e8e1e4ab9afcdce31c6511960", new DateTime(2025, 5, 5, 9, 29, 56, 597, DateTimeKind.Local).AddTicks(9082), true, "Overview", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(3339), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "07630c61118243b4bf6f54521afbe1ba", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4070), true, "BookingItem", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4071), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "0ba8ddeabd574355b3222fd7c39e506b", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4108), true, "CampaignList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4108), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "0c36e10aa7de4462b04d28d14d1af2d3", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4061), true, "Category", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4061), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "0d51b2374c6b47789c91c70cd31ab25a", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4097), true, "History", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4098), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "0fe7cfe74a4f4d05b06b5e7fe9dfba11", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4063), true, "Brand", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4064), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "114bd1ea63b448b4b3b1962dc652e516", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4099), true, "SurveyList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4100), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "17092c4955d343719e02f66baa0af616", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4053), true, "ArticleList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4054), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "3e655e5332df44128fa9c7e18b31216d", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4084), true, "Promotion", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4084), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "530e3c2c54ce4d6d908186e79f49e9d2", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4051), true, "ArticleCategory", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4051), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "545b1b23de344e4d95e65165b6517754", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4101), true, "GameList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4102), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "56f0adae7dd14c22a55870393752c168", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4116), true, "EventTemplateHistory", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4116), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "63e1f8af22ae4b1ea3d6609651086d07", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4106), true, "GamePrize", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4106), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "739e88e4ba3c471eba61bf4af5416c20", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4065), true, "ProductProperty", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4066), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "778cb268d81a46ab949e04339cd093ec", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4118), true, "TemplateUidList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4118), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "7bc852e23daf422294addb3f183e9f0b", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4122), true, "EnableFeatures", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4123), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "89c0bd84221b497c9bc70a5d1847ef72", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4091), true, "Rank", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4092), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "8b95763ff8124027ac1adbb95a13bd81", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4129), true, "CustomForm", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4130), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8c96afbba15b44b1a9fb1c577e208fb6", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4079), true, "InvoiceTemplate", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4080), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "98b3448a6d3f47599cfc05fc2c7706c6", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4120), true, "GeneralSetting", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4120), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "a39201c5b1854fafa03164aa1b2ee424", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4086), true, "VoucherList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4086), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "b0d7358392904df8a1a14dcf9b1cce41", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4094), true, "Tag", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4094), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "b42472b0066d4473b49e178da2ceffc0", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4077), true, "OrderList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4078), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "bef9d0e8c70b4f7baec9e97a84e54a01", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4072), true, "BookingList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4073), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "c19ad04fde4f41699cba3edc43586e47", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4068), true, "ProductList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4068), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c53be22b4ef34c008fe50f8667adeb46", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4126), true, "ShippingFeeConfig", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4127), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "c55233d1766247789cbb294f4a939677", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4104), true, "History", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4104), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "c700f4813f6c4699aaf49c83f2233545", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4036), true, "BranchList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4038), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "d4b37ad79e634cd4ab405bbff7dceca2", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4110), true, "CampaignHistory", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4111), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d95201e9f8d44f45883e519442f3efe7", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4041), true, "AffiliateList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4042), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "db14cb81e53f421f923b87b74aab8df1", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4112), true, "EventTemplateList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4113), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "dbd8fa3a6a2f4a7897eaf227a88df1a9", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4081), true, "DiscountList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4082), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "ec626bd74b4f4d5384231ec3e72b2453", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4124), true, "MembershipExtendDefaults", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4125), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "f9f1ceefa92d4057a324f55a6f6ba599", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4088), true, "MembershipList", new DateTime(2025, 5, 5, 9, 29, 56, 598, DateTimeKind.Local).AddTicks(4088), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "a8551d788f004767a4c8f4799dd34f93");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "00e3474e8e1e4ab9afcdce31c6511960");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "07630c61118243b4bf6f54521afbe1ba");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0ba8ddeabd574355b3222fd7c39e506b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0c36e10aa7de4462b04d28d14d1af2d3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0d51b2374c6b47789c91c70cd31ab25a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0fe7cfe74a4f4d05b06b5e7fe9dfba11");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "114bd1ea63b448b4b3b1962dc652e516");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "17092c4955d343719e02f66baa0af616");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3e655e5332df44128fa9c7e18b31216d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "530e3c2c54ce4d6d908186e79f49e9d2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "545b1b23de344e4d95e65165b6517754");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "56f0adae7dd14c22a55870393752c168");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "63e1f8af22ae4b1ea3d6609651086d07");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "739e88e4ba3c471eba61bf4af5416c20");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "778cb268d81a46ab949e04339cd093ec");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7bc852e23daf422294addb3f183e9f0b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "89c0bd84221b497c9bc70a5d1847ef72");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8b95763ff8124027ac1adbb95a13bd81");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8c96afbba15b44b1a9fb1c577e208fb6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "98b3448a6d3f47599cfc05fc2c7706c6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a39201c5b1854fafa03164aa1b2ee424");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b0d7358392904df8a1a14dcf9b1cce41");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b42472b0066d4473b49e178da2ceffc0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bef9d0e8c70b4f7baec9e97a84e54a01");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c19ad04fde4f41699cba3edc43586e47");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c53be22b4ef34c008fe50f8667adeb46");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c55233d1766247789cbb294f4a939677");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c700f4813f6c4699aaf49c83f2233545");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d4b37ad79e634cd4ab405bbff7dceca2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d95201e9f8d44f45883e519442f3efe7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "db14cb81e53f421f923b87b74aab8df1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dbd8fa3a6a2f4a7897eaf227a88df1a9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ec626bd74b4f4d5384231ec3e72b2453");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f9f1ceefa92d4057a324f55a6f6ba599");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "ActionButtonConfigs");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEEcPQmzFlWDpwrqX6lEhyOMoXtIf4+AS0K8oO20Uufiznuvnjo05H+eFiIxb5Nq+gg==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "7669df895d73456a8bf134cc0a900349", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 4, 0, 8, 58, 490, DateTimeKind.Local).AddTicks(4396), "FeaturesButton", new DateTime(2025, 5, 4, 0, 8, 58, 490, DateTimeKind.Local).AddTicks(4408) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "0599752851f84af29f30a8e6b11c4fce", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(1013), true, "Overview", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5216), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "1807f86b67be40d6b7d46d24e900192f", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6034), true, "History", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6034), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "193952b325b84fbc9f9374dd825981a1", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5972), true, "History", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5972), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "1a5759ac54ad495595699824c8b6bc6a", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5939), true, "Brand", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5940), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "1f603528c4944ce288efb2ce7610520c", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5964), true, "MembershipList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5965), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "1f67946195b44a96a8c564565a3cab9b", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5977), true, "GameList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5978), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "1f6862a8d2914eb3b74cab5d60ce2a20", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6041), true, "CampaignHistory", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6041), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "2cc201acd93c4e349b4f9464b59bbca6", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6045), true, "EventTemplateHistory", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6046), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "2e0067561ec545078867d553bcdc85c3", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5974), true, "SurveyList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5974), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "30170463c05e452dbea50566e8eb06f0", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6047), true, "TemplateUidList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6048), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "33e89d68c08449c4826177e9b52bf83f", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5970), true, "Tag", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5970), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "37764e17602e41d6a57c4b35abf3fd32", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5958), true, "DiscountList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5958), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "3d917cd4d5a54c71a9b62fc632d951ac", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6055), true, "MembershipExtendDefaults", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6056), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "3db5a4f9e19c4cc4be3b6a7ce7317a3d", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6039), true, "CampaignList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6039), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "455579a5c01b44cf8ab8936df9167f81", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6051), true, "GeneralSetting", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6051), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "4b64cdd8f5d74173864fbef4daaf9a0e", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5953), true, "InvoiceTemplate", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5954), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "5b03d93687ec4a6f8bbf1afcb167da35", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5962), true, "VoucherList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5962), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "6371743da95b42dc888d0c3acb922203", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6058), true, "ShippingFeeConfig", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6058), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "65c2d9d8a4994ed29b8f233ff493f7fc", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6037), true, "GamePrize", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6037), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "70e5b6c7d49c4bac99e48c50a3a1010d", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5967), true, "Rank", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5968), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "71a30ffa4fed47349497c24436869e45", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5937), true, "Category", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5937), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "935826b1c670497680801c440b2e0e64", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5915), true, "BranchList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5917), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "9ae8946f0b3849adbb354a52ec7a59ee", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6043), true, "EventTemplateList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6043), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "aea3de85262847258417fff19d68b632", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6053), true, "EnableFeatures", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6054), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "b11b5701cde1431198be3c65ff83d601", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5943), true, "ProductList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5944), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "bb03f9cf14114ce9a9e62024ed628460", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5947), true, "BookingItem", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5947), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "c385950b121b4a9cb6f5ac890492876a", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5951), true, "OrderList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5951), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "ca0479e1920c47399bfdb5d4f64eda60", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5941), true, "ProductProperty", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5942), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "d4e9267c5086404dbd8dd64e7628a1b9", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5921), true, "AffiliateList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5922), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "d7c083b5bb1f4b22b3efbcb59db6bded", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5949), true, "BookingList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5949), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "d8028fdc47a64a9298a37fa493a94073", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5960), true, "Promotion", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5960), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "d8856b7e15cb4ab0a52c64bff8b314ad", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5926), true, "ArticleList", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5926), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "f3fcc5063ed9409f83d9be0a78276404", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5924), true, "ArticleCategory", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5924), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "f53477a08c1144bebae4415036cfda73", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6061), true, "CustomForm", new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6062), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" }
                });
        }
    }
}
