﻿namespace MiniAppCore.Models.Responses.Orders.Carts
{
    public class CartItemGift
    {
        public string? ProductId { get; set; }
        public string? ProductName { get; set; }
        public string? VariantId { get; set; }
        private decimal _originalPrice;
        public decimal OriginalPrice
        {
            get => _originalPrice;
            set
            {
                // Nếu DiscountPrice chưa được set, mặc định bằng OriginalPrice
                _originalPrice = value;
                if (_discountPrice == 0)
                {
                    _discountPrice = value;
                }
            }
        }

        private decimal _discountPrice;
        public decimal DiscountPrice
        {
            get => _discountPrice;
            set => _discountPrice = value;
        }

        public bool IsDiscounted => DiscountPrice < OriginalPrice;
        public decimal DiscountValue => OriginalPrice > 0 ? Math.Round((1 - DiscountPrice / OriginalPrice) * 100, 2) : 0;

        public List<string> Images { get; set; } = new();
    }
}
