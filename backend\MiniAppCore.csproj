﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>1bfc4857-5f5f-4bbd-9bfe-8f6a35012c06</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>.</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="EPPlus" Version="7.6.1" />
    <PackageReference Include="Hangfire" Version="1.8.18" />
    <PackageReference Include="Hangfire.Dashboard.Authorization" Version="3.1.0" />
    <PackageReference Include="Hangfire.Dashboard.BasicAuthorization" Version="1.0.2" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.0" />
    <PackageReference Include="MediatR" Version="13.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="QRCoder" Version="1.6.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.0" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Services\OmniTool\Omni\Models\SendOmniMessgeResponse.cs" />
  </ItemGroup>
	<ItemGroup>
		<Folder Include="Features\CustomEntity\Handlers\" />
		<Folder Include="Views\Event\" />
		<Folder Include="Views\EventTrigger\" />
	</ItemGroup>
</Project>
