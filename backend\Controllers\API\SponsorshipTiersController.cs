﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events.Sponsors;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Services.Events.SponsorshipTiers;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class SponsorshipTiersController(ILogger<SponsorshipTiersController> logger, ISponsorshipTierService tierService) : ControllerBase
    {
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] RequestQuery query, [FromQuery] short activeStatus)
        {
            try
            {
                tierService.IsAdmin = User?.IsInRole("ADMIN") ?? false;

                var result = await tierService.GetPaged(query, activeStatus);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        #region ADMIN FUNCTIONS

        [HttpPost]
        public async Task<IActionResult> Create([FromForm] SponsorshipTierDTO request)
        {
            try
            {
                await tierService.CreateTierAsync(request);

                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo hạng nhà tài trợ thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Create(string id, [FromForm] SponsorshipTierDTO request)
        {
            try
            {
                await tierService.UpdateTierAsync(id, request);

                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật hạng nhà tài trợ thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                await tierService.DeleteTierAsync(id);

                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa hạng nhà tài trợ thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        #endregion
    }

}
