﻿<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
    </div>
    <div class="modal-body">
        <div id="ratingList"></div>
        <div class="text-center mt-2" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        <div class="text-center" id="loadMoreContainer">
            <button class="btn btn-primary d-none" id="btnLoadMore" data-page="1" data-product="@Model">
                Xem thêm
            </button>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
    </div>
</div>

<script>
    $(document).on("click", ".media-preview", function () {
        const type = $(this).data("type");
        const src = $(this).data("src");
        let content = "";

        if (type === "image") {
            content = `<img src="${src}" class="img-fluid rounded" style="max-height: 80vh;" />`;
        } else if (type === "video") {
            content = `
                <video controls autoplay style="max-height: 80vh;" class="w-100 rounded bg-dark">
                    <source src="${src}" type="video/mp4" />
                </video>`;
        }

        $("#mediaPreviewContent").html(content);
        $("#mediaPreviewModal").modal("show");
    });

    function GetFormOrder(id) {
        const url = id ? `@Url.Action("Detail", "Order")/${id}` : "@Url.Action("Create", "Order")"
        $.ajax({
            url: url,
            type: 'GET',
            success: function (data) {
                $("#modal-order-content").html(data);
                $("#modal-order").modal("toggle");
                $("#submitButton").addClass("d-none");
            }
        })
    }
</script>
