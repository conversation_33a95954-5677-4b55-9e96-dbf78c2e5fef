﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Models.Responses.Orders;
using MiniAppCore.Models.Responses.Products.Ratings;
using MiniAppCore.Services.Memberships;
using MiniAppCore.Services.Orders;
using System.Text.Json;

namespace MiniAppCore.Services.Products.Ratings
{
    public class ProductRatingService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        IWebHostEnvironment env,
        IHttpContextAccessor httpContextAccessor,
        IMembershipService membershipService,
        IOrderService orderService) : Service<OrderDetailReview>(unitOfWork), IProductRatingService
    {
        private readonly string _hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";
        private readonly IRepository<OrderDetail> _orderDetailRepo = unitOfWork.GetRepository<OrderDetail>();

        public async Task<int> AddProductRatingAsync(string orderId, string userZaloId, ProductRatingRequest request)
        {
            // Validate order
            var order = await ValidateOrderAccess(orderId, userZaloId, isWriteOperation: true);

            // Check if order is already rated
            //bool isRated = await IsOrderRatedAsync(orderId);
            //if (isRated)
            //{
            //    throw new CustomException(200, "Đơn hàng này đã được đánh giá");
            //}

            // Get order details
            var orderDetails = await _orderDetailRepo.AsQueryable()
                .Where(x => x.OrderId == orderId)
                .ToListAsync();

            if (orderDetails.Count == 0)
            {
                throw new CustomException(200, "Không tìm thấy chi tiết đơn hàng");
            }

            // Validate product ratings
            if (request.Products.Count == 0)
            {
                throw new CustomException(200, "Vui lòng đánh giá ít nhất một sản phẩm");
            }

            // Get existing ratings for this order's products
            var orderDetailIds = orderDetails.Select(x => x.Id).ToList();
            var existingRatings = await _repository.AsQueryable()
                .Where(x => orderDetailIds.Contains(x.OrderDetailId))
                .ToListAsync();
            int addedCount = 0;

            // Process ratings for each product
            foreach (var productRating in request.Products)
            {
                // Check if this specific product already has a rating
                var existingRating = existingRatings.FirstOrDefault(x => x.ProductId == productRating.ProductId);
                if (existingRating != null)
                {
                    // Skip already rated products
                    continue;
                }

                var orderDetail = orderDetails.FirstOrDefault(x => x.ProductId == productRating.ProductId);
                if (orderDetail == null)
                {
                    throw new CustomException(200, $"Sản phẩm có ID {productRating.ProductId} không tồn tại trong đơn hàng");
                }

                // Create review object
                var review = new OrderDetailReview
                {
                    OrderDetailId = orderDetail.Id,
                    ProductId = productRating.ProductId,
                    ReviewPoint = productRating.Star,
                    ReviewContent = productRating.Review ?? string.Empty,
                    UserZaloId = userZaloId,
                };

                // Process images if present
                if (productRating.Images?.Count > 0)
                {
                    review.Images = await ProcessUploadForRating(
                        productRating.Images,
                        $"ratings/{userZaloId}/{orderId}");
                }

                // Process videos if present
                if (productRating.Videos?.Count > 0)
                {
                    review.Videos = await ProcessUploadForRating(
                        productRating.Videos,
                        $"ratings/{userZaloId}/{orderId}");
                }

                // Add to repository
                _repository.Add(review);
                addedCount++;
            }

            // Only save if we actually added any ratings
            if (addedCount == 0)
            {
                return 0; // No ratings were added
            }

            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<ProductRatingResponse> GetProductRatingAsync(string orderId, string userZaloId)
        {
            // Validate order access
            var order = await ValidateOrderAccess(orderId, userZaloId, isWriteOperation: false);

            // Get order details
            var orderDetails = await _orderDetailRepo.AsQueryable()
                .Where(x => x.OrderId == orderId)
                .ToListAsync();

            if (orderDetails.Count == 0)
            {
                throw new CustomException(200, "Không tìm thấy chi tiết đơn hàng");
            }

            var response = new ProductRatingResponse { OrderId = orderId };

            // Get ratings for this order
            var orderDetailIds = orderDetails.Select(x => x.Id).ToList();
            var ratings = await _repository.AsQueryable()
                .Where(x => orderDetailIds.Contains(x.OrderDetailId))
                .ToListAsync();


            // Build response for each product
            foreach (var orderDetail in orderDetails)
            {
                var rating = ratings.FirstOrDefault(x => x.OrderDetailId == orderDetail.Id);
                var orderItem = order.OrderDetails.FirstOrDefault(x => x.ProductId == orderDetail.ProductId);

                var ratingItem = new ProductRatingItemResponse
                {
                    Product = orderItem,
                    OrderDetailId = orderDetail.Id,
                    IsRating = rating != null,
                    Star = rating?.ReviewPoint ?? 5
                };

                if (rating != null)
                {
                    ratingItem.Star = rating.ReviewPoint;
                    ratingItem.Review = rating.ReviewContent;
                    ratingItem.Images = DeserializeMediaPaths(rating.Images);
                    ratingItem.Videos = DeserializeMediaPaths(rating.Videos);
                }

                response.Products.Add(ratingItem);
            }

            return response;
        }

        public async Task<CustomerProductRatingResponse?> GetRatingByOrderIdAsync(string orderId)
        {
            // Get the order details
            var order = await orderService.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                return null;
            }

            // Get order details
            var orderDetails = await _orderDetailRepo.AsQueryable()
                .Where(x => x.OrderId == orderId)
                .ToListAsync();

            if (orderDetails.Count == 0)
            {
                return null;
            }

            // Create response with customer information
            var userZaloId = order.UserZaloId ?? string.Empty;
            var membership = await membershipService.GetByUserZaloId(userZaloId);
            var response = new CustomerProductRatingResponse
            {
                OrderId = orderId,
                CustomerId = userZaloId,
                CustomerName = membership?.UserZaloName ?? "Khách vãng lai",
                CustomerPhone = membership?.PhoneNumber ?? "Không có thông tin",
            };

            // Get ratings for this order
            var orderDetailIds = orderDetails.Select(x => x.Id).ToList();
            var ratings = await _repository.AsQueryable()
                .Where(x => orderDetailIds.Contains(x.OrderDetailId))
                .ToListAsync();

            // Build response for each product
            foreach (var orderDetail in orderDetails)
            {
                var rating = ratings.FirstOrDefault(x => x.OrderDetailId == orderDetail.Id);
                var orderItem = order.OrderDetails.FirstOrDefault(x => x.ProductId == orderDetail.ProductId);

                var ratingItem = new ProductRatingItemResponse
                {
                    Product = orderItem,
                    OrderDetailId = orderDetail.Id,
                    IsRating = rating != null,
                    Star = rating?.ReviewPoint ?? 5
                };

                if (rating != null)
                {
                    ratingItem.Star = rating.ReviewPoint;
                    ratingItem.Review = rating.ReviewContent;
                    ratingItem.Images = DeserializeMediaPaths(rating.Images);
                    ratingItem.Videos = DeserializeMediaPaths(rating.Videos);
                    response.RatingDate = rating.CreatedDate;
                }
                response.Products.Add(ratingItem);
            }

            return response;
        }

        public async Task<(PagedResult<ProductReviewDetailResponse>, SummaryProductReviewResponse)> GetProductReviews(string productId, RequestQuery query, bool isAdmin = false)
        {
            var reviews = _repository.AsQueryable().AsNoTracking().Where(r => r.ProductId == productId);

            //if (!string.IsNullOrEmpty(query.Keyword))
            //{
            //    products = products.Where(p => p.Name.Contains(query.Keyword));
            //}

            if (!isAdmin)
            {
                reviews = reviews.Where(r => r.IsShow == true);
            }

            var totalItems = await reviews.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)query.PageSize);

            var averagePoint = totalItems > 0 ? await reviews.Where(r => r.ReviewPoint > 0).AverageAsync(r => r.ReviewPoint) : 0;

            var summary = new SummaryProductReviewResponse
            {
                TotalRating = totalItems,
                AveragePoint = Math.Round(averagePoint, 1)
            };

            // Lấy dữ liệu từ database trước
            var reviewList = await reviews
                .OrderByDescending(x => x.CreatedDate)
                .Skip(query.Skip)
                .Take(query.PageSize)
                .ToListAsync();

            var memberZaloIds = reviewList.Select(r => r.UserZaloId).Distinct().ToList();
            var members = await membershipService.GetByUserZaloIds(memberZaloIds);
            var memberDict = members.ToDictionary(m => m.UserZaloId, m => m);

            var orderDetailIds = reviewList.Select(r => r.OrderDetailId).Distinct().ToList();
            var orders = await _orderDetailRepo.AsQueryable().Where(d => orderDetailIds.Contains(d.Id)).ToListAsync();
            var orderIdDict = orders.ToDictionary(m => m.Id, m => m.OrderId);

            // Ánh xạ dữ liệu và xử lý Images
            var items = reviewList.Select(r =>
            {
                memberDict.TryGetValue(r.UserZaloId, out var member);
                orderIdDict.TryGetValue(r.OrderDetailId, out var orderId);

                var reviewResponse = mapper.Map<ProductReviewDetailResponse>((member, r));

                reviewResponse.Images = DeserializeMediaPaths(r.Images ?? "");
                reviewResponse.Videos = DeserializeMediaPaths(r.Videos ?? "");
                reviewResponse.OrderId = isAdmin ? orderId : "";
                reviewResponse.IsRating = true;

                return reviewResponse;
            }).ToList();

            var result = new PagedResult<ProductReviewDetailResponse>
            {
                Data = items,
                TotalPages = totalPages
            };

            return (result, summary);
        }

        public async Task<int> UpdateReviewShown(string id, bool isShow)
        {
            var review = await _repository.FindByIdAsync(id);
            if (review == null)
            {
                throw new CustomException(404, "Không tìm thấy đánh giá này");
            }

            review.IsShow = isShow;
            _repository.Update(review);

            return await unitOfWork.SaveChangesAsync();
        }

        #region private helpers method

        /// <summary>
        /// Check order rated
        /// </summary>
        public Task<bool> IsOrderRatedAsync(string orderId)
        {
            // Get order detail IDs
            var orderDetailIds = _orderDetailRepo.AsQueryable()
                .Where(x => x.OrderId == orderId)
                .Select(x => x.Id);

            // Check if any reviews exist for these order details
            return _repository.AsQueryable()
                .AnyAsync(x => orderDetailIds.Contains(x.OrderDetailId));
        }

        /// <summary>
        /// Validates order exists and user has access to it
        /// </summary>
        private async Task<OrderDetailResponse> ValidateOrderAccess(string orderId, string userZaloId, bool isWriteOperation)
        {
            var order = await orderService.GetOrderByIdAsync(orderId);
            if (order == null)
            {
                throw new CustomException(200, "Không tìm thấy đơn hàng này");
            }

            // Check if order belongs to current user
            if (order.UserZaloId != userZaloId)
            {
                string message = isWriteOperation
                    ? "Bạn không có quyền đánh giá đơn hàng này"
                    : "Bạn không có quyền xem đánh giá đơn hàng này";

                throw new CustomException(403, message);
            }

            return order;
        }

        /// <summary>
        /// Process file uploads for ratings
        /// </summary>
        private async Task<string> ProcessUploadForRating(List<IFormFile> files, string subFolder)
        {
            var savePath = Path.Combine(env.WebRootPath, "uploads", subFolder);

            // Ensure directory exists
            Directory.CreateDirectory(savePath);

            var fileResult = await FileHandler.SaveFiles(files, savePath);

            // Convert file paths to JSON for DB storage
            var paths = fileResult.Select(f => $"/uploads/{subFolder}/{Path.GetFileName(f)}").ToList();
            return JsonSerializer.Serialize(paths);
        }

        /// <summary>
        /// Deserializes media paths from JSON and adds host URL
        /// </summary>
        private List<string> DeserializeMediaPaths(string jsonPaths)
        {
            if (string.IsNullOrEmpty(jsonPaths))
                return new List<string>();

            try
            {
                var paths = JsonSerializer.Deserialize<List<string>>(jsonPaths) ?? new List<string>();
                return paths.Select(path => $"{_hostUrl}{path}").ToList();
            }
            catch
            {
                return new List<string>();
            }
        }

        #endregion
    }
}
