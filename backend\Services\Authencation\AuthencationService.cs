﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.DTOs.SystemSettings;
using MiniAppCore.Services.Memberships;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace MiniAppCore.Services.Authencation
{
    public class AuthencationService(IConfiguration configuration,
                                     IUnitOfWork unitOfWork,
                                     IMembershipService membershipService,
                                     UserManager<ApplicationUser> userManager) : IAuthencationService
    {
        private readonly IRepository<ViewPermission> _viewPermissionRepo = unitOfWork.GetRepository<ViewPermission>();
        private readonly IRepository<Common> _commonRepo = unitOfWork.GetRepository<Common>();

        private async Task<List<ViewPermission>> GetUserViewPermissionsAsync(string userId)
        {
            return await _viewPermissionRepo.AsQueryable().Where(vp => vp.UserId == userId && vp.IsActive).ToListAsync();
        }

        private async Task<Common> GetCommonAsync(string name)
        {
            return await _commonRepo.AsQueryable().FirstOrDefaultAsync(c => c.Name == name);
        }

        public async Task<List<Claim>> GetUserClaimsAsync(string email, string password)
        {
            var user = await userManager.FindByEmailAsync(email);

            if (user == null || user.Email == null)
            {
                throw new Exception("Không tìm thấy người dùng.");
            }

            if (!(await userManager.CheckPasswordAsync(user, password)))
            {
                throw new Exception("Tên đăng nhập hoặc khẩu không đúng. Vui lòng kiểm tra lại.");
            }

            var userRoles = await userManager.GetRolesAsync(user);

            var authClaims = new List<Claim>
            {
                new Claim("UserId", user.Id),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            // user roles
            foreach (var userRole in userRoles)
            {
                authClaims.Add(new Claim(ClaimTypes.Role, userRole));
            }

            // Add view permissions to claims
            var viewPermissions = await GetUserViewPermissionsAsync(user.Id);
            foreach (var permission in viewPermissions)
            {
                authClaims.Add(new Claim("ViewPermission", $"{permission.ViewId}:{permission.SubViewId}"));
            }

            var cmsRef = await GetCommonAsync("CMSRef");
            if (cmsRef != null && cmsRef.Content != null)
            {
                var cmsRefContent = Newtonsoft.Json.JsonConvert.DeserializeObject<List<CMSRefDto>>(cmsRef.Content);
                if (cmsRefContent != null)
                {
                    foreach (var info in cmsRefContent)
                    {
                        if (string.IsNullOrEmpty(info.key) || string.IsNullOrEmpty(info.value))
                        {
                            continue;
                        }

                        authClaims.Add(new Claim(info.key, info.value));
                    }
                }
            }

            return authClaims;
        }

        public Task<List<Claim>> GetClaimsCmsAsync(string email, string password)
        {
            return GetUserClaimsAsync(email, password);
        }

        public async Task<string> LoginCms(string email, string password)
        {
            var claims = await GetUserClaimsAsync(email, password);
            return JwtGenerator.GenerateJwtToken(configuration, claims);
        }

        public async Task<string> LoginMiniApp(string phoneNumber, string userZaloId)
        {
            var membership = await membershipService.GetMember(phoneNumber, userZaloId, true);

            if (membership == null)
            {
                throw new CustomException(200, $"Số điện thoại {phoneNumber} chưa đăng kí thành viên!");
            }

            List<Claim> claims = new List<Claim>()
            {
                new Claim("UserId", membership.Id),
                new Claim("UserZaloId", membership.UserZaloId),
                new Claim("PhoneNumber", membership.PhoneNumber),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
            };

            return JwtGenerator.GenerateJwtToken(configuration, claims);
        }
    }
}
