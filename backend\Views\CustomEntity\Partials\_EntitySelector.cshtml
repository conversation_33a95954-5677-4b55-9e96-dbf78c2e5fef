@model string
@{
    var availableEntities = ViewBag.AvailableEntities as List<string>;
    var selectedEntity = Model;
}

<div class="card border-primary mb-4">
    <div class="card-header bg-primary bg-soft">
        <div class="d-flex align-items-center">
            <div class="bg-primary rounded-circle p-2 me-3">
                <i class="ri-building-line text-white"></i>
            </div>
            <div>
                <h5 class="mb-0 text-primary fw-bold">Bước 1: Chọn Entity</h5>
                <small class="text-muted">Chọn loại dữ liệu bạn muốn cấu hình custom fields</small>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="row align-items-end">
            <div class="col-md-8">
                <label for="entitySelector" class="form-label fw-semibold">
                    <i class="ri-database-2-line me-1"></i>
                    Chọ<PERSON> <span class="text-danger">*</span>
                </label>
                <select id="entitySelector" class="form-select form-select-lg">
                    <option value="">🔹 Chọn loại dữ liệu cần cấu hình...</option>
                    @if (availableEntities != null)
                    {
                        @foreach (var entity in availableEntities)
                        {
                            <option value="@entity" selected="@(entity == selectedEntity)">
                                📁 @entity
                            </option>
                        }
                    }
                </select>
                <div class="form-text">
                    <i class="ri-information-line text-info me-1"></i>
                    Mỗi entity có thể có các custom fields riêng để mở rộng thông tin
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid gap-2">
                    <button id="configureEntityBtn" type="button" class="btn btn-primary btn-lg" disabled>
                        <i class="ri-settings-3-line me-2"></i>
                        Cấu hình Fields
                    </button>
                    <button id="viewEntityBtn" type="button" class="btn btn-outline-primary" disabled>
                        <i class="ri-eye-line me-2"></i>
                        Xem Fields hiện có
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Stats (when entity is selected) -->
        <div id="entityStats" class="mt-3" style="display: none;">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="border rounded p-3 text-center bg-light">
                        <div class="text-primary fs-4 fw-bold" id="totalFields">0</div>
                        <small class="text-muted">Tổng Fields</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 text-center bg-light">
                        <div class="text-warning fs-4 fw-bold" id="requiredFields">0</div>
                        <small class="text-muted">Bắt buộc</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 text-center bg-light">
                        <div class="text-success fs-4 fw-bold" id="optionalFields">0</div>
                        <small class="text-muted">Tùy chọn</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="border rounded p-3 text-center bg-light">
                        <div class="text-info fs-4 fw-bold" id="lastUpdated">-</div>
                        <small class="text-muted">Cập nhật gần nhất</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("Scripts/_EntitySelectorScripts")