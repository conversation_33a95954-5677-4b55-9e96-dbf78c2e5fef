﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Entities.Products;
using MiniAppCore.Entities.Products.Variants;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Carts;
using MiniAppCore.Models.Responses.Orders.Carts;
using MiniAppCore.Models.Responses.Products;
using MiniAppCore.Services.Offers.Discounts;
using MiniAppCore.Services.Offers.Promotions;
using MiniAppCore.Services.Products;
using MiniAppCore.Services.Products.Variants;

namespace MiniAppCore.Services.Orders.Carts
{
    public class CartService(IUnitOfWork unitOfWork,
                             IHttpContextAccessor httpContextAccessor,
                             IMapper mapper,
                             IProductService productService,
                             IDiscountService discountService,
                             IPromotionService promotionService,
                             IProductPropertyService productPropertyService) : Service<Cart>(unitOfWork), ICartService
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        // 🛒 Lấy giỏ hàng của người dùng
        public async Task<CartResponse> GetUserCart(RequestQuery query, string userId, string? branchId)
        {
            // ✅ Lấy danh sách giỏ hàng của user
            var queryable = _repository.AsQueryable()
                .Where(c => c.UserZaloId == userId);

            if (!string.IsNullOrEmpty(branchId))
            {
                queryable = queryable.Where(c => c.BranchId == branchId);
            }

            var cartItems = await queryable
                .OrderByDescending(c => c.CreatedDate)
                .ToListAsync(); // 🔥 Lấy tất cả giỏ hàng của user

            if (!cartItems.Any())
            {
                return new CartResponse
                {
                    TotalPrice = 0,
                    TotalQuantity = 0,
                    CartItems = new List<CartItemResponse>()
                };
            }

            // ✅ Sử dụng hàm chung để lấy danh sách CartItemResponse
            var cartItemResponses = await GetCartItemsResponse(cartItems);
            var totalPages = (int)Math.Ceiling((double)cartItemResponses.Count / query.PageSize);

            cartItemResponses = cartItemResponses.Skip(query.Skip).Take(query.PageSize).ToList();

            return new CartResponse
            {
                TotalPrice = cartItemResponses.Sum(c => c.DiscountPrice * c.Quantity),
                TotalQuantity = cartItemResponses.Sum(c => c.Quantity),
                CartItems = cartItemResponses,
                TotalPages = totalPages
            };
        }

        public async Task<List<CartItemResponse>> GetCartItemsResponse(List<Cart> cartItems)
        {
            // ✅ Lấy danh sách VariantId từ giỏ hàng
            var variantIds = cartItems
                .Where(c => !string.IsNullOrEmpty(c.VariantId))
                .Select(c => c.VariantId!)
                .ToList();

            // ✅ Lấy thông tin giá của Variant
            var variants = await unitOfWork.GetRepository<Variant>()
                .AsQueryable()
                .Where(v => variantIds.Contains(v.Id))
                .ToDictionaryAsync(v => v.Id, v => v.Price);

            // ✅ Lấy danh sách PropertyValues của Variant
            var variantValueMap = await productPropertyService.GetPropertyValuesByVariantIds(variantIds);

            // ✅ Lấy danh sách sản phẩm từ ProductService
            var productIds = cartItems.Select(x => x.ProductId).ToList();
            var productsInCart = await productService.GetByIdsAsync(productIds);

            // lấy danh sách discount cho sản phẩm này, xong rồi discountDict
            var discount = await discountService.GetDiscountsByProductIdsAsync(productIds);
            var discountDict = discount.Item1; // Danh sách Discount
            var discountItemsDict = discount.Item2; // Danh sách DiscountItem

            // giá variant có trong product
            var variantsDict = await productPropertyService.GetPriceByVariantIds(variantIds);

            var productsGiftsDict = new Dictionary<string, List<ProductResponse>>();
            foreach (var item in cartItems)
            {
                productsGiftsDict[item.ProductId] = await promotionService.GetProductGiftByProductId(item.ProductId);
            }

            // ✅ Ánh xạ dữ liệu từ Cart + Product sang CartItemResponse
            return cartItems
                .Select(cartItem => CreateCartItemResponse(cartItem, productsInCart, variants, variantValueMap, productsGiftsDict[cartItem.ProductId], discountDict, discountItemsDict, variantsDict))
                .Where(response => response != null)
                .ToList()!;
        }

        #region Cart Helpers

        private CartItemResponse? CreateCartItemResponse(
                Cart cartItem,
                IEnumerable<Product> productsInCart,
                Dictionary<string, decimal> variants,
                Dictionary<string, List<string>> variantValueMap,
                List<ProductResponse> gifts,
                IEnumerable<Discount> discounts,
                IEnumerable<DiscountItem> discountItems,
                Dictionary<string, decimal> variantDicts)
        {
            var product = productsInCart.FirstOrDefault(p => p.Id == cartItem.ProductId);
            if (product == null) return null;

            var cartItemResponse = mapper.Map<CartItemResponse>((cartItem, product));

            cartItemResponse.VariantId = cartItem.VariantId;
            cartItemResponse.Images = GetProductImages(product);

            // ✅ Thêm danh sách giá trị của Variant
            if (!string.IsNullOrEmpty(cartItem.VariantId) && variantValueMap.TryGetValue(cartItem.VariantId, out var propertyValues))
            {
                cartItemResponse.PropertyValues = propertyValues;
            }

            // Nếu có variant thì sử dụng giá variant, ngược lại dùng giá gốc của product
            var originalPrice = !string.IsNullOrEmpty(cartItem.VariantId) && variants.TryGetValue(cartItem.VariantId, out var variantPrice)
                ? variantPrice
                : product.Price;

            cartItemResponse.OriginalPrice = originalPrice;
            cartItemResponse.DiscountPrice = originalPrice;

            // Tính giảm giá
            var (discountType, discountPrice) = discountService.CalculateDiscountedPrice(product.Id, cartItemResponse.DiscountPrice, discountItems, discounts);
            cartItemResponse.DiscountType = discountType;
            cartItemResponse.DiscountPrice = discountPrice;

            // ✅ Thêm quà tặng nếu có
            if (gifts.Any())
            {
                cartItemResponse.Gifts = gifts.Select(mapper.Map<CartItemGift>).ToList();
            }

            return cartItemResponse;
        }

        private List<string> GetProductImages(Product product)
        {
            return string.IsNullOrEmpty(product.Images)
                ? new List<string>()
                : product.Images.Split(",").Select(x =>
                    {
                        var trimmed = x.Trim();
                        if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                            return trimmed;
                        else
                            return $"{hostUrl}/uploads/images/products/{trimmed}";
                    }).ToList();
        }

        #endregion

        #region Cart functions

        // ➕ Thêm sản phẩm vào giỏ hàng
        public async Task<Cart> AddItem(string userId, CartItemRequest request, bool increaseQuantity = true)
        {
            var existingCartItem = await _repository.AsQueryable().FirstOrDefaultAsync(c =>
                c.UserZaloId == userId && c.ProductId == request.ProductId && c.VariantId == request.VariantId && c.Note == request.Note && c.BranchId == request.BranchId);

            if (existingCartItem != null)
            {
                if (increaseQuantity)
                    existingCartItem.Quantity += request.Quantity;
                else
                    existingCartItem.Quantity = request.Quantity;

                _repository.Update(existingCartItem);
                await unitOfWork.SaveChangesAsync();
                return existingCartItem;
            }

            var product = await productService.GetByIdAsync(request.ProductId);
            if (product == null)
            {
                throw new CustomException(200, "Sản phẩm không tồn tại!");
            }

            if (product != null && product.Status != EProduct.InStock)
            {
                throw new CustomException(200, "Sản phẩm không tồn tại!");
            }

            var basePrice = product?.Price ?? 0;
            if (!string.IsNullOrEmpty(request.VariantId))
            {

                var variantsDict = await productPropertyService.GetPriceByVariantIds(new List<string>() { request.VariantId });
                // Ưu tiên dùng giá variant nếu có, ngược lại dùng giá gốc của sản phẩm
                basePrice = !string.IsNullOrEmpty(request.VariantId) && variantsDict.TryGetValue(request.VariantId, out var variantPrice)
                    ? variantPrice
                    : basePrice;
            }

            var newCartItem = new Cart
            {
                Note = request.Note,
                UserZaloId = userId,
                Price = basePrice,
                Quantity = request.Quantity,
                ProductId = request.ProductId,
                VariantId = request.VariantId,
                BranchId = request.BranchId
            };

            _repository.Add(newCartItem);
            await unitOfWork.SaveChangesAsync();
            return newCartItem;
        }

        // ➕ Thêm nhiều sản phẩm vào giỏ hàng
        public async Task<IEnumerable<Cart>> AddItems(string userId, List<CartItemRequest> requests)
        {
            var cartItems = new List<Cart>();

            foreach (var request in requests)
            {
                var item = await AddItem(userId, request, true);
                cartItems.Add(item);
            }

            return cartItems;
        }

        // ✏ Cập nhật sản phẩm trong giỏ hàng
        public async Task<Cart> UpdateItem(string cartItemId, CartItemRequest request)
        {
            var cartItem = await _repository.FindByIdAsync(cartItemId);
            if (cartItem == null) throw new CustomException("Sản phẩm không tồn tại trong giỏ hàng!");

            cartItem.Quantity = request.Quantity;
            cartItem.Note = request.Note;

            _repository.Update(cartItem);
            await unitOfWork.SaveChangesAsync();
            return cartItem;
        }

        // ❌ Xóa một sản phẩm khỏi giỏ hàng
        public async Task RemoveItem(string cartItemId)
        {
            var cartItem = await _repository.FindByIdAsync(cartItemId);
            if (cartItem == null) throw new CustomException("Sản phẩm không tồn tại trong giỏ hàng!");

            await base.DeleteAsync(cartItem);
        }

        // ❌ Xóa nhiều sản phẩm khỏi giỏ hàng
        public async Task RemoveItems(List<string> cartItemIds)
        {
            foreach (var itemId in cartItemIds)
            {
                await RemoveItem(itemId);
            }
        }

        // 🗑 Xóa toàn bộ giỏ hàng
        public async Task ClearCart(string userId)
        {
            var cartItems = await _repository.AsQueryable().Where(c => c.UserZaloId == userId).ToListAsync();
            await base.DeleteRangeAsync(cartItems);
        }

        public async Task ClearCart(string userId, string? branchId)
        {
            var cartItems = await _repository.AsQueryable().Where(c => c.UserZaloId == userId && c.BranchId == branchId).ToListAsync();
            await base.DeleteRangeAsync(cartItems);
        }

        // 🔢 Lấy tổng số lượng sản phẩm trong giỏ hàng
        public async Task<long> GetTotalQuantity(string userId)
        {
            var cartItems = await _repository.AsQueryable().Where(c => c.UserZaloId == userId).ToListAsync();
            return cartItems.Sum(c => c.Quantity);
        }

        // 🔄 Đồng bộ giỏ hàng khi đăng nhập
        public async Task SyncCart(string guestId, string userId)
        {
            var userCartQuery = _repository.AsQueryable().Where(c => c.UserZaloId == userId);
            var guestCartQuery = _repository.AsQueryable().Where(c => c.UserZaloId == guestId);

            var userCart = await userCartQuery.ToListAsync();
            var guestCart = await guestCartQuery.ToListAsync();

            // Nếu không có sản phẩm trong giỏ hàng của khách, không làm gì cả
            if (!guestCart.Any())
            {
                return;
            }

            foreach (var guestItem in guestCart)
            {
                var existingItem = userCart.FirstOrDefault(c => c.ProductId == guestItem.ProductId && c.VariantId == guestItem.VariantId && c.Note == guestItem.Note);
                if (existingItem != null)
                {
                    // Nếu sản phẩm đã tồn tại -> Cộng dồn số lượng
                    existingItem.Quantity += guestItem.Quantity;
                }
                else
                {
                    // Nếu sản phẩm chưa có -> Chuyển từ guest sang user
                    guestItem.UserZaloId = userId;
                }
            }

            // Lưu thay đổi vào database
            await unitOfWork.SaveChangesAsync();
        }

        #endregion
    }
}
