﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Categories;
using MiniAppCore.Services.Categories;
using Newtonsoft.Json;

namespace MiniAppCore.Controllers.API
{

    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    [Route("api/[controller]")]
    public class CategoriesController(ILogger<CategoriesController> logger, ICategoryService categoryService) : ControllerBase
    {
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> GetPage([FromQuery] CategoryQueryParams query, [FromQuery] string? branchId, [FromQuery] bool isGetAll = false, [FromQuery] bool excludeAlreadyItems = false)
        {
            try
            {
                //var hasRoleClaim = User.Claims.Any(c => c.Type == ClaimTypes.Role);
                //var claims = User.Claims;
                //if (!hasRoleClaim && string.IsNullOrEmpty(branchId))
                //{
                //    return Ok(new
                //    {
                //        Code = 1,
                //        Message = "BranchId là bắt buộc!"
                //    });
                //}
                //else if (hasRoleClaim && !isGetAll)
                //{
                //    branchId = User.Claims.FirstOrDefault(c => c.Type == "BranchId")?.Value ?? string.Empty;
                //}

                var result = await categoryService.GetPage(query);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        //[HttpPost("PullProduct")]
        //[Authorize(Roles = "EMPLOYEE")]
        //public async Task<IActionResult> PullCategoriesForBranch([FromBody] PullBranchRequest dto)
        //{
        //    try
        //    {
        //        var branchId = User.Claims.FirstOrDefault(c => c.Type == "BranchId")?.Value ?? string.Empty;
        //        if (string.IsNullOrEmpty(branchId))
        //        {
        //            return Ok(new
        //            {
        //                Code = 1,
        //                Message = "BranchId là bắt buộc!"
        //            });
        //        }
        //        await _categoryService.PullCategoryForBranchId(branchId, dto.Ids);
        //        return Ok(new
        //        {
        //            Code = 0,
        //            Message = "Thành công!",
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex.Message);
        //        logger.LogError(ex.Message);
        //        return StatusCode(200, new
        //        {
        //            Code = 1,
        //            Message = "Internal Server Error"
        //        });
        //    }
        //}

        //[HttpDelete("{id}/Branch")]
        //[Authorize(Roles = "EMPLOYEE")]
        //public async Task<IActionResult> DeleteBranchCategory(int id)
        //{
        //    try
        //    {
        //        var branchId = User.Claims.FirstOrDefault(c => c.Type == "BranchId")?.Value ?? string.Empty;
        //        if (string.IsNullOrEmpty(branchId))
        //        {
        //            return Ok(new
        //            {
        //                Code = 1,
        //                Message = "BranchId là bắt buộc!"
        //            });
        //        }

        //        await _categoryService.DeleteCategoryBranchId(branchId, id);
        //        return Ok(new
        //        {
        //            Code = 0,
        //            Message = "Thành công!",
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        logger.LogError(ex.Message);
        //        return StatusCode(200, new
        //        {
        //            Code = 1,
        //            Message = "Internal Server Error"
        //        });
        //    }
        //}

        [HttpPost]
        public async Task<IActionResult> Create([FromForm] CategoryRequest model, [FromForm(Name = "listChildJson")] string listChildJson)
        {
            try
            {
                if (!string.IsNullOrEmpty(listChildJson))
                {
                    model.listChild = JsonConvert.DeserializeObject<List<CategoryChild>>(listChildJson);
                }
                var rs = await categoryService.CreateAsync(model);
                if (rs > 0)
                {
                    return Ok(new
                    {
                        Code = 0,
                        Message = "Thêm mới danh mục thành công!",
                    });
                }
                else
                {
                    return Ok(new
                    {
                        Code = 1,
                        Message = "Thêm mới danh mục thất bại!",
                    });
                }

            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromForm] CategoryRequest model, [FromForm(Name = "listChildJson")] string listChildJson)
        {
            try
            {
                if (!string.IsNullOrEmpty(listChildJson))
                {
                    model.listChild = JsonConvert.DeserializeObject<List<CategoryChild>>(listChildJson);
                }
                await categoryService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật danh mục thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost("QuickUpdate")]
        public async Task<IActionResult> QuickUpdate([FromBody] List<string> categoryIds)
        {
            try
            {
                if (categoryIds == null || categoryIds.Count == 0)
                {
                    return Ok(new
                    {
                        Code = 1,
                        Message = "Vui lòng chọn ít nhất một danh mục!",
                    });
                }

                var rs = await categoryService.QuickUpdate(categoryIds);

                if (rs > 0)
                {
                    return Ok(new
                    {
                        Code = 0,
                        Message = "Xóa các danh mục thành công!",
                    });
                }
                else
                {
                    return Ok(new
                    {
                        Code = 1,
                        Message = "Xóa các danh mục thất bại!",
                    });
                }

            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {

                await categoryService.DeleteByIdAsync(id);

                //await categoryService.AddOrRemoveCategoryChild(id, new List<CategoryChild>());
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa danh mục thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }
    }
}
