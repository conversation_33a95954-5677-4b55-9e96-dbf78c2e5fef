﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Models.Common;

namespace MiniAppCore.Models.Queries
{
    public class VoucherQueryParams : RequestQuery, IRequestQuery
    {
        public short? Type { get; set; }
        public short? Status { get; set; }
        public decimal? TotalOrder { get; set; }
        public DateTime? EndDate { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public List<string>? ProductIds { get; set; }
        public bool? IsActive { get; set; }
    }
}
