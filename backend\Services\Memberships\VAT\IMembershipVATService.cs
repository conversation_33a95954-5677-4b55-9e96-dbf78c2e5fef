﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Memberships;

namespace MiniAppCore.Services.Memberships.VAT
{
    public interface IMembershipVATService : IService<MembershipVAT>
    {
        Task<int> UpdateAsync(string id, MembershipVATRequest entity);
        Task<int> CreateAsync(string userZaloId, MembershipVATRequest entity);

        Task<MembershipVAT?> GetDefaultMembershipVAT(string userZaloId);
        Task<PagedResult<MembershipVAT>> GetMembershipVATUserZaloId(string userUserZaloId, RequestQuery query);
    }
}
