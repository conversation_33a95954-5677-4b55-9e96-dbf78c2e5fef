﻿@using MiniAppCore.Entities.Products
@using MiniAppCore.Entities.Categories
@using Newtonsoft.Json

@model (Category category, List<CategoryChild> categoryChildren);
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body" style="@(User.IsInRole("EMPLOYEE") ? "pointer-events: none;" : "")">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.category.Id" />
                <div class="row">
                    <div class="row">
                        <div class="col-md-10 ">
                            <div class="form-group">
                                <label>Tên danh mục <span style="color:red">*</span></label>
                                <input id="name" type="text" class="form-control" maxlength="65"
                                       value="@Model.category.Name"
                                       placeholder="Tên danh mục... "
                                       data-errors="Vui lòng nhập tên danh mục." required>
                                <div class="help-block with-errors"></div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Thứ tự hiển thị</label>
                                <input id="orderPriority" type="number" min="0" class="form-control" value="@(Model.category.OrderPriority.ToString("0.##"))" placeholder="Thứ tự hiển thị." oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/g, '')">
                            </div>
                        </div>
                    </div>
                    

                    <div class="col-md-3" hidden>
                        <div class="form-group">
                            <label>Mã màu <span style="color:red">*</span></label>
                            <input id="hexColor" type="color" class="form-control" value="@Model.category.HexColor" placeholder="Mã màu... " data-errors="Vui lòng nhập mã màu." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12" id="branchSelectContainer" style="pointer-events: none;" hidden>
                        <div class="form-group">
                            <label>Chi nhánh</label>
                            <select id="branchId" class="form-control" required multiple disable>
                                @if (ViewBag.Branches != null)
                                {
                                    @* foreach (Branch item in ViewBag.Branches) *@
                                    @* { *@
                                    @*     <option value="@item.Id" selected="@(selectedBranches.Contains(item.Id))">@item.Name</option> *@
                                    @* } *@
                                }
                            </select>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="description">Mô tả danh mục</label>
                            <textarea id="description" class="form-control" rows="4" maxlength="200">@Model.category.Description</textarea>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group row">
                            <label class="col-sm-2">Danh mục con</label>
                            <button type="button" class="btn btn-success col-md-2" onclick="addCategoryChild()">Thêm danh mục con</button> 
                        </div>
                        <div id="div-category-child">
                            <div class="col-md-12">
                                <div class="row">
                                    <div class="col-md-4">
                                        <strong>Tên danh mục con</strong><span style="color:red">*</span>
                                    </div>
                                    <div class="col-md-4">
                                        <strong>Mô tả</strong>
                                    </div>
                                    <div class="col-md-2">
                                        <strong>Thứ tự hiển thị</strong> <span style="color:red">*</span>
                                    </div>
                                    <div class="col-md-2">
                                        <strong>Thao tác</strong>
                                    </div>
                                </div>
                            </div>
                            @if(Model.categoryChildren.Count > 0)
                            {
                                foreach (CategoryChild item in Model.categoryChildren)
                                {
                                    <div id="category-child-@(item.Id)" class="row" style="padding:5px">
                                        <input id="Id_@(item.Id)" value="@(item.Id)" hidden />
                                        <input id="CreatedDate_@(item.Id)" value="@(item.CreatedDate)" hidden />
                                        <input id="UpdatedDate_@(item.Id)" value="@(item.UpdatedDate)" hidden />
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" id="name_@(item.Id)" value="@(item.Name)" placeholder="Tên danh mục con" />
                                        </div>
                                        <div class="col-md-4">
                                            <input id="discription_@(item.Id)" type="text" class="form-control" value="@(item.Description)" placeholder="Mô tả"  />
                                        </div>
                                        <div class="col-md-2">
                                            <input id="orderPriority_@(item.Id)" type="number" class="form-control" value="@(item.OrderPriority)" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/g, '')" placeholder="thứ tự" />
                                        </div>
                                        <div class="col-md-2">
                                            <a onclick="removeCategoryChild('@(item.Id)')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                            </a>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="col-md-12 text-center p-2" id="noInfo-category-child">Không có danh mục con </div>
                            }
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Ảnh <!--<span style=" color:red">*</span>--></label>
                            <input type="file" id="pics" class="form-control image-file" name="pic" accept="image/*"
                                   onchange="handleImagePreview(this, { width: 0, height: 0 }, null, '#preview', {isSingleImage: true, defaultImagePath: '/images/no-image-2.jpg'})">
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div id="preview" class="row d-flex flex-nowrap" style="overflow-x: scroll;">
                            @if (!string.IsNullOrEmpty(Model.category.Images))
                            {
                                var images = Model.category.Images.Split(",").Select(x => $"/uploads/images/categories/{x}");
                                foreach (var item in images)
                                {
                                    <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                        <img src="@item" class="card-img-top h-100" alt="Alternate Text" style="object-fit:contain" />
                                        <span class="btn-preview-remove" data-url="@item">x</span>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (User.IsInRole("ADMIN"))
        {
            <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
            <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.category.Id')">@ViewBag.Button</button>
        }
    </div>
</div>

<script>

     var lstCtChild = @Html.Raw(JsonConvert.SerializeObject(Model.categoryChildren))

    $(document).ready(() => {
        currentImages = $("#preview .image-preview img").length;
        $("#branchId").select2({
            dropdownParent: $("#modal-category")
        })
        $("#isGlobalCategorySelect").change(function () {
            if ($(this).val() === "true") {
                $("#branchSelectContainer").hide();
            } else {
                $("#branchSelectContainer").show();
            }
        });
    });


    var renderCategoryChildItem = (item) => {
        $("#noInfo-category-child").html("")
        $("#div-category-child").append(`
      <div id="category-child-${item.Id}" class="row" style="padding:5px">
        <input id="Id_${item.Id}" value="${item.Id}" hidden />
        <input id="CreatedDate_${item.Id}" value="${item.CreatedDate}" hidden />
        <input id="UpdatedDate_${item.Id}" value="${item.UpdatedDate}" hidden />
        <div class="col-md-4">
          <input type="text" class="form-control" id="name_${item.Id}" value="${item.Name}" placeholder="Tên danh mục con" />
        </div>
        <div class="col-md-4">
          <input id="discription_${item.Id}" type="text" class="form-control" value="${item.Description}" placeholder="Mô tả" />
        </div>
        <div class="col-md-2">
          <input id="orderPriority_${item.Id}" type="number" class="form-control" value="${item.OrderPriority}" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/g, '')" placeholder="thứ tự" />
        </div>
        <div class="col-md-2">
          <a onclick="removeCategoryChild('${item.Id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
            <i class="ri-delete-bin-line fs-6 mr-0"></i>
          </a>
        </div>
      </div>
    `)
    }

    function addCategoryChild() {

        var obj = {
            Id: countCategoryChildren() + 1.0,
            Name: "",
            Description: "",
            OrderPriority:"0",
            UserZaloId: $("#UserZaloId").val(),
            CreatedDate: moment().format(),
            UpdatedDate: moment().format(),
        }
        renderCategoryChildItem(obj)
        $("#optionValue").val("")
    }

        function removeCategoryChild(id) {
          const elementId = `category-child-${id}`;
          const element = document.getElementById(elementId);
          if (element) {
            element.remove(); // Xóa phần tử khỏi DOM
          }
        }

            function countCategoryChildren() {
              const elements = document.querySelectorAll("[id^='category-child-']");
              return elements.length;
            }
</script>