﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableCampaignAndRank : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CommissionPercentage",
                table: "Ranks");

            migrationBuilder.DropColumn(
                name: "RequiredPoint",
                table: "Ranks");

            migrationBuilder.RenameColumn(
                name: "Status",
                table: "Ranks",
                newName: "IsActive");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Ranks",
                type: "nvarchar(max)",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<float>(
                name: "ConvertRate",
                table: "Ranks",
                type: "real",
                nullable: false,
                defaultValue: 0f);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "Ranks",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "RankingPoint",
                table: "Ranks",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "AccountCmsId",
                table: "CampaignCSKHs",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "********-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEJK4qXSS5fOv10j1WwH15lAaYNyySRVC+feUOfyigoKATKAt5dKAQl7T5BBP69V2WQ==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ConvertRate",
                table: "Ranks");

            migrationBuilder.DropColumn(
                name: "Description",
                table: "Ranks");

            migrationBuilder.DropColumn(
                name: "RankingPoint",
                table: "Ranks");

            migrationBuilder.DropColumn(
                name: "AccountCmsId",
                table: "CampaignCSKHs");

            migrationBuilder.RenameColumn(
                name: "IsActive",
                table: "Ranks",
                newName: "Status");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Ranks",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "nvarchar(max)",
                oldNullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "CommissionPercentage",
                table: "Ranks",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "RequiredPoint",
                table: "Ranks",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "********-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEAbzA75Jc32NkqPSk2tYtPvCUYTb0ifl4eMYwBQlKAB/YncG6H6m+sQsKEQIRx7uMw==");
        }
    }
}
