﻿using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations.Schema;

namespace MiniAppCore.Entities.Affiliates
{
    public class AffiliateCommissionRate : BaseEntity
    {
        public int Level { get; set; }

        [Column(TypeName = "decimal(18,6)")]
        public decimal Rate { get; set; }

        public bool IsActive { get; set; }
        public DateTime EffectiveDate { get; set; }
    }
}
