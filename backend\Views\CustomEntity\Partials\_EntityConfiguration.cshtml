@model MiniAppCore.Features.CustomEntity.Handlers.EntityConfigurationRequest
@{
    var dataTypes = ViewBag.DataTypes as List<object>;
    var entityName = Model?.EntityName;
}

<div class="modal-content">
    <div class="modal-header bg-primary bg-soft border-0">
        <div class="d-flex align-items-center">
            <div class="bg-primary rounded-circle p-2 me-3">
                <i class="ri-settings-3-line text-white fs-5"></i>
            </div>
            <div>
                <h4 class="modal-title text-primary fw-bold mb-0">
                    Cấu hình <PERSON> Fields
                </h4>
                <small class="text-muted">Entity: <strong>@entityName</strong></small>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>

    <form id="entityConfigurationForm" action="@Url.Action("SaveEntityConfiguration", "CustomEntity")" method="post" novalidate>
        <input type="hidden" name="EntityName" value="@entityName" />

        <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">

            <!-- Controls -->
            <div class="d-flex align-items-center justify-content-between mb-4 p-3 bg-light rounded">
                <div class="d-flex align-items-center">
                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                        <i class="ri-list-settings-line fs-5"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">Danh sách Fields</h6>
                        <span class="text-muted small">Tổng: <strong id="fieldCount" class="text-primary">@(Model?.Fields?.Count ?? 0)</strong></span>
                    </div>
                </div>
                <button type="button" id="addFieldBtn" class="btn btn-success">
                    <i class="ri-add-line me-1"></i>
                    Thêm Field
                </button>
            </div>

            <!-- Fields Container -->
            <div id="fieldsContainer" class="sortable-container">
                @if (Model?.Fields != null && Model.Fields.Any())
                {
                    @for (int i = 0; i < Model.Fields.Count; i++)
                    {
                        var field = Model.Fields[i];
                        <div class="field-item-wrapper">
                            @await Html.PartialAsync("_CustomFieldItem", field, new ViewDataDictionary(ViewData)
                            {
                                { "Index", i },
                                { "DataTypes", dataTypes }
                            })
                        </div>
                    }
                }
            </div>

            <!-- Field Template (Hidden) -->
            <div id="fieldTemplate" style="display: none;">
                <div class="field-item-wrapper">
                    @await Html.PartialAsync("_CustomFieldItem", new MiniAppCore.Features.CustomEntity.Handlers.CustomFieldRequest(), new ViewDataDictionary(ViewData)
                    {
                        { "Index", -1 },
                        { "DataTypes", dataTypes }
                    })
                </div>
            </div>

            <!-- Empty State -->
            <div id="noFieldsMessage" class="text-center py-5" style="display: @(Model?.Fields?.Any() == true ? "none" : "block")">
                <div class="mb-3">
                    <i class="ri-add-box-line text-success" style="font-size: 4rem; opacity: 0.3;"></i>
                </div>
                <h5 class="text-muted mb-2">Chưa có Custom Fields</h5>
                <p class="text-muted mb-4">
                    Bắt đầu thêm các trường dữ liệu tùy chỉnh để mở rộng thông tin cho <strong>@entityName</strong>.
                </p>
                <button type="button" class="btn btn-success" onclick="$('#addFieldBtn').click()">
                    <i class="ri-add-line me-1"></i>
                    Thêm Field Đầu Tiên
                </button>
            </div>
        </div>

        <div class="modal-footer bg-light border-0">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
            <button type="submit" id="saveConfigBtn" class="btn btn-primary" disabled>
                <i class="ri-save-3-line me-1"></i>
                Lưu cấu hình
            </button>
        </div>
    </form>
</div>

@await Html.PartialAsync("Scripts/_EntityConfigurationScripts")
