﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Models.DTOs.InvoiceTemplates;
using MiniAppCore.Services.InvoiceTemplates;
using MiniAppCore.Services.Memberships;
using MiniAppCore.Services.Orders;
using MiniAppCore.Services.Products.Ratings;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class OrderController(IOrderService orderService,
                                 IProductRatingService productRatingService,
                                 IInvoiceTemplateService invoiceTemplateService, IMembershipService membershipService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("Order/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Chi tiết đơn hàng";
            var orderDetail = await orderService.GetOrderByIdAsync(id);
            var settings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver(),
                Formatting = Formatting.Indented
            };
            var stringDetail = JsonConvert.SerializeObject(orderDetail, settings);
            return PartialView("_Order", orderDetail);
        }

        [HttpGet("Order/Rating/{id}")]
        public async Task<IActionResult> Rating(string id)
        {
            var orderRating = await productRatingService.GetRatingByOrderIdAsync(id);
            return PartialView("_Rating", orderRating);
        }

        [HttpGet("Order/Invoice/{id}")]
        public async Task<IActionResult> Invoice(string id, string? templateId = null)
        {
            var orderDetail = await orderService.GetOrderByIdAsync(id);
            var template = string.IsNullOrEmpty(templateId)
                ? await invoiceTemplateService.GetDefaultTemplate()
                : await invoiceTemplateService.GetByIdAsync(templateId);

            if (template == null)
            {
                ViewBag.Invoice = @"
                    <div class='text-center text-danger'>
                        <h5>Không tìm thấy mẫu hóa đơn phù hợp</h5>
                        <p>Vui lòng kiểm tra lại danh sách mẫu hóa đơn.</p>
                    </div>";
                ViewBag.Templates = new List<InvoiceTemplate>();
                ViewBag.InvoiceTemplateId = null;
                ViewBag.OrderId = orderDetail?.OrderId ?? id;
                ViewBag.Title = "Lỗi hóa đơn";
                return PartialView("_Invoice");
            }

            var invoiceTemplateVarable = new InvoiceTemplateVariables
            {
                OrderId = orderDetail.OrderId,
                PhoneNumber = orderDetail.PhoneNumber,
                MembershipName = orderDetail.UserZaloName,
                DeliveryAddress = orderDetail.Address,
                Notes = orderDetail.Note,
                Total = orderDetail.TotalAmount,
                ShippingFee = orderDetail.ShippingFee,
                DiscountAmount = orderDetail.DiscountAmount,
                ReceiverName = orderDetail.NameReciver,
                ReceiverPhoneNumber = orderDetail.PhoneReciver,
                CreatedDate = orderDetail.CreatedDate,
                ProductList = orderDetail.OrderDetails.Select(x => new InvoiceProductItem
                {
                    Quantity = x.Quantity,
                    Price = x.DiscountPrice,
                    ProductName = x.ProductName
                }).ToList()
            };

            ViewBag.Invoice = ReplaceVariables(template.Content, invoiceTemplateVarable);
            ViewBag.Templates = await invoiceTemplateService.GetAllAsync();
            ViewBag.InvoiceTemplateId = template.Id;
            ViewBag.OrderId = orderDetail.OrderId;
            ViewBag.Title = "Chi tiết đơn hàng";

            return PartialView("_Invoice");
        }

        private string ReplaceVariables(string content, InvoiceTemplateVariables data)
        {
            var replaced = content
                .Replace("{OrderId}", data.OrderId)
                .Replace("{PhoneNumber}", data.PhoneNumber)
                .Replace("{MembershipName}", data.MembershipName)
                .Replace("{DeliveryAddress}", data.DeliveryAddress ?? "")
                .Replace("{Notes}", data.Notes ?? "")
                .Replace("{DiscountAmount}", data.DiscountAmount.ToString("N0"))
                .Replace("{ShippingFee}", data.ShippingFee.ToString("N0"))
                .Replace("{Total}", data.Total.ToString("N0"))
                .Replace("{CreatedDate}", data.CreatedDate.ToString("dd/MM/yyyy HH:mm"))
                .Replace("{ReceiverName}", data.ReceiverName ?? "")
                .Replace("{ReceiverPhoneNumber}", data.ReceiverPhoneNumber ?? "");

            var doc = new HtmlAgilityPack.HtmlDocument();
            doc.LoadHtml(replaced);

            var tbodyList = doc.DocumentNode.SelectNodes("//table//tbody");
            if (tbodyList != null)
            {
                foreach (var tbodyNode in tbodyList)
                {
                    var rowTemplate = tbodyNode.SelectSingleNode("./tr");
                    if (rowTemplate == null) continue;

                    if (!rowTemplate.InnerHtml.Contains("{ProductName}") &&
                        !rowTemplate.InnerHtml.Contains("{ProductPrice}") &&
                        !rowTemplate.InnerHtml.Contains("{ProductQuantity}") &&
                        !rowTemplate.InnerHtml.Contains("{ProductSubtotal}"))
                    {
                        continue; // Bỏ qua table này nếu không phải table sản phẩm
                    }

                    tbodyNode.RemoveAllChildren();

                    foreach (var p in data.ProductList)
                    {
                        var newRow = rowTemplate.Clone();
                        foreach (var node in newRow.DescendantsAndSelf())
                        {
                            if (node.NodeType == HtmlAgilityPack.HtmlNodeType.Text)
                            {
                                node.InnerHtml = node.InnerHtml
                                    .Replace("{ProductName}", p.ProductName)
                                    .Replace("{ProductPrice}", p.Price.ToString("N0"))
                                    .Replace("{ProductQuantity}", p.Quantity.ToString())
                                    .Replace("{ProductSubtotal}", p.SubTotal.ToString("N0"));
                            }
                        }
                        tbodyNode.AppendChild(newRow);
                    }
                }
            }

            return doc.DocumentNode.OuterHtml;
        }
    }
}
