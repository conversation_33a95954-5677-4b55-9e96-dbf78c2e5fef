﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Models.Responses.Products.Ratings;

namespace MiniAppCore.Services.Products.Ratings
{
    public interface IProductRatingService : IService<OrderDetailReview>
    {
        Task<bool> IsOrderRatedAsync(string orderId);
        Task<CustomerProductRatingResponse?> GetRatingByOrderIdAsync(string orderId);
        Task<ProductRatingResponse> GetProductRatingAsync(string orderId, string userZaloId);
        Task<int> AddProductRatingAsync(string orderId, string userZaloId, ProductRatingRequest request);

        Task<(PagedResult<ProductReviewDetailResponse>, SummaryProductReviewResponse)> GetProductReviews(string productId, RequestQuery query, bool isAdmin = false);
        Task<int> UpdateReviewShown(string id, bool isShow);
    }
}
