﻿@model MiniAppCore.Models.DTOs.Surveys.SurveyQuestionDTO
<div class="options-container">
    <div class="form-group mb-3">
        <label class="form-label fw-bold"><PERSON><PERSON> sách lựa chọn</label>
        <div class="options-list mb-3">
            @if (Model.Answers == null || !Model.Answers.Any())
            {
                <div class="alert alert-info">Chưa có lựa chọn nào</div>
            }
            else
            {
                foreach (var option in Model.Answers)
                {
                    <partial name="_AnswerPartial" model="option" />
                }
            }
        </div>
        <label class="form-label">Thêm lựa chọn mới</label>
        <div class="input-group">
            <input type="text" class="form-control new-option-value" placeholder="Nhập lựa chọn">
            <button class="btn btn-primary" onclick="SurveyModule.addOption('@Model.Id')">
                <i class="ri-add-line me-1"></i> Thêm
            </button>
        </div>
    </div>
</div>