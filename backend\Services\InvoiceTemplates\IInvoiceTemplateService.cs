﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Models.DTOs.InvoiceTemplates;

namespace MiniAppCore.Services.InvoiceTemplates
{
    public interface IInvoiceTemplateService : IService<InvoiceTemplate>
    {
        Task<InvoiceTemplate?> GetDefaultTemplate();
        Task<int> CreateAsync(InvoiceTemplateDTO dto);
        Task<int> UpdateAsync(string id, InvoiceTemplateDTO dto);
    }
}