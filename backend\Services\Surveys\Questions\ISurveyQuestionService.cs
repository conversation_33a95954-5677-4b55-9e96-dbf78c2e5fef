﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Models.Responses.Surveys;

namespace MiniAppCore.Services.Surveys.Questions
{
    public interface ISurveyQuestionService : IService<SurveyQuestion>
    {
        Task<int> CreateOrUpdateQuestionsAsync(List<SurveyQuestionDTO> questions);
        Task<int> CreateOrUpdateQuestionsAsync(Dictionary<string, List<SurveyQuestionDTO>> questions); // section Id và questions

        Task<Dictionary<string, List<QuestionResponse>>> GetQuestionsMapBySectionIds(List<string> sectionIds);
        Task<Dictionary<string, List<SurveyQuestionDTO>>> GetQuestionDTOsMapBySectionIds(List<string> sectionIds);
    }
}