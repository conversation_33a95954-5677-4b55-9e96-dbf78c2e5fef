﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Memberships
{
    [Index(nameof(UserZaloId))]
    [Index(nameof(ReferenceId))]
    public class PointHistory : BaseEntity
    {
        public long Amount { get; set; }

        [MaxLength(20)]
        public string? Type { get; set; }

        [MaxLength(50)]
        public string? UserZaloId { get; set; }

        [MaxLength(36)]
        public string? ReferenceId { get; set; }
    }
}
