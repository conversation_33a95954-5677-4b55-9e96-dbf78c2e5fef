﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Articles;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Articles;

namespace MiniAppCore.Services.Articles
{
    public interface IArticleService : IService<Article>
    {
        Task<int> CreateAsync(ArticleRequest model);
        Task<int> UpdateAsync(string id, ArticleRequest model);
        Task<PagedResult<Article>> GetPage(ArticleQueryParams query);
    }
}