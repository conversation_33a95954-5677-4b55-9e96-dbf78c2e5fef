﻿namespace MiniAppCore.Models.Responses.BookingItem
{
    public class BookingItemResponse
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }

        private decimal _originalPrice;
        public decimal OriginalPrice
        {
            get => _originalPrice;
            set
            {
                // Nếu DiscountPrice chưa được set, mặc định bằng OriginalPrice
                _originalPrice = value;
                if (_discountPrice == 0)
                {
                    _discountPrice = value;
                }
            }
        }

        private decimal _discountPrice;
        public decimal DiscountPrice
        {
            get => _discountPrice;
            set => _discountPrice = value;
        }

        public int LikeCount { get; set; }
        public int ViewCount { get; set; }
        public int BoughtCount { get; set; }
        public int ReviewCount { get; set; }
        public float ReviewPoint { get; set; }

        public short Status { get; set; }
        public bool IsDiscounted => DiscountPrice < OriginalPrice;
        public decimal DiscountValue => OriginalPrice > 0 ? Math.Round((1 - DiscountPrice / OriginalPrice) * 100, 2) : 0;
        public List<string> Images { get; set; } = new List<string>();

        public int DisplayOrder { get; set; }
    }
}
