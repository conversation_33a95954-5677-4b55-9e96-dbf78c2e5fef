﻿using MiniAppCore.Enums;

namespace MiniAppCore.Models.Requests.Offers
{
    public class DiscountRequest
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public short Type { get; set; } = (short)DiscountType.PERCENTAGE;
        public decimal DiscountValue { get; set; }
        public decimal MaxDiscountAmount { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime StartDate { get; set; } = DateTime.Now;
        public DateTime ExpiryDate { get; set; } = DateTime.Now;

        public List<string> Products { get; set; } = new();
    }
}
