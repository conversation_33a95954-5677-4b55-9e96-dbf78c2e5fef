﻿﻿﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON>h sách hạng nhà tài trợ</h4>
            </div>
            <button onclick="GetFormSponsorshipTier('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-sponsorship-tier">
                <i class="las la-plus mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i><PERSON><PERSON> lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">T<PERSON><PERSON> kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-3">
                        <label for="filter-active-status" class="form-label">Trạng thái hoạt động</label>
                        <select id="filter-active-status" class="form-control">
                            <option value="0">Tất cả</option>
                            <option value="1">Đang hoạt động</option>
                            <option value="2">Ngưng hoạt động</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="sponsorship-tier-table" class="table-responsive rounded mb-3">
            <table id="list-sponsorship-tier" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-sponsorship-tier" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade" style="overflow-y:scroll;">
    <div id="modal-content" class="modal-dialog modal-xl" style="width: 90vw"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListSponsorshipTier();

            $('#search').on('input', () => {
                table.ajax.reload();
            });

            $('#filter-active-status').on('change', function () {
                table.ajax.reload();
            });
        });

        function InitialEditor(id) {
            window.editor = new Quill(id, {
                theme: 'snow',
                modules: {
                    imageResize: {
                        displaySize: true,
                    },
                    toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered' }, { 'list': 'bullet' }],  // Thêm chỉnh list
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'align': [] }], // Căn lề (trái, phải, giữa, đều)
                        ['blockquote', 'code-block'], // Thêm blockquote và code block
                        ['image'],
                        ['link',], // Thêm liên kết
                        ['clean'] // Xóa định dạng
                    ]
                }
            });

            window.editor.on('text-change', function (delta, oldDelta, source) {
                const content = window.editor.root.innerHTML;
                if (content === "<p><br></p>") {
                    $("#preview-content").html("Nội dung tin tức");
                    return;
                }
                $("#preview-content").html(content);
            });
        }

        async function previewImage(input) {
            const file = input.files[0];
            if (!file || !file.type.startsWith("image/")) return;

            const isValid = await validateImageAspectRatio(file, 1, 1);
            if (!isValid.valid) {
                AlertResponse(`Ảnh phải có tỉ lệ 1:1. Kích thước hiện tại: ${isValid.actualWidth}x${isValid.actualHeight}`, "warning");
                $(input).val("");
                return;
            }

            const reader = new FileReader();
            reader.onload = function (e) {
                $('#preview-image').html(`
                    <div class="image-preview position-relative d-inline-block">
                        <span class="btn-preview-remove" onclick="removePreviewImage()">x</span>
                        <img src="${e.target.result}" class="rounded border" style="height: 120px; width: 120px; object-fit: cover;" />
                    </div>
                `);
            };
            reader.readAsDataURL(file);
        }

        function removePreviewImage() {
            $('#preview-image').empty();
            $('#image').val("");
        }

        async function validateImageAspectRatio(file, requiredWidth, requiredHeight) {
            return new Promise(resolve => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = e => {
                    const img = new Image();
                    img.src = e.target.result;
                    img.onload = () => {
                        const actualRatio = img.width / img.height;
                        const expectedRatio = requiredWidth / requiredHeight;
                        resolve({ valid: Math.abs(actualRatio - expectedRatio) <= 0.01, actualWidth: img.width, actualHeight: img.height });
                    };
                };
            });
        }

        function GetListSponsorshipTier() {
            table = new DataTable("#list-sponsorship-tier", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const status = $("#filter-active-status").val();

                    $.ajax({
                        url: '@Url.Action("GetAll", "SponsorshipTiers")',
                        type: 'GET',
                        data: {
                            page: page,
                            pageSize: data.length,
                            keyword: keyword,
                            activeStatus: status || 0
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                rowNum: data.start + index + 1,
                                tierName: item.tierName || '-',
                                description: item.description || '-',
                                image: item.image
                                    ? `<img src="${item.image}" style="height:40px; object-fit:cover; border-radius:4px;" />`
                                    : '<span class="text-muted">Không có</span>',
                                status: item.isActive ?
                                    `<div class="m-auto bg-success circle-active" title="Đang hoạt động"></div>` :
                                    `<div class="m-auto circle-inactive" title="Ngưng hoạt động"></div>`,
                                actions: `<div class="d-flex align-items-center justify-content-center list-action">
                                            <a onclick="GetFormSponsorshipTier('${item.id}')" class="badge badge-info mx-1" data-toggle="tooltip" title="Chi tiết">
                                                <i class="ri-pencil-line fs-6"></i>
                                            </a>
                                            <a onclick="DeleteSponsorshipTier('${item.id}')" class="badge bg-warning mx-1" data-toggle="tooltip" title="Xóa">
                                                <i class="ri-delete-bin-line fs-6"></i>
                                            </a>
                                        </div>`
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "rowNum", className: 'text-center' },
                    { title: "Hình ảnh", data: "image", className: 'text-center' },
                    { title: "Tên hạng", data: "tierName" },
                    { title: "Trạng thái", data: "status", className: 'text-center' },
                    { title: "Thao tác", data: "actions", className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetActiveBadge(status) {
            if (status === true) {
                return '<span class="badge bg-success">Đang hoạt động</span>';
            } else {
                return '<span class="badge bg-secondary">Không hoạt động</span>';
            }
        }

        function GetFormSponsorshipTier(id) {
            const url = id ? `@Url.Action("Detail", "SponsorshipTier")/${id}` : "@Url.Action("Create", "SponsorshipTier")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-sponsorship-tier").modal("toggle");

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function DeleteSponsorshipTier(id) {
            if (id === '') return;
            const url = `/api/SponsorshipTiers/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const tierName = $('#tierName').val().trim();
            const description = window.editor.root.innerHTML.trim();
            const isActive = $('#isActive').val() === 'true';

            const imageFile = $('#image')[0]?.files[0];
            const hasPreviewImage = $('#preview-image img').length > 0;

            if (!tierName) {
                AlertResponse("Vui lòng nhập tên hạng tài trợ.", "warning");
                return;
            }

            if (!imageFile && !hasPreviewImage) {
                AlertResponse("Vui lòng chọn ít nhất một hình ảnh hợp lệ.", "warning");
                return;
            }

            const formData = new FormData();
            formData.append("TierName", tierName);
            formData.append("Description", description);
            formData.append("IsActive", isActive);

            if (imageFile) formData.append("Image", imageFile);

            const url = id ? `/api/sponsorshiptiers/${id}` : '/api/sponsorshiptiers';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'warning');
                    }
                    $('#modal-sponsorship-tier').modal('toggle');
                },
                error: function () {
                    AlertResponse('Lỗi máy chủ, vui lòng thử lại sau!', 'error');
                }
            });
        }
    </script>
}
