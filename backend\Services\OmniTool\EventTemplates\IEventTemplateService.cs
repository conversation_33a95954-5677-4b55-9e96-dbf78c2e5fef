﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.ETM;
using MiniAppCore.Models.Requests.OmniTools;

namespace MiniAppCore.Services.OmniTool.EventTemplates
{
    public interface IEventTemplateService : IService<EventTemplate>
    {
        Task<int> CreateAsync(EventTemplateRequest campaignRequest);
        Task<int> UpdateAsync(string id, EventTemplateRequest campaignRequest);

        Task<ZaloTemplateConfig> GetZaloUidConfigById(string id);
        Task<int> CreateAsync(ZaloUidConfigRequest zaloUidConfigRequest);
        Task<int> UpdateAsync(string id, ZaloUidConfigRequest zaloUidConfigRequest);
    }
}
