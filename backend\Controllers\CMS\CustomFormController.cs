﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Models.Responses.Memberships;
using MiniAppCore.Services.Memberships.CustomForms;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class CustomFormController(IConfiguration configuration, ICustomFormService customFormService) : Controller
    {
        public IActionResult Index()
        {
            ViewBag.QrCodeBaseUrl = configuration["MiniAppSettings:RegisterFormTemplateUrl"];
            return View();
        }

        [HttpGet("CustomForm/Create")]
        public async Task<IActionResult> Create()
        {
            var formCustomResponse = new FormCustomResponse();
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới form";
            return PartialView("_CustomForm", formCustomResponse);
        }

        [HttpGet("CustomForm/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            var result = await customFormService.GetByIdAsync(id, true);
            if (result == null) return RedirectToAction("Create");
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật form";
            return PartialView("_CustomForm", result);
        }
    }
}
