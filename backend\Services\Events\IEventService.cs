﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Events;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events;
using MiniAppCore.Models.Responses.Events;

namespace MiniAppCore.Services.Events
{
    public interface IEventService : IService<Event>
    {
        Task<int> CreateEventAsync(EventDTO data);
        Task<int> UpdateEventAsync(string id, EventDTO data);
        Task<int> DeleteEventAsync(string id);

        Task<PagedResult<EventResponse>> GetPaged(RequestQuery query, string userZaloId, string sponsorId, short status, short activeStatus);
        Task<EventDetailResponse?> GetDetailAsync(string id, string userZaloId);
        Task<List<EventGiftResponse>> GetGifts(string id);
        Task<List<EventSponsorResponse>> GetSponsors(string id);
    }

}
