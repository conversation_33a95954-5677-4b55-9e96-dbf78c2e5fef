﻿using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Branches
{
    public class BranchCategory
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString("N");
        public required int CategoryId { get; set; }
        public required string BranchId { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdateDate { get; set; } = DateTime.Now;
    }
}
