﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Models.Requests.Surveys;
using MiniAppCore.Services.Surveys;
using MiniAppCore.Services.Surveys.UserSurveys;

namespace MiniAppCore.Controllers.API
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class SurveysController(ILogger<SurveysController> logger, ISurveyService surveyService, ISurveySubmissionService surveySubmissionService) : ControllerBase
    {
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetPage([FromQuery] RequestQuery requestQuery, [FromQuery] string? visitorId, [FromQuery] short? status)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value ?? visitorId;
                var result = await surveyService.GetPage(requestQuery, userZaloId ?? string.Empty, status);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    result.Data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    Data = new object[] { },
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting surveys.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        [HttpGet("{id}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetSurvey(string id)
        {
            try
            {
                var result = await surveyService.GetSurveyByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting surveys.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        [HttpPost("{id}/Submit")]
        [AllowAnonymous]
        public async Task<IActionResult> SubmitSurvey(string id, SurveySubmissionRequest model)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value ?? model.VisitorId ?? Guid.NewGuid().ToString("N");
                await surveySubmissionService.SubmitSurveyAsync(userZaloId, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Gửi khảo sát thành công!",
                    VisitorId = userZaloId,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting surveys.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        [HttpPost("Sync/{visitorId}")]
        public async Task<IActionResult> SyncSubmissionSurveys(string visitorId)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                await surveySubmissionService.SyncDataFromVisitorIdToUserZaloId(visitorId, userZaloId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Đồng bộ dữ liệu khảo sát thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting surveys.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        [HttpGet("History")]
        public async Task<IActionResult> GetSurveyHistory([FromQuery] RequestQuery requestQuery, [FromQuery] string? surveyId, [FromQuery] DateTime? fromDate, [FromQuery] DateTime? toDate)
        {
            try
            {
                var result = await surveySubmissionService.GetSurveyHistoryAsync(requestQuery, surveyId, fromDate, toDate);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    result.Data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting survey history.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        [HttpGet("Submission/{id}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetSurveySubmission(string id)
        {
            try
            {
                var data = await surveySubmissionService.GetSurveyResultAsync(id);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    data,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting survey history.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        [HttpPost("Submissions/Export")]
        [AllowAnonymous]
        public async Task<IActionResult> ExportSubmissions([FromBody] List<string> surveys)
        {
            try
            {
                if (surveys == null || surveys.Count == 0)
                    return Ok(new
                    {
                        Code = 1,
                        Message = "Danh sách khảo sát không được để trống."
                    });

                var excelBytes = await surveySubmissionService.ExportSurveysToExcelAsync(surveys);
                var fileName = $"survey_export_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                return File(excelBytes,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    fileName);
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting survey history.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        #region Admin function

        [HttpPost]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> CreateSurvey([FromBody] SurveyDTO model)
        {
            try
            {
                await surveyService.CreateSurveyAsync(model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting surveys.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> UpdateSurvey(string id, [FromBody] SurveyDTO model)
        {
            try
            {
                await surveyService.UpdateSurveyAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting surveys.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> DeleteSurvey(string id)
        {
            try
            {
                await surveyService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting surveys.");
                return StatusCode(StatusCodes.Status500InternalServerError, "An error occurred while processing your request.");
            }
        }

        #endregion
    }
}
