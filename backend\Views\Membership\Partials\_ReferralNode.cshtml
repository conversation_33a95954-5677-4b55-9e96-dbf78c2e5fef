@using MiniAppCore.Models.Responses.Affiliates
@model ReferralTreeResponse

<div class="tree-node <EMAIL>" data-user-id="@Model.UserZaloId">
    <div class="level-indicator">Cấp @Model.Level</div>

    <div class="tree-node-header">
        <img src="@Model.Avatar" alt="@Model.UserZaloName" class="member-avatar" onerror="this.src='/images/default-avatar.png'">

        <div class="member-info">
            <div class="member-name" title="@Model.UserZaloName">@Model.UserZaloName</div>
            <div class="member-details">
                <div><i class="fas fa-phone me-1"></i> @Model.PhoneNumber</div>
                <div><i class="fas fa-calendar me-1"></i> @Model.JoinDate.ToString("dd/MM/yyyy")</div>
                @if (!string.IsNullOrEmpty(Model.ReferralCode))
                {
                    <div><i class="fas fa-tag me-1"></i> @Model.ReferralCode</div>
                }
            </div>

            <div class="member-stats">
                <span class="stat-badge primary">
                    <i class="fas fa-users"></i> @Model.DirectReferrals trực tiếp
                </span>
                <span class="stat-badge">
                    <i class="fas fa-sitemap"></i> @Model.TotalReferrals tổng
                </span>
                @if (Model.TotalCommission > 0)
                {
                    <span class="stat-badge commission">
                        <i class="fas fa-coins"></i> @Model.TotalCommission.ToString("N0")đ
                    </span>
                }
            </div>

            @if (Model.Children.Count > 0)
            {
                <div class="expand-toggle" data-count="@Model.Children.Count" data-expanded="true">
                    <i class="fas fa-minus-circle"></i> Thu gọn
                </div>
            }
        </div>
    </div>

    @if (Model.Children.Count > 0)
    {
        <div class="tree-children">
            @foreach (var child in Model.Children)
            {
                @await Html.PartialAsync("Partials/_ReferralNode", child)
            }
        </div>
    }
</div>
