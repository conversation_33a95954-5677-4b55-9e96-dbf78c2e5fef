﻿<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">Thê<PERSON> sản phẩm v<PERSON>o kho</h5>
        <button type="button" class="close" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label>Chọn sản phẩm</label>
                    <select id="products" class="form-control" multiple required></select>
                    <div class="help-block with-errors"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdateProductBranch()"><PERSON><PERSON><PERSON></button>
    </div>
</div>
<script>
    $(document).ready(function() {
        $("#products").select2({
            placeholder: 'Chọn sản phẩm',
            allowClear: false,
            minimumInputLength:0 ,
            dropdownParent: $("#modal-pullProduct"),
            ajax: {
                url: '@Url.Action("GetAllProduct", "Products")' + "?isGetAll=false&excludeAlreadyItems=true&stockStatus=1",
                dataType: 'json',
                delay: options.delay || 250, // Thời gian trễ khi gõ tìm kiếm
                data: function (params) {
                    return {
                        keyword: params.term, // Keyword tìm kiếm
                        page: params.page || 1,
                        pageSize: options.pageSize || 10
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    // Map data
                    const mappedData = data.data.map(function (item) {
                        return {
                            id: item[options.idField || 'id'],
                            text: item[options.textField || 'name']
                        };
                    });

                    // Luôn có "Tất cả" ở đầu nếu là trang 1
                    if (!params.term && params.page === 1) {
                        mappedData.unshift({
                            id: '',
                            text: options.defaultOptionText || 'Tất cả'
                        });
                    }

                    return {
                        results: mappedData,
                        pagination: {
                            more: (params.page * (options.pageSize || 10)) < (data.totalItems || 0)
                        }
                    };
                },
                cache: true,
            }
        });
    });
</script>