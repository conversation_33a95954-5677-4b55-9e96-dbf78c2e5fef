﻿using MiniAppCore.Enums;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Offers.Discounts
{
    public class Discount
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString("N").ToUpper();
        public required string Name { get; set; }
        public string? Description { get; set; }
        public short Type { get; set; } = (short)DiscountType.PERCENTAGE;
        public decimal DiscountValue { get; set; }
        public decimal MaxDiscountAmount { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime StartDate { get; set; } = DateTime.Now;
        public DateTime ExpiryDate { get; set; } = DateTime.Now;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
