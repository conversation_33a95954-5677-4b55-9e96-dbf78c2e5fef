﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Settings;
using MiniAppCore.Services.Memberships.Ranks;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class RankController(IRankService rankService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("Rank/Create")]
        public async Task<IActionResult> Create()
        {
            var category = new Rank()
            {
                Id = string.Empty,
                Name = string.Empty,
                IsActive = true,
                IsDefault = false,
                Description = string.Empty,
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới hạng thành viên";
            return PartialView("_Rank", category);
        }

        [HttpGet("Rank/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            var result = await rankService.GetByIdAsync(id);
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật hạng thành viên";
            return PartialView("_Rank", result);
        }
    }
}
