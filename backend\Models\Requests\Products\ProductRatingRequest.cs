﻿using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Models.Requests.Products
{
    public class ProductRatingRequest
    {
        public List<ProducRatingItemRequest> Products { get; set; } = new List<ProducRatingItemRequest>();
    }

    public class ProducRatingItemRequest
    {
        [Required]
        [Range(1, 5, ErrorMessage = "Giá trị cho star phải nằm từ 1 - 5")]
        public float Star { get; set; }

        [Required]
        public string? ProductId { get; set; }

        public string? Review { get; set; }
        public List<IFormFile> Images { get; set; } = new List<IFormFile>(); // không bắt buộc
        public List<IFormFile> Videos { get; set; } = new List<IFormFile>(); // không bắt buộc
    }
}
