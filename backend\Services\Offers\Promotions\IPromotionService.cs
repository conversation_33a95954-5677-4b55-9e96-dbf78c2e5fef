﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Offers;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Models.Responses.Products;

namespace MiniAppCore.Services.Offers.Promotions
{
    public interface IPromotionService : IService<Promotion>
    {
        Task<int> CreateAsync(PromotionRequest request);
        Task<int> UpdateAsync(string id, PromotionRequest request);
        Task<List<ProductResponse>> GetProductGiftByProductId(string productId);

        Task<List<ProductResponse>> GetProductBuyByPromotionId(string promotionId);
        Task<List<ProductResponse>> GetProductGiftByPromotionId(string promotionId);
    }
}