﻿@model MiniAppCore.Models.DTOs.Properties.PropertyDTO;

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Tên nhóm phân loại <span style="color:red">*</span></label>
                            <input id="name" type="text" class="form-control" value="@Model.Name" placeholder="Ví dụ: Màu sắc v.v" data-errors="Vui lòng nhập tên nhóm phân loại." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label><PERSON><PERSON><PERSON> thức chọn <span style="color:red">*</span></label>
                            <select id="isMultipleChoice" class="form-control">
                                @* <option value="true" selected="@Model.IsMultipleChoice">Nhiều lựa chọn</option> *@
                                <option value="false" selected="@(!Model.IsMultipleChoice)">Một lựa chọn</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Số lựa chọn tối đa <span style="color:red">*</span></label>
                            <input id="maxSelection" type="number" class="form-control" value="@Model.MaxSelection" min="1" max="100" data-errors="Vui lòng nhập số lượng." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="descr">Mô tả nhóm phân loại</label>
                            <textarea id="descr" class="form-control" rows="4" placeholder="Ví dụ: Vui lòng chọn 1, Quà tặng đính kèm,...">@Model.Description</textarea>
                        </div>
                    </div>

                    <!-- Thêm Options -->
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Phân loại hàng</label>
                            <div class="input-group">
                                <input id="optionValue" type="text" class="form-control" placeholder="Ví dụ: Trắng, Đỏ, v.v">
                                <button type="button" id="addOptionButton" class="btn btn-success" onclick="AddOption()">+</button>
                                <button type="button" id="cancelEditButton" class="btn btn-secondary" onclick="CancelEdit()" style="display:none;">Hủy</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>STT</th>
                                    <th class="">Giá trị</th>
                                    <th class="text-end">Hành động</th>
                                </tr>
                            </thead>
                            <tbody id="optionsTableBody">
                                @if (Model.Options != null)
                                {
                                    @for (int i = 0; i < Model.Options.Count; i++)
                                    {
                                        <tr data-property-item-id="@Model.Options[i].PropertyValueId">
                                            <td>@(i + 1)</td>
                                            <td class="option-value">@Model.Options[i].Value</td>
                                            <td class="text-end">
                                                <a onclick="EditOption(this)" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Chỉnh sửa">
                                                    <i class="ri-edit-line fs-6 mr-0"></i>
                                                </a>
                                                <a onclick="RemoveOption(this)" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                    <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (string.IsNullOrEmpty(Model.Id))
        {
            <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
        }
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
    </div>
</div>

<script>
    let currentEditRow = null;

    $(document).ready(function () {
        $("#isMultipleChoice").change(function () {
            ToggleMaxSelection();
        });

        ToggleMaxSelection();
    });

    function ToggleMaxSelection() {
        const isMultiple = $("#isMultipleChoice").val() === "true";
        const maxSelectionInput = $("#maxSelection");

        if (!isMultiple) {
            maxSelectionInput.val(1);
            maxSelectionInput.prop("disabled", true);
        } else {
            maxSelectionInput.prop("disabled", false);
        }
    }

    function AddOption() {
        const value = $("#optionValue").val()?.trim();
        if (value === "") return;

        const index = $("#optionsTableBody tr").length + 1;

        if (currentEditRow) {
            $(currentEditRow).find(".option-value").text(value);
            currentEditRow = null;
            $("#addOptionButton").text("+");
            $("#cancelEditButton").hide();
        } else {
            const row = `<tr>
                            <td>${index}</td>
                            <td class="option-value">${value}</td>
                            <td class="text-end">
                                <button type="button" class="btn btn-warning btn-sm" onclick="EditOption(this)">Chỉnh sửa</button>
                                <button type="button" class="btn btn-danger btn-sm" onclick="RemoveOption(this)">Xóa</button>
                            </td>
                        </tr>`;
            $("#optionsTableBody").append(row);
        }
        $("#optionValue").val("");
    }

    function RemoveOption(button) {
        $(button).closest("tr").remove();
        UpdateOptionIndex();
    }

    function EditOption(button) {
        const row = $(button).closest("tr");
        const value = $(row).find(".option-value").text().trim();

        $("#optionValue").val(value);
        currentEditRow = row;
        $("#addOptionButton").text("Cập nhật");
        $("#cancelEditButton").show();
    }

    function CancelEdit() {
        $("#optionValue").val("");
        currentEditRow = null;
        $("#addOptionButton").text("+");
        $("#cancelEditButton").hide();
    }

    function UpdateOptionIndex() {
        $("#optionsTableBody tr").each(function (index, row) {
            $(row).find("td:first").text(index + 1);
        });
    }
</script>
