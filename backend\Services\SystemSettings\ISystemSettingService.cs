﻿using MiniAppCore.Entities.Memberships;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.SystemSettings;
using MiniAppCore.Models.Responses.Categories;

namespace MiniAppCore.Services.SystemSettings
{
    public interface ISystemSettingService
    {
        Task<string> GetLinkPaymentGuidAsync();
        Task<int> AddOrUpdateLinkPaymentGuideAsync(string linkPaymentGuide);

        Task<List<CategoryResponse>> GetCategoryHomeAsync();
        Task<int> AddOrUpdateCategoryHomeAsync(List<string> categoryIds);

        Task<OmniAccountDTO> GetOmniAccountAsync();
        Task<int> AddOrUpdateAccountOmniAsync(OmniAccountDTO omniAccount);

        Task<EnterpriseInfomationDTO> GetEnterpriseInformationAsync();
        Task<int> AddOrUpdateEnterpriseInformationAsync(EnterpriseInfomationDTO enterpriseInfomation);


        Task<int> DeleteMembershipExtendDefault(string id);
        Task<int> CreateMembershipExtendDefault(MembershipExtendDefault data);
        Task<int> UpdateMembershipExtendDefault(MembershipExtendDefault data);

        Task<List<string>> GetAttributeFromExtendInfo();
        Task<MembershipExtendDefault?> GetMembershipExtendDefaultById(string id);
        Task<PagedResult<MembershipExtendDefault>> GetPageMemberShipExtendDefault(RequestQuery query);

        Task<List<FeatureButtonDto>> GetFeaturesButton();
    }
}
