﻿@model MiniAppCore.Models.Responses.Surveys.SurveyResultResponse
﻿
<style>
    .question-card {
        margin-bottom: 1rem;
        border-left: 4px solid #007bff;
        border-radius: 0.5rem;
        transition: box-shadow 0.3s ease, transform 0.2s ease;
    }

        .question-card:hover {
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

    .question-title {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .response-text {
        background-color: #f8f9fa;
        padding: 0.5rem;
        border-radius: 0.25rem;
    }
</style>

<div class="modal-content">
    <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="surveyResultModalLabel">@Model.Title</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    <div class="modal-body">
        <!-- Survey Info -->
        <div class="card mb-4">
            <div class="card-header bg-light" style="border-top-left-radius: 9px; border-top-right-radius: 9px;">
                <h6>Thông tin chung</h6>
            </div>
            <div class="card-body row row-cols-1 row-cols-md-2 g-3">
                <div class="col"><strong>Mã khảo sát:</strong> @Model.SurveyId</div>
                <div class="col"><strong>Trạng thái:</strong> @Model.Status</div>
                <div class="col"><strong>Bắt đầu:</strong> @Model.StartedDate.ToString("dd/MM/yyyy")</div>
                <div class="col"><strong>Kết thúc:</strong> @Model.EndDate.ToString("dd/MM/yyyy")</div>
                <div class="col"><strong>Người nộp:</strong> @Model.DisplayName</div>
                <div class="col"><strong>SĐT:</strong> @Model.PhoneNumber</div>
                <div class="col"><strong>Ngày nộp:</strong> @Model.SubmissionDate.ToString("dd/MM/yyyy HH:mm:ss")</div>
            </div>
        </div>

        <!-- Sections -->
        @foreach (var section in Model.Sections)
        {
            @await Html.PartialAsync("SurveySubmission/_SectionResultPartial", section)
        }
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
    </div>
</div>

