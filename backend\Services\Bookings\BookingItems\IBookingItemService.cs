﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Bookings;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.BookingItems;
using MiniAppCore.Models.Responses.BookingItem;

namespace MiniAppCore.Services.Bookings
{
    public interface IBookingItemService : IService<BookingItem>
    {
        Task<int> CreateAsync(BookingItemRequest dto);
        Task<int> UpdateAsync(string id, BookingItemRequest dto);
        Task<BookingItemResponse> GetDetailResponseByIdAsync(string id);
        Task<PagedResult<BookingItemResponse>> GetPage(ProductQueryParams query);
    }
}