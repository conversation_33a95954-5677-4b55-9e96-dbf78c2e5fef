﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Memberships;
using MiniAppCore.Models.Responses.Memberships;

namespace MiniAppCore.Services.Memberships
{
    public interface IMembershipService : IService<Membership>
    {
        Task<PagedResult<Membership>> GetPage(UserQueryParameters queryParams);
        Task<Membership?> GetByUserZaloId(string userZaloId);
        Task<Membership?> GetByPhoneNumber(string phoneNumber);
        Task<Membership?> GetByReferralCode(string referralCode);
        Task<MembershipResponse?> GetMembershipProfile(string userZaloId);

        Task<Membership?> GetMember(string phoneNumber, string userZaloId, string userZaloIdByOa);
        Task<Membership?> GetMember(string phoneNumber, string userZaloId, bool isFormLogin = false);

        Task<IEnumerable<Membership>> GetByUserZaloIds(List<string> userZaloIds);

        Task<int> UpdateAsync(string id, MembershipDTO dto);
        Task<int> UpdateProfileAsync(string userZaloId, UpdateProfileRequest dto);
        Task<int> RegisterMembership(RegisterMember info, string? source, string? referralCode);
        Task<List<string>> ImportMembershipList(IFormFile file);

        Task<List<string>> GetTagsMembership(string userZaloId);

        Task<List<MembershipExtendDefault>> GetListMembershipExtendDefault();
        Task<List<MembershipExtend>> GetListMembershipExtendByUserZaloId(string userZaloId);

        Task<byte[]> ExportMemberships(DateTime? startDate, DateTime? endDate);

        Task<List<ExtendInfoFormResponse>> GetFormExtendInfo();

        Task<PagedResult<PointHistoryResponse>> GetPointHistoriesByUserZaloIdAsync(string userZaloId, RequestQuery query);
    }
}