namespace MiniAppCore.Models.Responses.Affiliates
{
    public class ReferralTreeResponse
    {
        public string UserZaloId { get; set; } = string.Empty;
        public string UserZaloName { get; set; } = string.Empty;
        public string Avatar { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string ReferralCode { get; set; } = string.Empty;
        public DateTime JoinDate { get; set; }
        public int Level { get; set; }
        public decimal TotalCommission { get; set; }
        public int TotalReferrals { get; set; }
        public int DirectReferrals { get; set; }
        public List<ReferralTreeResponse> Children { get; set; } = new List<ReferralTreeResponse>();
    }
}
