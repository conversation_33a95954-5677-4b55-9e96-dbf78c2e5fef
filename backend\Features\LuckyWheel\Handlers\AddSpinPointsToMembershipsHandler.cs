using MediatR;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;

namespace MiniAppCore.Features.LuckyWheel.Commands
{
    public class AddSpinPointsToMembershipsCommand : IRequest<int>
    {
        public required string LuckyWheelId { get; set; }
        public string? TargetUserZaloId { get; set; } // Nếu null thì cộng cho tất cả memberships
    }
    public class AddSpinPointsToMembershipsHandler(
        IUnitOfWork unitOfWork,
        IMediator mediator,
        ILogger<AddSpinPointsToMembershipsHandler> logger) : IRequestHandler<AddSpinPointsToMembershipsCommand, int>
    {
        private readonly IRepository<Membership> _membershipRepo = unitOfWork.GetRepository<Membership>();
        private readonly IRepository<Entities.LuckyWheels.LuckyWheel> _luckyWheelRepo = unitOfWork.GetRepository<Entities.LuckyWheels.LuckyWheel>();
        private const int BATCH_SIZE = 100;

        public async Task<int> Handle(AddSpinPointsToMembershipsCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // Kiểm tra Lucky Wheel có tồn tại và được bật tính năng auto add spin points
                var luckyWheel = await _luckyWheelRepo.AsQueryable()
                    .FirstOrDefaultAsync(x => x.Id == request.LuckyWheelId
                                            && x.IsActive
                                            && x.EnableAutoSpinPoints
                                            && x.SpinPointsToAdd > 0, cancellationToken);

                if (luckyWheel == null)
                {
                    logger.LogWarning("LuckyWheel {LuckyWheelId} not found or auto spin points not enabled", request.LuckyWheelId);
                    return 0;
                }

                // Kiểm tra thời gian hiệu lực
                var now = DateTime.Now;
                if (now < luckyWheel.StartDate || now > luckyWheel.ExpiryDate)
                {
                    logger.LogWarning("LuckyWheel {LuckyWheelId} is outside valid time range. Current: {Now}, Start: {StartDate}, End: {ExpiryDate}",
                        request.LuckyWheelId, now, luckyWheel.StartDate, luckyWheel.ExpiryDate);
                    return 0;
                }

                var pointsToAdd = luckyWheel.SpinPointsToAdd;
                var updatedCount = 0;

                // Nếu có target user cụ thể
                if (!string.IsNullOrEmpty(request.TargetUserZaloId))
                {
                    updatedCount = await AddPointsToSpecificUser(request.TargetUserZaloId, pointsToAdd, cancellationToken);
                }
                else
                {
                    // Cộng điểm cho tất cả memberships
                    updatedCount = await AddPointsToAllMemberships(pointsToAdd, cancellationToken);
                }

             
                logger.LogInformation("Successfully added {PointsToAdd} spin points to {UpdatedCount} memberships for LuckyWheel {LuckyWheelId}",
                    pointsToAdd, updatedCount, request.LuckyWheelId);

                return updatedCount;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while adding spin points for LuckyWheel {LuckyWheelId}", request.LuckyWheelId);
                throw;
            }
        }

        private async Task<int> AddPointsToSpecificUser(string userZaloId, long pointsToAdd, CancellationToken cancellationToken)
        {
            var membership = await _membershipRepo.AsQueryable()
                .FirstOrDefaultAsync(x => x.UserZaloId == userZaloId, cancellationToken);

            if (membership == null)
            {
                logger.LogWarning("Membership not found for UserZaloId: {UserZaloId}", userZaloId);
                return 0;
            }

            membership.SpinPoint += pointsToAdd;
            _membershipRepo.Update(membership);
            await unitOfWork.SaveChangesAsync();

            return 1;
        }

        private async Task<int> AddPointsToAllMemberships(long pointsToAdd, CancellationToken cancellationToken)
        {
            var totalMemberships = await _membershipRepo.AsQueryable().CountAsync(cancellationToken);
            var updatedCount = 0;

            logger.LogInformation("Starting to add {PointsToAdd} spin points to {TotalMemberships} memberships in batches of {BatchSize}",
                pointsToAdd, totalMemberships, BATCH_SIZE);

            // Xử lý theo batch để tránh quá tải
            for (int skip = 0; skip < totalMemberships; skip += BATCH_SIZE)
            {
                var membershipsBatch = await _membershipRepo.AsQueryable()
                    .Skip(skip)
                    .Take(BATCH_SIZE)
                    .ToListAsync(cancellationToken);

                if (!membershipsBatch.Any())
                    break;

                // Cập nhật spin points cho batch hiện tại
                foreach (var membership in membershipsBatch)
                {
                    membership.SpinPoint += pointsToAdd;
                    _membershipRepo.Update(membership);
                    updatedCount++;
                }

                // Lưu batch hiện tại
                await unitOfWork.SaveChangesAsync();

                logger.LogDebug("Processed batch {BatchNumber}: {BatchCount} memberships updated",
                    (skip / BATCH_SIZE) + 1, membershipsBatch.Count);

                // Thêm delay nhỏ giữa các batch để tránh quá tải DB
                if (skip + BATCH_SIZE < totalMemberships)
                {
                    await Task.Delay(100, cancellationToken);
                }
            }

            return updatedCount;
        }
    }
}
