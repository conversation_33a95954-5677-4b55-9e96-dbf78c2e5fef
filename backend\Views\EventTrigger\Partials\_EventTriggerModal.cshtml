﻿@model MiniAppCore.Entities.Notifications.EventTriggerSetting;

<div class="modal-dialog modal-xl modal-dialog-scrollable">
    <div class="modal-content border-0 shadow rounded-3">
        <div class="modal-header bg-primary text-white">
            <h5 class="modal-title fw-bold">@ViewBag.Title</h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body p-4">
            <form id="eventTemplateForm" class="needs-validation" novalidate>
                <div class="row gap-0">
                    <!-- Thông tin cấu hình -->
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body">
                                <h6 class="fw-semibold mb-3 text-primary">C<PERSON>u hình sự kiện</h6>
                                <div class="mb-3">
                                    <label for="eventName" class="form-label">Sự kiện</label>
                                    <select id="eventName" class="form-select">
                                        <option value="Order.Create" selected="@(Model.EventName == "Order.Create")">Tạo đơn hàng</option>
                                        <option value="Order.Update" selected="@(Model.EventName == "Order.Update")">Cập nhật đơn hàng</option>
                                        <option value="Membership.Register" selected="@(Model.EventName == "Membership.Register")">Đăng kí thành viên</option>
                                        <option value="Membership.UpdateRank" selected="@(Model.EventName == "Membership.UpdateRank")">Cập nhật hạng thành viên</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="isEnable" class="form-label">Trạng thái hoạt động</label>
                                    <select id="isEnable" class="form-select">
                                        <option value="true" selected="@Model.IsActive">Đang hoạt động</option>
                                        <option value="false" selected="@(!Model.IsActive)">Ngưng hoạt động</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="typeTemplate" class="form-label">Loại tin</label>
                                    <select id="typeTemplate" class="form-select" onchange="handleGetTemplateConfig(this, '@Model.ReferenceId')">
                                        <option value="1" selected="@(Model.Type == 1)">Zalo UID</option>
                                        <option value="2" selected="@(Model.Type == 2)">Incom Omni</option>

                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="conditions" class="form-label">Điều kiện thực thi</label>
                                    <input id="conditions" type="text" class="form-control" value="@Model.Conditions" placeholder="Nhập điều kiện..." />
                                    <small class="text-muted">
                                        Ví dụ: Status = 1 or Status = 2 and Amount > 100000<br>
                                        Hỗ trợ: =, !=, and, or (không phân biệt hoa thường)
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <input type="hidden" value="@Model.ReferenceId" id="ReferenceId" />

                    <!-- Chi tiết cấu hình -->
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body">
                                <div id="configContainer"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Chi tiết dữ liệu có thể sử dụng-->
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body">
                                <h5 class="card-title">Payload của Event Trigger</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-primary fw-semibold">🛒 Order Events (Order.Create, Order.Update)</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Trường</th>
                                                        <th>Kiểu</th>
                                                        <th>Mô tả</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><code>OrderId</code></td>
                                                        <td>string</td>
                                                        <td>Mã đơn hàng</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>UserZaloId</code></td>
                                                        <td>string</td>
                                                        <td>ID người dùng Zalo</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>UserZaloName</code></td>
                                                        <td>string</td>
                                                        <td>Tên khách hàng</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>UserZaloIdByOA</code></td>
                                                        <td>string</td>
                                                        <td>ID Zalo OA của khách hàng</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>PhoneNumber</code></td>
                                                        <td>string</td>
                                                        <td>Số điện thoại</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>DeliveryAddress</code></td>
                                                        <td>string</td>
                                                        <td>Địa chỉ giao hàng</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>Total</code></td>
                                                        <td>decimal</td>
                                                        <td>Tổng tiền đơn hàng</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>DiscountAmount</code></td>
                                                        <td>decimal</td>
                                                        <td>Số tiền được giảm</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>OrderStatus</code></td>
                                                        <td>int</td>
                                                        <td>Trạng thái đơn hàng (số)</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>PaymentStatus</code></td>
                                                        <td>int</td>
                                                        <td>Trạng thái thanh toán (số)</td>
                                                    </tr>
                                                    <tr class="table-success">
                                                        <td><code>OrderStatusLabel</code></td>
                                                        <td>string</td>
                                                        <td>Nhãn trạng thái đơn hàng</td>
                                                    </tr>
                                                    <tr class="table-success">
                                                        <td><code>PaymentStatusLabel</code></td>
                                                        <td>string</td>
                                                        <td>Nhãn trạng thái thanh toán</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-success fw-semibold">👥 Membership Events</h6>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Trường</th>
                                                        <th>Kiểu</th>
                                                        <th>Mô tả</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><code>UserZaloId</code></td>
                                                        <td>string</td>
                                                        <td>ID người dùng</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>UserZaloName</code></td>
                                                        <td>string</td>
                                                        <td>Tên thành viên</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>PhoneNumber</code></td>
                                                        <td>string</td>
                                                        <td>Số điện thoại</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>OldRank</code></td>
                                                        <td>string</td>
                                                        <td>Hạng cũ (chỉ UpdateRank)</td>
                                                    </tr>
                                                    <tr>
                                                        <td><code>NewRank</code></td>
                                                        <td>string</td>
                                                        <td>Hạng mới (chỉ UpdateRank)</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <h6 class="text-info fw-semibold mt-3">💡 Ghi chú về Status Labels</h6>
                                        <div class="alert alert-info">
                                            <small>
                                                <strong>OrderStatusLabel:</strong><br>
                                                - "Chờ xác nhận" (Pending)<br>
                                                - "Đã xác nhận" (Confirmed)<br>
                                                - "Đã hoàn thành" (Completed)<br>
                                                - "Đã hủy" (Cancelled)<br><br>

                                                <strong>PaymentStatusLabel:</strong><br>
                                                - "Chưa thanh toán" (Unpaid)<br>
                                                - "Đã thanh toán" (Paid)<br>
                                                - "Thanh toán thất bại" (Failed)<br>
                                                - "Đã hoàn tiền" (Refunded)
                                            </small>
                                        </div>

                                        <h6 class="text-warning fw-semibold">⚠️ Lưu ý Template</h6>
                                        <div class="alert alert-warning">
                                            <small>
                                                Sử dụng <strong>OrderStatusLabel</strong> và <strong>PaymentStatusLabel</strong>
                                                thay vì số để hiển thị trạng thái cho người dùng cuối.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="modal-footer px-4 py-3">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">
                <i class="ri-save-line me-2"></i> Lưu
            </button>
        </div>
    </div>
</div>

@await Html.PartialAsync("Scripts/_ModalScripts")
