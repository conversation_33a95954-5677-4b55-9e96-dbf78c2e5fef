﻿namespace MiniAppCore.Exceptions
{
    public class CustomException : Exception
    {
        public int Code { get; set; }
        public object? Detail { get; set; }
        public CustomException() : base() { }
        public CustomException(string message) : base(message) { }

        public CustomException(int code, string message) : base(message)
        {
            Code = code;
        }

        public CustomException(int code, object message) : base("")
        {
            Code = code;
            Detail = message;
        }
    }
}
