﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Models.Common;

namespace MiniAppCore.Models.Queries
{
    public class ArticleQueryParams : RequestQuery, IRequestQuery
    {
        public int? Status { get; set; }
        public string? Type { get; set; }
        public string? Email { get; set; }
        public string? CategoryId { get; set; }
        public string? ZaloUserId { get; set; }
        public string? PhoneNumber { get; set; }

        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }
}
