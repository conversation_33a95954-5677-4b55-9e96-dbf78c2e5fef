using MediatR;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.LuckyWheels;
using MiniAppCore.Entities.Memberships;

namespace MiniAppCore.Services.Gamifications.LuckyWheels
{
    /// <summary>
    /// Background Job Service để xử lý batch operations cho LuckyWheel
    /// Tất cả operations sẽ chạy trong Hangfire background jobs
    /// </summary>
    public interface ILuckyWheelBackgroundJobService
    {
        /// <summary>
        /// Background job để cộng điểm cho tất cả memberships
        /// </summary>
        Task ProcessAutoAddSpinPointsBatch(string luckyWheelId);

        /// <summary>
        /// Background job để cộng điểm cho một user cụ thể
        /// </summary>
        Task ProcessAddSpinPointsToUser(string luckyWheelId, string userZaloId);
    }

    public class LuckyWheelBackgroundJobService(
        IUnitOfWork unitOfWork,
        ILogger<LuckyWheelBackgroundJobService> logger) : ILuckyWheelBackgroundJobService
    {
        private readonly IRepository<LuckyWheel> _luckyWheelRepo = unitOfWork.GetRepository<LuckyWheel>();
        private readonly IRepository<Membership> _membershipRepo = unitOfWork.GetRepository<Membership>();
        private const int BATCH_SIZE = 500; // Tăng batch size cho hiệu quả hơn

        /// <summary>
        /// Background job để cộng điểm cho tất cả memberships với batch processing
        /// </summary>
        public async Task ProcessAutoAddSpinPointsBatch(string luckyWheelId)
        {
            try
            {
                logger.LogInformation("Starting ProcessAutoAddSpinPointsBatch for LuckyWheel {LuckyWheelId}", luckyWheelId);

                // Kiểm tra Lucky Wheel có tồn tại và được bật tính năng auto add spin points
                var luckyWheel = await _luckyWheelRepo.AsQueryable()
                    .FirstOrDefaultAsync(x => x.Id == luckyWheelId
                                            && x.IsActive
                                            && x.EnableAutoSpinPoints
                                            && x.SpinPointsToAdd > 0);

                if (luckyWheel == null)
                {
                    logger.LogWarning("LuckyWheel {LuckyWheelId} not found or auto spin points not enabled", luckyWheelId);
                    return;
                }

                // Kiểm tra thời gian hiệu lực
                var now = DateTime.Now;
                if (now < luckyWheel.StartDate || now > luckyWheel.ExpiryDate)
                {
                    logger.LogWarning("LuckyWheel {LuckyWheelId} is outside valid time range. Current: {Now}, Start: {StartDate}, End: {ExpiryDate}",
                        luckyWheelId, now, luckyWheel.StartDate, luckyWheel.ExpiryDate);
                    return;
                }

                var pointsToAdd = luckyWheel.SpinPointsToAdd;
                var updatedCount = await ProcessBatchAddPoints(pointsToAdd);

                logger.LogInformation("Successfully added {PointsToAdd} spin points to {UpdatedCount} memberships for LuckyWheel {LuckyWheelId}",
                    pointsToAdd, updatedCount, luckyWheelId);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while processing auto add spin points batch for LuckyWheel {LuckyWheelId}", luckyWheelId);
                throw;
            }
        }

        /// <summary>
        /// Background job để cộng điểm cho một user cụ thể
        /// </summary>
        public async Task ProcessAddSpinPointsToUser(string luckyWheelId, string userZaloId)
        {
            try
            {
                logger.LogInformation("Starting ProcessAddSpinPointsToUser for LuckyWheel {LuckyWheelId}, User {UserZaloId}",
                    luckyWheelId, userZaloId);

                var luckyWheel = await _luckyWheelRepo.AsQueryable()
                    .FirstOrDefaultAsync(x => x.Id == luckyWheelId
                                            && x.IsActive
                                            && x.EnableAutoSpinPoints
                                            && x.SpinPointsToAdd > 0);

                if (luckyWheel == null)
                {
                    logger.LogWarning("LuckyWheel {LuckyWheelId} not found or auto spin points not enabled", luckyWheelId);
                    return;
                }

                var membership = await _membershipRepo.AsQueryable()
                    .FirstOrDefaultAsync(x => x.UserZaloId == userZaloId);

                if (membership == null)
                {
                    logger.LogWarning("Membership not found for UserZaloId: {UserZaloId}", userZaloId);
                    return;
                }

                membership.SpinPoint += luckyWheel.SpinPointsToAdd;
                _membershipRepo.Update(membership);
                await unitOfWork.SaveChangesAsync();

                logger.LogInformation("Successfully added {PointsToAdd} spin points to user {UserZaloId} for LuckyWheel {LuckyWheelId}",
                    luckyWheel.SpinPointsToAdd, userZaloId, luckyWheelId);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while adding spin points to user {UserZaloId} for LuckyWheel {LuckyWheelId}",
                    userZaloId, luckyWheelId);
                throw;
            }
        }

        /// <summary>
        /// Xử lý batch add points cho tất cả memberships
        /// </summary>
        private async Task<int> ProcessBatchAddPoints(long pointsToAdd)
        {
            var totalMemberships = await _membershipRepo.AsQueryable().CountAsync();
            var updatedCount = 0;

            logger.LogInformation("Starting batch processing for {TotalMemberships} memberships with batch size {BatchSize}",
                totalMemberships, BATCH_SIZE);

            // Xử lý theo batch để tránh quá tải memory và database
            for (int skip = 0; skip < totalMemberships; skip += BATCH_SIZE)
            {
                try
                {
                    var membershipsBatch = await _membershipRepo.AsQueryable()
                        .Skip(skip)
                        .Take(BATCH_SIZE)
                        .ToListAsync();

                    if (!membershipsBatch.Any())
                        break;

                    // Cập nhật spin points cho batch hiện tại
                    foreach (var membership in membershipsBatch)
                    {
                        membership.SpinPoint += pointsToAdd;
                        _membershipRepo.Update(membership);
                        updatedCount++;
                    }

                    // Lưu batch hiện tại
                    await unitOfWork.SaveChangesAsync();

                    var batchNumber = (skip / BATCH_SIZE) + 1;
                    logger.LogDebug("Processed batch {BatchNumber}/{TotalBatches}: {BatchCount} memberships updated",
                        batchNumber, (int)Math.Ceiling((double)totalMemberships / BATCH_SIZE), membershipsBatch.Count);

                    // Thêm delay giữa các batch để tránh quá tải DB
                    if (skip + BATCH_SIZE < totalMemberships)
                    {
                        await Task.Delay(200); // 200ms delay
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error processing batch starting at skip {Skip}", skip);
                    // Continue với batch tiếp theo thay vì fail toàn bộ
                    continue;
                }
            }

            return updatedCount;
        }
    }
}
