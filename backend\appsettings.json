{"ConnectionStrings": {"Development": "Data Source=.;Initial Catalog=MiniAppCore;Integrated Security=True;Trust Server Certificate=True", "Production": "Data Source=103.216.124.163,1433;Initial Catalog=MiniAppCore_Test;User Id=icsg;Password=***************; Trust Server Certificate=True", "Hangfire_Dev": "Data Source=.;Initial Catalog=Hangfire_MiniAppCore;Integrated Security=True;Trust Server Certificate=True", "Hangfire_Production": "Data Source=103.216.124.163,1433;Initial Catalog=Hangfire_MiniAppCore_Test;User Id=icsg;Password=***************; Trust Server Certificate=True"}, "BookingSettings": {"MinHoursAhead": 1}, "Jwt": {"secretKey": "3fd9beadcb8dabd02f049650ccedd457c7ea8a8c13ab02643dc910bd456f5a16", "Issuer": "saigon.incom.vn", "Audience": "miniappcore.incom.vn"}, "OmniTool": {"BatchSize": 1, "BatchPerGroup": 2}, "NotificationSetting": {"Constants": {"BatchSize": 200, "BatchPerGroup": 5, "TimeToSendUID": 30}, "Telegram": {"BotToken": "**********************************************", "ChatId": "-4662948128", "PrefixMessage": "MiniAppCore: <PERSON><PERSON> request tới server nhi<PERSON><PERSON> lần. <PERSON><PERSON> lòng kiểm tra lại. Request IP: "}, "AppInfo": {"AppName": "Mini App Core", "Version": "v1.0.0"}}, "MiniAppSettings": {"PrivateKey": "32301276687ef7261ea4a2ef5d83b91f", "StoreId": "incomsg", "StoreName": "incomsg", "OrderGroupId": "1166385188174072095", "ZaloUrl": "https://zalo.me/s/", "Source": "CMS", "RegisterFormTemplateUrl": "https://zalo.me/s/1166385188174072095/register/{formId}?default=true", "PointHistoryMessageTemplate": "Mã đơn #{referenceId}, Bạn được tặng {amount} điểm thành viên"}, "Branches": [{"Id": "b2a1b5e9d10e4e5fb4c3cdd03e1180a9", "Name": "INCOM SÀI GÒN", "Address": "607 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> <PERSON>"}], "ZaloNotification": {"API_KEY": "3zRlRT7AAXOCr_5crSaf00pKy7xIoou23D3rUpBeAkj8rium4m", "MiniAppId": "1166385188174072095"}, "AdminSettings": {"PrivateKey": "0ea7efea1b1eacbf6d99a8dd65b8a6e2"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}