﻿using AutoMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.BookingItems;
using MiniAppCore.Services.Bookings;
using System.Text.RegularExpressions;

namespace MiniAppCore.Controllers.API
{
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    [Route("api/[controller]")]
    public class BookingItemsController(ILogger<BookingItemsController> logger, IMapper mapper, IBookingItemService bookingItemService) : ControllerBase
    {
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetPage([FromQuery] ProductQueryParams queryParams)
        {
            try
            {
                if (!User.IsInRole("ADMIN"))
                {
                    queryParams.StockStatus = new List<Enums.EProduct>() { Enums.EProduct.InStock, Enums.EProduct.OutOfStock };
                    //queryParams.IsOrderByCreatedDate = false;
                }

                var result = await bookingItemService.GetPage(queryParams);
                result.Data.ToList().ForEach(x =>
                {
                    x.Description = Regex.Replace(x.Description ?? string.Empty, "<.*?>", "");
                });
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<IActionResult> GetBookingItemById(string id)
        {
            try
            {
                var product = await bookingItemService.GetDetailResponseByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = product
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateBookingItem([FromForm] BookingItemRequest dto)
        {
            try
            {
                await bookingItemService.CreateAsync(dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBookingItem(string id, [FromForm] BookingItemRequest model)
        {

            try
            {
                await bookingItemService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật sản phẩm thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBookingItem(string id)
        {
            try
            {
                await bookingItemService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }
    }
}
