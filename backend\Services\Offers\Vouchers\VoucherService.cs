﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Offers.Vouchers;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Models.Responses.Vouchers;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;
using System.Globalization;

namespace MiniAppCore.Services.Offers.Vouchers
{
    public class VoucherService(IUnitOfWork unitOfWork, IMapper mapper) : Service<Voucher>(unitOfWork), IVoucherService
    {
        private readonly IRepository<OrderVoucher> _orderVoucherRepo = unitOfWork.GetRepository<OrderVoucher>();
        private readonly IRepository<VoucherProduct> _voucherProductRepo = unitOfWork.GetRepository<VoucherProduct>();

        private readonly IRepository<Membership> _membershipRepo = unitOfWork.GetRepository<Membership>();
        private readonly IRepository<PointHistory> _pointHistoryRepo = unitOfWork.GetRepository<PointHistory>();
        private readonly IRepository<MembershipTag> _membershipTagRep = unitOfWork.GetRepository<MembershipTag>();
        private readonly IRepository<MembershipVoucher> _membershipVoucherRepo = unitOfWork.GetRepository<MembershipVoucher>();

        public async Task<PagedResult<VoucherResponse>> GetPage(VoucherQueryParams queryParams, bool? isActive = null, bool? isExchange = null, string? userZaloId = null)
        {
            var vouchers = _repository.AsQueryable();

            // query trạng thái của voucher
            if (isActive.HasValue)
            {
                vouchers = vouchers.Where(x => x.IsActive == isActive);
                if (isActive.Value)
                {
                    var now = DateTime.Now;
                    vouchers = vouchers.Where(x => x.StartDate <= now && x.EndDate >= now);
                }
            }

            // có thể đổi bằng điểm hay ko
            if (isExchange.HasValue)
            {
                vouchers = vouchers.Where(x => x.IsExchange == isExchange);
            }

            // lấy theo danh sách của người dùng
            if (!string.IsNullOrEmpty(userZaloId))
            {
                var now = DateTime.Now;
                var membershipVoucherIds = _membershipVoucherRepo
                    .AsQueryable()
                    .Where(x => x.MembershipId == userZaloId && x.Status == EOffers.Available && x.ExpiryDate >= now)
                    .Select(x => x.VoucherId)
                    .Distinct();
                vouchers = vouchers.Where(x => membershipVoucherIds.Contains(x.Id));
            }

            if (queryParams.ProductIds != null && queryParams.ProductIds.Any())
            {
                var voucherProducts = _voucherProductRepo.AsQueryable().Where(vp => queryParams.ProductIds.Contains(vp.ProductId)).Select(x => x.VoucherId);
                vouchers = vouchers.Where(x => voucherProducts.Contains(x.Id) || x.IsAllProducts);
            }

            // lấy theo số tiền
            if (queryParams.TotalOrder.HasValue)
            {
                vouchers = vouchers.Where(x => x.MinimumOrderValue <= queryParams.TotalOrder.Value);
            }

            // lấy theo loại voucher
            if (queryParams.Type.HasValue)
            {
                vouchers = vouchers.Where(x => x.VoucherType == (EVoucherType)queryParams.Type);
            }

            // lấy theo keyword
            if (!string.IsNullOrEmpty(queryParams.Keyword))
            {
                var keyword = queryParams.Keyword;
                vouchers = vouchers.Where(x => x.Code.Contains(keyword) || x.Name.Contains(keyword));
            }

            var totalItems = await vouchers.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / queryParams.PageSize);
            var vouchersList = await vouchers
                .OrderByDescending(x => x.CreatedDate)
                .Skip(queryParams.Skip)
                .Take(queryParams.PageSize)
                .ToListAsync();

            var items = mapper.Map<List<VoucherResponse>>(vouchersList);

            return new PagedResult<VoucherResponse>()
            {
                Data = items,
                TotalPages = 0,
            };
        }

        public async Task<PagedResult<T>> GetPage<T>(VoucherQueryParams queryParams, bool? isActive = null, bool? isExchange = null, string? userZaloId = null)
        {
            var vouchers = _repository.AsQueryable();

            // query trạng thái của voucher
            if (isActive.HasValue)
            {
                vouchers = vouchers.Where(x => x.IsActive == isActive);
                if (isActive.Value)
                {
                    var now = DateTime.Now;
                    vouchers = vouchers.Where(x => x.StartDate <= now && x.EndDate >= now);
                }
            }

            // có thể đổi bằng điểm hay ko
            if (isExchange.HasValue)
            {
                vouchers = vouchers.Where(x => x.IsExchange == isExchange);
            }

            // lấy theo danh sách của người dùng
            if (!string.IsNullOrEmpty(userZaloId))
            {
                var now = DateTime.Now;
                var membershipVoucherIds = _membershipVoucherRepo
                    .AsQueryable()
                    .Where(x => x.MembershipId == userZaloId && x.Status == EOffers.Available && x.ExpiryDate >= now)
                    .Select(x => x.VoucherId)
                    .Distinct();
                vouchers = vouchers.Where(x => membershipVoucherIds.Contains(x.Id));
            }

            if (queryParams.ProductIds != null && queryParams.ProductIds.Any())
            {
                var voucherProducts = _voucherProductRepo.AsQueryable().Where(vp => queryParams.ProductIds.Contains(vp.ProductId)).Select(x => x.VoucherId);
                vouchers = vouchers.Where(x => voucherProducts.Contains(x.Id) || x.IsAllProducts);
            }

            // lấy theo số tiền
            if (queryParams.TotalOrder.HasValue)
            {
                vouchers = vouchers.Where(x => x.MinimumOrderValue <= queryParams.TotalOrder.Value);
            }

            // lấy theo loại voucher
            if (queryParams.Type.HasValue)
            {
                vouchers = vouchers.Where(x => x.VoucherType == (EVoucherType)queryParams.Type);
            }

            // lấy theo keyword
            if (!string.IsNullOrEmpty(queryParams.Keyword))
            {
                var keyword = queryParams.Keyword;
                vouchers = vouchers.Where(x => x.Code.Contains(keyword) || x.Name.Contains(keyword));
            }

            var totalItems = await vouchers.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / queryParams.PageSize);
            var vouchersList = await vouchers
                .OrderByDescending(x => x.CreatedDate)
                .Skip(queryParams.Skip)
                .Take(queryParams.PageSize)
                .ToListAsync();

            var items = mapper.Map<List<T>>(vouchersList);
            return new PagedResult<T>()
            {
                Data = items,
                TotalPages = 0,
            };
        }

        public async Task<VoucherResponse> GetVoucherByIdAsync(string id)
        {
            var voucher = await base.GetByIdAsync(id);
            return mapper.Map<VoucherResponse>(voucher);
        }

        public async Task<IEnumerable<Voucher>> GetVoucherByCode(List<string> codes)
        {
            return await _repository.AsQueryable().Where(x => codes.Contains(x.Code)).ToListAsync();
        }

        public async Task<VoucherDetailResponse> GetVoucherDetailResponseByIdAsync(string id)
        {
            var voucher = await base.GetByIdAsync(id);
            var voucherResponse = mapper.Map<VoucherDetailResponse>(voucher);
            voucherResponse.Products = await _voucherProductRepo.AsQueryable().Where(x => x.VoucherId == id).Select(x => x.ProductId).ToListAsync();
            return voucherResponse;
        }

        public async Task<int> Redeem(string userZaloId, List<string> voucherIds)
        {
            // Lấy danh sách voucher theo voucherIds
            var vouchers = await _repository.AsQueryable()
                                            .Where(x => voucherIds.Contains(x.Id) || voucherIds.Contains(x.Code))
                                            .ToListAsync();

            if (!vouchers.Any())
            {
                throw new CustomException(200, "Không tìm thấy voucher!");
            }

            // Lấy thông tin membership
            var membership = await _membershipRepo.AsQueryable()
                                                  .FirstOrDefaultAsync(x => x.UserZaloId == userZaloId);

            if (membership == null)
            {
                throw new CustomException(201, "Không tìm thấy thành viên!");
            }

            var now = DateTime.Now;
            var timestampNow = DateTimeOffset.Now.ToUnixTimeMilliseconds();

            // Lấy danh sách voucher đã đổi của thành viên (chỉ lấy những voucher chưa hết hạn)
            var redeemedVouchers = await _membershipVoucherRepo.AsQueryable()
                                        .Where(x => x.MembershipId == userZaloId) //  && x.ExpiryDateTimeStamps >= timestampNow)
                                        .ToListAsync();

            // Chuyển danh sách redeemedVouchers thành Dictionary để tra cứu nhanh hơn
            var redeemedCounts = redeemedVouchers
                                    .GroupBy(x => x.VoucherId)
                                    .ToDictionary(g => g.Key, g => g.Count());

            foreach (var voucher in vouchers)
            {
                // Kiểm tra số lượng voucher có thể đổi
                if (voucher.Quantity == 0 && voucher.Quantity != -1)
                {
                    throw new CustomException(200, $"Voucher {voucher.Name} đã hết số lượng!");
                }

                // Kiểm tra điểm cần thiết để đổi voucher
                if (voucher.PointRequired > membership.UsingPoint && voucher.IsExchange)
                {
                    throw new CustomException(200, $"Bạn không đủ điểm để đổi voucher {voucher.Name}!");
                }

                // Kiểm tra điểm cần thiết để đổi voucher
                if (voucher.RankingPoint > membership.RankingPoint)
                {
                    throw new CustomException(200, $"Điểm hạng thành viên không đủ điểm để đổi voucher {voucher.Name}!");
                }

                // Kiểm tra thời gian hợp lệ của voucher
                if (voucher.EndDate < now || voucher.StartDate > now)
                {
                    throw new CustomException(200, $"Voucher {voucher.Name} không hợp lệ!");
                }

                // Kiểm tra số lần đã đổi voucher
                int redeemedCount = redeemedCounts.GetValueOrDefault(voucher.Id, 0);
                if (redeemedCount >= voucher.ExchangeTimes)
                {
                    throw new CustomException(200, $"Bạn đã đạt đến giới hạn đổi voucher {voucher.Name}!");
                }

                // Tạo mới MembershipVoucher
                var membershipVoucher = new MembershipVoucher
                {
                    VoucherId = voucher.Id,
                    MembershipId = membership.UserZaloId,
                    Status = EOffers.Available,
                    ExpiryDate = voucher.ExpiryDate,
                    ExpiryDateTimeStamps = ((DateTimeOffset)voucher.ExpiryDate).ToUnixTimeMilliseconds()
                };
                _membershipVoucherRepo.Add(membershipVoucher);

                // Trừ điểm nếu voucher yêu cầu đổi điểm
                if (voucher.IsExchange)
                {
                    membership.UsingPoint -= voucher.PointRequired;

                    // thêm vào lịch sử sử dụng điểm
                    _pointHistoryRepo.Add(new PointHistory()
                    {
                        Type = "EXCHANGE_VOUCHER",
                        Amount = -voucher.PointRequired,
                        UserZaloId = membership.UserZaloId,
                        ReferenceId = voucher.Id,
                    });
                }

                // Giảm số lượng voucher (nếu có giới hạn)
                if (voucher.Quantity > 0)
                {
                    voucher.Quantity -= 1;
                }
            }
            return await base.UpdateRangeAsync(vouchers);
        }

        public async Task<int> CreateAsync(VoucherRequest model)
        {
            if (await _repository.AsQueryable().AnyAsync(x => x.Code == model.Code))
            {
                throw new CustomException(200, "Đã tồn tại mã voucher này rồi! Vui lòng nhập mã khác!");
            }
            var voucher = mapper.Map<Voucher>(model);

            if (model.ProductIds.Any())
            {
                var voucherProducts = model.ProductIds.Select(x => new VoucherProduct()
                {
                    ProductId = x,
                    VoucherId = voucher.Id
                });
                _voucherProductRepo.AddRange(voucherProducts);
            }

            _repository.Add(voucher);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> UpdateAsync(string id, VoucherRequest model)
        {
            // Kiểm tra mã giảm giá đã tồn tại
            if (await _repository.AsQueryable().AnyAsync(x => x.Code == model.Code && x.Id != id))
            {
                throw new CustomException(200, "Đã tồn tại mã voucher này rồi! Vui lòng nhập mã khác!");
            }

            // Lấy thông tin voucher
            var voucher = await GetByIdAsync(id) ?? throw new NotFoundException(3001, "Không tìm thấy voucher!");

            // Cập nhật thông tin voucher
            mapper.Map(model, voucher);

            // Xóa các sản phẩm không còn liên kết với voucher
            var productsToRemove = await _voucherProductRepo.AsQueryable()
                .Where(x => x.VoucherId == id && !model.ProductIds.Contains(x.ProductId))
                .ToListAsync();
            _voucherProductRepo.DeleteRange(productsToRemove);

            // Thêm các sản phẩm mới liên kết với voucher
            if (model.ProductIds.Any())
            {
                var newVoucherProducts = model.ProductIds
                    .Where(productId => !productsToRemove.Any(x => x.ProductId == productId))
                    .Select(productId => new VoucherProduct
                    {
                        ProductId = productId,
                        VoucherId = voucher.Id
                    });
                _voucherProductRepo.AddRange(newVoucherProducts);
            }

            // Xóa tất cả sản phẩm nếu áp dụng cho tất cả sản phẩm
            if (model.IsAllProducts)
            {
                var allProductsToRemove = await _voucherProductRepo.AsQueryable()
                    .Where(x => x.VoucherId == id)
                    .ToListAsync();
                _voucherProductRepo.DeleteRange(allProductsToRemove);
            }

            // Cập nhật ngày sửa đổi
            voucher.UpdatedDate = DateTime.Now;

            // Lưu thay đổi
            return await base.UpdateAsync(voucher);
        }

        public async Task<int> UpdateMembershipVoucherByOrderId(string orderId, string userZaloId, EOffers status)
        {
            // Lấy các voucherId liên quan đến order
            var orderVoucherIds = await _orderVoucherRepo.AsQueryable()
                                                .Where(x => x.OrderId == orderId)
                                                .Select(x => x.VoucherId)
                                                .ToListAsync();

            // Lấy toàn bộ các MembershipVoucher tương ứng với các voucherId đó
            var membershipVouchers = await _membershipVoucherRepo.AsQueryable()
                                                .Where(x => orderVoucherIds.Contains(x.VoucherId) && x.MembershipId == userZaloId)
                                                .ToListAsync();

            // Group theo VoucherId và chọn bản ghi có CreatedDate nhỏ nhất
            var selectedVouchers = membershipVouchers
                                    .GroupBy(x => x.VoucherId)
                                    .Select(g =>
                                        // Ưu tiên chọn bản ghi có Status khác status cần cập nhật, nếu không có thì lấy CreatedDate nhỏ nhất
                                        g.FirstOrDefault(x => x.Status != status) ?? g.OrderBy(x => x.CreatedDate).FirstOrDefault()
                                    )
                                    .Where(x => x != null)
                                    .Select(x => x!)
                                    .ToList();

            // Cập nhật status và updatedDate
            selectedVouchers.ForEach(x =>
            {
                x.Status = status;
                x.UpdatedDate = DateTime.Now;
            });

            _membershipVoucherRepo.UpdateRange(selectedVouchers);
            return await unitOfWork.SaveChangesAsync();
        }

        #region Send Gift

        public async Task<IEnumerable<string>> SendGift(List<string> userZaloIds, List<string> voucherIds)
        {
            var messages = new List<string>();

            // Lấy danh sách vouchers từ DB (O(V))
            var vouchers = (await GetByIdsAsync(voucherIds)).ToDictionary(v => v.Id);
            if (!vouchers.Any()) throw new CustomException(200, new List<string> { "Không tìm thấy voucher hợp lệ." });

            // Lấy danh sách memberships từ DB (O(U))
            var memberships = await _membershipRepo
                .AsQueryable()
                .Where(x => userZaloIds.Contains(x.UserZaloId))
                .ToDictionaryAsync(x => x.UserZaloId);
            if (!memberships.Any()) throw new CustomException(200, new List<string> { "Không tìm thấy khách hàng hợp lệ." });

            // Lấy danh sách các voucher mà user đã có (O(U + V))
            var existingVouchers = await _membershipVoucherRepo
                .AsQueryable()
                .Where(x => userZaloIds.Contains(x.MembershipId) && voucherIds.Contains(x.VoucherId))
                .ToListAsync();

            // Chuyển thành HashSet để kiểm tra nhanh O(1)
            var existingVoucherSet = new HashSet<(string, string)>(existingVouchers.Select(x => (x.MembershipId, x.VoucherId)));

            // Tạo danh sách tất cả các cặp user-voucher trước (O(U * V))
            var allPairs = userZaloIds
                .SelectMany(user => voucherIds, (user, voucher) => new { user, voucher })
                .ToList();

            // Lọc bỏ user đã có voucher (O(N))
            var validPairs = allPairs
                .Where(pair => !existingVoucherSet.Contains((pair.user, pair.voucher)))
                .ToList();

            // Lọc bỏ voucher: hết số lượng (O(N))
            var finalPairs = validPairs
                .Where(pair => vouchers.TryGetValue(pair.voucher, out var voucher) && (voucher.Quantity > 0 || voucher.Quantity == -1))
                .ToList();

            // Nếu không còn voucher hợp lệ sau khi lọc
            if (!finalPairs.Any()) throw new CustomException(200, new List<string> { "Không còn voucher hợp lệ để tặng!" });

            // Tạo danh sách insert (O(N))
            var newMembershipVouchers = finalPairs
                .Select(pair => new MembershipVoucher
                {
                    MembershipId = pair.user,
                    VoucherId = pair.voucher,
                    Status = EOffers.Available,
                })
                .ToList();

            // Thêm vào DB (O(N))
            _membershipVoucherRepo.AddRange(newMembershipVouchers);

            // Cập nhật số lượng voucher còn lại (O(V))
            foreach (var voucher in vouchers.Values)
            {
                if (voucher.Quantity > 0)
                {
                    voucher.Quantity -= finalPairs.Count(v => v.voucher == voucher.Id);
                }
            }
            _repository.UpdateRange(vouchers.Values);
            await unitOfWork.SaveChangesAsync();

            // Tạo message phản hồi
            messages = finalPairs
                .Select(pair =>
                {
                    // Kiểm tra xem khóa có tồn tại trong từ điển memberships và vouchers không
                    if (memberships.TryGetValue(pair.user, out var membership) && vouchers.TryGetValue(pair.voucher, out var voucher))
                    {
                        // Nếu có, tạo message
                        return $"Tặng {membership.UserZaloName} voucher {voucher.Code} thành công!";
                    }
                    else
                    {
                        // Nếu không có, trả về thông báo lỗi hoặc thông báo mặc định
                        return $"Tặng voucher cho khách hàng {pair.user} không thành công!";
                    }
                })
                .ToList();
            return messages;
        }

        public async Task<IEnumerable<string>> SendGiftByTags(List<string> tagIds, List<string> voucherIds)
        {
            var messages = new List<string>();
            List<string> userZaloIds = new List<string>();

            // Check if "select all" tag is included
            bool selectAll = tagIds.Contains("all"); // Assuming "all" is the ID for select all tag

            if (selectAll)
            {
                // Get all users
                userZaloIds = await _membershipRepo
                    .AsQueryable()
                    .Select(x => x.UserZaloId)
                    .ToListAsync();
            }
            else
            {
                // Get users from tags
                userZaloIds = await _membershipTagRep
                    .AsQueryable()
                    .Where(m => tagIds.Any(x => x == m.TagId))
                    .Select(x => x.UserZaloId!)
                    .Distinct()
                    .ToListAsync();
            }
            if (!userZaloIds.Any())
            {
                throw new CustomException(200, new List<string> { "Không tìm thấy khách hàng nào thuộc các tag đã chọn." });
            }

            // Use existing SendGift method to process vouchers
            return await SendGiftWithOwnershipLimit(userZaloIds, voucherIds);
        }

        public async Task<IEnumerable<string>> SendGiftWithOwnershipLimit(List<string> userZaloIds, List<string> voucherIds)
        {
            var messages = new List<string>();

            // Lấy danh sách vouchers từ DB
            var vouchers = (await GetByIdsAsync(voucherIds)).ToDictionary(v => v.Id);
            if (!vouchers.Any()) throw new CustomException(200, new List<string> { "Không tìm thấy voucher hợp lệ." });

            // Lấy danh sách memberships từ DB
            var memberships = await _membershipRepo
                .AsQueryable()
                .Where(x => userZaloIds.Contains(x.UserZaloId))
                .ToDictionaryAsync(x => x.UserZaloId);
            if (!memberships.Any()) throw new CustomException(200, new List<string> { "Không tìm thấy khách hàng hợp lệ." });

            // Lấy danh sách các voucher mà user đã có
            var existingVouchers = await _membershipVoucherRepo
                .AsQueryable()
                .Where(x => userZaloIds.Contains(x.MembershipId) && voucherIds.Contains(x.VoucherId))
                .ToListAsync();

            // Đếm số lượng voucher đã có theo cặp user-voucher
            var userVoucherCounts = existingVouchers
                .GroupBy(x => new { x.MembershipId, x.VoucherId })
                .ToDictionary(
                    g => (g.Key.MembershipId, g.Key.VoucherId),
                    g => g.Count()
                );

            // Tạo danh sách tất cả các cặp user-voucher
            var allPairs = userZaloIds
                .SelectMany(user => voucherIds, (user, voucher) => new { user, voucher })
                .ToList();

            // Lọc các cặp hợp lệ (số lượng voucher hiện tại chưa đạt giới hạn)
            var validPairs = allPairs
                .Where(pair =>
                {
                    // Lấy số lượng voucher hiện tại của user
                    int currentCount = userVoucherCounts.GetValueOrDefault((pair.user, pair.voucher), 0);

                    // Kiểm tra xem đã đạt giới hạn chưa
                    return vouchers.TryGetValue(pair.voucher, out var voucher) &&
                           currentCount < voucher.ExchangeTimes;
                })
                .ToList();

            // Lọc bỏ voucher hết số lượng
            var finalPairs = validPairs
                .Where(pair => vouchers.TryGetValue(pair.voucher, out var voucher) &&
                             (voucher.Quantity > 0 || voucher.Quantity == -1))
                .ToList();

            if (!finalPairs.Any())
                throw new CustomException(200, new List<string> { "Không còn voucher hợp lệ để tặng hoặc khách hàng đã đạt giới hạn sở hữu!" });

            // Tạo danh sách voucher mới cần thêm
            var newMembershipVouchers = finalPairs
                .Select(pair => new MembershipVoucher
                {
                    MembershipId = pair.user,
                    VoucherId = pair.voucher,
                    Status = EOffers.Available,
                    ExpiryDate = vouchers[pair.voucher].ExpiryDate,
                    ExpiryDateTimeStamps = ((DateTimeOffset)vouchers[pair.voucher].ExpiryDate).ToUnixTimeMilliseconds()
                })
                .ToList();

            // Thêm vào DB
            _membershipVoucherRepo.AddRange(newMembershipVouchers);

            // Cập nhật số lượng voucher còn lại
            foreach (var voucher in vouchers.Values)
            {
                if (voucher.Quantity > 0)
                {
                    voucher.Quantity -= finalPairs.Count(v => v.voucher == voucher.Id);
                }
            }
            _repository.UpdateRange(vouchers.Values);
            await unitOfWork.SaveChangesAsync();

            // Tạo message phản hồi kết hợp cả thành công và thất bại
            var successMessages = finalPairs
                .Select(pair =>
                {
                    if (memberships.TryGetValue(pair.user, out var membership) && vouchers.TryGetValue(pair.voucher, out var voucher))
                    {
                        return $"Tặng {membership.UserZaloName} voucher {voucher.Code} thành công!";
                    }
                    return $"Tặng voucher cho khách hàng {pair.user} thành công!";
                });

            var rejectedPairs = allPairs.Except(finalPairs).ToList();
            var failMessages = rejectedPairs
                .Select(pair =>
                {
                    if (memberships.TryGetValue(pair.user, out var membership) && vouchers.TryGetValue(pair.voucher, out var voucher))
                    {
                        int currentCount = userVoucherCounts.GetValueOrDefault((pair.user, pair.voucher), 0);
                        if (currentCount >= voucher.ExchangeTimes)
                        {
                            return $"{membership.UserZaloName} đã đạt giới hạn sở hữu voucher {voucher.Code} ({voucher.ExchangeTimes} lần)!";
                        }
                        else if (voucher.Quantity == 0)
                        {
                            return $"Voucher {voucher.Code} đã hết số lượng!";
                        }
                    }
                    return null;
                })
                .Where(m => m != null)
                .Select(m => m!);

            return successMessages.Concat(failMessages);
        }

        #endregion

        public async Task<(List<Voucher> productVouchers, List<Voucher> shippingVouchers, List<Voucher> orderVouchers)> CategorizeVouchers(List<string> voucherCodes, string userZaloId)
        {
            var vouchers = await GetVoucherByCode(voucherCodes);
            if (!vouchers.Any()) return (new List<Voucher>(), new List<Voucher>(), new List<Voucher>());

            var voucherIds = vouchers.Select(x => x.Id).ToList();
            var membershipVouchers = _membershipVoucherRepo
                .AsQueryable()
                .Where(x => voucherIds.Contains(x.VoucherId) && x.MembershipId == userZaloId)
                .ToList();

            var now = DateTime.Now;

            // Lấy danh sách voucher không hợp lệ theo ID
            var invalidVoucherIds = membershipVouchers
                .GroupBy(x => x.VoucherId)
                .Where(g =>
                    g.All(x => x.ExpiryDate < now || x.Status is EOffers.Used or EOffers.Expired)
                )
                .Select(g => g.Key)
                .ToList();

            // Tìm các mã voucher tương ứng
            var invalidVoucherCodes = vouchers
                .Where(v => invalidVoucherIds.Contains(v.Id))
                .Select(v => v.Code)
                .ToList();

            if (invalidVoucherCodes.Any())
            {
                throw new CustomException(200, $"Các voucher sau không hợp lệ: {string.Join(", ", invalidVoucherCodes)}");
            }

            // Phân loại vouchers
            var orderVouchers = new List<Voucher>();
            var productVouchers = new List<Voucher>();
            var shippingVouchers = new List<Voucher>();

            foreach (var voucher in vouchers)
            {
                switch (voucher.VoucherType)
                {
                    case EVoucherType.Order:
                        orderVouchers.Add(voucher);
                        break;
                    case EVoucherType.Product:
                        productVouchers.Add(voucher);
                        break;
                    case EVoucherType.Shipping:
                        shippingVouchers.Add(voucher);
                        break;
                }
            }

            return (productVouchers, shippingVouchers, orderVouchers);
        }

        public async Task<Dictionary<string, List<string>>> GetAppliedProductsForVouchersAsync(List<Voucher> productVouchers, List<string> productIds)
        {
            var result = new Dictionary<string, List<string>>();

            if (productVouchers == null || productVouchers.Count == 0 || productIds == null || productIds.Count == 0)
                return result;

            // Lấy id các voucher áp dụng cho từng sản phẩm
            var specificProductVoucherIds = productVouchers
                .Where(v => v != null && v.VoucherType == EVoucherType.Product && !v.IsAllProducts)
                .Select(v => v.Id)
                .ToList();

            // Lấy mapping voucher-product cho các voucher này và các sản phẩm hiện tại
            var voucherProducts = await _voucherProductRepo.AsQueryable()
                .Where(x => specificProductVoucherIds.Contains(x.VoucherId) && productIds.Contains(x.ProductId))
                .Select(x => new { x.VoucherId, x.ProductId })
                .ToListAsync();

            // Group các ProductId theo VoucherId
            var grouped = voucherProducts
                .GroupBy(x => x.VoucherId)
                .ToDictionary(
                    g => g.Key,
                    g => g.Select(x => x.ProductId).ToList()
                );

            foreach (var voucher in productVouchers)
            {
                if (voucher == null || voucher.VoucherType != EVoucherType.Product)
                    continue;

                if (voucher.IsAllProducts)
                {
                    result[voucher.Id] = productIds.ToList();
                }
                else
                {
                    result[voucher.Id] = grouped.ContainsKey(voucher.Id) ? grouped[voucher.Id] : new List<string>();
                }
            }

            return result;
        }

        #region Apply Vouchers

        // Áp dụng voucher cho tổng hóa đơn
        public decimal ApplyOrderVouchers(Order order, List<Voucher> vouchers, List<OrderVoucher> appliedVoucher)
        {
            decimal totalDiscount = 0;
            foreach (var voucher in vouchers)
            {
                if (IsApplicable(voucher, order.Total))
                {
                    decimal discount = CalculateDiscount(voucher, order.Total);
                    totalDiscount += discount;

                    appliedVoucher.Add(new OrderVoucher()
                    {
                        OrderId = order.Id,
                        VoucherId = voucher.Id,
                        VoucherCode = voucher.Code,
                        VoucherName = voucher.Name
                    });
                }
            }

            // Cập nhật tổng giảm giá nhưng không vượt quá tổng tiền đơn hàng
            order.DiscountAmount += Math.Min(order.Total, totalDiscount);
            //return order.Total - order.DiscountAmount; // Đảm bảo không bị âm

            return Math.Max(0, order.Total - totalDiscount);
        }

        // Áp dụng voucher cho phí ship
        public decimal ApplyShippingVouchers(Order order, List<Voucher> vouchers, List<OrderVoucher> appliedVoucher)
        {
            decimal totalShippingDiscount = 0;
            foreach (var voucher in vouchers)
            {
                if (IsApplicable(voucher, order.ShippingFee)) // Sửa từ order.Total -> order.ShippingFee
                {
                    decimal discount = CalculateDiscount(voucher, order.ShippingFee);
                    totalShippingDiscount += discount;

                    appliedVoucher.Add(new OrderVoucher()
                    {
                        OrderId = order.Id,
                        VoucherId = voucher.Id,
                        VoucherCode = voucher.Code,
                        VoucherName = voucher.Name
                    });
                }
            }

            // Cập nhật phí ship nhưng không để âm
            order.ShippingFee = Math.Max(0, order.ShippingFee - totalShippingDiscount);
            return order.ShippingFee;
        }

        // Áp dụng voucher cho từng sản phẩm
        public decimal ApplyProductVouchers(OrderDetail detail, List<Voucher> vouchers, List<OrderVoucher> appliedVoucher)
        {
            decimal totalDiscount = 0;

            foreach (var voucher in vouchers)
            {
                if (IsApplicable(voucher, detail.OriginalPrice) || voucher.IsAllProducts)
                {
                    totalDiscount += CalculateDiscount(voucher, detail.OriginalPrice);
                    appliedVoucher.Add(new OrderVoucher()
                    {
                        OrderId = detail.OrderId,
                        VoucherId = voucher.Id,
                        VoucherCode = voucher.Code,
                        VoucherName = voucher.Name
                    });
                }
            }

            // Tính giá cuối cùng sau khi giảm
            detail.DiscountPrice = Math.Max(0, detail.OriginalPrice - totalDiscount);
            return detail.DiscountPrice;
        }

        // Kiểm tra voucher có áp dụng được hay không
        private bool IsApplicable(Voucher voucher, decimal price)
        {
            return price > 0 && voucher.MinimumOrderValue < price; // Điều kiện đơn giản, có thể thêm điều kiện khác
        }

        // Tính giảm giá dựa trên loại voucher
        private decimal CalculateDiscount(Voucher voucher, decimal originalPrice)
        {
            if (voucher.DiscountType == EDiscountType.Percentage)
            {
                var discountAmount = originalPrice * (voucher.DiscountValue / 100);
                if (voucher.MaxDiscountAmount > 0 && voucher.MaxDiscountAmount != 0)                 // Áp dụng giới hạn giảm giá tối đa nếu vượt quá max discount = 0 là ko giới hạn
                {
                    discountAmount = Math.Min(discountAmount, voucher.MaxDiscountAmount);
                }
                return discountAmount;
            }
            return Math.Min(originalPrice, voucher.DiscountValue); // Nếu là giảm giá cố định, không vượt quá giá gốc
        }

        #endregion

        #region Export & Import Vouchers

        public async Task<(int importCount, List<string> messages)> ImportVouchersAsync(IFormFile file)
        {
            int importCount = 0;
            var messages = new List<string>();
            var culture = new CultureInfo("vi-VN");

            // Track codes in the current import file
            var importCodes = new HashSet<string>();

            // Get existing codes from the database
            var existingCodes = await _repository.AsQueryable()
                .Select(v => v.Code)
                .ToListAsync();
            var existingCodesSet = new HashSet<string>(existingCodes);

            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);

            using var package = new ExcelPackage(stream);
            var worksheet = package.Workbook.Worksheets[0];

            int rows = worksheet.Dimension.Rows;

            for (int i = 2; i <= rows; i++)
            {
                try
                {
                    string name = worksheet.Cells[i, 1].Text?.Trim() ?? "";
                    string code = worksheet.Cells[i, 2].Text?.Trim() ?? "";
                    string description = worksheet.Cells[i, 3].Text?.Trim() ?? "";

                    // Skip empty rows
                    if (string.IsNullOrWhiteSpace(name) || string.IsNullOrWhiteSpace(code))
                        continue;

                    // Check for duplicates within the import file
                    if (importCodes.Contains(code))
                    {
                        messages.Add($"Dòng {i}: Mã voucher '{code}' đã xuất hiện trước đó trong file import");
                        continue;
                    }

                    // Check for duplicates in the database
                    if (existingCodesSet.Contains(code))
                    {
                        messages.Add($"Dòng {i}: Mã voucher '{code}' đã tồn tại trong hệ thống");
                        continue;
                    }

                    // Add to tracking set
                    importCodes.Add(code);

                    decimal discountValue = decimal.TryParse(worksheet.Cells[i, 4].Text, out var dVal) ? dVal : 0;
                    long pointRequired = long.TryParse(worksheet.Cells[i, 5].Text, out var pReq) ? pReq : 0;


                    DateTime startDate = DateTime.ParseExact(worksheet.Cells[i, 6].Text, "dd/MM/yyyy", culture);
                    DateTime endDate = DateTime.ParseExact(worksheet.Cells[i, 7].Text, "dd/MM/yyyy", culture);
                    DateTime expiryDate = DateTime.ParseExact(worksheet.Cells[i, 8].Text, "dd/MM/yyyy", culture);

                    string discountTypeStr = worksheet.Cells[i, 9].Text?.Trim();
                    if (!Enum.TryParse<EDiscountType>(discountTypeStr, true, out var discountType))
                        throw new Exception($"Giá trị DiscountType không hợp lệ: {discountTypeStr}");

                    long quantity = long.TryParse(worksheet.Cells[i, 10].Text, out var _quantity) ? _quantity : 0;
                    long exchangeTimes = long.TryParse(worksheet.Cells[i, 11].Text, out var _exchangeTimes) ? _exchangeTimes : 1;

                    var voucher = new Voucher
                    {
                        Name = name,
                        Code = code,
                        Description = description,
                        DiscountValue = discountValue,
                        PointRequired = pointRequired,
                        StartDate = startDate,
                        EndDate = endDate,
                        ExpiryDate = expiryDate,
                        ExpiryDateTimeStamps = new DateTimeOffset(expiryDate).ToUnixTimeMilliseconds(),

                        Quantity = quantity,
                        ExchangeTimes = _exchangeTimes,
                        DiscountType = discountType,

                        // Các giá trị mặc định
                        IsActive = true,
                        IsExchange = true,
                        IsAllProducts = true,
                        RankingPoint = 0,
                        VoucherType = EVoucherType.Order,
                    };

                    _repository.Add(voucher);
                    importCount++;
                }
                catch (Exception ex)
                {
                    messages.Add($"Lỗi ở dòng {i}: {ex.Message}");
                }
            }

            await unitOfWork.SaveChangesAsync();
            return (importCount, messages);
        }

        // Implementation example for VoucherService.cs
        public async Task<byte[]> ExportVouchersToExcelAsync(VoucherQueryParams queryParams)
        {
            queryParams ??= new VoucherQueryParams { PageSize = int.MaxValue };  // Export all if no params specified

            var vouchersPage = await GetPage<VoucherDetailResponse>(queryParams, queryParams.IsActive);
            var vouchers = vouchersPage.Data.ToList();

            if (vouchers.Count == 0)
            {
                throw new CustomException("Không có dữ liệu");
            }

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Vouchers");

            // Add header (giống thứ tự đoạn kỹ thuật)
            worksheet.Cells[1, 1].Value = "Tên Voucher";                            // Name
            worksheet.Cells[1, 2].Value = "Mã Voucher";                             // Code
            worksheet.Cells[1, 3].Value = "Mô tả";                                  // Description
            worksheet.Cells[1, 4].Value = "Giá trị";                                // DiscountValue
            worksheet.Cells[1, 5].Value = "Điểm yêu cầu";                           // PointRequired
            worksheet.Cells[1, 6].Value = "Ngày bắt đầu (dd/MM/yyyy)";             // StartDate
            worksheet.Cells[1, 7].Value = "Ngày kết thúc (dd/MM/yyyy)";            // EndDate
            worksheet.Cells[1, 8].Value = "Ngày hết hạn (dd/MM/yyyy)";             // ExpiryDate
            worksheet.Cells[1, 9].Value = "Loại giảm giá (Phần trăm | Cố định)";   // DiscountType
            worksheet.Cells[1, 10].Value = "Số lượng";                              // Quantity
            worksheet.Cells[1, 11].Value = "Số lần đổi";                            // ExchangeTimes

            // Style header
            using (var range = worksheet.Cells[1, 1, 1, 11])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                range.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            }

            // Add data
            for (int i = 0; i < vouchers.Count; i++)
            {
                var voucher = vouchers[i];
                int row = i + 2;

                worksheet.Cells[row, 1].Value = voucher.Name;
                worksheet.Cells[row, 2].Value = voucher.Code;
                worksheet.Cells[row, 3].Value = voucher.Description;
                worksheet.Cells[row, 4].Value = voucher.DiscountValue;
                worksheet.Cells[row, 5].Value = voucher.PointRequired;

                worksheet.Cells[row, 6].Value = voucher.StartDate.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 7].Value = voucher.EndDate.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 8].Value = voucher.ExpiryDate.ToString("dd/MM/yyyy");
                worksheet.Cells[row, 9].Value = voucher.DiscountType == (short)EDiscountType.FixedAmount
                    ? "Cố định"
                    : "Phần trăm";
                worksheet.Cells[row, 10].Value = voucher.Quantity;
                worksheet.Cells[row, 11].Value = voucher.ExchangeTimes;
            }

            // Auto fit columns
            worksheet.Cells.AutoFitColumns();

            return await package.GetAsByteArrayAsync();
        }

        #endregion

    }
}
