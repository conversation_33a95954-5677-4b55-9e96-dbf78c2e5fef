﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Services.Surveys.Questions;

namespace MiniAppCore.Services.Surveys.Sections
{
    public class SurveySectionService(IUnitOfWork unitOfWork, ISurveyQuestionService surveyQuestionService)
        : Service<SurveySection>(unitOfWork), ISurveySectionService
    {
        public async Task<int> CreateOrUpdateSectionsAsync(List<SurveySectionDTO> sections)
        {
            if (sections == null || !sections.Any())
                return 0;

            var processor = new SectionProcessor(this, _repository, surveyQuestionService);
            return await processor.ProcessSections(sections);
        }

        public async Task<int> CreateOrUpdateSectionsAsync(string surveyId, List<SurveySectionDTO> sections)
        {
            if (sections == null || !sections.Any())
                return 0;

            var processor = new SectionProcessor(this, _repository, surveyQuestionService);
            return await processor.ProcessSections(surveyId, sections);
        }

        public async Task<List<SectionResponse>> GetSectionsBySurveyIdAsync(string surveyId)
        {
            var dataLoader = new SectionDataLoader(_repository, surveyQuestionService);
            return await dataLoader.LoadSectionResponses(surveyId);
        }

        public async Task<List<SurveySectionDTO>> GetSectionDTOsBySurveyIdAsync(string surveyId)
        {
            var dataLoader = new SectionDataLoader(_repository, surveyQuestionService);
            return await dataLoader.LoadSectionDTOs(surveyId);
        }

        #region Helper Classes

        private class SectionProcessor
        {
            private readonly SurveySectionService _service;
            private readonly IRepository<SurveySection> _sectionRepository;
            private readonly ISurveyQuestionService _questionService;

            public SectionProcessor(
                SurveySectionService service,
                IRepository<SurveySection> sectionRepository,
                ISurveyQuestionService questionService)
            {
                _service = service;
                _sectionRepository = sectionRepository;
                _questionService = questionService;
            }

            public async Task<int> ProcessSections(List<SurveySectionDTO> sections)
            {
                // Ensure all sections have a surveyId if the first one does
                if (sections.Any() && !string.IsNullOrEmpty(sections.First().SurveyId))
                {
                    string surveyId = sections.First().SurveyId;
                    return await ProcessSections(surveyId, sections);
                }

                // We can't process without knowing the survey ID
                return 0;
            }

            public async Task<int> ProcessSections(string surveyId, List<SurveySectionDTO> sections)
            {
                var questionsDict = new Dictionary<string, List<SurveyQuestionDTO>>();
                var processedSectionIds = new List<string>();

                // Get existing sections for the survey
                var existingSections = await _sectionRepository.AsQueryable()
                    .Where(s => s.SurveyId == surveyId)
                    .ToDictionaryAsync(s => s.Id, s => s);

                foreach (var sectionDto in sections)
                {
                    var section = await ProcessSingleSection(sectionDto, surveyId, existingSections);
                    processedSectionIds.Add(section.Id);

                    // Add ALL sections to the dictionary, even those with empty questions
                    // This ensures questions in empty sections will be deleted
                    questionsDict[section.Id] = sectionDto.Questions ?? new List<SurveyQuestionDTO>();

                    // Update section IDs in questions if they exist
                    if (sectionDto.Questions?.Any() == true)
                    {
                        foreach (var question in sectionDto.Questions)
                        {
                            question.SectionId = section.Id;
                        }
                    }
                }

                // Delete sections that weren't in the update
                var sectionsToDelete = existingSections.Values
                    .Where(s => !processedSectionIds.Contains(s.Id))
                    .ToList();

                if (sectionsToDelete.Any())
                {
                    _sectionRepository.DeleteRange(sectionsToDelete);
                }

                await _service.unitOfWork.SaveChangesAsync();

                // Process all questions using optimized dictionary approach - including empty sections
                await _questionService.CreateOrUpdateQuestionsAsync(questionsDict);

                return await _service.unitOfWork.SaveChangesAsync();
            }

            private async Task<SurveySection> ProcessSingleSection(
                SurveySectionDTO sectionDto,
                string surveyId,
                Dictionary<string, SurveySection> existingSections)
            {
                bool isNewSection = string.IsNullOrEmpty(sectionDto.Id) || sectionDto.Id.StartsWith("temp_");
                SurveySection section;

                // Đảm bảo tiêu đề không bị null
                string titleSection = sectionDto.TitleSection ?? "Phần không có tiêu đề";

                if (isNewSection)
                {
                    // Create new section
                    section = new SurveySection
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        SurveyId = surveyId,
                        TitleSection = titleSection, // Sử dụng biến đã kiểm tra null
                        DisplayOrder = sectionDto.DisplayOrder
                    };

                    _sectionRepository.Add(section);
                }
                else if (existingSections.TryGetValue(sectionDto.Id, out section))
                {
                    // Update existing section - đảm bảo tiêu đề không bị mất
                    if (!string.IsNullOrEmpty(titleSection)) // Chỉ cập nhật khi có giá trị
                    {
                        section.TitleSection = titleSection;
                    }
                    section.DisplayOrder = sectionDto.DisplayOrder;

                    _sectionRepository.Update(section);
                }
                else
                {
                    // Create new section if ID was provided but not found
                    section = new SurveySection
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        SurveyId = surveyId,
                        TitleSection = titleSection, // Sử dụng biến đã kiểm tra null
                        DisplayOrder = sectionDto.DisplayOrder
                    };

                    _sectionRepository.Add(section);
                }

                return section;
            }
        }

        private class SectionDataLoader
        {
            private readonly IRepository<SurveySection> _sectionRepository;
            private readonly ISurveyQuestionService _questionService;

            public SectionDataLoader(
                IRepository<SurveySection> sectionRepository,
                ISurveyQuestionService questionService)
            {
                _sectionRepository = sectionRepository;
                _questionService = questionService;
            }

            public async Task<List<SectionResponse>> LoadSectionResponses(string surveyId)
            {
                // Get all sections for the survey
                var sections = await _sectionRepository.AsQueryable()
                    .Where(x => x.SurveyId == surveyId)
                    .OrderBy(x => x.DisplayOrder)
                    .ToListAsync();

                if (!sections.Any())
                    return new List<SectionResponse>();

                // Get all section IDs to fetch questions in one query
                var sectionIds = sections.Select(s => s.Id).ToList();
                var questionsMap = await _questionService.GetQuestionsMapBySectionIds(sectionIds);

                // Map to response objects
                return sections.Select(section => new SectionResponse
                {
                    Id = section.Id,
                    TitleSection = section.TitleSection,
                    DisplayOrder = section.DisplayOrder,
                    ListQuestion = questionsMap.TryGetValue(section.Id, out var questions)
                        ? questions
                        : new List<QuestionResponse>()
                }).ToList();
            }

            public async Task<List<SurveySectionDTO>> LoadSectionDTOs(string surveyId)
            {
                // Get all sections for the survey
                var sections = await _sectionRepository.AsQueryable()
                    .Where(x => x.SurveyId == surveyId)
                    .OrderBy(x => x.DisplayOrder)
                    .ToListAsync();

                if (!sections.Any())
                    return new List<SurveySectionDTO>();

                // Get all section IDs to fetch questions in one query
                var sectionIds = sections.Select(s => s.Id).ToList();
                var questionsMap = await _questionService.GetQuestionDTOsMapBySectionIds(sectionIds);

                // Map to DTO objects
                return sections.Select(section => new SurveySectionDTO
                {
                    Id = section.Id,
                    SurveyId = section.SurveyId,
                    TitleSection = section.TitleSection,
                    DisplayOrder = section.DisplayOrder,
                    Questions = questionsMap.TryGetValue(section.Id, out var questions)
                        ? questions
                        : new List<SurveyQuestionDTO>()
                }).ToList();
            }
        }

        #endregion
    }
}
