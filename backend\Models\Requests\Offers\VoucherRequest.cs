﻿namespace MiniAppCore.Models.Requests.Offers
{
    public class VoucherRequest
    {
        public required string Name { get; set; }
        public required string Code { get; set; }
        public string? Description { get; set; }
        public int PointRequired { get; set; }
        public long Quantity { get; set; }
        public short ApplyType { get; set; }

        public short DiscountType { get; set; }
        public decimal DiscountValue { get; set; }

        public decimal MaxDiscountAmount { get; set; }
        public decimal MinimumOrderValue { get; set; }

        public bool IsAllProducts { get; set; }
        public bool IsActive { get; set; }

        public int Expiry { get; set; } // hạn sử dụng của voucher tính từ ngày phát voucher cho user (ví dụ 7 giờ)
        public bool IsExchange { get; set; } // voucher có thể được đổi bằng điểm không?
        public int ExchangeTimes { get; set; } = 1; // một user được đổi voucher này bao nhiêu lần?
        public long RankingPoint { get; set; } //  điểm thành viên của user cần để có thể sử dụng voucher.

        public DateTime StartDate { get; set; } = DateTime.Now;
        public DateTime EndDate { get; set; } = DateTime.Now;
        public DateTime ExpiryDate { get; set; } = DateTime.Now;

        public List<string> ProductIds { get; set; } = new List<string>();
    }
}
