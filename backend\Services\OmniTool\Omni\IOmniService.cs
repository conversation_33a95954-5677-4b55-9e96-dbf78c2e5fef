﻿using MiniAppCore.Models.DTOs.SystemSettings;
using MiniAppCore.Services.OmniTool.Omni.Models;

namespace MiniAppCore.Services.OmniTool.Omni
{
    public interface IOmniService
    {
        Task<TemplateOmniResponse> GetAllOwnedTemplate();
        Task<TemplateOmniResponse> GetAllOwnedTemplate(OmniAccountDTO omniAccountDTO);

        Task<SendOmniMessgeResponse> SendOmniMessageAsync(string templateCode, string phoneNumber, string routeRule, Dictionary<string, string> listParams);
        Task<SendOmniMessgeResponse> SendOmniMessageAsync(OmniAccountDTO omniAccountDTO, string templateCode, string phoneNumber, string routeRule, Dictionary<string, string> listParams);
    }
}
