﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableLuckyWheel : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "WheelId",
                table: "SpinHistories",
                newName: "LuckyWheelId");

            migrationBuilder.AddColumn<bool>(
                name: "IsWon",
                table: "SpinHistories",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "PrizeType",
                table: "SpinHistories",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAENZN4Yq8N0GfK4wYASGk7eDniL/17X9ZcZQpiClLCZJYiMMq2ZTiCdS+DeIC2jkk8w==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsWon",
                table: "SpinHistories");

            migrationBuilder.DropColumn(
                name: "PrizeType",
                table: "SpinHistories");

            migrationBuilder.RenameColumn(
                name: "LuckyWheelId",
                table: "SpinHistories",
                newName: "WheelId");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEPoFKb6Zd4idnFIDSHTrMM5uPEelxCsz5HKWJvskqRBR5GRT2Pc/cgWlesye59zi/A==");
        }
    }
}
