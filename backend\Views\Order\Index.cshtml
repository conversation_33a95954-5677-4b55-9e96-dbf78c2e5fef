﻿@{
    var containsBranch = User.Claims
    .Where(c => c.Type == "ViewPermission")
    .Any(c => c.Value.Contains("Branch"));
}

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON>h sách đơn hàng</h4>
            </div>
            <a class="btn border add-btn shadow-none mx-2 d-none d-md-block" data-bs-toggle="modal" data-bs-target="#new-order">
                <i class="ri-add-line mr-3"></i>Tạo đơn hàng
            </a>
        </div>
    </div>
    <div class="col-lg-12">
        <div class="card shadow-sm mb-4">
            <div class="card-body">
                <div class="row g-3 align-items-end">
                    <!-- Search -->
                    <div class="col-md-3 col-sm-6">
                        <label for="search" class="form-label">T<PERSON><PERSON> kiếm</label>
                        <div class="input-group">
                            <input id="search" type="text" class="form-control" placeholder="Mã đơn hàng, tên, số điện thoại..." />
                            <button class="btn btn-primary" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Branch Filter (Admin only) -->
                    @if (User.IsInRole("ADMIN") && containsBranch)
                    {
                        <div class="col-md-2 col-sm-6">
                            <label for="filter-order-branch" class="form-label">Chi nhánh</label>
                            <select id="filter-order-branch" class="form-select" onchange="table.ajax.reload()">
                                <option selected disabled>Chọn chi nhánh</option>
                            </select>
                        </div>
                    }

                    <!-- Order Status Filter -->
                    <div class="col-md-2 col-sm-6">
                        <label for="filter-order-status" class="form-label">Trạng thái đơn</label>
                        <select id="filter-order-status" class="form-select" onchange="table.ajax.reload()">
                            <option selected disabled>Trạng thái đơn hàng</option>
                            <option value="">Tất cả</option>
                            <option value="0">Chờ xác nhận</option>
                            <option value="1">Đã xác nhận</option>
                            <option value="2">Đã hoàn thành</option>
                            <option value="3">Đã hủy</option>
                        </select>
                    </div>

                    <!-- Payment Status Filter -->
                    <div class="col-md-2 col-sm-6">
                        <label for="filter-payment-status" class="form-label">Trạng thái thanh toán</label>
                        <select id="filter-payment-status" class="form-select" onchange="table.ajax.reload()">
                            <option selected disabled>Trạng thái thanh toán</option>
                            <option value="">Tất cả</option>
                            <option value="0">Chưa thanh toán</option>
                            <option value="1">Đã thanh toán</option>
                            <option value="2">Thanh toán thất bại</option>
                            <option value="3">Đã hoàn tiền</option>
                        </select>
                    </div>

                    <!-- Date Range Filter -->
                    <div class="col-md-3 col-sm-6">
                        <label class="form-label">Thời gian đặt hàng</label>
                        <div class="input-group">
                            <input id="start" type="datetime-local" class="form-control" onchange="table.ajax.reload()" />
                            @* <span class="input-group-text"><i class="ri-arrow-right-line"></i></span> *@
                            <input id="end" type="datetime-local" class="form-control" onchange="table.ajax.reload()" />
                        </div>
                    </div>

                    <!-- Export Button -->
                    <div class="col-12 text-end">
                        <button class="btn btn-primary" onclick="table.ajax.reload()">
                            <i class="ri-filter-line me-1"></i>
                            Lọc dữ liệu
                        </button>
                        <button id="exportExcelOrderBtn" type="button" class="btn btn-success" onclick="Order.handleExportData()">
                            <i class="ri-file-excel-line me-1"></i>Xuất Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div id="quick-update-container" style="display: none !important;">
            <div class="d-flex flex-wrap flex-wrap align-items-center mt-4">
                <div class="form-group col-3">
                    <label>Trạng thái đơn hàng</label>
                    <select id="update-order-status" class="selectpicker select2 form-control">
                        <option value="0">Chờ xác nhận</option> <!-- Pending -->
                        <option value="1">Đã xác nhận</option> <!-- Confirmed -->
                        <option value="2">Đã hoàn thành</option> <!-- Completed -->
                        <option value="3">Đã hủy</option> <!-- Cancelled -->
                    </select>
                </div>

                <div class="form-group col-3">
                    <label>Trạng thái thanh toán</label>
                    <select id="updated-order-payment" class="selectpicker select2 form-control">
                        <option value="0">Chưa thanh toán</option> <!-- Unpaid -->
                        <option value="1">Đã thanh toán</option> <!-- Paid -->
                        <option value="2">Thanh toán thất bại</option> <!-- Failed -->
                        <option value="3">Đã hoàn tiền</option> <!-- Refunded -->
                    </select>
                </div>
                <div class="form-group col-3 d-block">
                    <button class="btn btn-primary w-50" id="btn-quick-update-status" onclick="Order.handleQuickUpdate()" style="margin-top: 35px;">
                        <i class="ri-check-line"></i>
                        <span>Cập nhật</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-order" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<!-- Detail order -->
<div id="modal-order" class="modal fade" tabindex="-1" aria-modal="true" data-bs-backdrop="static" role="dialog" style="overflow-y: scroll">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<!-- View Rating -->
<div id="modal-rating" class="modal fade" tabindex="-1" aria-modal="true" data-bs-backdrop="static" role="dialog">
    <div id="modal-rating-content" class="modal-dialog modal-dialog-centered modal-lg"></div>
</div>

<!-- MODAL FORM HÓA ĐƠN -->
<div id="modal-invoice" class="modal fade" tabindex="-1" aria-modal="true" data-bs-backdrop="static" role="dialog">
    <div id="modal-invoice-content" class="modal-dialog modal-lg"></div>
</div>

<!-- Modal new order -->
<div id="new-order" class="modal fade" tabindex="-1" aria-modal="true" data-bs-backdrop="static" role="dialog" style="overflow-y: scroll">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tạo đơn hàng mới</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="card-body">
                    <div class="row">
                        <!-- Customer information section -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Số điện thoại <span style="color:red">*</span></label>
                                <div class="position-relative">
                                    <div class="input-group">
                                        <input id="new-order__cusPhone" type="text" class="form-control" placeholder="Nhập số điện thoại khách hàng" />
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="button" onclick="Order.searchMembership()">
                                                <i class="ri-search-line"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="membership-search-results" class="dropdown-menu w-100" style="max-height: 250px; overflow-y: auto;"></div>
                                </div>
                                <small class="form-text text-muted">Nhập số điện thoại để tìm kiếm thông tin khách hàng</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Tên khách hàng <span style="color:red">*</span></label>
                                <input id="new-order__memebershipName" type="text" class="form-control" placeholder="Tên khách hàng" />
                            </div>
                        </div>

                        <div class="col-md-6" hidden>
                            <div class="form-group">
                                <label>Mã giảm giá</label>
                                <div class="input-group">
                                    <input id="new-order__voucher" type="text" class="form-control" placeholder="Nhập mã giảm giá nếu có" />
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="button" onclick="Order.addVoucher()">Áp dụng</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Ghi chú</label>
                                <textarea id="new-order__notes" class="form-control" rows="1" placeholder="Ghi chú cho đơn hàng"></textarea>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Địa chỉ giao hàng <span style="color:red">*</span></label>
                                <textarea id="new-order__deliveryAddress" class="form-control" rows="2" placeholder="Địa chỉ giao hàng"></textarea>
                            </div>
                        </div>

                        <!-- Product search section -->
                        <div class="col-md-12 mt-3">
                            <h6 class="font-weight-bold">Thêm sản phẩm vào đơn hàng</h6>
                            <div class="form-group">
                                <div class="input-group">
                                    <input id="new-order-search" type="text" class="form-control" placeholder="Tìm kiếm sản phẩm hoặc tư vấn sản phẩm để thêm vào đơn hàng" />
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="button"
                                            onclick="ProductSearchUtils.searchProducts($('#new-order-search').val(), '#new-order__search__result', Order.openProductDetailModal)">
                                            <i class="ri-search-line"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Search results will appear here -->
                            <div id="new-order__search__result" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto;"></div>
                        </div>

                        <!-- Selected products preview -->
                        <div class="col-md-12 mt-3">
                            <h6 class="font-weight-bold">Sản phẩm đã chọn</h6>
                            <div id="new-order__preview__detail" class="card p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Sản phẩm</th>
                                                <th class="text-center">Đơn giá</th>
                                                <th class="text-center" style="width: 160px">Số lượng</th>
                                                <th class="text-end">Thành tiền</th>
                                                <th class="text-center" style="width: 80px">Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody id="new-order-product-list">
                                            <tr id="empty-product-row">
                                                <td colspan="5" class="text-center py-3">Chưa có sản phẩm nào được thêm vào đơn hàng</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Order summary -->
                        <div class="col-md-12 mt-4">
                            <div class="card bg-light p-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-2">
                                            <label>Phương thức thanh toán:</label>
                                            <select id="new-order__paymentMethod" class="form-control">
                                                <option value="1">Tiền mặt</option>
                                                <option value="2">Chuyển khoản</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex flex-column align-items-end">
                                            <div class="d-flex justify-content-between w-100 mb-2">
                                                <span>Tạm tính:</span>
                                                <span id="total-temp" class="text-end font-weight-bold">0 đ</span>
                                            </div>
                                            <div class="d-flex justify-content-between w-100 mb-2">
                                                <span>Giảm giá:</span>
                                                <span id="discount-temp" class="text-end font-weight-bold">0 đ</span>
                                            </div>
                                            <div class="d-flex justify-content-between w-100">
                                                <span class="font-weight-bold">Tổng cộng:</span>
                                                <span id="total" class="text-end font-weight-bold text-primary">0 đ</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="submit" class="btn btn-primary" onclick="Order.createNewOrder()">
                    <i class="ri-shopping-cart-line mr-1"></i> Tạo đơn hàng
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Product detail modal - shared between create and update order -->
<div class="modal fade" id="product-detail-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi tiết sản phẩm</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Content will be dynamically populated -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="add-to-order-btn">
                    <i class="ri-shopping-cart-line mr-1"></i> Thêm vào đơn hàng
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="@Url.Content("~/js/product-search-utils.js")"></script>
    <script src="@Url.Content("~/js/order-detail.js")"></script>
    <script src="@Url.Content("~/js/order.js")"></script>

    <script>
        let orderId = getQueryParam('id');
        $(document).ready(function () {
            $('#start').val(moment().startOf('month').format('YYYY-MM-DDT00:00'));
            $('#end').val(moment().endOf('month').format('YYYY-MM-DDT23:59'));

            GetListOrder();

            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#search').on('input', search);

            $('#update-order-status').select2();
            $('#update-order-payment').select2();

            // filter theo chi nhánh
            if ($("#filter-order-branch")) {
                $("#filter-order-branch").select2({
                    placeholder: 'Chọn chi nhánh',
                    allowClear: false,
                    minimumInputLength: 0,
                    ajax: {
                        url: '@Url.Action("GetPage", "Branches")' + "?isGetAll=true",
                        dataType: 'json',
                        delay: 250, // Thời gian trễ khi gõ tìm kiếm
                        data: function (params) {
                            return {
                                keyword: params.term, // Keyword tìm kiếm
                                page: params.page || 1,
                                pageSize: 10
                            };
                        },
                        processResults: function (data, params) {
                            params.page = params?.page || 1;
                            // Map data
                            const mappedData = data.data.map(function (item) {
                                return {
                                    id: item['id'],
                                    text: item['name']
                                };
                            });

                            if (!params.term && params.page === 1) {
                                // Thêm phần tử rỗng vào đầu mảng
                                mappedData.unshift({ id: 'all', text: 'Tất cả chi nhánh' });
                            }

                            return {
                                results: mappedData,
                                pagination: {
                                    more: (params.page * (10)) < (data.totalItems || 0)
                                }
                            };
                        },
                        cache: true,
                    }
                });
            }
            //---------------------
        });

        function GetListOrder() {
            table = new DataTable("#list-order", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    let keyword = $("#search").val();
                    let start = $("#start").val();
                    let end = $("#end").val();
                    const fOrderStatus = $("#filter-order-status").val();
                    const fPaymentStatus = $("#filter-payment-status").val();

                    const branchId = $("#filter-order-branch").val();
                    // const orderId = getQueryParam('id');

                    if (orderId) {
                        keyword = orderId;
                        start = null;
                        end = null;
                        orderId = null;
                    }

                    $.ajax({
                        url: '@Url.Action("History", "Orders")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            startDate: start,
                            endDate: end,
                            orderStatus: fOrderStatus,
                            paymentStatus: fPaymentStatus,
                            branchId: branchId === "all" ? null : branchId
                        },
                        success: function (response) {
                            const formattedData = FormatOrderData(response.data)
                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "<input type='checkbox' id='selectAll' onclick='Order.handleCheckTableRow()' />", data: "select", className: 'text-center select-checkbox' },
                    { title: "STT", data: "index", className: 'text-center index-width' },
                    { title: "Thông tin đơn hàng", data: "orderInfo", className: 'col-5' },
                    { title: "Thông tin thanh toán", data: "paymentInfo", className: 'col-4' },
                    { title: "Thao tác", data: "action", className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');

            $('#list-order tbody').on('click', 'td.select-checkbox', function (event) {
                // Nếu click trực tiếp vào checkbox thì không làm gì cả
                if ($(event.target).is('input[type="checkbox"]')) return;

                // Tìm checkbox trong cell
                const checkbox = $(this).find('input[type="checkbox"]');

                // Đảo trạng thái của checkbox (nếu đang checked thì bỏ check và ngược lại)
                checkbox.prop('checked', !checkbox.prop('checked'));
                Order.showQuickUpdate()
            });
        }

        function FormatOrderData(data) {
            return data.map((item, index) => ({
                select: `<input type="checkbox" class="order-checkbox" data-order-id="${item.orderId}" onchange='Order.showQuickUpdate()' />`,
                index: index + 1,
                orderInfo: `<div class="d-flex flex-column">
                                    <p class="mb-0" data-bs-toggle="tooltip" data-bs-placement="top" title="${item.orderId}"><small>Mã đơn hàng:  <strong> ${item.orderId} </strong> </small> </span>
                                    <p class="mb-0"><small>Tên người nhận: <strong>${item.userZaloName}</strong> </small></p>
                                    <p class="mb-0"><small>Số điện thoại:  <strong>${item.phoneNumber}</strong> </small></p>
                                    <p class="mb-0 text-truncate"><small>Địa chỉ giao hàng: <strong>${item.address ? item.address : ""}</strong></small></p>
                                    <p class="mb-0"><small>Tổng tiền: <strong>${item.totalAmount ? item.totalAmount?.toLocaleString("vi-VN") : "0"} VNĐ</strong></small></p>
                                    <p class="mb-0"><small>Ghi chú: <strong>${item.note || "Không có ghi chú"}</strong></small></p>
                                    <p class="mb-0"><small>Ngày đặt hàng: <strong>${FormatDateTime(item.createdDate)}</strong></small></p>
                                </div>`,
                paymentInfo: `<div class="d-flex align-items-center">
                                    <div>
                                        <p class="mb-0"><small>Phương thức thanh toán: <strong>${item.paymentMethod == "1" ? "Tiền mặt" : "Chuyển khoản"}</strong></small></p>
                                        <div class="mb-0">
                                            <small>
                                                <p class="d-inline">Trạng thái thanh toán:</p>
                                                ${paymentStatusMapping[item.paymentStatus] || "Unknown"}
                                            </small>
                                        </div>
                                        <div class="mb-0">
                                            <small>
                                                <p class="d-inline">Trạng thái đơn hàng:</p>
                                                ${orderStatusMapping[item.orderStatus] || "Unknown"}
                                            </small>
                                        </div>
                                    </div>
                                </div>`,
                action: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                    <a onclick="GetFormOrder('${item.orderId}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Cập nhật">
                                        <i class="ri-edit-line fs-6 mr-0"></i>
                                    </a>
                                    <a onclick="GetFormInvoice('${item.orderId}')" class="badge bg-success" data-toggle="tooltip" data-placement="top" title="In hóa đơn">
                                        <i class="ri-printer-line fs-6 mr-0"></i>
                                    </a>
                                    <a onclick="ViewRating('${item.orderId}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xem đánh giá">
                                        <i class="ri-star-line fs-6 mr-0"></i>
                                    </a>
                                </div>`,
            }));
        }

        function ViewRating(id) {
            $.ajax({
                url: `/order/rating/${id}`,
                type: 'GET',
                success: function (data) {
                    $("#modal-rating-content").html(data);
                    $("#modal-rating").modal("toggle");
                }
            });
        }

        function GetFormOrder(id) {
            const url = id ? `@Url.Action("Detail", "Order")/${id}` : "@Url.Action("Create", "Order")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-order").modal("toggle");
                }
            })
        }

        function GetFormInvoice(id) {
            window.currentInvoiceOrderId = id;
            const url = `@Url.Action("Invoice", "Order")/${id}`
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-invoice-content").html(data);
                    $("#modal-invoice").modal("toggle");
                }
            })
        }

        function PrintInvoice() {
            const content = document.getElementById("invoice-content");
            if (!content) {
                AlertResponse("Không tìm thấy nội dung hóa đơn!", "error");
                return;
            }

            const originalContent = document.body.innerHTML;

            document.body.innerHTML = content.innerHTML;

            window.print();

            document.body.innerHTML = originalContent;

            location.reload();
        }

        function GetData() {
            return {
                id: $('#orderId').val(),
                // bookingId: $('#bookingId').val(),
                membershipName: $('#memebershipName').val(),
                phoneNumber: $('#cusPhone').val(),
                address: $('#deliveryAddress').val(),
                paymentStatus: $('#paymentStatus').val(),
                paymentMethod: $('#paymentMethod').val(),
                paymentChannel: $('#paymentChannel').val(),
                orderStatus: $('#orderStatus').val(),
                note: $('#notes').val(),
                isChangeOrderDetails: OrderDetail.isChangeOrderDetails,
                voucherCode: $('#voucherCode').val(),
                products: []
            }
        }

        function HandleSaveOrUpdate(id) {
            const data = GetData(id)

            const url = id !== '0' ? `/api/orders/${id}` : '/api/orders';
            const method = id !== '0' ? 'PUT' : 'POST'

            if (window.orderDetails) {
                data.products = [...data.products, ...window.orderDetails]
            }

            if (!data.products.length > 0) {
                $.alert({
                    title: "Thông báo",
                    content: "Chi tiết đơn hàng không có sản phẩm, vui lòng thêm sản phẩm!",
                });
                return;
            }

            $.ajax({
                url: url,
                type: method,
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    }
                    $("#modal-order").modal("toggle");
                },
                error: function (err) {
                    AlertResponse('Cập nhật đơn hàng thất bại!', 'error')
                }
            });
        }
    </script>
}
