﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Services.Offers.Vouchers;

namespace MiniAppCore.Controllers.API
{
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    [Route("api/[controller]")]
    public class VouchersController(ILogger<VouchersController> logger, IVoucherService voucherService) : ControllerBase
    {
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetPage([FromQuery] VoucherQueryParams query)
        {
            try
            {
                bool? isExchange = User.IsInRole("ADMIN") ? null : true;
                var resultList = await voucherService.GetPage(query, isActive: (User.IsInRole("ADMIN") ? query.IsActive : true), isExchange: isExchange);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    resultList.Data,
                    resultList.TotalPages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<IActionResult> GetVoucherDetail(string id)
        {
            try
            {
                var voucher = await voucherService.GetVoucherByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = voucher
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpGet("my-voucher")]
        public async Task<IActionResult> GetMyVoucher([FromQuery] VoucherQueryParams query, [FromQuery] short? status, [FromQuery] string? productIds)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await voucherService.GetPage(query, userZaloId: userZaloId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("Redeem/{id}")]
        public async Task<IActionResult> Redeem(string id)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await voucherService.Redeem(userZaloId, new List<string>() { id });
                return Ok(new
                {
                    Code = 0,
                    Message = "Đổi voucher thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("SendGift")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> SendGift([FromBody] SendVoucherRequest model)
        {
            try
            {

                var messagesResult = await voucherService.SendGiftWithOwnershipLimit(model.UserZaloIds, model.VoucherIds);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = messagesResult
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    Messsages = ex.Detail
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("send-gift-by-tags")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> SendGiftByTags([FromBody] SendGiftByTagsRequest request)
        {
            try
            {
                var messages = await voucherService.SendGiftByTags(request.TagIds, request.VoucherIds);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tặng voucher thành công!",
                    Messages = messages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    Messages = ex.Detail
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while gifting vouchers by tags");
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> Create([FromBody] VoucherRequest model)
        {
            try
            {
                await voucherService.CreateAsync(model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thêm mới thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> Update(string id, [FromBody] VoucherRequest model)
        {
            try
            {
                await voucherService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật voucher thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                await voucherService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa voucher thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }
    }
}
