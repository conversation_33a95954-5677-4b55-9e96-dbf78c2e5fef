﻿namespace MiniAppCore.Models.Requests.Carts
{
    public class CartItemRequest
    {
        public long Quantity { get; set; }
        public string? Note { get; set; }
        public string? CartId { get; set; }
        public string? BranchId { get; set; }
        public string? VariantId { get; set; }
        public required string ProductId { get; set; }
        public List<string>? Options { get; set; }
    }
}
