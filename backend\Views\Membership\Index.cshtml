@using Newtonsoft.Json

<partial name="Styles/_IndexStyles" />

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Kh<PERSON>ch hàng</h4>
            </div>
            <div>
                <button type="button" class="btn btn-success mt-2" data-bs-toggle="modal" data-bs-target="#modal-import-membership"><i class="ri-upload-cloud-line"></i>Nhập danh sách khách
                    hàng</button>
                <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-excel-export"><i class="ri-file-excel-line"></i>Xuất Excel</button>
            </div>
        </div>
    </div>

    <!--<PERSON><PERSON> lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên/số điện thoại">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Thời gian đăng ký</label>
                        <div class="d-flex align-items-center">
                            <div class="input-group">
                                <input id="filterStart" type="date" class="form-control">
                                <span class="input-group-text bg-light border-0">đến</span>
                                <input id="filterEnd" type="date" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 d-flex justify-content-end mt-3">
                        <button class="btn btn-primary" onclick="table.ajax.reload()">
                            <i class="ri-filter-line me-1"></i> Lọc
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-membership" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<!-- Include all modals -->
<partial name="Partials/_IndexModals" />
<partial name="Partials/_ViewOrdersModal" />

@section Scripts {
    <!-- Include all JavaScript partials -->
    <partial name="Scripts/_IndexScripts" />
    <partial name="Scripts/_MessageScripts" />
    <partial name="Scripts/_ImportExportScripts" />
    <partial name="Scripts/_OrderScripts" />
    <partial name="Scripts/_ReferralTreeScripts" />
}
