﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Models.DTOs.Properties;
using MiniAppCore.Services.Products.Variants;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class ProductPropertyController(IProductPropertyService productPropertyService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("ProductProperty/Create")]
        public IActionResult Create()
        {
            var productProperty = new PropertyDTO();
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới thuộc tính";
            return PartialView("_ProductProperty", productProperty);
        }

        [HttpGet("ProductProperty/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật thuộc tính";
            var property = await productPropertyService.GetDetailProperty(id);
            return PartialView("_ProductProperty", property);
        }
    }
}
