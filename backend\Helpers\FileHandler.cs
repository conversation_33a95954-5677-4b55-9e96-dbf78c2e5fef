﻿namespace MiniAppCore.Helpers
{
    public class FileHandler
    {
        public static async Task<List<string>> SaveFiles(List<IFormFile> files, string uploadFolder)
        {
            List<string> filesResult = new List<string>();

            foreach (var file in files)
            {
                using (var memoryStream = new MemoryStream())
                {
                    if (!Directory.Exists(uploadFolder))
                    {
                        Directory.CreateDirectory(uploadFolder);
                    }

                    var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(file.FileName).Replace(" ", "-");
                    var newName = $"{Guid.NewGuid().ToString("N")}{Path.GetExtension(file.FileName)}";

                    var filePath = Path.Combine(uploadFolder, newName);

                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await file.CopyToAsync(fileStream);
                    }

                    filesResult.Add(newName);
                }
            }

            return filesResult;
        }

        public static async Task<List<string>> SaveFile(IFormFile file, string uploadFolder)
        {
            List<string> filesResult = new List<string>();

            using (var memoryStream = new MemoryStream())
            {
                if (!Directory.Exists(uploadFolder))
                {
                    Directory.CreateDirectory(uploadFolder);
                }

                var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(file.FileName).Replace(" ", "-");
                var newName = $"{Guid.NewGuid().ToString("N")}{Path.GetExtension(file.FileName)}";

                var filePath = Path.Combine(uploadFolder, newName);

                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(fileStream);
                }

                filesResult.Add(newName);
            }

            return filesResult;
        }

        public static void RemoveFiles(List<string> paths)
        {
            foreach (var path in paths)
            {
                if (File.Exists(path))
                {
                    File.Delete(path);
                }
            }
        }

        public static bool IsFileInUploadFolder(string fileName, string uploadFolder)
        {
            if (!Directory.Exists(uploadFolder))
            {
                return false;
            }

            var filePath = Path.Combine(uploadFolder, fileName);

            return File.Exists(filePath);
        }

        public static IEnumerable<string> GetMediaUrls(string webrootPath, string hostUrl, string folderPath)
        {
            var files = Path.Combine(webrootPath, folderPath);
            IEnumerable<string>? imageUrls = null;
            if (Directory.Exists(files))
            {
                var imageFiles = Directory.GetFiles(files);

                imageUrls = imageFiles.Select(file =>
                    $"{hostUrl}/{folderPath}/{Path.GetFileName(file)}"
                );
            }

            return imageUrls ?? Enumerable.Empty<string>();
        }

        public static async Task<string> SaveFileWithName(IFormFile file, string uploadFolder, string name)
        {
            var newName = $"{name}{Path.GetExtension(file.FileName)}";
            var filePath = Path.Combine(uploadFolder, newName);
            using (var memoryStream = new MemoryStream())
            {
                if (!Directory.Exists(uploadFolder))
                {
                    Directory.CreateDirectory(uploadFolder);
                }

                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await file.CopyToAsync(fileStream);
                }
            }
            return newName;
        }

        public static void DeleteFolder(string folderPath)
        {
            if (Directory.Exists(folderPath))
            {
                Directory.Delete(folderPath, true);
            }
        }

        public static void DeleteFile(string filePath)
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }

        public static void DeleteAllFiles(string folderPath)
        {
            if (Directory.Exists(folderPath))
            {
                foreach (var file in Directory.GetFiles(folderPath))
                {
                    File.Delete(file);
                }

                foreach (var directory in Directory.GetDirectories(folderPath))
                {
                    Directory.Delete(directory, true);
                }
            }
        }

    }
}
