﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Models.Responses.Surveys;

namespace MiniAppCore.Services.Surveys.Sections
{
    public interface ISurveySectionService : IService<SurveySection>
    {
        Task<int> CreateOrUpdateSectionsAsync(List<SurveySectionDTO> sections);
        Task<int> CreateOrUpdateSectionsAsync(string surveyId, List<SurveySectionDTO> sections); // tạo mới hoặc cập nhật các section của survey Id

        Task<List<SectionResponse>> GetSectionsBySurveyIdAsync(string surveyId);
        Task<List<SurveySectionDTO>> GetSectionDTOsBySurveyIdAsync(string surveyId);
    }
}
