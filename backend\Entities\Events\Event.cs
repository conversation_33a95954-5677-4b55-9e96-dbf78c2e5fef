﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;
using static Microsoft.IO.RecyclableMemoryStreamManager;

namespace MiniAppCore.Entities.Events
{
    public class Event : BaseEntity
    {
        public required string Title { get; set; }
        public required string Content { get; set; }
        public string? Banner { get; set; }
        public string? Images { get; set; }

        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        public EEvent Type { get; set; } // Type == Online -> MeetingLink or Type == Offline -> GoogleMapURL
        public string? MeetingLink { get; set; }
        public string? GoogleMapURL { get; set; }

        public string? Address { get; set; } // Full địa chỉ
        
        public bool IsActive { get; set; } = true;

        public EEventStatus Status { get; set; }
    }
}
