﻿using MiniAppCore.Models.Responses.Orders;

namespace MiniAppCore.Models.Responses.Products.Ratings
{
    public class ProductRatingResponse
    {
        public string? OrderId { get; set; }
        public List<ProductRatingItemResponse> Products { get; set; } = new List<ProductRatingItemResponse>();
    }

    public class ProductReviewResponse
    {
        public bool IsRating { get; set; } = false;
        public float Star { get; set; }
        public string? Review { get; set; }
        public string? OrderDetailId { get; set; }
        public List<string> Images { get; set; } = new List<string>();
        public List<string> Videos { get; set; } = new List<string>();
    }

    public class SummaryProductReviewResponse
    {
        public int TotalRating { get; set; }
        public double AveragePoint { get; set; }
    }

    public class ProductRatingItemResponse : ProductReviewResponse
    {
        public OrderItemResponse? Product { get; set; }
    }

    public class ProductReviewDetailResponse : ProductReviewResponse
    {
        public string? CustomerName { get; set; }
        public string? CustomerPhone { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerId { get; set; }
        public DateTime RatingDate { get; set; } = DateTime.Now;
        public string? CustomerAvatar { get; set; }

        public string? RatingId { get; set; }
        public string? OrderId { get; set; }
        public bool IsShow { get; set; }
    }

    public class CustomerProductRatingResponse : ProductRatingResponse
    {
        public string? CustomerName { get; set; }
        public string? CustomerPhone { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerId { get; set; }
        public DateTime RatingDate { get; set; } = DateTime.Now;
        public string? CustomerAvatar { get; set; }
    }
}
