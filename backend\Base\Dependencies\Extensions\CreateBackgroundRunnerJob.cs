﻿using MiniAppCore.Services.Gamifications.LuckyWheels;

namespace MiniAppCore.Base.Dependencies.Extensions
{
    public static class CreateBackgroundRunnerJob
    {
        public static async Task InitialCreateBackgroundJob(this IHost app)
        {
            using (var scope = app.Services.CreateScope())
            {
                var jobManagementService = scope.ServiceProvider.GetRequiredService<ILuckyWheelJobManagementService>();
                await jobManagementService.InitializeAllAutoSpinPointsJobs();
            }
        }
    }
}
