﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Services.InvoiceTemplates;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class InvoiceTemplateController(IInvoiceTemplateService invoiceTemplateService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("InvoiceTemplate/Create")]
        public IActionResult Create()
        {
            var template = new InvoiceTemplate()
            {
                Id = string.Empty,
                Name = string.Empty,
                Content = string.Empty,
                IsDefault = false,
            };
            ViewBag.Title = "Thêm mới mẫu hóa đơn";
            ViewBag.Button = "Lưu";
            return PartialView("_InvoiceTemplate", template);
        }

        [HttpGet("InvoiceTemplate/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Title = "Cập nhật mẫu hóa đơn";
            ViewBag.Button = "Cập nhật";
            var result = await invoiceTemplateService.GetByIdAsync(id);
            if (result == null)
            {
                return RedirectToAction("Create");
            }
            return PartialView("_InvoiceTemplate", result);
        }
    }
}
