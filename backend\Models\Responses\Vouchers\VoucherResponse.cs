﻿namespace MiniAppCore.Models.Responses.Vouchers
{
    public class VoucherResponse
    {

        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Code { get; set; }
        public long PointRequired { get; set; }
        public string? Description { get; set; }
        public decimal MaxDiscountAmount { get; set; }
        public decimal MinimumOrderValue { get; set; }

        public bool Status { get; set; }
        public short DiscountType { get; set; }
        public decimal DiscountValue { get; set; }

        public DateTime EndDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime ExpiryDate { get; set; }
    }
}
