﻿@model MiniAppCore.Models.DTOs.Surveys.SurveyAnswerDTO

<div class="option-item" data-option-id="@Model.Id">
    <div class="flex-grow-1 me-3">
        <div class="input-group">
            <span class="input-group-text bg-light"><i class="ri-price-tag-3-line"></i></span>
            <input type="text" class="form-control option-value" value="@Model.Value" onchange="SurveyModule.updateOption('@Model.QuestionId', '@Model.Id', this.value)">
        </div>
    </div>

    <div class="allow-input-container d-flex flex-column align-items-center me-2">
        <label class="form-check-label small mb-1" for="<EMAIL>">Cho phép nhập</label>
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" role="switch" id="<EMAIL>" checked="@(Model.IsInput)"
                onchange="SurveyModule.toggleAllowInput('@Model.QuestionId', '@Model.Id', this.checked)">
        </div>
    </div>

    <button type="button" class="btn btn-sm btn-outline-danger btn-action" onclick="SurveyModule.removeOption('@Model.QuestionId', '@Model.Id')">
        <i class="ri-delete-bin-line fs-6"></i>
    </button>
</div>
