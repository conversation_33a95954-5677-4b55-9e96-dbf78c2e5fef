﻿using Hangfire;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Dependencies.Zalo;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.ETM;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Notifications;
using MiniAppCore.Entities.Notifications.Templates;
using MiniAppCore.Models;
using MiniAppCore.Models.DTOs.SystemSettings;
using MiniAppCore.Models.Responses.Products;
using MiniAppCore.Services.OmniTool.Omni;
using MiniAppCore.Services.OmniTool.TokenManager;
using MiniAppCore.Services.SystemSettings;
using Newtonsoft.Json;
using System.Net.Http.Json;
using System.Reflection;
using System.Text;
using static QRCoder.PayloadGenerator;

namespace MiniAppCore.Features.EventTrigger
{
    public class EmitEventArgs : IRequest<int>
    {
        public required string EventName { get; set; }

        public string? TriggerZaloIdByOA { get; set; }
        public string? TriggerPhoneNumber { get; set; }

        public required object Payload { get; set; }
    }

    public class EventTriggerHandler(IUnitOfWork unitOfWork, IOmniService omniService, ITokenManagerService tokenManagerService, ISystemSettingService systemSetting) : IRequestHandler<EmitEventArgs, int>
    {
        private readonly IRepository<EventTriggerSetting> _eventTriggerSettingRepository = unitOfWork.GetRepository<EventTriggerSetting>();

        private readonly IRepository<OmniTemplate> _omniTemplateRepo = unitOfWork.GetRepository<OmniTemplate>();
        private readonly IRepository<Entities.ETM.ZaloTemplateUid> _zaloTemplateUidRepo = unitOfWork.GetRepository<Entities.ETM.ZaloTemplateUid>();
        public async Task<int> Handle(EmitEventArgs request, CancellationToken cancellationToken)
        {
            // Danh sách các event setting đang hoạt động
            var eventTriggerSetting = await _eventTriggerSettingRepository.AsQueryable().Where(x => x.EventName == request.EventName && x.IsActive).ToListAsync();

            var zaloTemplateUid = eventTriggerSetting.Where(x => x.Type == 1).Select(x => x.ReferenceId).ToList();
            var omniTemplateIDs = eventTriggerSetting.Where(x => x.Type == 2).Select(x => x.ReferenceId).ToList();

            var omniTemplates = await _omniTemplateRepo.AsQueryable().Where(x => omniTemplateIDs.Contains(x.Id)).ToListAsync();


            var zaloTemplateConfig = await unitOfWork.GetRepository<Entities.ETM.ZaloTemplateConfig>()
                                                     .AsQueryable()
                                                     .Where(x => zaloTemplateUid.Contains(x.Id))
                                                     .ToListAsync();

            var zaloTemplateConfigIds = zaloTemplateConfig.Select(x => x.TemplateId).ToList();
            var zaloTemplates = await _zaloTemplateUidRepo.AsQueryable().Where(x => zaloTemplateConfigIds.Contains(x.Id)).ToListAsync();

            var totalSent = 0;
            var accountDto = await systemSetting.GetOmniAccountAsync();
            // Xử lý từng event setting
            foreach (var setting in eventTriggerSetting)
            {
                #region Handle data & conditions

                // 0. Kiểm tra điều kiện trước khi xử lý
                if (!string.IsNullOrWhiteSpace(setting.Conditions))
                {
                    var conditionMet = EvaluateCondition(setting.Conditions, request.Payload);
                    if (!conditionMet)
                    {
                        continue; // Bỏ qua setting này nếu điều kiện không thỏa mãn
                    }
                }

                // 1. Lấy đúng template và chuỗi JSON
                string? json = setting.Type switch
                {
                    1 => zaloTemplateConfig
                           .FirstOrDefault(x => x.Id == setting.ReferenceId)
                           ?.TemplateMapping,
                    2 => omniTemplates
                           .FirstOrDefault(x => x.Id == setting.ReferenceId)
                           ?.TemplateMapping,
                    _ => null
                };


                string routingRule = setting.Type switch
                {
                    2 => omniTemplates
                           .FirstOrDefault(x => x.Id == setting.ReferenceId)
                           ?.RoutingRule ?? string.Empty,
                    _ => string.Empty
                };

                string templateCode = setting.Type switch
                {
                    1 => zaloTemplateConfig
                              .FirstOrDefault(x => x.Id == setting.ReferenceId)
                              ?.TemplateId,
                    2 => omniTemplates
                           .FirstOrDefault(x => x.Id == setting.ReferenceId)
                           ?.TemplateCode,
                    _ => null
                } ?? string.Empty;

                // 2. Deserialize về List<MappingParams>
                var paramsConfig = !string.IsNullOrWhiteSpace(json)
                    ? JsonConvert.DeserializeObject<List<MappingParams>>(json)
                    : new List<MappingParams>();

                var mappedData = new Dictionary<string, string>(paramsConfig!.Count);
                var payload = request.Payload;
                if (payload != null)
                {
                    var payloadType = payload.GetType();
                    var flags = BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase;

                    foreach (var param in paramsConfig)
                    {
                        // khởi tạo luôn dưới dạng string
                        string current = param.DefaultValue?.ToString() ?? string.Empty;

                        // thử đọc property
                        var prop = payloadType.GetProperty(param.MappingColumnName ?? "", flags);
                        if (prop != null)
                        {
                            var raw = prop.GetValue(payload);
                            if (raw != null)
                                current = raw.ToString() ?? string.Empty;
                        }

                        // gán vào dictionary
                        mappedData[param.ParamName ?? string.Empty] = current;
                    }
                }
                else
                {
                    // nếu payload null thì gán toàn bộ về default
                    foreach (var param in paramsConfig)
                        mappedData[param.ParamName!] = param.DefaultValue?.ToString() ?? string.Empty;
                }

                #endregion

                #region Handle recipient

                totalSent++;
                if (setting.Type == 1)
                {
                    // gửi Zalo template
                    // --- Gửi notification ---
                    var allRecipients = ParseRecipientsUserZaloIdByOa(setting.Recipients);

                    if (!string.IsNullOrWhiteSpace(request.TriggerZaloIdByOA))
                        allRecipients.Add(request.TriggerZaloIdByOA);

                    var templateContent = zaloTemplates
                        .FirstOrDefault(x => x.Id == templateCode)
                        ?.Message ?? string.Empty;

                    allRecipients = allRecipients
                        .Where(r => !string.IsNullOrWhiteSpace(r))
                        .Distinct()
                        .ToList();
                    foreach (var to in allRecipients)
                    {
                        BackgroundJob.Enqueue<EventTriggerHandler>(x => x.SendZaloUidMessage(templateCode, templateContent, to, mappedData));
                    }
                }
                else if (setting.Type == 2)
                {
                    // gửi Omni message
                    var recipients = ParseRecipientsPhoneNumber(setting.Recipients);

                    // luôn thêm người kích hoạt (phone)
                    if (!string.IsNullOrWhiteSpace(request.TriggerPhoneNumber))
                        recipients.Add(PhoneNumberHandler.FixFormatPhoneNumber(request.TriggerPhoneNumber)!);

                    // loại bỏ trùng
                    var allRecipients = recipients
                        .Where(r => !string.IsNullOrWhiteSpace(r))
                        .Distinct()
                        .ToList();

                    // --- Gửi notification ---
                    foreach (var to in allRecipients)
                    {
                        BackgroundJob.Enqueue<EventTriggerHandler>(x => x.SendOmniMessage(accountDto, templateCode, to, routingRule, mappedData));
                    }
                }
                #endregion
            }

            return totalSent;
        }

        private List<string> ParseRecipientsPhoneNumber(string recipients)
        {
            var result = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            if (string.IsNullOrWhiteSpace(recipients))
                return result.ToList();
            var parts = recipients
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(x => x.Trim())
                .Where(x => x.Length > 0);
            foreach (var part in parts)
            {
                if (PhoneNumberHandler.IsValidPhoneNumber(part))
                {
                    var formatted = PhoneNumberHandler.FixFormatPhoneNumber(part);
                    if (!string.IsNullOrWhiteSpace(formatted))
                    {
                        result.Add(formatted);
                    }
                }
            }
            return result.ToList();
        }

        private List<string> ParseRecipientsUserZaloIdByOa(string recipients)
        {
            if (string.IsNullOrWhiteSpace(recipients))
                return new List<string>();

            // 1. Tách, trim, loại bỏ các giá trị rỗng hoặc "trigger"
            var parts = recipients
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(x => x.Trim())
                .Where(x => !string.IsNullOrEmpty(x)
                            && !x.Equals("trigger", StringComparison.OrdinalIgnoreCase))
                .ToList();

            // 2. Query lên DB: lọc theo parts, bỏ null/empty, DISTINCT đơn giản
            var interimList = unitOfWork.GetRepository<Membership>()
                .AsQueryable()
                .Where(x => parts.Contains(x.Id))
                .Select(x => x.UserZaloIdByOA)
                .Where(id => !string.IsNullOrEmpty(id))
                .Distinct()        // đây là SQL DISTINCT
                .ToList();         // lên DB rồi mới materialize

            // 3. Nếu cần distinct case-insensitive, xử lý tiếp ở client:
            var finalList = interimList
                .Where(x => !string.IsNullOrEmpty(x))
                .Distinct(StringComparer.OrdinalIgnoreCase)
                .Cast<string>()
                .ToList();

            return finalList;
        }

        public async Task SendOmniMessage(OmniAccountDTO omniAccount, string templateCode, string phoneNumber, string routingRule, Dictionary<string, string> paramValues)
        {
            var response = await omniService.SendOmniMessageAsync(
                        omniAccount,
                        templateCode,
                        phoneNumber,
                        routingRule,
                        paramValues);

            var metadata = $"{JsonConvert.SerializeObject(new
            {
                EventType = "StartTrip",
                IdOMniMess = response.IdOmniMess,
                TelcoId = PhoneNumberHandler.GetIdTelco(phoneNumber),
                Status = response.Status.ToString(),
            })}";

            var requestString = $"{phoneNumber} - {templateCode} - {string.Join(",", routingRule)} - {JsonConvert.SerializeObject(paramValues)}";
            var responseString = JsonConvert.SerializeObject(response);
            await SaveLog(phoneNumber, requestString, responseString, response.Status, "2", metadata);
        }

        public async Task SendZaloUidMessage(string templateId, string templateContent, string userZaloIdByOA, Dictionary<string, string> paramValues)
        {
            var accessToken = await tokenManagerService.GetAccessToken();
            if (string.IsNullOrEmpty(accessToken))
            {
                return;
            }
            // replace nội dung template với các biến
            var content = $@"{{ 
                        ""recipient"": {{
                            ""user_id"": ""{userZaloIdByOA}""
                        }},
                        ""message"": {templateContent}
                    }}";
            foreach (var kv in paramValues)
            {
                var keyReplace = "{" + kv.Key.Trim() + "}";
                content = content.Replace(keyReplace, kv.Value ?? string.Empty);
            }
            // TODO: gửi content tới recipient.UserZaloId + lưu log
            var paramValuesString = string.Join(", ", paramValues.Select(kvp => $"{kvp.Key}: {kvp.Value}"));

            var client = new HttpClient();
            var url = "https://openapi.zalo.me/v3.0/oa/message/transaction";
            var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Headers = { { "access_token", accessToken } },
                Content = new StringContent(content, Encoding.UTF8, "application/json")
            };

            var response = await client.SendAsync(request);
            var responseString = await response.Content.ReadAsStringAsync();
            var zaloMessageResponse = JsonConvert.DeserializeObject<ZaloDataResponse>(responseString);

            var requestString = $"{userZaloIdByOA} - {templateId} - {JsonConvert.SerializeObject(paramValues)}";
            await SaveLog(userZaloIdByOA, content, responseString, zaloMessageResponse?.error, "1", requestString);
        }

        private async Task SaveLog(string recipient, string requestString, string responseString, int? statusCode, string type = "2", string metadata = "")
        {
            var log = new EventTriggerLog()
            {
                Type = type,
                Metadata = metadata,
                Recipient = recipient,
                RequestBody = requestString,
                ResponseBody = responseString,
                ResultCode = $"{statusCode}",
            };

            unitOfWork.GetRepository<EventTriggerLog>().Add(log);
            var result = await unitOfWork.SaveChangesAsync();
            Console.WriteLine($"Số bản ghi được lưu khi chạy job: {result}");
        }

        private bool EvaluateCondition(string condition, object payload)
        {
            if (string.IsNullOrWhiteSpace(condition) || payload == null)
            {
                return true;
            }

            try
            {
                var payloadType = payload.GetType();
                var flags = BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase;

                // Tách các điều kiện bằng OR
                var orConditions = condition.Split(new[] { " or ", " OR " }, StringSplitOptions.RemoveEmptyEntries);

                foreach (var orCondition in orConditions)
                {
                    var andConditions = orCondition.Split(new[] { " and ", " AND " }, StringSplitOptions.RemoveEmptyEntries);
                    bool allAndConditionsMet = true;

                    foreach (var andCondition in andConditions)
                    {
                        var trimmedCondition = andCondition.Trim();

                        // Phân tích điều kiện đơn giản: PropertyName = Value hoặc PropertyName != Value
                        if (trimmedCondition.Contains("!="))
                        {
                            var parts = trimmedCondition.Split("!=", 2);
                            if (parts.Length == 2)
                            {
                                var propName = parts[0].Trim();
                                var expectedValue = parts[1].Trim();

                                var prop = payloadType.GetProperty(propName, flags);
                                if (prop != null)
                                {
                                    var actualValue = prop.GetValue(payload)?.ToString() ?? string.Empty;
                                    if (actualValue.Equals(expectedValue, StringComparison.OrdinalIgnoreCase))
                                    {
                                        allAndConditionsMet = false;
                                        break;
                                    }
                                }
                            }
                        }
                        else if (trimmedCondition.Contains("="))
                        {
                            var parts = trimmedCondition.Split("=", 2);
                            if (parts.Length == 2)
                            {
                                var propName = parts[0].Trim();
                                var expectedValue = parts[1].Trim();

                                var prop = payloadType.GetProperty(propName, flags);
                                if (prop != null)
                                {
                                    var actualValue = prop.GetValue(payload)?.ToString() ?? string.Empty;
                                    if (!actualValue.Equals(expectedValue, StringComparison.OrdinalIgnoreCase))
                                    {
                                        allAndConditionsMet = false;
                                        break;
                                    }
                                }
                                else
                                {
                                    // Nếu không tìm thấy property, coi như điều kiện không thỏa mãn
                                    allAndConditionsMet = false;
                                    break;
                                }
                            }
                        }
                    }

                    // Nếu tất cả AND conditions trong một OR condition được thỏa mãn
                    if (allAndConditionsMet)
                    {
                        return true;
                    }
                }

                // Nếu không có OR condition nào được thỏa mãn
                return false;
            }
            catch (Exception ex)
            {
                // Log lỗi nếu cần
                Console.WriteLine($"Error evaluating condition '{condition}': {ex.Message}");
                return false;
            }
        }
    }
}
