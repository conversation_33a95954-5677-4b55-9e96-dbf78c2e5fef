﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Carts;
using MiniAppCore.Services.Orders.Carts;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    public class CartsController(ILogger<CartsController> logger, ICartService cartService) : ControllerBase
    {
        [HttpGet]
        public async Task<IActionResult> GetCartItems([FromQuery] RequestQuery query, [FromQuery] string? cartId, [FromQuery] string? branchId)
        {
            try
            {
                if (string.IsNullOrEmpty(cartId))
                {
                    cartId = Guid.NewGuid().ToString("N");
                }
                cartId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? cartId;
                var data = await cartService.GetUserCart(query, cartId, branchId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Lấy giỏ hàng thành công!",
                    Data = data,
                    CartId = cartId,
                    TotalQuantity = data.CartItems.Sum(x => x.Quantity),
                    TotalPrice = data.CartItems.Sum(x => x.DiscountPrice * x.Quantity),
                    data.TotalPages
                });
            }
            catch (CustomException ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(ex.Code, new { Code = 1, ex.Message });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("Add")]
        public async Task<IActionResult> AddCartItem([FromBody] CartItemRequest model)
        {
            try
            {
                if (User.Identity != null && User.Identity.IsAuthenticated)
                {
                    var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                    model.CartId = userZaloId;
                }
                else if (string.IsNullOrEmpty(model.CartId))
                {
                    model.CartId = Guid.NewGuid().ToString("N");
                }

                await cartService.AddItem(model.CartId, model);
                var data = await cartService.GetUserCart(new RequestQuery(), model.CartId, model.BranchId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thêm sản phẩm vào giỏ hàng thành công!",
                    model.CartId,
                    TotalQuantity = data.CartItems.Sum(x => x.Quantity),
                    TotalPrice = data.CartItems.Sum(x => x.DiscountPrice * x.Quantity),
                });
            }
            catch (CustomException ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(ex.Code, new { Code = 1, ex.Message });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("Sync/{cartId}")]
        [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
        public async Task<IActionResult> SyncCart(string cartId, [FromQuery] string? branchId)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(cartId) || string.IsNullOrEmpty(userZaloId))
                {
                    return Ok(new { Code = 1, Message = "Thiếu GuestId hoặc chưa đăng nhập!" });
                }
                await cartService.SyncCart(cartId, userZaloId);
                //var data = await cartService.GetUserCart(new RequestQuery(), userZaloId, branchId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Đồng bộ giỏ hàng thành công!",
                    //Data = data,
                    //TotalQuantity = data.CartItems.Sum(x => x.Quantity),
                    //TotalPrice = data.CartItems.Sum(x => x.DiscountPrice * x.Quantity),
                });
            }
            catch (CustomException ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(ex.Code, new { Code = 1, ex.Message });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("Update/{cartItemId}")]
        public async Task<IActionResult> UpdateCartItem(string cartItemId, [FromBody] CartItemRequest model)
        {
            try
            {
                await cartService.UpdateItem(cartItemId, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật sản phẩm thành công!",
                });
            }
            catch (CustomException ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(ex.Code, new { Code = 1, ex.Message });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("Clear")]
        public async Task<IActionResult> ClearCart([FromQuery] string? cartId, [FromQuery] string? branchId)
        {
            try
            {

                if (User.Identity != null && User.Identity.IsAuthenticated)
                {
                    var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                    cartId = userZaloId;
                }
                //cartId ??= User.Identity?.IsAuthenticated == true
                //? User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value
                //: null;

                // If cartId is still null or empty, return a BadRequest
                if (string.IsNullOrEmpty(cartId))
                {
                    return BadRequest(new { Code = 1, Message = "ID giỏ hàng được yêu cầu hoặc người dùng không được xác thực" });
                }

                await cartService.ClearCart(cartId, branchId);
                var data = await cartService.GetUserCart(new RequestQuery(), cartId, branchId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Đã xóa toàn bộ giỏ hàng!",
                    Data = data,
                    TotalQuantity = data.CartItems.Sum(x => x.Quantity),
                    TotalPrice = data.CartItems.Sum(x => x.DiscountPrice * x.Quantity),
                });
            }
            catch (CustomException ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(ex.Code, new { Code = 1, ex.Message });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("Remove/{cartItemId}")]
        public async Task<IActionResult> RemoveCartItem(string cartItemId)
        {
            try
            {
                await cartService.RemoveItem(cartItemId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Sản phẩm đã được xóa khỏi giỏ hàng!",
                });
            }
            catch (CustomException ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(ex.Code, new { Code = 1, ex.Message });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("RemoveList")]
        public async Task<IActionResult> RemoveCartItems([FromBody] List<string> cartItemIds)
        {
            try
            {
                await cartService.RemoveItems(cartItemIds);
                return Ok(new
                {
                    Code = 0,
                    Message = "Các sản phẩm đã được xóa khỏi giỏ hàng!",
                });
            }
            catch (CustomException ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(ex.Code, new { Code = 1, ex.Message });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

    }
}
