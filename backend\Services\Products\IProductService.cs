﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Products;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Models.Responses.Products;

namespace MiniAppCore.Services.Products
{
    public interface IProductService : IService<Product>
    {
        Task<int> ImportProduct(IFormFile file);
        Task<int> CreateAsync(ProductRequest dto);
        Task<int> UpdateAsync(string id, ProductRequest dto);
        Task<ProductDetailResponse> GetProductDetailAsync(string id);
        Task<PagedResult<ProductResponse>> GetPage(ProductQueryParams query);
        //Task<List<CartItemResponse>> GetProductItemsResponse(List<Cart> cartItems);
    }
}