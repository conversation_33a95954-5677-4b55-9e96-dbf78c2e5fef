﻿<!-- Modal <PERSON>ài đặt -->
<div class="modal fade" id="modal-config" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cài đặt tỷ lệ hoa hồng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="ri-information-line mr-2"></i>
                    Cài đặt tỷ lệ hoa hồng theo từng cấp giới thiệu. Cấp 1 là người giới thiệu trực tiếp.
                </div>

                <div class="d-flex justify-content-end mb-3">
                    <button type="button" class="btn btn-primary btn-sm" onclick="openCommissionRateForm()">
                        <i class="ri-add-line mr-1"></i>Thêm cấp mới
                    </button>
                </div>

                <div class="table-responsive">
                    <table id="commission-rates-table" class="table table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center" width="10%">Cấp</th>
                                <th class="text-center" width="20%">Tỷ lệ (%)</th>
                                <th class="text-center" width="20%">Trạng thái</th>
                                <th class="text-center" width="30%">Ngày hiệu lực</th>
                                <th class="text-center" width="20%">Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="commission-rates-body">
                            <tr>
                                <td colspan="5" class="text-center">Đang tải dữ liệu...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal thêm/sửa tỷ lệ hoa hồng -->
<div class="modal fade" id="modal-commission-rate-form" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered" style="max-width: 700px; width: 700px;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rate-form-title">Thêm tỷ lệ hoa hồng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="commission-rate-form">
                    <input type="hidden" id="rate-id" />

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="level" class="form-label">Cấp hoa hồng <span style="color:red">*</span></label>
                            <input id="level" 
                                   type="number" 
                                   class="form-control" 
                                   placeholder="Cấp hoa hồng..." min="1" 
                                   required
                                   oninput="InputValidator.number(this, {allowZeroFirst: true})" />
                            <small class="form-text text-muted">
                                Cấp của người dùng trong hệ thống affiliate (1 = cấp trực tiếp)
                            </small>
                        </div>

                        <div class="col-md-6">
                            <label for="rate" class="form-label">Tỷ lệ hoa hồng (%) <span style="color:red">*</span></label>
                            <input id="rate" 
                                   type="number" 
                                   class="form-control" 
                                   placeholder="Tỷ lệ hoa hồng..." 
                                   min="0" max="100" 
                                   step="0.01" required 
                                   oninput="InputValidator.decimal(this, {allowZeroFirst: true, min: 0, max: 100})" />
                            <small class="form-text text-muted">
                                Tỷ lệ hoa hồng theo phần trăm (VD: 5.5 = 5.5%)
                            </small>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="isActive">Trạng thái</label>
                        <select id="isActive" class="form-control">
                            <option value="true">Hoạt động</option>
                            <option value="false">Không hoạt động</option>
                        </select>
                        <small class="form-text text-muted">
                            Chỉ những cấu hình đang hoạt động mới được áp dụng
                        </small>
                    </div>

                    <div class="form-group mb-3">
                        <label for="effectiveDate">Ngày hiệu lực <span style="color:red">*</span></label>
                        <input id="effectiveDate" type="datetime-local" class="form-control" required />
                        <small class="form-text text-muted">
                            Thời điểm cấu hình bắt đầu có hiệu lực
                        </small>
                    </div>
                </form>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="saveCommissionRate()">Lưu</button>
            </div>
        </div>
    </div>
</div>
