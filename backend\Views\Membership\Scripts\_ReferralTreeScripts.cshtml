@using Newtonsoft.Json

<script>
    // =================== REFERRAL TREE FUNCTIONS =====================

    // Hiển thị cây đa cấp của thành viên
    // userZaloId: ID Zalo của thành viên
    // userZaloName: Tên của thành viên  
    // phoneNumber: Số điện thoại của thành viên
    // maxDepth: Đ<PERSON> sâu tối đa của cây (mặc định 3)
    function ViewReferralTree(userZaloId, userZaloName, phoneNumber, maxDepth = 3) {
        // Reset tree initialization flag
        window.treeInitialized = false;
        
        // Reset và hiển thị modal với size lớn hơn
        $("#modal-ref .modal-dialog").removeClass("modal-dialog-centered").addClass("modal-fullscreen-lg-down");

        $("#modal-ref .modal-header").html(`
            <h5 class="modal-title">
                <i class="fas fa-sitemap"></i> Cây đa cấp - ${userZaloName}
            </h5>
            <div class="d-flex align-items-center">
                <button class="btn btn-sm btn-outline-info me-2" onclick="collapseAllNodes()" title="Thu gọn tất cả">
                    <i class="fas fa-compress-alt"></i> Thu gọn tất cả
                </button>
                <div class="dropdown me-3">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        Độ sâu: ${maxDepth}
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="ViewReferralTree('${userZaloId}', '${userZaloName}', '${phoneNumber}', 1)">1 cấp</a></li>
                        <li><a class="dropdown-item" href="#" onclick="ViewReferralTree('${userZaloId}', '${userZaloName}', '${phoneNumber}', 2)">2 cấp</a></li>
                        <li><a class="dropdown-item" href="#" onclick="ViewReferralTree('${userZaloId}', '${userZaloName}', '${phoneNumber}', 3)">3 cấp</a></li>
                        <li><a class="dropdown-item" href="#" onclick="ViewReferralTree('${userZaloId}', '${userZaloName}', '${phoneNumber}', 5)">5 cấp</a></li>
                        <li><a class="dropdown-item" href="#" onclick="ViewReferralTree('${userZaloId}', '${userZaloName}', '${phoneNumber}', 10)">10 cấp</a></li>
                    </ul>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
        `);

        $("#modal-ref .modal-body").html(`
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <p class="mt-2">Đang tải cây đa cấp...</p>
                </div>
            `);

        $("#modal-ref .modal-footer").html(`
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary d-none" onclick="ExportReferralTree('${userZaloId}')">
                    <i class="fas fa-download"></i> Xuất Excel
                </button>
            `);

        $("#modal-ref").modal("show");

        // Gọi API lấy cây đa cấp
        $.ajax({
            url: `/Membership/ReferralTree/${userZaloId}?maxDepth=${maxDepth}`,
            method: "GET",
            success: function (response) {
                $("#modal-ref .modal-body").html(response);
                // Đợi DOM render xong rồi mới khởi tạo
                setTimeout(() => {
                    initializeTreeCollapse();
                }, 50);
            },
            error: function (xhr, status, error) {
                $("#modal-ref .modal-body").html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Không thể tải cây đa cấp. Vui lòng thử lại sau.
                            <br><small>${error}</small>
                        </div>
                    `);
            }
        });
    }

    // Lấy thống kê đa cấp của thành viên
    // userZaloId: ID Zalo của thành viên
    function GetReferralStatistics(userZaloId) {
        return $.ajax({
            url: `/Membership/ReferralStatistics/${userZaloId}`,
            method: "GET"
        });
    }

    // Lấy danh sách thành viên được giới thiệu trực tiếp
    // userZaloId: ID Zalo của thành viên
    function GetDirectReferrals(userZaloId) {
        return $.ajax({
            url: `/Membership/DirectReferrals/${userZaloId}`,
            method: "GET"
        });
    }

    // Lấy đường dẫn từ gốc đến thành viên
    // userZaloId: ID Zalo của thành viên
    function GetReferralPath(userZaloId) {
        return $.ajax({
            url: `/Membership/ReferralPath/${userZaloId}`,
            method: "GET"
        });
    }

    // Xuất cây đa cấp ra Excel
    // userZaloId: ID Zalo của thành viên
    function ExportReferralTree(userZaloId) {
        // Hiển thị loading
        const originalText = event.target.innerHTML;
        event.target.innerHTML = '<i class="spinner-border spinner-border-sm"></i> Đang xuất...';
        event.target.disabled = true;

        GetReferralStatistics(userZaloId)
            .then(function (response) {
                if (response.success) {
                    // Tạo dữ liệu Excel
                    const data = response.data;
                    const excelData = [];

                    // Header
                    excelData.push([
                        'Tên thành viên',
                        'Số điện thoại',
                        'Tổng giới thiệu trực tiếp',
                        'Tổng giới thiệu gián tiếp',
                        'Tổng giới thiệu',
                        'Hoa hồng kiếm được',
                        'Hoa hồng chờ thanh toán',
                        'Độ sâu tối đa'
                    ]);

                    // Data row
                    excelData.push([
                        data.userZaloName,
                        data.phoneNumber,
                        data.totalDirectReferrals,
                        data.totalIndirectReferrals,
                        data.totalReferrals,
                        data.totalCommissionEarned,
                        data.pendingCommission,
                        data.maxDepth
                    ]);

                    // Thống kê theo level
                    excelData.push([]);
                    excelData.push(['Thống kê theo cấp độ:']);
                    excelData.push(['Cấp độ', 'Số lượng']);

                    Object.keys(data.referralsByLevel).forEach(level => {
                        excelData.push([`Cấp ${level}`, data.referralsByLevel[level]]);
                    });

                    // Tạo và download file Excel
                    const ws = XLSX.utils.aoa_to_sheet(excelData);
                    const wb = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(wb, ws, "Thống kê cây đa cấp");

                    const fileName = `Cay_Da_Cap_${data.userZaloName}_${new Date().toISOString().slice(0, 10)}.xlsx`;
                    XLSX.writeFile(wb, fileName);

                    toastr.success('Xuất file Excel thành công!');
                } else {
                    toastr.error('Không thể xuất dữ liệu: ' + response.message);
                }
            })
            .catch(function (error) {
                toastr.error('Đã xảy ra lỗi khi xuất dữ liệu');
                console.error(error);
            })
            .finally(function () {
                // Khôi phục button
                event.target.innerHTML = originalText;
                event.target.disabled = false;
            });
    }

    // Hiển thị modal với danh sách thành viên được giới thiệu trực tiếp
    // userZaloId: ID Zalo của thành viên
    // userZaloName: Tên của thành viên
    function ViewDirectReferrals(userZaloId, userZaloName) {
        $("#modal-ref .modal-header").html(`
                <h5 class="modal-title">
                    <i class="fas fa-users"></i> Danh sách giới thiệu trực tiếp - ${userZaloName}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            `);

        $("#modal-ref .modal-body").html(`
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách...</p>
                </div>
            `);

        $("#modal-ref .modal-footer").html(`
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            `);

        $("#modal-ref").modal("show");

        GetDirectReferrals(userZaloId)
            .then(function (response) {
                if (response.success && response.data.length > 0) {
                    let html = '<div class="table-responsive"><table class="table table-striped">';
                    html += '<thead><tr><th>STT</th><th>Tên</th><th>SĐT</th><th>Ngày tham gia</th><th>Số người giới thiệu</th><th>Hoa hồng</th><th>Thao tác</th></tr></thead><tbody>';

                    response.data.forEach((member, index) => {
                        html += `
                                <tr>
                                    <td>${index + 1}</td>
                                    <td>${member.userZaloName}</td>
                                    <td>${member.phoneNumber}</td>
                                    <td>${FormatDateTime(member.joinDate)}</td>
                                    <td>${member.directReferrals}</td>
                                    <td>${member.totalCommission.toLocaleString()} VNĐ</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="ViewReferralTree('${member.userZaloId}', '${member.userZaloName}', '${member.phoneNumber}')">
                                            <i class="fas fa-sitemap"></i>
                                        </button>
                                    </td>
                                </tr>
                            `;
                    });

                    html += '</tbody></table></div>';
                    $("#modal-ref .modal-body").html(html);
                } else {
                    $("#modal-ref .modal-body").html(`
                            <div class="text-center">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Chưa có thành viên được giới thiệu trực tiếp</p>
                            </div>
                        `);
                }
            })
            .catch(function (error) {
                $("#modal-ref .modal-body").html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            Không thể tải danh sách. Vui lòng thử lại sau.
                        </div>
                    `);
            });
    }

    // Thu gọn tất cả các node trong cây
    function collapseAllNodes() {
        $('.expand-toggle').each(function () {
            const $toggle = $(this);
            const $node = $toggle.closest('.tree-node');
            const $children = $node.find('> .tree-children');
            const childCount = $children.find('> .tree-node').length;
            const level = parseInt($node.attr('class').match(/level-(\d+)/)?.[1] || 0);

            // Chỉ collapse các node level >= 2 (giữ lại root level 0-1 mở)
            if (level >= 2 && $children.is(':visible')) {
                $children.hide();
                $toggle.html(`<i class="fas fa-plus-circle"></i> Mở rộng (${childCount})`);
                $toggle.attr('data-expanded', 'false');
            }
        });

        toastr.info('Đã thu gọn tất cả các node');
    }

    // Khởi tạo collapse behavior cho cây đa cấp
    function initializeTreeCollapse() {
        // Tránh double initialization
        if (window.treeInitialized) {
            return;
        }
        
        // Clear any existing animations
        $('.tree-children').stop(true, true);
        
        // Clear event handlers để tránh duplicate
        $(document).off('click.treeCollapse', '.expand-toggle');
        
        // Auto-collapse các node từ level 2 trở lên
        $('.tree-node').each(function () {
            const level = parseInt($(this).attr('class').match(/level-(\d+)/)?.[1] || 0);
            if (level >= 2) {
                const $node = $(this);
                const $children = $node.find('> .tree-children');
                const $toggle = $node.find('.expand-toggle').first();
                const childCount = $children.find('> .tree-node').length;

                if ($children.length > 0 && childCount > 0) {
                    // Tắt ngay lập tức, không animation
                    $children.hide();
                    $toggle.html(`<i class="fas fa-plus-circle"></i> Mở rộng (${childCount})`);
                    $toggle.attr('data-expanded', 'false');
                }
            }
        });
        
        // Bind event handler
        $(document).on('click.treeCollapse', '.expand-toggle', function (e) {
            e.preventDefault();
            e.stopPropagation();

            const $this = $(this);
            const $node = $this.closest('.tree-node');
            const $children = $node.find('> .tree-children');
            const childCount = $children.find('> .tree-node').length;
            const isExpanded = $this.attr('data-expanded') === 'true';

            if (isExpanded) {
                // Collapse - Không dùng animation
                $children.hide();
                $this.html(`<i class="fas fa-plus-circle"></i> Mở rộng (${childCount})`);
                $this.attr('data-expanded', 'false');
            } else {
                // Expand - Không dùng animation
                $children.show();
                $this.html('<i class="fas fa-minus-circle"></i> Thu gọn');
                $this.attr('data-expanded', 'true');
            }
        });

        // Tooltip for truncated text
        $('.member-name, .member-details').each(function () {
            if (this.scrollWidth > this.clientWidth) {
                $(this).attr('title', $(this).text());
            }
        });
        
        // Mark as initialized
        window.treeInitialized = true;
    }
</script>
