﻿<script>
    // JavaScript cho view này
    function updateClaimStatus(historyId) {
        // Lấy giá trị trạng thái mới từ select box
        const newStatus = parseInt($("#claim-status-select").val());

        // Disable button và show loading state
        $("#btn-update-status").prop("disabled", true).html('<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Đang xử lý...');

        swal({
            title: "<PERSON><PERSON>c nhận cập nhật",
            text: "Bạn có chắc chắn muốn cập nhật trạng thái yêu cầu nhận thưởng?",
            icon: "warning",
            buttons: {
                cancel: {
                    text: "Hủy",
                    value: null,
                    visible: true,
                    className: "",
                    closeModal: true,
                },
                confirm: {
                    text: "Xác nhận",
                    value: true,
                    visible: true,
                    className: "",
                    closeModal: true,
                },
            },
        }).then((result) => {
            if (result) {
                // Gọi API cập nhật trạng thái
                $.ajax({
                    url: '@Url.Action("UpdateClaimStatus", "LuckyWheels")',
                    type: 'PUT',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        id: historyId,
                        status: newStatus
                    }),
                    success: function (response) {
                        $("#btn-update-status").prop("disabled", false).html('<i class="ri-save-line me-1"></i> Cập nhật trạng thái');

                        if (response.code === 0) {
                            swal({
                                icon: "success",
                                title: "Thành công",
                                text: "Cập nhật trạng thái thành công!",
                                button: "Đóng"
                            }).then(() => {
                                $("#historyDetailModal").modal('hide');
                                if (typeof table !== 'undefined') {
                                    table.ajax.reload();
                                }
                            });
                        } else {
                            swal({
                                icon: "error",
                                title: "Lỗi",
                                text: response.Message || "Có lỗi xảy ra khi cập nhật trạng thái",
                                button: "Đóng"
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        $("#btn-update-status").prop("disabled", false).html('<i class="ri-save-line me-1"></i> Cập nhật trạng thái');
                        console.error("Error updating claim status:", error);
                        swal({
                            icon: "error",
                            title: "Lỗi",
                            text: "Đã có lỗi xảy ra khi cập nhật dữ liệu",
                            button: "Đóng"
                        });
                    }
                });
            } else {
                // Người dùng bấm hủy
                $("#btn-update-status").prop("disabled", false).html('<i class="ri-save-line me-1"></i> Cập nhật trạng thái');
            }
        });
    }

    // Activate any tooltips in the modal
    $(function () {
        $('[data-bs-toggle="tooltip"]').tooltip();
    });
</script>