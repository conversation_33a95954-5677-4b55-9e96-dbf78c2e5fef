﻿using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.OmniTool;

namespace MiniAppCore.Controllers.API
{
    [Route("api/[controller]")]
    [ApiController]
    public class WebhooksController(ILogger<WebhooksController> logger, IUnitOfWork unitOfWork) : ControllerBase
    {
        [HttpPost("ProcessingOmniWehook")]
        public async Task<IActionResult> ProcessingOmniWebhook([FromBody] OmniWebhook webhookData)
        {
            try
            {
                var webhookLogRepo = unitOfWork.GetRepository<WebHookLogs>();
                webhookLogRepo.Add(new WebHookLogs()
                {
                    Status = webhookData.Status,
                    Channel = webhookData.Channel,
                    ErrorCode = webhookData.ErrorCode,
                    IdOmniMess = webhookData.IdOmniMess,

                    MtCount = webhookData.MtCount,
                    TelcoId = webhookData.TelcoId
                });
                await unitOfWork.SaveChangesAsync();
                return Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing Omni webhook");
                return Ok();
            }
        }
    }

    public class OmniWebhook
    {
        public string? Status { get; set; }
        public string? Channel { get; set; }
        public string? ErrorCode { get; set; }
        public string? IdOmniMess { get; set; }
        public int MtCount { get; set; }
        public int TelcoId { get; set; }
    }
}
