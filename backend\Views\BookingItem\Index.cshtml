﻿<style>
    .description {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 180px;
    }
</style>
<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON>h sách dịch vụ</h4>
                <p class="mb-0">
                    Tạo dịch vụ để khách hàng lựa chọn trong quá trình đặt lịch
                </p>
            </div>
            @if (User.IsInRole("ADMIN"))
            {
                <button type="button" class="btn btn-primary mt-2" onclick="GetFormBookingItem()">
                    <i class="ri-add-line mr-3"></i>Thêm mới
                </button>
            }
        </div>
    </div>

    <div class="col-lg-12">
        <div class="card mb-4">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i><PERSON><PERSON> lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="bookingItem-table" class="table-responsive rounded mb-3">
            <table id="list-bookingItem" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-bookingItem" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        let variantIndex = 0;
        let removedOldImages = [];
        let newImages = [];
        let currentImages = 0;
        let allProperties = null;

        const options = {
            dropdownParent: $("#modal-bookingItem")
        }

        $(document).ready(function () {
            GetListBookingItem();
            $('#search').on('input', search);
        });

        function ClearForm() {
            $("form").trigger("reset");
            ShowPreview();
        }

        function ShowPreview(event) {
            if (!event) return;

            const files = event.target.files;

            // Kiểm tra tất cả file phải là ảnh
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (!file.type.startsWith("image/")) {
                    AlertResponse("Chỉ được upload ảnh!", "warning");
                    event.target.value = ""; // Reset input
                    return;
                }
            }

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.readAsDataURL(file);
                    reader.onload = (e) => {
                        const previewItem = $(`
                        <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height: 170px;">
                            <img src="${e.target.result}" class="card-img-top h-100" alt="Hình ảnh" style="object-fit:contain" />
                            <span class="btn-preview-remove">x</span>
                        </div>`);

                        previewItem.find('.btn-preview-remove').click(function () {
                            $(this).parent().remove();
                            const currentFiles = Array.from(event.target.files);
                            const newFiles = currentFiles.filter(f => f !== file);
                        });

                        newImages.push(file);
                        $("#preview").append(previewItem);
                    };
                }
            }
        }

        $(document).on("click", ".btn-preview-remove", function () {
            const fullImageUrl = $(this).data("url");
            const imageUrl = fullImageUrl.split("/").pop();

            $(this).parent().remove();

            if (!removedOldImages.includes(imageUrl)) {
                removedOldImages.push(imageUrl);
            }
        });

        function InitialEditor() {
            window.editor = createQuillEditor("#description");
        }

        function GetListBookingItem() {
            table = new DataTable("#list-bookingItem", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const type = $("#filter-type").val();
                    const categoryId = $("#filter-category").val()?.join(",") || "";
                    const keyword = $("#search").val();
                    const branchId = $("#filter-branch").val();
                    const brandId = $("#filter-brand").val();

                    $.ajax({
                        url: '@Url.Action("GetPage", "BookingItems")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword,
                            type: type,
                            categoryId: categoryId,
                            branchId,
                            brandId
                        },
                        success: function (response) {
                            const stockStatus = {
                                "1": "Đang hoạt động",
                                "2": "Hết hàng",
                                "3": "Ngừng hoạt động"
                            }

                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: `<div class="d-flex align-items-center">
                                    <img src="${item.images ? item.images[0] : "/images/product/01.png"}" class="img-fluid rounded avatar-50 mr-3" alt="image" />
                                    <div>
                                        ${item.name}
                                    </div>
                                </div>`,
                                4: item.originalPrice.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
                                id: item.id,
                                reviewPoint: (item?.reviewPoint || 0) + '⭐',
                                likeCount: item?.likeCount || 0,
                                reviewCount: item?.reviewCount || 0,
                                boughtCount: item?.boughtCount || 0,
                                viewCount: item?.viewCount || 0,
                                status: stockStatus[item.status] ?? "-",
                                action: ``,
                                displayOrder: item.displayOrder,
                                performance: `
                                    <div class="text-start">
                                        <i class="ri-shopping-cart-line text-primary me-1"></i> <strong>${item.boughtCount}</strong> bán<br/>
                                        <i class="ri-star-line text-warning me-1"></i> <strong>${item.reviewPoint}</strong> (${item.reviewCount} đánh giá)
                                    </div>`,
                            }));

                            formattedData.forEach((item) => {
                                item[6] = `<div class="d-flex align-items-center justify-content-evenly list-action">
                                    <a onclick="GetFormBookingItem('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                        <i class="ri-edit-line fs-6 mr-0"></i>
                                    </a>
                                    <a onclick="DeleteBookingItem('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                        <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                    </a>
                                </div>`
                            });

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên dịch vụ", data: 1 },
                    { title: "Giá", data: 4 },
                    { title: "Mức độ quan tâm", data: "performance", className: 'text-center' },
                    // { title: "Lượt bán", data: "boughtCount", className: 'text-center' },
                    // { title: "Lượt đánh giá", data: "reviewCount", className: 'text-center' },
                    // { title: "Điểm đánh giá", data: "reviewPoint", className: 'text-center' },
                    { title: "Thứ tự hiển thị(mini app)", data: "displayOrder", className: 'text-center' },
                    { title: "Trạng thái", data: "status", className: 'text-center' },
                    { title: "Thao tác", data: 6 }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormBookingItem(id) {
            const url = id ? `@Url.Action("Detail", "BookingItem")/${id}` : "@Url.Action("Create", "BookingItem")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-bookingItem").modal("toggle");

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function DeleteBookingItem(id) {
            const url = `/api/BookingItems/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate_(id) {
            const formData = new FormData();

            formData.append("name", $("#name").val()?.trim());
            formData.append("type", "sanpham");
            formData.append("description", window.editor.root.innerHTML);
            formData.append("price", $("#price").val()?.trim());

            formData.append("status", $("#status").val());
            formData.append("likeCount", $("#likeCount").val());
            formData.append("boughtCount", $("#boughtCount").val());
            formData.append("reviewCount", $("#reviewCount").val());
            formData.append("reviewPoint", $("#reviewPoint").val());
            formData.append("displayOrder", $("#displayOrder").val());

            const files = $('#pics')[0].files;
            // if (removedOldImages.length === currentImages) {
            //     if (files.length === 0) {
            //         AlertResponse("Bạn cần ít nhất một hình ảnh!", 'warning');
            //         return;
            //     }
            // }

            if (!id) {
                if (files.length == 0) {
                    AlertResponse("Bạn vui lòng tải hình ảnh lên!", 'warning');
                    return;
                }

            }

            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            if (removedOldImages.length > 0) {
                for (const removedImage of removedOldImages) {
                    formData.append('removedOldImages', removedImage);
                }
            }

            if (hasDuplicate) return;

            const url = id ? `/api/bookingItems/${id}` : '/api/bookingItems';
            const method = id ? 'PUT' : 'POST'

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        removedOldImages = [];
                        newImages = [];
                        currentImages = 0;
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    }
                    $("#modal-bookingItem").modal("toggle");
                },
                error: function (err) {
                    AlertResponse("Đã xảy ra lỗi, vui lòng thử lại sau!", 'error')
                }
            });
        }

        function HandleSaveOrUpdate(id) {
            const formData = new FormData();
            const price = InputValidator.parseCurrency($("#price").val());
            const boughtCount = InputValidator.parseCurrency($("#boughtCount").val());
            const reviewCount = InputValidator.parseCurrency($("#reviewCount").val());
            const reviewPoint = InputValidator.parseCurrency($("#reviewPoint").val());
            const displayOrder = InputValidator.parseCurrency($("#displayOrder").val());

            formData.append("name", $("#name")?.val()?.trim());
            formData.append("description", window.editor.root.innerHTML);
            formData.append("price", price);
            formData.append("status", $("#status").val()?.trim());

            formData.append("boughtCount", boughtCount);
            formData.append("reviewCount", reviewCount);
            formData.append("reviewPoint", reviewPoint);
            formData.append("displayOrder", displayOrder);

            const files = $('#pics')[0].files;
            if ((removedOldImages.length === currentImages) && files.length === 0 && !id) {
                AlertResponse("Bạn cần ít nhất một hình ảnh!", 'warning');
                return;
            }

            for (let i = 0; i < files.length; i++) {
                formData.append("images", files[i]);
            }

            if (removedOldImages.length > 0) {
                removedOldImages.forEach(image => {
                    formData.append("removedOldImages", image);
                });
            }

            // 🛠️ Gửi AJAX request
            const url = id ? `/api/BookingItems/${id}` : '/api/BookingItems';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        removedOldImages = [];
                        newImages = [];
                        currentImages = 0;
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                    }
                    $("#modal-bookingItem").modal("toggle");
                },
                error: function () {
                    AlertResponse("Đã xảy ra lỗi, vui lòng thử lại sau!", "error");
                }
            });
        }

    </script>
}
