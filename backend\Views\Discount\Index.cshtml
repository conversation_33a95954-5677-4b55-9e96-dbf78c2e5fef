﻿﻿<style>
    .spinner {
        z-index: 0 !important;
    }
</style>

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Ch<PERSON>ơng trình giảm giá</h4>
                <p class="mb-0">
                    Sử dụng danh sách chương trình giảm giá để áp dụng giảm giá cho các sản phẩm
                </p>
            </div>
            <button onclick="GetFormDiscount('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-discount">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>
    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0; z-index: -1;"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-discounts" class="data-table table mb-0 tbl-server-info"> </table>
        </div>
    </div>
</div>

<div id="modal-discount" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListDiscount();

            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#search').on('input', search);
        });

        function GetListDiscount() {
            const status = {
                1: `Tỉ lệ phần trăm`,
                2: `Giảm giá trực tiếp`,
            };

            table = new DataTable("#list-discounts", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    // const type = $("#filter-type").val();
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetAll", "discounts")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword,
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: `<div class="d-flex align-items-center">
                                                <div>
                                                    ${item.name}
                                                    <p class="mb-0"><small>${truncateText(item.description, 100)}</small></p>
                                                </div>
                                            </div>`,
                                2: status[item.type],
                                3: item.type === 1 ? `${item.discountValue}%` : item.discountValue.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
                                4: item.maxDiscountAmount.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
                                5: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                                            <a onclick="GetFormDiscount('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                                                <i class="ri-edit-line fs-6 mr-0"></i>
                                                                            </a>
                                                                            <a onclick="DeleteDiscount('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                                                <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                                            </a>
                                                                        </div>`,
                                6: `<div>${FormatDateTime(item.startDate)} - ${FormatDateTime(item.expiryDate)}</div>`,
                                // 7: FormatDateTime(item.expiryDate),
                                8: item.isActive ? `<div class="m-auto circle-active"><div>` : `<div class="m-auto circle-inactive"></div>`,
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên", data: 1 },
                    { title: "Loại", data: 2, className: "text-center" },
                    { title: "Giá trị giảm", data: 3, className: 'text-center' },
                    { title: "Giảm tối đa", data: 4, className: 'text-center' },
                    { title: "Trạng thái", data: 8 },
                    // { title: "Ngày bắt đầu", data: 6 },
                    // { title: "Ngày hết hạn", data: 7 },
                    { title: "Thời gian áp dụng", data: 6, className: "text-center" },
                    { title: "Thao tác", data: 5 },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormDiscount(id) {
            const url = id ? `@Url.Action("Detail", "Discount")/${id}` : "@Url.Action("Create", "Discount")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-discount").modal("toggle");

                    $("#type").select2();

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function DeleteDiscount(id) {
            const url = `/api/discounts/${id}`
            DeleteItem(url);
        }

        function HandleInputDiscountType() {
            const type = parseInt($("#type").val());

            if (type === 1) {
                $("#maxDiscountGroup").show();
                $("#maxDiscountAmount").prop("disabled", false);
            } else {
                $("#maxDiscountGroup").hide();
                $("#maxDiscountAmount").prop("disabled", true);
            }
        }

        function HandleSaveOrUpdate(id) {
            const type = parseInt($("#type").val());
            // const discountValue = parseFloat($("#discountValue").val());
            // const maxDiscountAmount = parseFloat($("#maxDiscountAmount").val());

            const rawPrice = $("#maxDiscountAmount").val()?.replace(/\./g, "").trim(); // Bỏ dấu chấm
            const maxDiscountAmount = rawPrice ? parseInt(rawPrice, 10) : 0;

            const rawDiscountValue = $("#discountValue").val()?.replace(/\./g, "").trim(); // Bỏ dấu chấm
            const discountValue = rawDiscountValue ? parseInt(rawDiscountValue, 10) : 0;

            // tỷ lệ %
            if (type === 1 && (discountValue <= 0 || discountValue > 100)) {
                AlertResponse("Giá trị giảm theo phần trăm phải nằm trong khoảng 0 - 100!", "error");
                return;
            }

            const data = {
                id: id != "" ? id : null,
                name: $("#name").val()?.trim(),
                type: parseInt($("#type").val()),
                description: $("#descr").val()?.trim(),
                discountValue: discountValue,
                maxDiscountAmount: maxDiscountAmount,
                startDate: $("#startDate").val(),
                expiryDate: $("#expiryDate").val(),
                isActive: $("#isActive").val() === "true",
                products: $("#products").val()
            };

            const url = id != "" ? `/api/discounts/${id}` : '/api/discounts';
            const method = id != "" ? 'PUT' : 'POST'

            $.ajax({
                url: url,
                type: method,
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'error')
                    }
                    $("#modal-discount").modal("toggle");
                },
                error: function (err) {
                    AlertResponse('Thêm mới danh mục thất bại!', 'error')
                }
            });
        }
    </script>
}
