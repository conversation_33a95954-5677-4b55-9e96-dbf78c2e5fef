﻿<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>

    <div class="modal-body">
        <div class="form-group mb-3">
            <label for="template-select">Chọn mẫu hóa đơn</label>
            <select id="template-select" class="form-control" onchange="onChangeInvoiceTemplate()">
                @foreach (var template in ViewBag.Templates)
                {
                    <option value="@template.Id" selected="@(ViewBag.InvoiceTemplateId == template.Id)">
                        @template.Name @(template.IsDefault ? "(Mặc định)" : "")
                    </option>
                }
            </select>
        </div>

        <div id="invoice-content" class="border rounded p-3 bg-white @(ViewBag.InvoiceTemplateId == null ? "border-danger" : "")">
            @Html.Raw(ViewBag.Invoice)
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="button" class="btn btn-success" onclick="PrintInvoice()">
            <i class="ri-printer-line mr-1"></i> In hóa đơn
        </button>
    </div>
</div>

<script>
    function onChangeInvoiceTemplate() {
        const templateId = $("#template-select").val();
        const orderId = '@ViewBag.OrderId';

        $.get(`/Order/Invoice/${orderId}`, { templateId: templateId })
            .done(function (html) {
                const newContent = $("<div>").html(html).find("#invoice-content").html();
                $("#invoice-content").html(newContent);
            })
            .fail(function (jqXHR, textStatus, errorThrown) {
                console.error("Lỗi khi đổi mẫu hóa đơn:", textStatus, errorThrown);
            });
    }
</script>
