@using Newtonsoft.Json

<script>
    // =====================MODULE GỬI TIN========================
    function InitDataTemplate() {
        $("#data-template-ctn").html("");
    }

    function GetFormSendMessage() {
        // LoadingSpinner(true, "Đang tải...");
        $("#loadingModal").show();
        $("#modal-loading-content").html(`
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">G<PERSON>i tin nhắn đến khách hàng</h5>
                            <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>Loại tin nhắn</label>
                                <select id="notification-type" onchange="HandleChangeTypeNotification(this)" class="form-control">
                                    <option value="">Chọn loại tin nhắn</option>
                                    <option value="zns">Zalo ZNS</option>
                                    <option value="zca">Zalo ZCA</option>
                                </select>
                            </div>
                            <div id="template-container" class="form-group" hidden="true">
                                <label>Template</label>
                                <input id="templdate-id" class="form-control" placeholder="Nhập Template ID" oninput="HandleGetListTemplate(event)" />
                                <small>Gõ để tìm kiếm template</small>
                                <div id="template-suggestion" class="list-group mt-2" style="max-height: 200px; overflow-y: auto;"></div>
                            </div>
                            <div id="template-detail-container" hidden="true" class="form-group">
                                <label>Template chi tiết</label>
                                <div id="template-detail-data"></div>
                            </div>
                            <div id="data-template-container" hidden="true" class="form-group">
                                <label>Dữ liệu template</label>
                                <div id="data-template-ctn"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                            <button type="button" class="btn btn-primary" onclick="HandleSendData()">Gửi</button>
                        </div>
                    </div>
                </div>
            `);

        // Hide the spinner and show the modal after the content is loaded
        setTimeout(function () {
            $("#loadingModal").hide();
            $("#modal-noti").modal("show");
            $("#modal-content").html($("#modal-loading-content").html());
        }, 100);
    }

    function HandleChangeTypeNotification(that) {
        const type = $(that).val();
        if (type) {
            $("#template-container").attr("hidden", false);
        } else {
            $("#template-container").attr("hidden", true);
            $("#template-detail-container").attr("hidden", true);
            $("#data-template-container").attr("hidden", true);
        }
    }

    function HandleChangeDataParam(that) {
        const key = $(that).data('key');
        const value = $(that).val();
        console.log(`Parameter ${key} changed to:`, value);
    }

    function GetDetailTemplate(that) {
        const templateId = $(that).data('template-id');
        const templateText = $(that).data('template-text');

        $("#templdate-id").val(templateId);
        $("#template-suggestion").html("");

        // Display template details
        $("#template-detail-data").html(`
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Template ID: ${templateId}</h6>
                        <p class="card-text">${templateText}</p>
                    </div>
                </div>
            `);
        $("#template-detail-container").attr("hidden", false);

        HandleProcessDataTemplate();
    }

    function HandleProcessDataTemplate() {
        const templateText = $("#template-detail-data").find(".card-text").text();
        const params = templateText.match(/{{(.*?)}}/g);

        if (params && params.length > 0) {
            let paramHtml = '<div class="row">';
            params.forEach((param, index) => {
                const paramName = param.replace(/[{}]/g, '');
                paramHtml += `
                        <div class="col-md-6 mb-2">
                            <label class="form-label">${paramName}</label>
                            <input type="text" class="form-control" data-key="${paramName}" onchange="HandleChangeDataParam(this)" placeholder="Nhập giá trị cho ${paramName}">
                        </div>
                    `;
            });
            paramHtml += '</div>';

            $("#data-template-ctn").html(paramHtml);
            $("#data-template-container").attr("hidden", false);
        } else {
            $("#data-template-container").attr("hidden", true);
        }
    }

    function HandleGetListTemplate(event) {
        const searchTerm = event.target.value;
        if (searchTerm.length < 2) {
            $("#template-suggestion").html("");
            return;
        }

        // Mock data for demonstration
        const mockTemplates = [
            { id: "template1", text: "Xin chào {{name}}, cảm ơn bạn đã đăng ký thành viên!" },
            { id: "template2", text: "{{name}} ơi, bạn có {{points}} điểm tích lũy" },
            { id: "template3", text: "Chúc mừng {{name}} đã lên hạng {{rank}}" }
        ];

        const filteredTemplates = mockTemplates.filter(template =>
            template.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
            template.text.toLowerCase().includes(searchTerm.toLowerCase())
        );

        let suggestionHtml = "";
        filteredTemplates.forEach(template => {
            suggestionHtml += `
                    <button type="button" class="list-group-item list-group-item-action" 
                            data-template-id="${template.id}" 
                            data-template-text="${template.text}"
                            onclick="GetDetailTemplate(this)">
                        <strong>${template.id}</strong><br>
                        <small>${template.text}</small>
                    </button>
                `;
        });

        $("#template-suggestion").html(suggestionHtml);
    }

    function HandleSendData() {
        const type = $("#notification-type").val();
        const templateId = $("#templdate-id").val();

        if (!type || !templateId) {
            AlertResponse("Vui lòng chọn loại tin nhắn và template", "error");
            return;
        }

        const templateParams = {};
        $("#data-template-ctn input").each(function () {
            const key = $(this).data('key');
            const value = $(this).val();
            templateParams[key] = value;
        });

        console.log("Sending notification:", {
            type: type,
            templateId: templateId,
            params: templateParams
        });

        AlertResponse("Gửi tin nhắn thành công!", "success");
        $("#modal-noti").modal("hide");
    }
</script>
