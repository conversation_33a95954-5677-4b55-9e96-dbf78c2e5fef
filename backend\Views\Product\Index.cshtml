﻿﻿<style>
    .description {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 180px;
    }

     .badge-gold {
         background-color: #ffeb3b !important;
         color: #fff !important;
         transition: background-color 0.2s ease, color 0.2s ease;
     }

         .badge-gold:hover {
             background-color: #fdd835 !important;
         }
</style>

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> sách sản phẩm</h4>
                <p class="mb-0">
                    
                </p>
            </div>
            @if (User.IsInRole("ADMIN"))
            {
                <div>
                    <button type="button" class="btn btn-primary mt-2" onclick="GetFormProduct()">
                        <i class="ri-add-line mr-3"></i>Thêm mới
                    </button>

                    <button type="button" class="btn btn-success mt-2 ml-2" data-bs-toggle="modal" data-bs-target="#modal-import">
                        <i class="ri-file-excel-line mr-2"></i>Import từ Excel
                    </button>
                </div>
            }

            @if (User.IsInRole("EMPLOYEE"))
            {
                <button type="button" class="btn btn-primary mt-2" onclick="GetFormPullProduct()">
                    <i class="ri-add-line mr-3"></i>Thêm mới từ dữ liệu sẵn có
                </button>
            }
        </div>
    </div>

    @await Html.PartialAsync("Partials/_ProductFilters")

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="product-table" class="table-responsive rounded mb-3">
            <table id="list-product" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<!-- Product -->
<div id="modal-product" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<!-- Rating -->
<div id="ratingModal" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-rating-content" class="modal-dialog modal-lg modal-dialog-scrollable"></div>
</div>

<!-- Pull Product -->
<div id="modal-pullProduct" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-pullContent" class="modal-dialog modal-xl"></div>
</div>

<!-- Detail order -->
<div id="modal-order" class="modal fade" tabindex="-1" aria-modal="true" data-bs-backdrop="static" role="dialog" style="overflow-y: scroll">
    <div id="modal-order-content" class="modal-dialog modal-xl"></div>
</div>

<!-- Preview Media -->
<div class="modal fade" id="mediaPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content bg-dark text-white">
            <div class="modal-body text-center">
                <div id="mediaPreviewContent"></div>
            </div>
        </div>
    </div>
</div>

<partial name="~/Views/Product/Partials/_ImportModal.cshtml" />

@section Scripts {

    <partial name="~/Views/Product/Scripts/_ScriptFilter.cshtml" />
    <partial name="~/Views/Product/Scripts/_ScriptProductVariant.cshtml" />

    <script src="~/js/rating-modal.js"></script>

    <script>
        let variantIndex = 0;
        let removedOldImages = [];
        let newImages = [];
        let currentImages = 0;
        let allProperties = null;
        let oldVariantData = {};

        const options = {
            dropdownParent: $("#modal-product")
        }

        $(document).ready(function () {
            GetListProduct();
            $('#search').on('input', search);
        });

        function ClearForm() {
            $("form").trigger("reset");
            ShowPreview();
        }

        function ShowPreview(event) {
            if (!event) return;
            const files = event.target.files;

            // Kiểm tra tất cả file phải là ảnh
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (!file.type.startsWith("image/")) {
                    AlertResponse("Chỉ được upload ảnh!", "warning");
                    event.target.value = ""; // Reset input
                    return;
                }
            }

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.readAsDataURL(file);
                    reader.onload = (e) => {
                        const previewItem = $(`<div class="image-preview mx-2 card position-relative" style="max-width: 250px; height: 170px;">
                                    <img src="${e.target.result}" class="card-img-top h-100" alt="Hình ảnh" style="object-fit:contain" />
                                    <span class="btn-preview-remove">x</span>
                                </div>`);

                        previewItem.find('.btn-preview-remove').click(function () {
                            $(this).parent().remove();
                            const currentFiles = Array.from(event.target.files);
                            const newFiles = currentFiles.filter(f => f !== file);
                        });

                        newImages.push(file);
                        $("#preview").append(previewItem);
                    };
                }
            }
        }

        $(document).on("click", ".btn-preview-remove", function () {
            const fullImageUrl = $(this).data("url");
            const imageUrl = fullImageUrl.split("/").pop();

            $(this).parent().remove();

            if (!removedOldImages.includes(imageUrl)) {
                removedOldImages.push(imageUrl);
            }
        });

        function GetCategoryByType(selector, queryselector, selectedCategories, options) {
            $(selector).select2({
                placeholder: "Chọn danh mục",
                allowClear: true,
                // dropdownParent: options?.dropdownParent || null,
                ajax: {
                    url: '@Url.Action("GetPage", "Categories")',
                    type: 'GET',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            keyword: params.term,
                            page: params.page || 1,
                            pageSize: 30
                        };
                    },
                    processResults: function (res, params) {
                        let data = res.data || [];

                        if (!params.page || params.page === 1) {
                            // data.unshift({ id: "all", name: "Tất cả danh mục"});
                        }

                        return {
                            results: data.map(item => ({ id: item.id, text: item.name })),
                            pagination: { more: data.length >= 30 }
                        };
                    },
                    cache: true
                }
            });

            // Set giá trị mặc định nếu có danh mục đã chọn
            if (selectedCategories && selectedCategories.length > 0) {
                let selectedValues = selectedCategories.map(item => item.id); // Lấy danh sách ID
                let selectedData = selectedCategories.map(item => ({
                    id: item.id,
                    text: item.name
                }));

                // Thêm các tùy chọn đã chọn vào Select2 nếu chúng chưa có trong danh sách
                selectedData.forEach(item => {
                    let newOption = new Option(item.text, item.id, true, true);
                    $(selector).append(newOption);
                });

                // Gán giá trị đã chọn
                $(selector).val(selectedValues).trigger("change");
            }

                 // Lắng nghe sự kiện thay đổi danh mục load danh mục mục con
            $(selector).on("change", async function (e) {
                let selected = $(this).val(); // Danh sách ID đang chọn
                    let currentCards = $("#selectedCategoryChildContainer").children().map(function () {
                    return $(this).data("id");
                }).get();

                // Thêm thẻ mới nếu có thuộc tính mới được chọn
                for (let id of selected) {
                    if (!currentCards.includes(id)) {
                            await RenderCategoryChildCard(id);
                    }
                }

                // Xoá thẻ nếu thuộc tính bị bỏ chọn
                for (let id of currentCards) {
                    if (!selected.includes(id)) {
                            $(`#CategoryChild-card-${id}`).remove();
                    }
                }
            });
        }

        function RenderCategoryChildCard(categoryId, selectedValueIds = []) {
                const url = `/api/products/Category/${categoryId}`;

            $.ajax({
                url: url,
                method: 'GET',
                success: function (res) {
                    const listDt = res.data;
                        const category = res.category;
                    
                        const optionsHtml = listDt.map(opt => {
                        const selected = selectedValueIds.includes(opt.id) ? 'selected' : '';
                        return `<option value="${opt.id}" ${selected}>${opt.name}</option>`;
                    }).join("");
                    const cardHtml = `
                    <div class="col-md-6 mb-3" id="CategoryChild-card-${categoryId}" data-id="${categoryId}">
                        <div class="card">
                                <div class="card-header font-weight-bold">Danh mục con của ${category.name} :</div>
                            <div class="card-body">
                                                <select class="form-control CategoryChild-option-select" multiple id="CategoryChild-options-${categoryId}" data-name="${category.name}">
                                    ${optionsHtml}
                                </select>
                            </div>
                        </div>
                    </div>`;

                        $("#selectedCategoryChildContainer").append(cardHtml);

                            $(`#CategoryChild-options-${categoryId}`).select2({
                        placeholder: `Chọn danh mục con`
                    });

                        

                            $(`#CategoryChild-options-${categoryId}`).val(selectedValueIds).trigger("change");
                },
                error: function (xhr, status, error) {
                    console.error("Lỗi khi gọi GetProperty:", error);
                }
            });
        }


        function InitialEditor() {
            window.editor = createQuillEditor("#description", null, 3000, "#editor-counter");
        }

        function GetListProduct() {
            table = new DataTable("#list-product", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    // Get all filter values instead of individual selectors
                    const filters = getFilterValues();

                    const params = {
                        page: page,
                        pagesize: data.length,
                        sort: filters.sort || (data.order[0]?.dir || 'asc'),
                        keyword: filters.search,
                        type: $("#filter-type").val(),
                        categoryId: filters.category?.join(",") || "",
                        branchId: filters.branch?.join(",") || "",
                        brandId: filters.brand || "",
                        isGift: filters.gift !== "all" ? filters.gift : null,
                        minPrice: filters.minPrice,
                        maxPrice: filters.maxPrice,
                        supplierId: filters.supplier,
                        rating: filters.rating,
                        dateFrom: filters.dateFrom,
                        dateTo: filters.dateTo
                    }

                    if (filters.stock !== "0") {
                        params.stockStatus = filters.stock;
                    }

                    $.ajax({
                        url: '@Url.Action("GetPage", "Products")',
                        type: 'GET',
                        data: params,
                        success: function (response) {
                            const stockStatus = {
                                1: "Còn hàng",
                                2: "Hết hàng",
                                3: "Ngừng kinh doanh"
                            }

                            const maxNameLength = 120;

                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: `<div class="d-flex align-items-center">
                                        <img src="${item.images ? item.images[0] : "/images/product/01.png"}" class="img-fluid rounded avatar-50 mr-3" alt="image" />
                                        <div style="max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;" title="${item.name}">
                                            ${item.name.length > maxNameLength ? item.name.substring(0, maxNameLength) + "..." : item.name}
                                        </div>
                                    </div>`,
                                4: item.originalPrice.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
                                reviewPoint: (item?.reviewPoint || 0) + ' ⭐',
                                likeCount: item?.likeCount || 0,
                                reviewCount: item?.reviewCount || 0,
                                boughtCount: item?.boughtCount || 0,
                                viewCount: item?.viewCount || 0,
                                performance: `
                                    <div class="text-start">
                                        <i class="ri-shopping-cart-line text-primary me-1"></i> <strong>${item.boughtCount}</strong> bán<br/>
                                        <i class="ri-star-line text-warning me-1"></i> <strong>${item.reviewPoint}</strong> (${item.reviewCount} đánh giá)
                                    </div>`,
                                id: item.id,
                                status: stockStatus[item.status] ?? "-",
                                isGift: item.isGift ? "Quà tặng" : "Sản phẩm",
                                displayOrder: item.displayOrder,
                            }));

                            formattedData.forEach((item) => {
                                item[6] = `<div class="d-flex align-items-center justify-content-evenly list-action">
                                    <a onclick="GetFormProduct('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                        <i class="ri-edit-line fs-6"></i>
                                    </a>
                                    <a onclick="DeleteProduct('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                        <i class="ri-delete-bin-line fs-6"></i>
                                    </a>
                                    <a onclick="ViewRating('${item.id}')" class="badge badge-gold" data-toggle="tooltip" data-placement="top" title="Xem đánh giá">
                                        <i class="ri-star-line fs-6"></i>
                                    </a>
                                </div>`
                            });

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên sản phẩm", data: 1 },
                    // { title: "Danh mục", data: 7, className: 'text-center' },
                    { title: "Giá", data: 4 },
                    // { title: "Lượt bán", data: "boughtCount", className: 'text-center' },
                    // { title: "Lượt đánh giá", data: "reviewCount", className: 'text-center' },
                    // { title: "Điểm đánh giá", data: "reviewPoint", className: 'text-center' },
                    { title: "Mức độ quan tâm", data: "performance", className: 'text-center' },
                    { title: "Loại", data: "isGift", width: '10%', className: 'text-center' },
                    { title: "Thứ tự (mini app)", data: "displayOrder", className: 'text-center' },
                    { title: "Trạng thái", data: "status", className: 'text-center' },
                    { title: "Thao tác", data: 6 }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormProduct(id) {
            const url = id ? `@Url.Action("Detail", "Product")/${id}` : "@Url.Action("Create", "Product")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-product").modal("toggle");

                    if (!id) {
                        $("#categories").select2(options);

                        InitValidator();
                    }
                }
            })
        }

        function DeleteProduct(id) {
            const url = `/api/products/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const formData = new FormData();

            // Loading style tránh spam khi request lâu
            setProductSaveButtonLoading(true);

            formData.append("name", $("#name").val()?.trim());
            formData.append("type", "sanpham");
            formData.append("description", window.editor.root.innerHTML);
            formData.append("brandId", $("#brandId").val());
            formData.append("status", $("#stockStatus").val()?.trim());

            const numericPrice = InputValidator.parseCurrency($("#price").val());

            formData.append("price", numericPrice);

            var selectedCategories = $("#categories").val();
            if (selectedCategories && selectedCategories.length > 0) {
                selectedCategories.forEach(categoryId => {
                    formData.append("categoryIds", categoryId);
                });
            } else {
                AlertResponse("Bạn phải chọn ít nhất một danh mục!", "warning");
                    setProductSaveButtonLoading(false);
                return;
            }
            const categoryElements = document.querySelectorAll("[id^='CategoryChild-options-']");
            //Xử lý danh mục con
            
            categoryElements.forEach(element => {                               
                const categoryChildIds = $(`#CategoryChild-options-${element.id.split('-')[2]}`).val();
                if (categoryChildIds && categoryChildIds.length > 0) {
                    categoryChildIds.map(item =>{
                        formData.append("CategoryChildIds", item);
                    })
                }
                    
            });

            const boughtCount = InputValidator.parseCurrency($("#boughtCount").val());
            const reviewCount = InputValidator.parseCurrency($("#reviewCount").val());
            const reviewPoint = InputValidator.parseCurrency($("#reviewPoint").val());
            const displayOrder = InputValidator.parseCurrency($("#displayOrder").val());
                    
            formData.append("boughtCount", boughtCount);
            formData.append("reviewCount", reviewCount);
            formData.append("reviewPoint", reviewPoint);
            formData.append("displayOrder", displayOrder);

            // 🛠️ Xử lý thuộc tính sản phẩm (properties)
            let selectedProperties = [];
            $(".property-options").each(function () {
                let propertyId = $(this).closest(".property-group").data("property-id");
                let selectedOptionIds = $(this).val();

                if (selectedOptionIds && selectedOptionIds.length > 0) {
                    selectedProperties.push({ propertyId: propertyId, options: selectedOptionIds });
                }
            });
            formData.append("properties", JSON.stringify(selectedProperties));

            // 🖼️ Xử lý hình ảnh sản phẩm
            const files = $('#pics')[0].files;
            if ((removedOldImages.length === currentImages) && files.length === 0) {
                AlertResponse("Bạn cần ít nhất một hình ảnh!", 'warning');
                    setProductSaveButtonLoading(false);
                return;
            }

            for (let i = 0; i < files.length; i++) {
                formData.append("images", files[i]);
            }

            if (removedOldImages.length > 0) {
                removedOldImages.forEach(image => {
                    formData.append("removedOldImages", image);
                });
            }

            formData.append("isGift", $("#isGift").val() === "true");

            // --- Biến thể ---
            const variants = [];
            const variantJson = [];
            let isValid = true;
            $("#variantContainer .card").each(function () {
                const card = $(this);
                const key = card.data("variant-key");
                const priceRaw = card.find(".variant-price").val();
                const price = parseCurrency(priceRaw);
                const stock = card.find(".variant-stock").val();
                const variantId = card.data("variant-id") || null;

                if (!price || !stock) {
                    isValid = false;
                    return false;
                }

                const optionIds = key.split("-");
                const properties = [];

                optionIds.forEach((valId, idx) => {
                    // formData.append(`variants[${variants.length + idx}].propertyValueId`, valId);
                    // Optionally include PropertyId if backend expects it
                    properties.push({
                        propertyId: "",
                        propertyValueId: valId
                    });
                });

                // formData.append(`variants[${variants.length}].price`, price);
                // formData.append(`variants[${variants.length}].status`, $("#stockStatus").val()); // Default same status

                variants.push(true); // just to track index
                variantJson.push({
                    variantId: variantId, // || key,
                    price: price,
                    quantity: 10,
                    status: stock,
                    properties: properties
                })
            });

            formData.append("variantsJson", JSON.stringify(variantJson));

            const branchIds = $("#branchId").val();

            if (branchIds && branchIds.length > 0) {
                branchIds.forEach(id => formData.append("branchIds", id));
                formData.append("IsGlobalProduct", false);
            } else {
                formData.append("IsGlobalProduct", true);
            }

            if (!isValid) {
                AlertResponse("Mỗi biến thể cần nhập đủ giá và trạng thái sản phẩm!", "warning");
                    setProductSaveButtonLoading(false);
                return;
            }

            // 🛠️ Gửi AJAX request
            const url = id ? `/api/products/${id}` : '/api/products';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        removedOldImages = [];
                        newImages = [];
                        currentImages = 0;
                        oldVariantData = {};
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'error');
                        table.ajax.reload(null, false);
                    }
                    $("#modal-product").modal("toggle");
                },
                error: function () {
                    AlertResponse("Đã xảy ra lỗi, vui lòng thử lại sau!", "error");
                },
                complete: function () {
                        setProductSaveButtonLoading(false);
                }
            });
        }

        function importProducts() {
            const fileInput = $("#import-file")[0];

            // Validate file selection
            if (!fileInput.files || fileInput.files.length === 0) {
                $("#import-file").addClass("is-invalid");
                return;
            }

            const file = fileInput.files[0];
            const fileExt = file.name.split('.').pop().toLowerCase();

            // Validate file type
            if (fileExt !== "xlsx" && fileExt !== "xls") {
                $("#import-file").addClass("is-invalid");
                return;
            }

            // Show loading indicator
            $("#import-spinner").removeClass("d-none");
            $("#btn-import").attr("disabled", true);

            const formData = new FormData();
            formData.append("file", file);

            // Send import request
            $.ajax({
                url: '/api/products/Import',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse("Import sản phẩm thành công!", "success");
                        table.ajax.reload(null, false);
                        $("#modal-import").modal("hide");
                    } else {
                        AlertResponse(response.message || "Có lỗi xảy ra khi import!", "error");
                    }
                },
                error: function (xhr) {
                    const errorMsg = xhr.responseJSON?.message || "Có lỗi xảy ra khi import!";
                    AlertResponse(errorMsg, "error");
                },
                complete: function () {
                    // Hide loading indicator
                    $("#import-spinner").addClass("d-none");
                    $("#btn-import").attr("disabled", false);
                }
            });
        }

        /**
         * Handle Excel export functionality for product listings
         */
        $(document).ready(function () {
            // Attach click handler to the export button
            $("#btn-export-excel").on("click", function () {
                exportProductsToExcel();
            });
        });

        /**
         * Export filtered products to Excel file
         */
    function exportProductsToExcel() {
        // Show loading state
        const exportBtn = $("#btn-export-excel");
        const originalBtnText = exportBtn.html();
        exportBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xuất...');
        exportBtn.prop("disabled", true);
        
        // Get all current filter values
        const filters = getFilterValues();
        
        // Build query parameters object
        const params = {};
        
        if (filters.search) params.Keyword = filters.search;
        if (filters.category?.length) params.CategoryId = filters.category.join(",");
        if (filters.branch?.length) params.BranchId = filters.branch.join(",");
        if (filters.stock !== "0") params.StockStatus = filters.stock;
        if (filters.gift !== "all") params.IsGift = filters.gift;
        if (filters.minPrice) params.MinPrice = filters.minPrice;
        if (filters.maxPrice) params.MaxPrice = filters.maxPrice;
        if (filters.supplier) params.SupplierId = filters.supplier;
        if (filters.brand) params.BrandId = filters.brand;
        
        $.ajax({
            url: '/api/Products/Export',
            method: 'GET',
            data: params,
            xhrFields: {
                responseType: 'blob' // Important for binary data like Excel files
            },
            success: function(data, status, xhr) {
                // Get the filename from the Content-Disposition header if available
                let filename = "Products_Export.xlsx";
                const disposition = xhr.getResponseHeader('Content-Disposition');
                
                if (disposition && disposition.indexOf('attachment') !== -1) {
                    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                    const matches = filenameRegex.exec(disposition);
                    if (matches != null && matches[1]) {
                        filename = matches[1].replace(/['"]/g, '');
                    }
                }
                
                // Create blob and download
                const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(blob);
                link.download = filename;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(link.href);
                
                // Show success notification
                AlertResponse("Xuất Excel thành công!", "success");
            },
            error: function(xhr, status, error) {
                console.error("Export error:", xhr);
                try {
                    // Try to parse error response
                    const response = JSON.parse(xhr.responseText);
                    AlertResponse(response.Message || "Có lỗi xảy ra khi xuất dữ liệu", "error");
                } catch(e) {
                    AlertResponse("Có lỗi xảy ra khi xuất dữ liệu", "error");
                }
            },
            complete: function() {
                // Reset button state
                exportBtn.html(originalBtnText);
                exportBtn.prop("disabled", false);
            }
        });
    }

        function setProductSaveButtonLoading(isLoading, loadingText = 'Đang lưu...') {
            const btn = document.getElementById('btn-save-product');
            if (!btn) return;
            const spinner = document.getElementById('btn-save-product-spinner');
            const btnText = btn.querySelector('.btn-save-product-text');
            const originalText = btnText ? btnText.dataset.buttonText : '';

            if (isLoading) {
                btn.disabled = true;
                if (spinner) spinner.classList.remove('d-none');
                if (btnText) btnText.textContent = loadingText;
            } else {
                btn.disabled = false;
                if (spinner) spinner.classList.add('d-none');
                if (btnText) btnText.textContent = originalText;
            }
        }
    </script>

}
 