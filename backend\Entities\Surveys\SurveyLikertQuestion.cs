﻿using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Surveys
{
    public class SurveyLikertQuestion : BaseEntity
    {
        [MaxLength(36)]
        public required string SurveyQuestionId { get; set; }
        public string? QuestionLikertTitle { get; set; }
        public short OptionCount { get; set; }
        public short DisplayOrder { get; set; }
    }
}
