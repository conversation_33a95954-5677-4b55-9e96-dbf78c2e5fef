﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Carts;
using MiniAppCore.Models.Responses.Orders.Carts;

namespace MiniAppCore.Services.Orders.Carts
{
    public interface ICartService : IService<Cart>
    {
        Task<long> GetTotalQuantity(string userId);
        Task<CartResponse> GetUserCart(RequestQuery query, string userId, string? branchId);
        Task<List<CartItemResponse>> GetCartItemsResponse(List<Cart> cartItems);

        Task<IEnumerable<Cart>> AddItems(string userId, List<CartItemRequest> requests);
        Task<Cart> AddItem(string userId, CartItemRequest request, bool increaseQuantity = true);
        Task<Cart> UpdateItem(string cartItemId, CartItemRequest request);

        Task ClearCart(string userId);
        Task ClearCart(string userId, string? branchId);

        Task RemoveItem(string cartItemId);
        Task RemoveItems(List<string> cartItemIds);
        Task SyncCart(string guestId, string userId);
    }
}