﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableETM : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "eed9801da536467d9e6949c2a0f4fd2c");

            migrationBuilder.AddColumn<string>(
                name: "ReferenceId",
                table: "EventTemplates",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "EventTemplates",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "EventTemplateLogs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Message = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Recipient = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ResultCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RequestBody = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ResponseBody = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Metadata = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EventTemplateLogs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OmniTemplateConfigs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    RoutingRule = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TemplateCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TemplateMapping = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OmniTemplateConfigs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ZaloTemplateConfigs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Recipients = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TemplateId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TemplateMapping = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZaloTemplateConfigs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ZaloTemplateUids",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Message = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ListParams = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ZaloTemplateUids", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOd1kT4s26lc7NYWgIy8l6alPkzTIe3YRUPVUdWpTSUxb59BDCN3qiZErozaV6kIsQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "2102a48e05014dd3aa77d583570386bb", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 15, 15, 48, 14, 510, DateTimeKind.Local).AddTicks(8453), "FeaturesButton", new DateTime(2025, 4, 15, 15, 48, 14, 511, DateTimeKind.Local).AddTicks(2698) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "EventTemplateLogs");

            migrationBuilder.DropTable(
                name: "OmniTemplateConfigs");

            migrationBuilder.DropTable(
                name: "ZaloTemplateConfigs");

            migrationBuilder.DropTable(
                name: "ZaloTemplateUids");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "2102a48e05014dd3aa77d583570386bb");

            migrationBuilder.DropColumn(
                name: "ReferenceId",
                table: "EventTemplates");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "EventTemplates");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAELxMgkgVaFTMIgUEnjdibXyCAH2R7bHIeLB+lVLeG7POV+exosuexMvnV2r9Q4BHRQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "eed9801da536467d9e6949c2a0f4fd2c", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 14, 17, 29, 10, 825, DateTimeKind.Local).AddTicks(823), "FeaturesButton", new DateTime(2025, 4, 14, 17, 29, 10, 826, DateTimeKind.Local).AddTicks(274) });
        }
    }
}
