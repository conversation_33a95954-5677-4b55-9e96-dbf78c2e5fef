﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddButtonTextToCustomForm : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "cd28160aa17040cdbeb5ef8cace487c3");

            migrationBuilder.AddColumn<string>(
                name: "ButtonText",
                table: "CustomForms",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEBljDL0tIlpD+zVnINjFKkspALlRYFJdPmfN4S9MXvZO/Py+zS/9mYm8cAXALdGuSQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "9d3ece5a3ed541ebab2c192e20c4b9b6", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 17, 14, 54, 38, 584, DateTimeKind.Local).AddTicks(2678), "FeaturesButton", new DateTime(2025, 4, 17, 14, 54, 38, 584, DateTimeKind.Local).AddTicks(7109) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "9d3ece5a3ed541ebab2c192e20c4b9b6");

            migrationBuilder.DropColumn(
                name: "ButtonText",
                table: "CustomForms");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEEt8PfFo8+q7C44PEUmm7Fsv6pd02XpRWvBshKcXWWT8GlWEyTa5s7Ye1fPP36uAdg==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "cd28160aa17040cdbeb5ef8cace487c3", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 17, 11, 41, 14, 511, DateTimeKind.Local).AddTicks(3473), "FeaturesButton", new DateTime(2025, 4, 17, 11, 41, 14, 511, DateTimeKind.Local).AddTicks(8726) });
        }
    }
}
