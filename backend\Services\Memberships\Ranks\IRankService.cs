﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Settings;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Ranks;
using MiniAppCore.Models.Responses.Memberships;
using MiniAppCore.Models.Responses.Ranks;

namespace MiniAppCore.Services.Memberships.Ranks
{
    public interface IRankService : IService<Rank>
    {
        Task<Rank?> GetRankDefault();
        Task<int> CreateAsync(RankRequest request);
        Task<int> UpdateAsync(string id, RankRequest model);
        Task<PagedResult<RankResponse>> GetPage(RequestQuery query);
        Task<MembershipRankResponse?> GetMembershipRankResponseAsync(string rankId, long currentPoints);
        Task<int> UpdateMembershipRank(string userZaloId);
    }
}
