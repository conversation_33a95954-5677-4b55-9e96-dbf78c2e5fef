﻿@model string

@{
    var typeLabel = Model switch
    {
        "paragraph" => "Đoạn văn",
        "multiChoice" => "Nhiều lựa chọn",
        "singleChoice" => "Một lựa chọn",
        "likert" => "Thang đo Likert",
        "dropDown" => "Dropdown",
        "date" => "Ngày",
        "time" => "Thời gian",
        _ => Model
    };

    var typeIcon = Model switch
    {
        "paragraph" => "ri-text",
        "multiChoice" => "ri-checkbox-multiple-line",
        "singleChoice" => "ri-radio-button-line",
        "likert" => "ri-scales-line",
        "dropDown" => "ri-list-settings-line",
        "date" => "ri-calendar-line",
        "time" => "ri-time-line",
        _ => "ri-question-line"
    };
}

<span class="badge bg-info question-badge mr-2" title="@typeLabel">
    <i class="@typeIcon me-1"></i> @typeLabel
</span>
