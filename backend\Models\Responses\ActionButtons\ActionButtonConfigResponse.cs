namespace MiniAppCore.Models.Responses.ActionButtons
{
    public class ActionButtonConfigResponse
    {
        public List<ButtonItemResponse> ListShortcut { get; set; } = new();
        public List<ButtonItemResponse> ListButton { get; set; } = new();
        public List<ButtonItemResponse> ListFloatingBubble { get; set; } = new();
    }

    public class ButtonItemResponse
    {
        public string Title { get; set; } = string.Empty;
        public string Image { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Desc { get; set; } = string.Empty;
    }
}