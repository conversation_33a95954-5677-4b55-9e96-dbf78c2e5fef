﻿@model MiniAppCore.Entities.Events.SponsorshipTiers.SponsorshipTier;

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Đóng"></button>
    </div>

    <div class="modal-body">
        <input type="hidden" id="id" value="@Model.Id" />

        <div class="row g-3">
            <!-- Tên hạng tài trợ -->
            <div class="col-md-8">
                <label for="tierName" class="form-label fw-semibold">Tên hạng tài trợ <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="tierName" value="@Model.TierName" placeholder="Nhập tên hạng tài trợ" />
            </div>

            <!-- Trạng thái -->
            <div class="col-md-4">
                <label for="isActive" class="form-label fw-semibold">Trạng thái hoạt động</label>
                <select id="isActive" class="selectpicker form-control" required>
                    @{
                        if (Model.IsActive == true)
                        {
                            <option value="true" selected>Đang hoạt động</option>
                            <option value="false">Ngưng hoạt động</option>
                        }
                        else if (Model.IsActive == false)
                        {
                            <option value="true">Đang hoạt động</option>
                            <option value="false" selected>Ngưng hoạt động</option>
                        }
                    }
                </select>
            </div>

            <!-- Mô tả -->
            <div class="col-md-12">
                <div class="form-group">
                    <label>Mô tả sản phẩm</label>
                    <div id="description" class="rounded-bottom" style="height: 200px" data-type="content">@Html.Raw(Model.Description)</div>
                </div>
            </div>

            <!-- Hình ảnh -->
            <div class="col-md-12">
                <label class="form-label fw-semibold">Hình ảnh</label>
                <input type="file" id="image" class="form-control" accept="image/*" onchange="previewImage(this)" />
                <div class="mt-3" id="preview-image">
                    @if (!string.IsNullOrEmpty(ViewBag.Image as string))
                    {
                        <div class="image-preview position-relative d-inline-block">
                            <span class="btn-preview-remove" onclick="removePreviewImage()">x</span>
                            <img src="@ViewBag.Image" class="rounded border" style="height: 120px; width: 120px; object-fit: cover;" />
                        </div>
                    }
                </div>
            </div>


        </div>
    </div>

    <div class="modal-footer mt-3">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="button" class="btn btn-primary" onclick="HandleSaveOrUpdate($('#id').val())">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        InitialEditor('#description');
    });
</script>
