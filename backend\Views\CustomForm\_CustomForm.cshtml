﻿@model MiniAppCore.Models.Responses.Memberships.FormCustomResponse
@{
    var typeList = new Dictionary<string, string>() { { "text", "Chữ" }, { "date", "<PERSON><PERSON><PERSON> tháng" }, { "number", "<PERSON>ố" }, { "option", "<PERSON>ự<PERSON> chọn" } };
}

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body" style="@(User.IsInRole("EMPLOYEE") ? "pointer-events: none;" : "")">
        <div class="card-body">
            <form id="customFormForm" method="post">

                <!--Trạng thái hoạt động-->
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label">Trạng thái</label>
                    <div class="col-sm-9">
                        <select class="form-select" name="IsActive">
                            <option value="true" selected="@(Model.IsActive)">Hoạt động</option>
                            <option value="false" selected="@(!Model.IsActive)">Không hoạt động</option>
                        </select>
                    </div>
                </div>

                <!--Tên form-->
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label">Tên form <span class="text-danger">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" name="FormName" value="@Model?.FormName" required>
                    </div>
                </div>

                <!--Tiêu đề form-->
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label">Tiêu đề <span class="text-danger">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" name="FormTitle" value="@Model?.FormTitle" required>
                    </div>
                </div>

                <!--Tên chiến dịch-->
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label">Tên chiến dịch</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" name="CampaignName" value="@Model?.CampaignName">
                    </div>
                </div>

                <!--Tên nút submit-->
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label">Tên nút submit</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" name="ButtonText" value="@(Model?.SubmitButtonText ?? "Đăng ký")">
                    </div>
                </div>

                <!--Màu sắc nút đăng kí-->
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label">Màu nút đăng ký</label>
                    <div class="col-sm-9">
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="SubmitButtonColorPicker" value="@(Model?.SubmitButtonColor ?? "#007bff")">
                            <input type="text" class="form-control" name="SubmitButtonColor" id="SubmitButtonColor" value="@Model?.SubmitButtonColor">
                        </div>
                    </div>
                </div>

                <!--Màu sắc chữ-->
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label">Màu chữ bênh cạnh checkbox</label>
                    <div class="col-sm-9">
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="CheckboxTextColorPicker" value="@(Model?.CheckboxTextColor ?? "#000000")">
                            <input type="text" class="form-control" name="CheckboxTextColor" id="CheckboxTextColor" value="@Model?.CheckboxTextColor">
                        </div>
                    </div>
                </div>

                <!--Màu nền form-->
                <div class="form-group row mb-3">
                    <label class="col-sm-3 col-form-label">Màu nền form</label>
                    <div class="col-sm-9">
                        <div class="input-group">
                            <input type="color" class="form-control form-control-color" id="FormBackgroundColorPicker" value="@(Model?.FormBackgroundColor ?? "#ffffff")">
                            <input type="text" class="form-control" name="FormBackgroundColor" id="FormBackgroundColor" value="@Model?.FormBackgroundColor">
                        </div>
                    </div>
                </div>

                <hr />
                <h4>Trường thông tin</h4>

                <div id="attributesContainer">

                    <div class="attribute-template d-none">
                        <div class="attribute-item card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Thông tin trường</h5>
                                <button type="button" class="btn btn-sm btn-danger remove-attribute">
                                    <i class="ri-delete-bin-line fs-6"></i> Xóa
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <label class="form-label">Trạng thái</label>
                                        <select class="form-control" name="InputFields[${attributeIndex}].IsActive">
                                            <option value="true" selected>Hoạt động</option>
                                            <option value="false">Không hoạt động</option>
                                        </select>
                                    </div>

                                    <div class="col-md-4">
                                        <label class="form-label">Kiểu thuộc tính <span class="text-danger">*</span></label>
                                        <select class="form-control type-selector" name="InputFields[${attributeIndex}].Type" required onblur="validateField(this)">
                                            @foreach (var type in typeList)
                                            {
                                                <option value="@type.Key">@type.Value</option>
                                            }
                                        </select>
                                    </div>

                                    <div class="col-md-4">
                                        <label class="form-label">Thứ tự hiển thị</label>
                                        <input type="number" class="form-control display-order-input" name="InputFields[${attributeIndex}].DisplayOrder" onblur="validateNumberField(this, 0)">
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">Key thuộc tính <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="InputFields[${attributeIndex}].Attribute" required onblur="validateField(this)">
                                    </div>

                                    <div class="col-md-6">
                                        <label class="form-label">Label <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="InputFields[${attributeIndex}].AttributeName" required onblur="validateField(this)">
                                    </div>

                                    <div class="col-md-6 min-container">
                                        <label class="form-label min-label">Giá trị tối thiểu</label>
                                        <input type="number" class="form-control attribute-min" name="InputFields[${attributeIndex}].Min" value="0" onblur="validateNumberField(this, 0)">
                                    </div>

                                    <div class="col-md-6 max-container">
                                        <label class="form-label max-label">Giá trị tối đa</label>
                                        <input type="number" class="form-control attribute-max" name="InputFields[${attributeIndex}].Max" value="0" onblur="validateNumberField(this, 0)">
                                    </div>

                                    <div class="col-md-12 content-container" style="display:none;">
                                        <label class="form-label content-label">Giá trị</label>
                                        <input type="text" class="form-control" name="InputFields[${attributeIndex}].Value" onblur="validateField(this)">
                                        <div class="form-text content-help">Với kiểu Lựa chọn, nhập các giá trị cách nhau bởi dấu phẩy (,)</div>
                                    </div>

                                    <div class="col-12 no-properties" style="display: none;">
                                        <div class="alert alert-info mb-0">Kiểu ngày không có thuộc tính phụ</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if (Model?.InputFields != null && Model.InputFields.Any())
                    {
                        @for (var i = 0; i < Model.InputFields.Count; i++)
                        {
                            <div class="attribute-item card mb-3">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">Thông tin trường</h5>
                                    <button type="button" class="btn btn-sm btn-danger remove-attribute">
                                        <i class="ri-delete-bin-line fs-6"></i> Xóa
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label class="form-label">Trạng thái</label>
                                            <select class="form-select" name="InputFields[@i].IsActive">
                                                <option value="true" selected="@(Model.InputFields[i].IsActive)">Hoạt động</option>
                                                <option value="false" selected="@(!Model.InputFields[i].IsActive)">Không hoạt động</option>
                                            </select>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="form-label">Kiểu thuộc tính <span class="text-danger">*</span></label>
                                            <select class="form-select type-selector" name="InputFields[@i].Type" required>
                                                @foreach (var type in typeList)
                                                {
                                                    <option value="@type.Key" selected="@(Model.InputFields[i].Type == type.Key)">@type.Value</option>
                                                }
                                            </select>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="form-label">Thứ tự hiển thị</label>
                                            <input type="number" class="form-control" name="InputFields[@i].DisplayOrder" value="@Model.InputFields[i].DisplayOrder">
                                        </div>

                                        <div class="col-md-6">
                                            <label class="form-label">Key thuộc tính <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="InputFields[@i].Attribute" value="@Model.InputFields[i].Attribute" required>
                                        </div>

                                        <div class="col-md-6">
                                            <label class="form-label">Label <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="InputFields[@i].AttributeName" value="@Model.InputFields[i].AttributeName" required>
                                        </div>

                                        <div class="col-md-6 min-container">
                                            <label class="form-label min-label">Giá trị tối thiểu</label>
                                            <input type="number" class="form-control attribute-min" name="InputFields[@i].Min" value="@Model.InputFields[i].Min">
                                        </div>

                                        <div class="col-md-6 max-container">
                                            <label class="form-label max-label">Giá trị tối đa</label>
                                            <input type="number" class="form-control attribute-max" name="InputFields[@i].Max" value="@Model.InputFields[i].Max">
                                        </div>

                                        <div class="col-md-6 content-container">
                                            <label class="form-label content-label">Giá trị</label>
                                            <input type="text" class="form-control" name="InputFields[@i].Value" value="@string.Join(", ", Model.InputFields[i].Options.Select(x => x.Key))">
                                            <div class="form-text content-help">Với kiểu Lựa chọn, nhập các giá trị cách nhau bởi dấu phẩy (,)</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    }
                </div>

                <div class="mb-3">
                    <button type="button" id="addAttribute" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Thêm trường thông tin
                    </button>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (User.IsInRole("ADMIN"))
        {
            <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
            <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.FormId')">@ViewBag.Button</button>
        }
    </div>
</div>

<script>
    $(document).ready(function () {
        let attributeIndex = @(Model?.InputFields?.Count ?? 0);

        // Khởi tạo hiển thị các trường dựa vào kiểu
        $('.attribute-item').each(function () {
            const type = $(this).find('.type-selector').val();
            updateAttributeFieldsVisibility($(this), type);
        });

        // Xử lý sự kiện thay đổi kiểu thuộc tính
        $(document).on('change', '.type-selector', function () {
            const $card = $(this).closest('.attribute-item');
            const type = $(this).val();
            updateAttributeFieldsVisibility($card, type);
        });

        // Thêm thuộc tính mới
        $("#addAttribute").click(function () {
            const template = $(".attribute-template").html().replace(/\${attributeIndex}/g, attributeIndex);
            $("#attributesContainer").append(template);

            // Khởi tạo hiển thị dựa vào kiểu mặc định
            const $newCard = $("#attributesContainer .attribute-item").last();

            // Set the default value for display order programmatically
            $newCard.find('.display-order-input').val(attributeIndex);

            const type = $newCard.find('.type-selector').val();
            updateAttributeFieldsVisibility($newCard, type);

            attributeIndex++;
        });

        // Xóa thuộc tính
        $(document).on('click', '.remove-attribute', function () {
            $(this).closest('.attribute-item').remove();
            reindexAttributes();
        });

        // Đánh lại chỉ số cho các thuộc tính sau khi xóa
        function reindexAttributes() {
            $('.attribute-item').each(function (index) {
                const item = $(this);
                item.find('input, select').each(function () {
                    const name = $(this).attr('name');
                    if (name) {
                        $(this).attr('name', name.replace(/InputFields\[\d+\]/g, `InputFields[${index}]`));
                    }
                });
            });
            attributeIndex = $('.attribute-item').length;
        }

        // Cập nhật hiển thị trường dựa vào kiểu thuộc tính
        function updateAttributeFieldsVisibility($card, type) {
            // Ẩn trước
            $card.find('.min-container, .max-container, .content-container, .no-properties').hide();

            switch (type) {
                case 'text':
                    $card.find('.min-label').text('Kí tự tối thiểu');
                    $card.find('.max-label').text('Kí tự tối đa');
                    $card.find('.min-container, .max-container').show();
                    if ($card.find('.attribute-max').val() === "0") {
                        $card.find('.attribute-max').val(1000);
                    }
                    break;

                case 'number':
                    $card.find('.min-label').text('Giá trị nhỏ nhất');
                    $card.find('.max-label').text('Giá trị lớn nhất');
                    $card.find('.min-container, .max-container').show();
                    if ($card.find('.attribute-max').val() === "0") {
                        $card.find('.attribute-max').val(99999);
                    }
                    break;

                case 'option':
                    $card.find('.content-label').text('Giá trị tùy chọn');
                    $card.find('.content-container').show();
                    break;

                case 'date':
                    $card.find('.no-properties').show();
                    break;
            }
        }

        // Đồng bộ giá trị cho input color và text
        $('#CheckboxTextColorPicker').on('input', function () {
            $('#CheckboxTextColor').val($(this).val());
        });

        $('#CheckboxTextColor').on('input', function () {
            $('#CheckboxTextColorPicker').val($(this).val());
        });

        $('#SubmitButtonColorPicker').on('input', function () {
            $('#SubmitButtonColor').val($(this).val());
        });

        $('#SubmitButtonColor').on('input', function () {
            $('#SubmitButtonColorPicker').val($(this).val());
        });

        $('#FormBackgroundColorPicker').on('input', function () {
            $('#FormBackgroundColor').val($(this).val());
        });

        $('#FormBackgroundColor').on('input', function () {
            $('#FormBackgroundColorPicker').val($(this).val());
        });

        // Validate form trước khi submit
        $("#customFormForm").on('submit', function (e) {
            e.preventDefault();
            HandleSaveOrUpdate('@Model?.FormId');
        });

        $(document).on('blur', '.attribute-item [required], .attribute-item input[type=number]', function () {
            if ($(this).attr('type') === 'number') {
                validateNumberField(this, 0);
            } else {
                validateField(this);
            }
        });

        // When adding new attributes, update the DisplayOrder
        $("#addAttribute").click(function () {
            // ...existing code...

            // Also call validation setup for the new fields
            $newCard.find('[required], input[type=number]').each(function () {
                if ($(this).attr('type') === 'number') {
                    validateNumberField(this, 0);
                } else {
                    validateField(this);
                }
            });
        });
    });

    function HandleSaveOrUpdate(formId) {
        const form = document.getElementById('customFormForm');

        // Temporarily disable validation on template elements
        const templateElements = document.querySelector('.attribute-template').querySelectorAll('[required]');
        templateElements.forEach(el => {
            el.setAttribute('data-required', 'true');
            el.removeAttribute('required');
        });

        // Get form data after template validation attributes are removed
        const formData = $('#customFormForm').serializeArray()
            .filter(field => !field.name.includes('${attributeIndex}'));
        console.log(formData);

        if (form.checkValidity()) {
            // Lấy dữ liệu từ form

            // Chuyển đổi dữ liệu form thành object theo cấu trúc API request
            const apiRequest = {
                IsActive: getFormValue(formData, 'IsActive') === 'true',
                Name: getFormValue(formData, 'FormName'),
                Title: getFormValue(formData, 'FormTitle'),
                CampaignName: getFormValue(formData, 'CampaignName'),
                TextColor: getFormValue(formData, 'CheckboxTextColor'),
                ButtonText: getFormValue(formData, 'ButtonText') || "Đăng ký", // Use form value or default
                ButtonColor: getFormValue(formData, 'SubmitButtonColor'),
                BackgroundColor: getFormValue(formData, 'FormBackgroundColor'),
                CustomFormAttributes: []
            };

            // Nhóm các trường InputFields theo index
            const inputFields = {};
            formData.forEach(item => {
                const match = item.name.match(/InputFields\[(\d+)\]\.(.+)/);
                if (match) {
                    const index = match[1];
                    const property = match[2];

                    if (!inputFields[index]) {
                        inputFields[index] = {};
                    }

                    inputFields[index][property] = item.value;
                }
            });

            // Chuyển đổi InputFields thành CustomFormAttributes
            Object.values(inputFields).forEach(field => {
                apiRequest.CustomFormAttributes.push({
                    IsActive: field.IsActive === "true",
                    Type: field.Type,
                    Attribute: field.Attribute,
                    AttributeLabel: field.AttributeName,
                    AttributeValue: field.Value || "",
                    DefaultValue: field.DefaultValue || "",
                    Min: parseInt(field.Min) || 0,
                    Max: parseInt(field.Max) || 0,
                    DislayOrder: parseInt(field.DisplayOrder) || 0
                });
            });

            // Xác định URL API dựa vào việc có formId hay không
            const apiUrl = formId ? `/api/CustomForms/${formId}` : `/api/CustomForms`;

            // Xác định phương thức HTTP
            const method = formId ? 'PUT' : 'POST';

            // Gọi API
            $.ajax({
                url: apiUrl,
                type: method,
                contentType: 'application/json',
                data: JSON.stringify(apiRequest),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse('Lưu form thành công', 'success');
                        table.ajax.reload();
                        $("#modal-customForm").modal('toggle');
                    } else {
                        AlertResponse(response.Message || 'Có lỗi xảy ra khi lưu form', 'error');
                    }
                },
                error: function (xhr) {
                    AlertResponse('Có lỗi xảy ra khi lưu form', 'error');
                    console.error(xhr.responseText);
                }
            });
        } else {
            // Restore required attributes
            templateElements.forEach(el => {
                if (el.getAttribute('data-required') === 'true') {
                    el.setAttribute('required', '');
                }
                el.removeAttribute('data-required');
            });

            // Only validate visible fields
            let hasError = false;
            $('.attribute-template').not('.d-none').find('[required]').each(function () {
                if (!this.checkValidity()) {
                    hasError = true;
                }
            });

            // Trigger HTML5 validation for visible fields only
            $(form).addClass('was-validated');
            AlertResponse('Vui lòng điền đầy đủ thông tin bắt buộc', 'warning');
        }
    }

    function getFormValue(formData, name) {
        const field = formData.find(item => item.name === name);
        return field ? field.value : '';
    }

    function validateField(field) {
        if (field.hasAttribute('required') && !field.value.trim()) {
            $(field).addClass('is-invalid');
            return false;
        } else {
            $(field).removeClass('is-invalid');
            return true;
        }
    }

    // Validate number fields with default value
    function validateNumberField(field, defaultValue) {
        let input = field.value.trim();

        // Nếu rỗng, gán giá trị mặc định
        if (!input) {
            field.value = defaultValue;
            return validateField(field);
        }

        // Không cho phép toàn số 0, ví dụ "0000", "00", v.v.
        if (/^0+$/.test(input)) {
            field.value = defaultValue;
            return validateField(field);
        }

        // Kiểm tra nếu không phải số hợp lệ
        const value = parseFloat(input);
        if (isNaN(value)) {
            field.value = defaultValue;
            return validateField(field);
        }

        // Nếu hợp lệ thì cập nhật lại field.value để loại bỏ 0 đầu (nếu có)
        field.value = value;

        return validateField(field);
    }
</script>
