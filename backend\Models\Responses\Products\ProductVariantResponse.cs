﻿namespace MiniAppCore.Models.Responses.Products
{
    public class ProductVariantResponse
    {
        public int MaxSelection { get; set; } // Số lượng tối đa có thể chọn (nếu checkbox)
        public string? PropertyId { get; set; }
        public string? OptionName { get; set; }
        public bool IsMultipleChoice { get; set; }
        public List<VariantRepsonse> Variants { get; set; } = new(); // Danh sách các giá trị thuộc tính và các biến thể tương ứng
    }

    public class VariantRepsonse
    {
        public string? PropertyValueId { get; set; } // ID giá trị thuộc tính (VD: "Red", "XL")
        public string? PropertyValueName { get; set; } // Tên giá trị thuộc tính (VD: "Đỏ", "Size L")
    }

    public class ProductVariantDetailResponse
    {
        public short Status { get; set; }
        public decimal Price { get; set; }
        public long Quantity { get; set; }
        public string? VariantId { get; set; }
        public List<string> PropertyValueIds { get; set; } = new();

        public decimal OriginalPrice { get; set; } // Giá gốc
        public decimal DiscountPrice { get; set; } // Giá gốc
        public bool IsDiscounted => DiscountPrice < OriginalPrice;
        public short DiscountType { get; set; } // 1. giam %, 2. truc tiep
        public decimal DiscountValue
        {
            get
            {
                if (OriginalPrice <= 0 || DiscountPrice <= 0)
                    return 0;

                return DiscountType switch
                {
                    1 => Math.Round((1 - DiscountPrice / OriginalPrice) * 100, 2), // Giảm theo %
                    2 => Math.Round(OriginalPrice - DiscountPrice, 2), // Giảm trực tiếp
                    _ => 0
                };
            }
        }
    }
}
