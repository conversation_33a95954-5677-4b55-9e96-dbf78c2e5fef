﻿@model MiniAppCore.Entities.Memberships.MembershipExtendDefault
@{
    var typeList = new Dictionary<string, string>() { { "text", "Chữ" }, { "date", "<PERSON><PERSON><PERSON> tháng" }, { "number", "<PERSON>ố" }, { "option", "Lựa chọn" } };
}
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title font-weight-bold">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body p-4">
        <div class="modal-wrapper">
            <input id="id_member" value="@Model.Id" hidden />

            <div class="row mb-3">
                <div class="col-md-9">
                    <div class="form-group mb-0">
                        <label class="form-label fw-semibold">Tên trường <span class="text-danger">*</span></label>
                        <input id="attribute_member" value="@Model.Attribute" class="form-control" placeholder="Nhập tên trường" />
                        <small class="form-text text-muted">Tên này sẽ được sử dụng trong hệ thống (không dấu, không khoảng trắng)</small>
                    </div>
                </div>
                <div class="col-md-3 d-flex align-items-center">
                    <div class="form-group mb-0 mt-3 w-100">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="is_active" checked="@Model.IsActive" />
                            <label class="custom-control-label" for="is_active">Kích hoạt</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class=" form-group mb-3">
                <label class="form-label fw-semibold">Tên trường đầy đủ <span class="text-danger">*</span></label>
                <input id="attribute_name" value="@Model.AttributeName" class="form-control" placeholder="Nhập tên hiển thị đầy đủ" />
                <small class="form-text text-muted">Tên này sẽ hiển thị cho khách hàng</small>
            </div>

            <div class="form-group mb-3">
                <label class="form-label fw-semibold">Loại trường <span class="text-danger">*</span></label>
                <select id="type_member" class="form-control" onchange="handleTypeChange()">
                    @foreach (var item in typeList)
                    {
                        <option value="@item.Key" selected="@(!string.IsNullOrEmpty(Model.Type) && Model.Type.Equals(item.Key, StringComparison.OrdinalIgnoreCase))">@item.Value</option>
                    }
                </select>
                <small class="form-text text-muted">Loại dữ liệu của trường này</small>
            </div>

            <div id="field_properties" class="card border mt-3 mb-3">
                <div class="card-header bg-light py-2">
                    <h6 class="mb-0 font-weight-bold">Thuộc tính của trường</h6>
                </div>
                <div class="card-body">
                    <div id="min_container" class="form-group mb-3" style="display: none;">
                        <label id="min_label" class="form-label fw-semibold"><span class="text-danger">*</span></label>
                        <input id="attribute_min" value="@Model.Min" class="form-control" />
                    </div>
                    <div id="max_container" class="form-group mb-3" style="display: none;">
                        <label id="max_label" class="form-label fw-semibold"><span class="text-danger">*</span></label>
                        <input id="attribute_max" value="@Model.Max" class="form-control" />
                    </div>

                    <div id="content_container" class="form-group mb-3" style="display: none;">
                        <label id="content_label" class="form-label fw-semibold"><span class="text-danger">*</span></label>
                        <input id="content_member" value="@Model.Content" class="form-control" />
                        <small id="content_help" class="form-text text-muted mt-1" style="display: none;">
                            <i class="ri-information-line me-1"></i> Dùng "," để phân biệt các giá trị
                        </small>
                    </div>

                    <div id="no_properties" class="text-center text-muted py-3">
                        Loại trường này không cần cài đặt thêm
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><i class="ri-close-line me-1"></i>Đóng</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')"><i class="ri-save-line me-1"></i>@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        $('#attribute_member').on('blur', function () {
            let value = $(this).val();
            let formattedValue = removeVietnameseAndSpace(value);
            $(this).val(formattedValue);
        });

        // Gọi function này ngay sau khi trang load để hiển thị các field đúng với loại trường hiện tại
        handleTypeChange();
    });

    function handleTypeChange() {
        const type = $("#type_member").val();

        // Ẩn tất cả các container trước
        $("#min_container, #max_container, #content_container, #no_properties").hide();

        // Dựa vào type để hiển thị các field phù hợp
        switch (type) {
            case "text":
                // Hiển thị min/max cho text
                $("#min_label").text("Kí tự tối thiểu");
                $("#max_label").text("Kí tự tối đa");
                $("#min_container, #max_container").show();
                if($("#attribute_max").val() === "0"){
                    $("#attribute_max").val(1000);
                }
                break;

            case "number":
                // Hiển thị min/max cho number
                $("#min_label").text("Giá trị nhỏ nhất");
                $("#max_label").text("Giá trị lớn nhất");
                $("#min_container, #max_container").show();

                if($("#attribute_max").val() === "0"){
                    $("#attribute_max").val(99999);
                }
                break;

            case "option":
                // Hiển thị content cho option
                $("#content_label").text("Giá trị tùy chọn");
                $("#content_container").show();
                $("#content_help").show();
                break;

            case "date":
                // Không hiển thị min/max và content
                $("#no_properties").show();
                break;
        }
    }
</script>