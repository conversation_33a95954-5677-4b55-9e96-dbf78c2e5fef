﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Categories;
using MiniAppCore.Models.Responses.Categories;

namespace MiniAppCore.Services.Categories
{
    public interface ICategoryService : IService<Category>
    {
        Task<int> CreateAsync(CategoryRequest dto);
        Task<int> UpdateAsync(string id, CategoryRequest dto);
        Task<PagedResult<CategoryResponse>> GetPage(CategoryQueryParams query);

        Task<List<CategoryResponse>> GetCategoriesByProductId(string productId);
        Task<int> AssignProductToCategories(string productId, List<string> categoryIds);
        Task<int> RemoveAllCategoriesFromProduct(string productId);
        Task<int> RemoveProductFromCategories(string productId, List<string> categoryIds);

        #region category child

        //Task<int> AddOrRemoveCategoryChild(string categoryId, List<CategoryChild> categoryChilds);
        Task<List<CategoryChild>> GetListCategoryChild(string categoryId);
        Task<int> AddOrUpdateCategoryChildByProduct(string productId, List<string> categoryChildIds);
        Task<int> RemoveCategoryChildFromProduct(string productId);
        #endregion

        Task<int> QuickUpdate(List<string> categoryIds);
    }
}