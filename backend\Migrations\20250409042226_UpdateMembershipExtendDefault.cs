﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateMembershipExtendDefault : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AttributeName",
                table: "MembershipExtendDefaults",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DefaultContent",
                table: "MembershipExtendDefaults",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                table: "MembershipExtendDefaults",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<long>(
                name: "Max",
                table: "MembershipExtendDefaults",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<long>(
                name: "Min",
                table: "MembershipExtendDefaults",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<string>(
                name: "Type",
                table: "MembershipExtendDefaults",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEFqktkBomURDc0I15ZgpiiyaiYWVu+Urp2aw5ddthwYBlc4FS9US70JYRNnGczGJRA==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AttributeName",
                table: "MembershipExtendDefaults");

            migrationBuilder.DropColumn(
                name: "DefaultContent",
                table: "MembershipExtendDefaults");

            migrationBuilder.DropColumn(
                name: "IsActive",
                table: "MembershipExtendDefaults");

            migrationBuilder.DropColumn(
                name: "Max",
                table: "MembershipExtendDefaults");

            migrationBuilder.DropColumn(
                name: "Min",
                table: "MembershipExtendDefaults");

            migrationBuilder.DropColumn(
                name: "Type",
                table: "MembershipExtendDefaults");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAELl7p/9mRA/Kn0t+Ye0Qn6HlORfkz6r8bdOMZWcRKEKGd3BiLpHGGX1GlWmmNXnNSg==");
        }
    }
}
