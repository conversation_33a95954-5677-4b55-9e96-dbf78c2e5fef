﻿@{
    var containsBranch = User.Claims
        .Where(c => c.Type == "ViewPermission")
        .Any(c => c.Value.Contains("Branch"));
}

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Danh sách đặt lịch</h4>
            </div>

            <div class="d-flex align-items-center justify-content-between">
                <button class="btn btn-primary text-white" data-bs-toggle="modal" data-bs-target="#modal-check-in"> 
                    <i class="ri-map-pin-user-line mr-2"></i>Check in
                </button> 

                <a class="btn border add-btn shadow-none mx-2 d-none" data-bs-toggle="modal" data-bs-target="#new-booking">
                    <i class="ri-add-line mr-3"></i>Đặt lịch
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0 d-flex align-items-center">
                    <i class="ri-filter-3-line me-2"></i>
                    Bộ lọc tìm kiếm
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <!-- Thanh tìm kiếm -->
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label">Tìm kiếm</label>
                            <div class="input-group">
                                <input id="search" type="text" class="form-control" placeholder="Tìm theo mã, tên, số điện thoại..." />
                                <button class="btn btn-primary">
                                    <i class="ri-search-line"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                   
                    <!-- Branch Filter (Admin only) -->
                    @if (User.IsInRole("ADMIN"))
                    {
                        <div class="col-md-2 col-sm-6">
                            <label for="filter-order-branch" class="form-label">Chi nhánh</label>
                            <select id="filter-order-branch" class="form-control" onchange="table.ajax.reload()">
                                <option selected disabled>Chọn chi nhánh</option>
                            </select>
                        </div>
                    }
                    
                    
                    <!-- Bộ lọc trạng thái -->
                    <div class="col-md-2 col-sm-6">
                        <label class="form-label">Trạng thái</label>
                        <select id="filter-status" class="form-control" onchange="table.ajax.reload()">
                            <option value="">Tất cả trạng thái</option>
                            <option value="0">Chờ xác nhận</option>
                            <option value="1">Đã xác nhận</option>
                            <option value="2">Đã check in</option>
                            <option value="4">Đã hoàn thành</option>
                            <option value="5">Đã hủy</option>
                        </select>
                    </div>

                    <!-- Bộ lọc thời gian -->
                    <div class="col-md-4 col-sm-6">
                        <label class="form-label">Thời gian</label>
                        <div class="input-group">
                            <input id="start" type="datetime-local" class="form-control" value='@DateTime.Now.ToString("yyyy-MM-ddTHH:mm")' />
                            <span class="input-group-text"><i class="ri-arrow-right-line"></i></span>
                            <input id="end" type="datetime-local" class="form-control" value='@DateTime.Now.ToString("yyyy-MM-ddTHH:mm")' />
                        </div>
                    </div>

                    <!-- Nút lọc và xuất excel -->
                    <div class="col-12 text-end">
                        <button class="btn btn-light border me-2" onclick="resetFilters()">
                            <i class="ri-refresh-line me-1"></i>
                            Đặt lại
                        </button>
                        <button class="btn btn-primary" onclick="table.ajax.reload()">
                            <i class="ri-filter-line me-1"></i>
                            Lọc dữ liệu
                        </button>
                        <button type="button" class="btn btn-success ms-2" onclick="HandleExportData()">
                            <i class="ri-file-excel-line me-1"></i>
                            Xuất Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-booking" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-booking" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<div id="modal-check-in" class="modal fade" tabindex="-1" data-bs-backdrop="static" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Check in</h5>
                <button type="button" class="close" aria-label="Close" data-bs-dismiss="modal">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div style="width:100%">
                    <div id="qr-reader"></div>
                    <div id="qr-reader-results"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="stop-scan">Đóng</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/lib/qrcodejs/qrcode.js"></script>
    <script>
        $(document).ready(function () {
            $('#start').val(moment().startOf('month').format('YYYY-MM-DDT00:00'));
            $('#end').val(moment().endOf('month').format('YYYY-MM-DDT23:59'));

            GetListBooking();

            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#search').on('input', search);
            initQrScanner();

            // filter theo chi nhánh
            if ($("#filter-order-branch")) {
                $("#filter-order-branch").select2({
                    placeholder: 'Chọn chi nhánh',
                    allowClear: false,
                    cache: true,
                    minimumInputLength: 0,
                    ajax: {
                        url: '@Url.Action("GetPage", "Branches")' + "?isGetAll=true",
                        dataType: 'json',
                        delay: 250, // Thời gian trễ khi gõ tìm kiếm
                        data: function (params) {
                            return {
                                keyword: params.term, // Keyword tìm kiếm
                                page: params.page || 1,
                                pageSize: 5
                            };
                        },
                        processResults: function (data, params) {
                            params.page = params?.page || 1;
                            // Map data
                            const mappedData = data.data.map(function (item) {
                                return {
                                    id: item['id'],
                                    text: item['name']
                                };
                            });

                            if (!params.term && params.page === 1) {
                                // Thêm phần tử rỗng vào đầu mảng
                                mappedData.unshift({ id: 'all', text: 'Tất cả chi nhánh' });
                            }

                            return {
                                results: mappedData,
                                pagination: {
                                    more: (params.page * (10)) < (data.totalItems || 0)
                                }
                            };
                        },
                        cache: true,
                    }
                });
            }
        });

       function initQrScanner() {
            const resultContainer = $("#qr-reader-results");
            let lastResult, countResults = 0;

            const html5QrcodeScanner = new Html5QrcodeScanner(
                "qr-reader", { fps: 10, qrbox: 200 });
            html5QrcodeScanner.render(onScanSuccess);

            $("#stop-scan").on('click', () => {
                html5QrcodeScanner.clear().then(() => {
                    console.log("QR scanning stopped.");
                }).catch(error => {
                    console.error("Failed to stop scanning:", error);
                });
            });
        }

        function onScanSuccess(decodedText, decodedResult) {
            console.log(`Scan result ${decodedText}`, decodedResult);

            beep();

            if (this.scanTimeout) {
                clearTimeout(this.scanTimeout);
            }

            if (this.decodedState && this.decodedState.isSuccess && this.decodedState.url == decodedText) {
                return;
            }

            this.scanTimeout = setTimeout(() => {
                $.ajax({
                    url: decodedText,
                    method: "POST",
                    success: function (response) {

                        this.decodedState = {
                            url: decodedText,
                            isSuccess: false
                        }

                        if (response.code === 0) {
                            AlertResponse(response.message, 'success');
                            this.decodedState.isSuccess = true;
                            table.ajax.reload(null, false);
                        } else {
                            AlertResponse(response.message, 'warning');
                        }
                    }
                });
            }, 1000);

            $.ajax({
                url: decodedText,
                method: "POST",
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'warning');
                    }
                }
            });
        }

        function beep() {
            var snd = new Audio("audios/scanner-beep.mp3");
            console.log("Beep");
            snd.play();
        }

        function GetListBooking() {
            table = new DataTable("#list-booking", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const status = $("#filter-status").val();
                    let start = $("#start").val();
                    let end = $("#end").val();
                    let keyword = $("#search").val();

                    const bookingId = getQueryParam('id');

                    if (bookingId) {
                        keyword = bookingId;
                        start = null;
                        end = null;
                    }

                    $.ajax({
                        url: '@Url.Action("GetPage", "Bookings")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            bookingStatus: status,
                            startDate: start,
                            endDate: end
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                index: data.start + index + 1, // Số thứ tự

                               bookingInfo: `<div class="d-flex flex-column">
                                    <p class="mb-0" data-bs-toggle="tooltip" data-bs-placement="top" title="${item.id}">
                                        <small>Mã đặt lịch: <strong>${item.id}</strong></small>
                                    </p>
                                    <p class="mb-0">
                                        <small>Tên khách hàng: <strong>${item.userZaloName || "Không có tên"}</strong></small>
                                    </p>
                                    <p class="mb-0">
                                        <small>Số điện thoại: <strong>${item.phoneNumber || "Không có số điện thoại"}</strong></small>
                                    </p>
                                    ${item.branch
                                        ? `<p class="mb-0 @(containsBranch ? "" : "d-none")"><small>Chi nhánh: <strong>${item.branch.name}</strong></small></p>`
                                        : ""}
                                    <p class="mb-0">
                                        <small>Ngày đặt lịch: <strong>${FormatDateTime(item.bookingDate) || "Không có ngày đặt"}</strong></small>
                                    </p>
                                    <p class="mb-0">
                                        <small>Ngày tạo: <strong>${FormatDateTime(item.createdDate) || "Không có ngày tạo"}</strong></small>
                                    </p>
                                    <p class="mb-0">
                                        <small>Ghi chú: <strong>${item.note || "Không có ghi chú"}</strong></small>
                                    </p>
                                </div>`,
                                status: `${bookingStatusMapping[item.status] || "Không có trạng thái"}`,
                                actions: `<div class="d-flex flex-column align-items-center">
                                                  <div class="d-flex align-items-center justify-content-evenly mt-1">
                                                      <a onclick="GetFormBooking('${item.id}')" class="badge badge-info"
                                                         data-toggle="tooltip" data-placement="top" title="Cập nhật">
                                                          <i class="ri-edit-line fs-6"></i>
                                                      </a>

                                                      ${item.status === 0 ? `<a onclick="Confirm('${item.id}')" class="badge badge-secondary"
                                                          data-toggle="tooltip" data-placement="top" title="Xác nhận">
                                                           <i class="ri-check-line fs-6"></i>
                                                      </a>` : ``}

                                                      ${item.status === 1 ? `<a onclick="CheckIn('${item.id}')" class="badge badge-success"
                                                          data-toggle="tooltip" data-placement="top" title="Check in">
                                                           <i class="ri-check-line fs-6"></i>
                                                      </a>` : ``}

                                                      ${item.status === 2 ? `<a onclick="Complete('${item.id}')" class="badge badge-success"
                                                          style="background-color: #e0474e !important" data-toggle="tooltip"
                                                          data-placement="top" title="Hoàn thành">
                                                           <i class="ri-check-line fs-6"></i>
                                                      </a>` : ``}
                                                  </div>
                                              </div>`
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "index", className: 'text-center' },
                    { title: "Thông tin đặt lịch", data: "bookingInfo", className: 'col-5' },
                    // { title: "Chi nhánh", data: "branch" },
                    // { title: "Ngày hẹn", data: "bookingDate", className: 'text-center' },
                    // { title: "Ngày đặt", data: "createdDate", className: 'text-center' },
                    { title: "Trạng thái", data: "status", className: 'text-center' },
                    // { title: "Ghi chú", data: "note"},
                    { title: "Thao tác", data: "actions" }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormBooking(id) {
            const url = id ? `@Url.Action("Detail", "Booking")/${id}` : "@Url.Action("Create", "Booking")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-booking").modal("toggle");
                }
            })
        }

        function CheckIn(id) {
            $.ajax({
                url: '@Url.Action("CheckIn", "Bookings", new { id = "1" })'.replace("/1", `/${id}`),
                method: "POST",
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'warning')
                    }
                }
            })
        }

        function Confirm(id) {
            $.ajax({
                url: '@Url.Action("Confirm", "Bookings", new { id = "1" })'.replace("/1", `/${id}`),
                method: "POST",
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'warning')
                    }
                }
            })
        }

        function Complete(id) {
            $.ajax({
                url: '@Url.Action("Complete", "Bookings", new { id = "1" })'.replace("/1", `/${id}`),
                method: "POST",
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'warning')
                    }
                }
            })
        }

        function HandleSaveOrUpdate(id) {
            const data = {
                id,
                bookingDate: $('#bookingDate').val(),
                branchId: $('#branchId').val(),
                status: $('#status').val(),
                membershipName: $('#memebershipName').val(),
                phoneNumber: $('#cusPhone').val(),
                notes: $('#notes').val()
            }

            const url = id !== '0' ? `/api/bookings/${id}` : '/api/bookings';
            const method = id !== '0' ? 'PUT' : 'POST'

            $.ajax({
                url: url,
                type: method,
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    }
                    $("#modal-booking").modal("toggle");
                },
                error: function (err) {
                    AlertResponse('Cập nhật đặt lịch thất bại!', 'error')
                }
            });
        }

        function HandleExportData() {
            const orderStatus = $("#filter-order-status").val() || "";
            const paymentStatus = $("#filter-payment-status").val() || "";

            // Chuyển đổi ngày sang format chuẩn (YYYY-MM-DD) nếu có giá trị
            const endDateInput = $("#end").val();
            const startDateInput = $("#start").val();

            const startDate = startDateInput ? moment(startDateInput).format("YYYY-MM-DD") : null;
            const endDate = endDateInput ? moment(endDateInput).format("YYYY-MM-DD") : null;

            const queryParams = new URLSearchParams({
                orderStatus: orderStatus,
                paymentStatus: paymentStatus,
                startDate: startDate,
                endDate: endDate
            }).toString();

            const apiUrl = `/api/Bookings/Export?${queryParams}`;
            $.ajax({
                url: apiUrl,
                type: 'POST',
                xhrFields: {
                    responseType: 'blob' // Để nhận file từ server
                },
                success: function(response, status, xhr) {
                    // Lấy tên file từ header response
                    const timestamp = moment().format("YYYYMMDD_HHmmss");
                    const fileName = `DatLich_${timestamp}.xlsx`;
                    // Tạo đường dẫn tải file
                    const url = window.URL.createObjectURL(new Blob([response]));
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    a.remove();
                    AlertResponse("Xuất file thành công!", "success");
                },
                error: function(xhr) {
                    // Kiểm tra xem response có phải là JSON hay không
                    if (xhr.responseJSON) {
                        console.log(xhr.responseJSON);
                        AlertResponse(xhr.responseJSON?.message || "Đã xảy ra lỗi. Vui lòng thử lại sau.", "error");
                    } else {
                        // Nếu không phải JSON (có thể là Blob), xử lý lỗi như vậy
                        AlertResponse("Không có dữ liệu!", "info");
                    }
                },
                complete: function() {
                    $("#modal-export").modal("toggle");
                }
            });
        }

        function resetFilters() {
            // Đặt lại ô tìm kiếm
            $('#search').val('');

            // Đặt lại dropdown trạng thái
            $('#filter-status').val('');

                // Lấy thời gian hiện tại bằng moment
            const now = moment().format('YYYY-MM-DDTHH:mm');

            // Đặt lại input datetime-local
            $('#start').val(now);
            $('#end').val(now);

            // Reload lại bảng dữ liệu
            if (typeof table !== 'undefined' && table.ajax) {
                table.ajax.reload();
            }
        }

    </script>
}
