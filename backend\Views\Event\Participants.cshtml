﻿@model MiniAppCore.Entities.Events.Event
﻿
@{
    string eventId = Model.Id;
}

<style>
    .description {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 180px;
    }
</style>
<div class="mb-3">
    <a href="@Url.Action("Index", "Event")" class="btn btn-outline-secondary">
        <i class="ri-arrow-left-line me-1"></i> Quay lại danh sách
    </a>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow-sm mb-4">
            <div class="card-body row">
                <div class="col-md-6">
                    <h5 class="fw-semibold text-primary">Sự kiện: @Model.Title</h5>
                    <p class="mb-1"><strong>Thời gian:</strong> @Model.StartTime.ToString("dd/MM/yyyy HH:mm") - @Model.EndTime.ToString("dd/MM/yyyy HH:mm")</p>
                    <p class="mb-0"><strong>Trạng thái:</strong> @(Model.IsActive ? "Đang hoạt động" : "Ngừng hoạt động")</p>
                </div>
                <div class="col-md-6 text-end">
                    @if (!string.IsNullOrEmpty(ViewBag.Banner))
                    {
                        <img src="@ViewBag.Banner" alt="Banner" style="height: 100px; object-fit: cover; border-radius: 6px;" />
                    }
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-12 mb-4">
        <div class="d-flex flex-wrap justify-content-between align-items-center gap-2">
            <div class="col-3 pl-0">
                <div class="position-relative">
                    <input id="search" type="text" class="form-control ps-5" placeholder="Nhập từ khóa..." />
                    <i class="ri-search-line position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                </div>
            </div>

            <div class="d-flex flex-wrap gap-2 justify-content-end">
                <button id="btn-checkin-range" class="btn btn-outline-dark d-none" onclick="CheckInRange()">
                    <i class="ri-checkbox-multiple-line"></i> Check-In hàng loạt
                </button>
                <button type="button" class="btn btn-success" onclick="ExportExcel('@eventId')">
                    <i class="ri-file-download-line me-1"></i> Xuất Excel
                </button>
                <button type="button" class="btn btn-primary" onclick="CheckIn()">
                    <i class="ri-checkbox-circle-line me-1"></i> Check-In
                </button>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="participant-table" class="table-responsive rounded mb-3">
            <table id="list-participant" class="table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-checkin-qr" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Check-In</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-4 align-items-start">
                    <!-- Camera -->
                    <div class="col-md-6">
                        <div class="border rounded p-2 position-relative" style="min-height: 300px;">
                            <!-- Vùng camera sẽ gắn vào đây -->
                            <div id="qr-reader" style="width: 100%;"></div>

                            <!-- Loading khi chưa sẵn sàng -->
                            <div id="camera-loading" class="position-absolute top-50 start-50 translate-middle text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Đang mở camera...</span>
                                </div>
                                <div class="mt-2 small text-muted">Đang mở camera...</div>
                            </div>
                        </div>
                    </div>

                    <!-- Danh sách đã check-in -->
                    <div class="col-md-6">
                        <div class="border rounded p-3">
                            <h6 class="mb-3 fw-semibold text-primary">Kết quả Check-In</h6>
                            <div id="checkin-message" class="text-success mb-2"></div>
                            <table class="table table-bordered table-sm table-striped mb-0" id="checkin-list">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>Họ tên</th>
                                        <th>SĐT</th>
                                        <th>Email</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- sẽ thêm dòng tại đây -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<audio id="beep" src="~/audios/scanner-beep.mp3" preload="auto"></audio>

<!-- Overlay loading -->
<div id="excel-loading-backdrop" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.3); z-index:1050;">
    <div class="d-flex justify-content-center align-items-center" style="height:100%;">
        <div class="spinner-border text-light" role="status" style="width: 3rem; height: 3rem;">
            <span class="visually-hidden">Đang tải...</span>
        </div>
    </div>
</div>

@section Scripts {
    <!--Scanner JS-->
    <script src="~/lib/qrcodejs/qrcode.js"></script>
    <script>
        let qrScanner;
        const recentlyScanned = new Set();
        let checkInCount = 0;

        const STATUS = {
            NotCheckIn: 1,
            CheckIn: 2,
            Cancel: 3
        };

        $(document).ready(function () {
            $('#search').on('input', search);
            GetListParticipant();
        });

        $(document).on('change', '#select-all', function () {
            $('.row-select').prop('checked', $(this).is(':checked'));
        });

        $(document).on('change', '.row-select, #select-all', function () {
            const anyChecked = $('.row-select:checked').length > 0;
            $('#btn-checkin-range').toggleClass('d-none', !anyChecked);
        });

        $('#modal-checkin-qr').on('hidden.bs.modal', function () {
            if (qrScanner) {
                qrScanner.stop().then(() => qrScanner.clear());
                qrScanner = null;
            }
            scanned = false;
            checkInCount = 0;
            $('#checkin-message').html('');
            $('#checkin-list tbody').empty();
        });

        function GetListParticipant() {
            table = new DataTable("#list-participant", {
                searching: false,
                sort: false,
                pageLength: 10,
                responsive: true,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetAll", "EventRegistrations", new { eventId = eventId })',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                rowNum: data.start + index + 1,
                                name: item.name || '-',
                                phone: item.phone || '-',
                                email: item.email || '-',
                                code: item.checkInCode,
                                status: item.isCheckIn === 2
                                    ? '<span class="badge bg-success">Đã Check-In</span>'
                                    : item.isCheckIn === 3
                                        ? '<span class="badge bg-danger">Đã hủy</span>'
                                        : '<span class="badge bg-secondary">Chưa Check-In</span>',
                                statusCode: item.isCheckIn
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });

                                $('#select-all').prop('checked', false);
                                $('#btn-checkin-range').addClass('d-none');
                            }, 400);
                        }
                    });
                },
                columns: [
                    {
                        title: '<div class="form-check"><input type="checkbox" id="select-all" class="form-check-input"></div>',
                        data: null,
                        orderable: false,
                        className: 'text-center',
                        render: function (data, type, row, meta) {
                            return `<div class="form-check">
                                <input type="checkbox" class="form-check-input row-select" data-code="${row.code}" />
                            </div>`;
                        }
                    },
                    { title: "STT", data: "rowNum", className: 'text-center' },
                    { title: "Họ và Tên", data: "name" },
                    { title: "Số điện thoại", data: "phone" },
                    { title: "Email", data: "email" },
                    { title: "Mã tham dự", data: "code", className: 'text-center' },
                    { title: "Trạng thái", data: "status", className: 'text-center' },
                    {
                        title: "Thao tác",
                        data: null,
                        className: 'text-center',
                        render: function (data, type, row) {
                            const disabledCheck = row.statusCode === STATUS.CheckIn || row.statusCode === STATUS.Cancel;
                            const disabledCancel = row.statusCode === STATUS.CheckIn || row.statusCode === STATUS.Cancel;

                            return `
                                <a class="badge badge-success me-1 ${disabledCheck ? 'disabled' : ''}"
                                   ${disabledCheck ? 'tabindex="-1" style="pointer-events:none;opacity:0.6;cursor:not-allowed;"' : ''}
                                   ${disabledCheck ? '' : `onclick="ManualCheckIn('${row.code}')"`}>
                                    <i class="ri-check-line fs-6"></i>
                                </a>
                                <a class="badge badge-danger ${disabledCancel ? 'disabled' : ''}"
                                   ${disabledCancel ? 'tabindex="-1" style="pointer-events:none;opacity:0.6;cursor:not-allowed;"' : ''}
                                   ${disabledCancel ? '' : `onclick="ManualCancel('${row.code}')"`}>
                                    <i class="ri-close-line fs-6"></i>
                                </a>
                            `;
                        }
                    }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });
        }

        function ExportExcel(eventId) {
            if (!eventId) return;

            $('#excel-loading-backdrop').show();

            $.ajax({
                url: '/api/EventRegistrations/Participants/Export',
                method: 'POST',
                data: JSON.stringify(eventId),
                contentType: 'application/json',
                xhrFields: {
                    responseType: 'blob'
                },
                success: async function (blob, status, xhr) {
                    const disposition = xhr.getResponseHeader('Content-Disposition');
                    let filename = 'event_export.xlsx';

                    if (disposition) {
                        const match = disposition.match(/filename\*\=UTF-8''(.+)/);
                        if (match && match[1]) {
                            filename = decodeURIComponent(match[1].trim());
                        }
                    }

                    const downloadUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = filename;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(downloadUrl);
                },
                error: function (xhr) {
                    AlertResponse("Xuất Excel thất bại. Vui lòng thử lại.", "error");
                },
                complete: function () {
                    $('#excel-loading-backdrop').hide();
                }
            });
        }

        function CheckIn() {
            $('#checkin-message').html('');
            $('#camera-loading').show();
            const modal = new bootstrap.Modal(document.getElementById("modal-checkin-qr"));
            modal.show();

            const beep = document.getElementById("beep");

            if (qrScanner) {
                qrScanner.clear();
            }

            qrScanner = new Html5Qrcode("qr-reader"); // ✅ Bỏ const

            Html5Qrcode.getCameras().then(cameras => {
                if (!cameras || cameras.length === 0) {
                    AlertResponse("Không tìm thấy camera.", "warning");
                    return;
                }

                const backCam = cameras.find(c => c.label.toLowerCase().includes('back')) || cameras[0];

                qrScanner.start(
                    backCam.id,
                    {
                        fps: 60,
                        qrbox: { width: 300, height: 300 },
                        aspectRatio: 1.0,
                        disableFlip: false,
                        rememberLastUsedCamera: true
                    },
                    onQrScanned,
                    (errorMessage) => {
                        // console.warn("Lỗi khi quét:", errorMessage);
                    }
                ).then(() => {
                    $("#camera-loading").hide();
                }).catch(err => {
                    $("#camera-loading").hide();
                    AlertResponse("Không thể mở camera: " + (err?.message || err), "error");
                });
            });
        }

        async function onQrScanned(decodedText, result) {
            const code = decodedText?.trim();
            if (!code) {
                AlertResponse("QR không chứa mã Check-In hợp lệ.", "warning");
                return;
            }

            if (recentlyScanned.has(code)) return;

            recentlyScanned.add(code);

            // Bật tiếng beep
            document.getElementById("beep").play();

            try {
                await PerformCheckIn(code);
            } catch (err) {
                console.error("Lỗi khi xử lý check-in:", err);
                AlertResponse("Lỗi khi check-in.", "error");
            } finally {
                setTimeout(() => {
                    recentlyScanned.delete(code);
                }, 2000);
            }
        }

        async function PerformCheckIn(code) {
            const eventId = '@eventId';

            console.log("Code:", code);
            console.log("EventId:", eventId);

            try {
                const res = await $.ajax({
                    url: `/api/EventRegistrations/CheckIn/${eventId}`,
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ code })
                });

                if (res.code === 0) {
                    const data = res.data;
                    checkInCount++;

                    // Cập nhật thông báo và danh sách
                    $('#checkin-message').html(`<i class="ri-check-line text-success"></i> <strong>${data.name}</strong> đã check-in!`);

                    $('#checkin-list tbody').prepend(`
                        <tr>
                            <td>${checkInCount}</td>
                            <td>${data.name || "-"}</td>
                            <td>${data.phone || "-"}</td>
                            <td>${data.email || "-"}</td>
                        </tr>
                    `);

                    // Reload bảng chính
                    table.ajax.reload(null, false);
                } else {
                    $('#checkin-message').html(`<span class="text-danger">${res.message}</span>`);
                }
            } catch (err) {
                $('#checkin-message').html(`<span class="text-danger">Lỗi khi check-in.</span>`);
            }
        }

        function CheckInRange() {
            const eventId = '@eventId';
            const codes = $('.row-select:checked').map(function () {
                return $(this).data('code');
            }).get();

            if (codes.length === 0) {
                AlertResponse("Vui lòng chọn ít nhất một người để check-in", "warning");
                return;
            }

            $.ajax({
                url: `/api/EventRegistrations/CheckInRange/${eventId}`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ code: codes }),
                success: function (res) {
                    if (res.code === 0) {
                        AlertResponse(`Đã check-in ${res.data.length} người`, 'success');
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(res.message, 'warning');
                    }
                },
                error: function () {
                    AlertResponse("Lỗi khi check-in hàng loạt", 'error');
                }
            });
        }

        function ManualCheckIn(code) {
            const eventId = '@eventId';
            $.ajax({
                url: `/api/EventRegistrations/CheckIn/${eventId}`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ code }),
                success: function (res) {
                    if (res.code === 0) {
                        AlertResponse(`${res.data.name} đã được check-in`, 'success');
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(res.message, 'warning');
                    }
                },
                error: function () {
                    AlertResponse("Lỗi khi check-in", 'error');
                }
            });
        }

        function ManualCancel(code) {
            const eventId = '@eventId';
            $.ajax({
                url: `/api/EventRegistrations/CancelByCode/${eventId}`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ code }),
                success: function (res) {
                    AlertResponse("Đã hủy tham dự", 'success');
                    table.ajax.reload(null, false);
                },
                error: function () {
                    AlertResponse("Lỗi khi hủy tham dự", 'error');
                }
            });
        }
    </script>
}
