﻿using MiniAppCore.Enums;

namespace MiniAppCore.Models.Requests.Products
{
    public class ProductRequest
    {
        public bool IsGift { get; set; } = false;
        public string? Name { get; set; }
        public decimal Price { get; set; }
        public string? Description { get; set; }
        public EProduct Status { get; set; }
        public bool IsGlobalProduct { get; set; }
        public List<IFormFile> Images { get; set; } = new();
        public List<string> BranchIds { get; set; } = new();
        public List<string> CategoryIds { get; set; } = new();
        public List<string> RemovedOldImages { get; set; } = new();
        public List<string> CategoryChildIds { get; set; } = new();

        // Statistic
        public int LikeCount { get; set; } = 0;
        public int BoughtCount { get; set; } = 0;
        public int ReviewCount { get; set; } = 0;
        public int DisplayOrder { get; set; } = 0;
        public float ReviewPoint { get; set; } = 0;

        // Product variants
        public string? VariantsJson { get; set; }
        public List<ProductVariantRequest> Variants { get; set; } = new();
    }

    public class ProductVariantRequest
    {
        public long Quantity { get; set; }
        public decimal Price { get; set; }
        public string? VariantId { get; set; }
        public EProduct Status { get; set; }
        public List<VariantProperty> Properties { get; set; } = new();
    }

    // Định nghĩa một thuộc tính của biến thể
    public class VariantProperty
    {
        public string PropertyId { get; set; } = string.Empty;
        public string PropertyValueId { get; set; } = string.Empty;
    }
}
