﻿using MiniAppCore.Entities.Memberships;

namespace MiniAppCore.Models.DTOs
{
    public class MembershipDTO
    {
        public byte Gender { get; set; }
        public string? RankId { get; set; }
        public string? UserZaloId { get; set; }
        public string? DisplayName { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime? DateOfBirth { get; set; }

        public long Point { get; set; }
        public long SpinPoint { get; set; }
        public long UsingPoint { get; set; }
        public long RankingPoint { get; set; }

        public string? Job { get; set; }
        public string? Notes { get; set; }
        public string? Address { get; set; }

        public List<string> TagIds { get; set; } = new List<string>();
        public List<MembershipExtend>? ListMembershipExtend { get; set; }
    }
}
