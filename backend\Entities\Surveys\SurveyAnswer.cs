﻿using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Surveys
{
    public class SurveyAnswer : BaseEntity
    {
        [MaxLength(36)]
        public string? SurveyQuestionId { get; set; }
        public string? Key { get; set; }

        public string? Value { get; set; }

        public bool IsInput { get; set; }
        public short DisplayOrder { get; set; }
    }
}
