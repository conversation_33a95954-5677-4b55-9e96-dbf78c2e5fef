﻿using Hangfire;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Affiliates;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Models.Responses.Affiliates;
using MiniAppCore.Services.Affiliates.CommissionRates;
using MiniAppCore.Services.Memberships;
using Newtonsoft.Json;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

namespace MiniAppCore.Services.Affiliates.Commissions
{
    public class CommissionService(IUnitOfWork unitOfWork,
                                   IConfiguration configuration,
                                   IMembershipService membershipService,
                                   ICommissionRateService affiliateRateService) : Service<CommissionTransaction>(unitOfWork), ICommissionService
    {
        private readonly IRepository<Order> _orderRepo = unitOfWork.GetRepository<Order>();
        private readonly IRepository<Common> _commonRepository = unitOfWork.GetRepository<Common>();
        private readonly IRepository<OrderDetail> _orderDetailRepo = unitOfWork.GetRepository<OrderDetail>();

        public async Task CalculateAndSaveCommissionForOrder(string orderId)
        {
            var MAX_LEVEL = configuration.GetSection("").GetValue<int?>("") ?? 3;

            var order = await _orderRepo.FindByIdAsync(orderId);
            if (order == null || order.OrderStatus != Enums.EOrder.Completed)
            {
                return; // Chỉ xử lý đơn hàng đã hoàn thành
            }

            // Lấy chi tiết đơn hàng có mã giới thiệu (RefCode)
            var orderDetails = await _orderDetailRepo.AsQueryable()
                .Where(x => x.OrderId == orderId && !string.IsNullOrEmpty(x.RefCode) && !x.IsUseForCommission)
                .ToListAsync();

            // Lấy thông tin người mua
            var buyer = await membershipService.GetByUserZaloId(order.UserZaloId);
            if (buyer == null) return;

            // cấu hình tỷ lệ quy đổi theo cấp
            var rateLevels = await affiliateRateService.GetActiveRatesAsync();

            // trong chi tiết không có giới thiệu thì tính hoa hồng cho người giới thiệu lúc đăng kí, tính trên đơn hàng
            if (!orderDetails.Any())
            {
                // Lấy chain affiliate từ người mua (bắt đầu từ người giới thiệu người mua)
                var affiliateChain = await affiliateRateService.GetUserAffiliateLevelAsync(buyer.UserZaloId, maxLevel: MAX_LEVEL);

                // danh sách người giới thiệu
                var referrers = await membershipService.GetByUserZaloIds(affiliateChain.Select(x => x.UserZaloId).ToList());
                foreach (var node in affiliateChain)
                {
                    var referrer = referrers.FirstOrDefault(x => x.UserZaloId == node.UserZaloId);
                    if (referrer == null) continue;

                    // Lấy tỷ lệ hoa hồng cho cấp đó
                    decimal rate = rateLevels.FirstOrDefault(r => r.Level == node.Level)?.Rate ?? 0;
                    if (rate == 0) continue;

                    // Không làm tròn tiền hoa hồng - giữ nguyên độ chính xác
                    decimal commissionAmount = order.Total * rate / 100m;

                    // Create or update commission transaction
                    await CreateOrUpdateTransactionAsync(
                        orderId,
                        referrer.UserZaloId,
                        referrer.ReferralCode,
                        buyer.UserZaloId,
                        buyer.UserZaloName,
                        buyer.Avatar,
                        buyer.PhoneNumber,
                        order.Total,
                        rate,
                        commissionAmount,
                        order.CreatedDate
                    );
                }
            }
            else
            {
                // chi tiết đơn hàng có mã giới thiệu - chỉ tính hoa hồng cho người có mã giới thiệu đó
                // Nhóm theo mã giới thiệu để xử lý trường hợp có nhiều người giới thiệu
                var refCodeGroups = orderDetails.GroupBy(x => x.RefCode!);
                foreach (var group in refCodeGroups)
                {
                    string refCode = group.Key;

                    // Tìm người giới thiệu từ mã giới thiệu
                    var referrer = await membershipService.GetByReferralCode(refCode);
                    if (referrer == null || referrer.UserZaloId == buyer.UserZaloId)
                    {
                        continue; // Bỏ qua nếu không tìm thấy referrer, hoặc tự giới thiệu cho bản thân
                    }

                    var amountForCommission = group.Sum(x => x.Quantity * x.DiscountPrice);

                    // Tính hoa hồng cho cấp 1 (người trực tiếp có mã giới thiệu)
                    decimal level1Rate = rateLevels.FirstOrDefault(x => x.Level == 1)?.Rate ?? 0;
                    if (level1Rate > 0)
                    {
                        // Không làm tròn tiền hoa hồng - giữ nguyên độ chính xác
                        decimal level1Commission = amountForCommission * level1Rate / 100m;

                        // Create or update commission transaction cho cấp 1
                        await CreateOrUpdateTransactionAsync(
                            orderId,
                            referrer.UserZaloId,
                            referrer.ReferralCode,
                            buyer.UserZaloId,
                            buyer.UserZaloName,
                            buyer.Avatar,
                            buyer.PhoneNumber,
                            order.Total,
                            level1Rate,
                            level1Commission,
                            order.CreatedDate);
                    }

                    // Tính hoa hồng cho các cấp cao hơn (cấp 2, 3, ...)
                    BackgroundJob.Enqueue(() => ProccessingCalculateMultiLevelCommission(
                            orderId,
                            buyer.UserZaloId,
                            referrer.UserZaloId,
                            amountForCommission,
                            order.Total,
                            order.CreatedDate,
                            MAX_LEVEL
                        ));
                }

                foreach (var item in orderDetails)
                {
                    item.IsUseForCommission = true;
                }
                _orderDetailRepo.UpdateRange(orderDetails);
            }

            await unitOfWork.SaveChangesAsync();
        }

        public async Task ProccessingCalculateMultiLevelCommission(
                string orderId,
                string buyerZaloId,
                string referrerZaloId,
                decimal amountForCommision,
                decimal totalOrderAmount,
                DateTime orderCreatedDate,
                int maxLevel)
        {
            var buyer = await membershipService.GetByUserZaloId(buyerZaloId);
            if (buyer == null) return;

            var rateLevels = await affiliateRateService.GetActiveRatesAsync();

            // Lấy chain affiliate từ người giới thiệu (bắt đầu từ cấp 2)
            var chain = await affiliateRateService.GetUserAffiliateLevelAsync(referrerZaloId, maxLevel);

            if (chain.Count == 0)
            {
                return;
            }

            var referrerIds = chain.Select(x => x.UserZaloId).Distinct().ToList();
            var referrers = await membershipService.GetByUserZaloIds(referrerIds);

            foreach (var node in chain)
            {
                var referrer = referrers.FirstOrDefault(x => x.UserZaloId == node.UserZaloId);
                if (referrer == null || referrer.UserZaloId == buyer.UserZaloId) continue;

                // Tính cấp thực tế: cấp trong chain + 1 (vì chain bắt đầu từ cấp 2)
                int actualLevel = node.Level + 1;

                var rate = rateLevels.FirstOrDefault(r => r.Level == actualLevel)?.Rate ?? 0;
                if (rate == 0) continue;

                // Không làm tròn tiền hoa hồng - giữ nguyên độ chính xác
                var commission = amountForCommision * rate / 100m;

                await CreateOrUpdateTransactionAsync(
                    orderId,
                    referrer.UserZaloId,
                    referrer.ReferralCode,
                    buyer.UserZaloId,
                    buyer.UserZaloName,
                    buyer.Avatar,
                    buyer.PhoneNumber,
                    totalOrderAmount,
                    rate,
                    commission,
                    orderCreatedDate
                );
            }
        }

        private async Task CreateOrUpdateTransactionAsync(
            string orderId,
            string referrerZaloId,
            string? referrerCode,
            string referredZaloId,
            string referredName,
            string referredAvatar,
            string referredPhone,
            decimal orderTotal,
            decimal commissionRate,
            decimal totalCommission,
            DateTime orderDate)
        {
            var existingTransaction = await _repository.AsQueryable()
                .FirstOrDefaultAsync(x => x.OrderId == orderId && x.ReferrerZaloId == referrerZaloId);

            if (existingTransaction == null)
            {
                var transaction = new CommissionTransaction
                {
                    ReferrerZaloId = referrerZaloId,
                    ReferrerCode = referrerCode,
                    ReferredZaloId = referredZaloId,
                    ReferredName = referredName,
                    ReferredAvatar = referredAvatar,
                    ReferredPhone = referredPhone,
                    OrderId = orderId,
                    TotalOrder = orderTotal,
                    CommissionRate = commissionRate,
                    TotalCommission = totalCommission,
                    OrderDate = orderDate
                };
                await CreateAsync(transaction);
            }
            else
            {
                existingTransaction.TotalCommission = totalCommission;
                await UpdateAsync(existingTransaction);
            }
        }

        public async Task<List<CommissionTransactionResponse>> GetUserCommissionTransactions(string userZaloId)
        {
            var transactions = await _repository.AsQueryable()
                .Where(x => x.ReferrerZaloId == userZaloId)
                .OrderByDescending(x => x.OrderDate)
                .ToListAsync();

            return transactions.Select(t => new CommissionTransactionResponse
            {
                Avatar = t.ReferredAvatar,
                UserZaloId = t.ReferredZaloId,
                UserZaloName = t.ReferredName,
                PhoneNumber = t.ReferredPhone,
                ReferralCode = t.ReferrerCode,
                OrderId = t.OrderId,
                TotalOrder = t.TotalOrder,
                TotalCommission = t.TotalCommission,
                CreatedDate = t.OrderDate,
                IsPaid = t.IsPaid
            }).ToList();
        }

        public async Task<List<CommissionTransactionResponse>> GetReferredHistoryCommissions(string userZaloId, string referredUserZaloId)
        {
            var transactions = await _repository.AsQueryable()
                .Where(x => x.ReferrerZaloId == userZaloId && x.ReferredZaloId == referredUserZaloId)
                .OrderByDescending(x => x.OrderDate)
                .ToListAsync();

            return transactions.Select(t => new CommissionTransactionResponse
            {
                Avatar = t.ReferredAvatar,
                UserZaloId = t.ReferredZaloId,
                UserZaloName = t.ReferredName,
                PhoneNumber = t.ReferredPhone,
                ReferralCode = t.ReferrerCode,
                OrderId = t.OrderId,
                TotalOrder = t.TotalOrder,
                TotalCommission = t.TotalCommission,
                CreatedDate = t.OrderDate,
                IsPaid = t.IsPaid
            }).ToList();
        }

        public async Task<(List<CommissionRecipientResponse> data, int count)> GetCommissionRecipients(int page, int pageSize, string? keyword, string? status, DateTime? startDate, DateTime? endDate)
        {
            var query = _repository.AsQueryable()
                .AsNoTracking();

            if (startDate.HasValue && endDate.HasValue)
            {
                query = query.Where(t => t.OrderDate >= startDate && t.OrderDate <= endDate);
            }

            if (!string.IsNullOrEmpty(status))
            {
                var isPaid = bool.Parse(status);
                query = query.Where(t => t.IsPaid == isPaid);
            }

            // Lấy dữ liệu gộp không có ReferrerInfo
            var groupedData = await query
                .GroupBy(t => t.ReferrerZaloId)
                .Select(g => new
                {
                    ReferrerZaloId = g.Key,
                    ReferrerCode = g.Max(t => t.ReferrerCode),
                    TotalCommission = g.Sum(t => t.TotalCommission),
                    PaidCommission = g.Where(t => t.IsPaid).Sum(t => t.TotalCommission),
                    PendingCommission = g.Where(t => !t.IsPaid).Sum(t => t.TotalCommission),
                    ReferralCount = g.Select(t => t.ReferredZaloId).Distinct().Count()
                })
                .ToListAsync();

            // Nếu có từ khóa, lấy thông tin membership cho tất cả các referrer trước
            var referrerIds = groupedData.Select(r => r.ReferrerZaloId).Distinct().ToList();
            var memberships = new Dictionary<string, Entities.Memberships.Membership>();

            // Lấy thông tin membership một lần cho tất cả các referrer
            foreach (var referrerId in referrerIds)
            {
                if (!string.IsNullOrEmpty(referrerId))
                {
                    var membership = await membershipService.GetByUserZaloId(referrerId);
                    if (membership != null)
                    {
                        memberships[referrerId] = membership;
                    }
                }
            }

            // Tạo kết quả với thông tin đã lấy được
            var result = new List<CommissionRecipientResponse>();
            foreach (var r in groupedData)
            {
                if (!string.IsNullOrEmpty(r.ReferrerZaloId))
                {
                    memberships.TryGetValue(r.ReferrerZaloId, out var membership);

                    // Lọc theo từ khóa nếu có
                    if (!string.IsNullOrEmpty(keyword) && membership != null)
                    {
                        if (!(membership.UserZaloName?.Contains(keyword, StringComparison.OrdinalIgnoreCase) == true ||
                              membership.PhoneNumber?.Contains(keyword) == true))
                        {
                            continue;
                        }
                    }

                    result.Add(new CommissionRecipientResponse
                    {
                        ReferrerZaloId = r.ReferrerZaloId,
                        ReferralCode = r.ReferrerCode,
                        DisplayName = membership?.UserZaloName ?? "Không xác định",
                        PhoneNumber = membership?.PhoneNumber ?? "Không xác định",
                        Avatar = membership?.Avatar,
                        TotalCommission = r.TotalCommission,
                        PaidCommission = r.PaidCommission,
                        PendingCommission = r.PendingCommission,
                        ReferralCount = r.ReferralCount
                    });
                }
            }

            // Áp dụng phân trang
            var totalCount = result.Count;
            var pagedResult = result
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return (pagedResult, totalCount);
        }

        public async Task<(List<CommissionTransaction> data, int count)> GetTransactionsByReferrerId(int page, int pageSize, string referrerZaloId, DateTime? startDate, DateTime? endDate)
        {
            var query = _repository.AsQueryable()
                .Where(t => t.ReferrerZaloId == referrerZaloId)
                .AsNoTracking();

            if (startDate.HasValue && endDate.HasValue)
            {
                query = query.Where(t => t.OrderDate >= startDate && t.OrderDate <= endDate);
            }

            var count = await query.CountAsync();

            var transactions = await query
                .OrderByDescending(t => t.OrderDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (transactions, count);
        }

        public async Task<bool> UpdateTransactionStatus(string transactionId, bool status)
        {
            var transaction = await _repository.FindByIdAsync(transactionId);
            if (transaction == null)
            {
                return false;
            }

            transaction.IsPaid = status;
            return await UpdateAsync(transaction) > 0;
        }

        public async Task<bool> PayAllCommissions(string referrerZaloId)
        {
            var transactions = await _repository.AsQueryable()
                .Where(t => t.ReferrerZaloId == referrerZaloId && !t.IsPaid)
                .ToListAsync();

            if (!transactions.Any())
            {
                return false;
            }

            foreach (var transaction in transactions)
            {
                transaction.IsPaid = true;
            }

            _repository.UpdateRange(transactions);
            return await unitOfWork.SaveChangesAsync() > 0;
        }

        public async Task<byte[]> ExportCommissionsToExcel(DateTime startDate, DateTime endDate)
        {
            // Lấy tất cả dữ liệu trong khoảng thời gian
            var transactions = await _repository.AsQueryable()
                .Where(t => t.OrderDate >= startDate && t.OrderDate <= endDate)
                .OrderByDescending(t => t.OrderDate)
                .ToListAsync();

            // Nhóm theo cộng tác viên
            var groupedData = transactions
                .GroupBy(t => t.ReferrerZaloId)
                .Select(g => new
                {
                    ReferrerZaloId = g.Key,
                    ReferrerCode = g.First().ReferrerCode,
                    TotalCommission = g.Sum(t => t.TotalCommission),
                    PaidCommission = g.Where(t => t.IsPaid).Sum(t => t.TotalCommission),
                    PendingCommission = g.Where(t => !t.IsPaid).Sum(t => t.TotalCommission),
                    TransactionCount = g.Count(),
                    ReferralCount = g.Select(t => t.ReferredZaloId).Distinct().Count()
                })
                .ToList();

            // Tạo file Excel sử dụng EPPlus
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Cộng tác viên");

            // Tạo tiêu đề
            worksheet.Cells[1, 1].Value = "STT";
            worksheet.Cells[1, 2].Value = "Mã giới thiệu";
            worksheet.Cells[1, 3].Value = "Tổng hoa hồng";
            worksheet.Cells[1, 4].Value = "Đã thanh toán";
            worksheet.Cells[1, 5].Value = "Chưa thanh toán";
            worksheet.Cells[1, 6].Value = "Số đơn hàng";
            worksheet.Cells[1, 7].Value = "Số người giới thiệu";

            // Định dạng tiêu đề
            using (var range = worksheet.Cells[1, 1, 1, 7])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                range.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }

            // Điền dữ liệu
            int row = 2;
            foreach (var item in groupedData)
            {
                string displayName = "Không xác định";
                string phoneNumber = "Không xác định";

                if (!string.IsNullOrEmpty(item.ReferrerZaloId))
                {
                    var membership = await membershipService.GetByUserZaloId(item.ReferrerZaloId);
                    displayName = membership?.UserZaloName ?? "Không xác định";
                    phoneNumber = membership?.PhoneNumber ?? "Không xác định";
                }

                worksheet.Cells[row, 1].Value = row - 1;
                worksheet.Cells[row, 2].Value = $"{displayName} ({phoneNumber})";
                worksheet.Cells[row, 3].Value = item.TotalCommission;
                worksheet.Cells[row, 4].Value = item.PaidCommission;
                worksheet.Cells[row, 5].Value = item.PendingCommission;
                worksheet.Cells[row, 6].Value = item.TransactionCount;
                worksheet.Cells[row, 7].Value = item.ReferralCount;

                // Định dạng cột số tiền
                worksheet.Cells[row, 3].Style.Numberformat.Format = "#,##0";
                worksheet.Cells[row, 4].Style.Numberformat.Format = "#,##0";
                worksheet.Cells[row, 5].Style.Numberformat.Format = "#,##0";

                row++;
            }

            // Tổng cộng
            worksheet.Cells[row, 1].Value = "Tổng cộng";
            worksheet.Cells[row, 3].Value = groupedData.Sum(g => g.TotalCommission);
            worksheet.Cells[row, 4].Value = groupedData.Sum(g => g.PaidCommission);
            worksheet.Cells[row, 5].Value = groupedData.Sum(g => g.PendingCommission);
            worksheet.Cells[row, 6].Value = groupedData.Sum(g => g.TransactionCount);
            worksheet.Cells[row, 7].Value = groupedData.Sum(g => g.ReferralCount);

            // Định dạng dòng tổng cộng
            using (var range = worksheet.Cells[row, 1, row, 7])
            {
                range.Style.Font.Bold = true;
            }
            worksheet.Cells[row, 3, row, 5].Style.Numberformat.Format = "#,##0";

            // Tự động điều chỉnh độ rộng cột
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            // Thêm border cho bảng
            using (var range = worksheet.Cells[1, 1, row, 7])
            {
                range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            }

            // Tạo trang chi tiết giao dịch
            var detailSheet = package.Workbook.Worksheets.Add("Chi tiết giao dịch");

            // Thiết lập tiêu đề
            detailSheet.Cells[1, 1].Value = "STT";
            detailSheet.Cells[1, 2].Value = "Mã đơn hàng";
            detailSheet.Cells[1, 3].Value = "Người giới thiệu";
            detailSheet.Cells[1, 4].Value = "Người được giới thiệu";
            detailSheet.Cells[1, 5].Value = "Ngày đặt hàng";
            detailSheet.Cells[1, 6].Value = "Giá trị đơn hàng";
            detailSheet.Cells[1, 7].Value = "Tỷ lệ hoa hồng";
            detailSheet.Cells[1, 8].Value = "Hoa hồng";
            detailSheet.Cells[1, 9].Value = "Trạng thái";

            // Định dạng tiêu đề cho trang chi tiết
            using (var range = detailSheet.Cells[1, 1, 1, 9])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                range.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
                range.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }

            // Điền dữ liệu chi tiết
            int detailRow = 2;
            foreach (var transaction in transactions)
            {
                string referrerName = "Không xác định";
                if (!string.IsNullOrEmpty(transaction.ReferrerZaloId))
                {
                    var referrer = await membershipService.GetByUserZaloId(transaction.ReferrerZaloId);
                    referrerName = referrer?.UserZaloName ?? "Không xác định";
                }

                detailSheet.Cells[detailRow, 1].Value = detailRow - 1;
                detailSheet.Cells[detailRow, 2].Value = transaction.OrderId;
                detailSheet.Cells[detailRow, 3].Value = referrerName;
                detailSheet.Cells[detailRow, 4].Value = $"{transaction.ReferredName ?? "Không xác định"} ({transaction.ReferredPhone ?? "Không xác định"})";
                detailSheet.Cells[detailRow, 5].Value = transaction.OrderDate;
                detailSheet.Cells[detailRow, 6].Value = transaction.TotalOrder;
                detailSheet.Cells[detailRow, 7].Value = transaction.CommissionRate;
                detailSheet.Cells[detailRow, 8].Value = transaction.TotalCommission;
                detailSheet.Cells[detailRow, 9].Value = transaction.IsPaid ? "Đã thanh toán" : "Chưa thanh toán";

                // Định dạng các cột
                detailSheet.Cells[detailRow, 5].Style.Numberformat.Format = "dd/MM/yyyy";
                detailSheet.Cells[detailRow, 6].Style.Numberformat.Format = "#,##0";
                detailSheet.Cells[detailRow, 7].Style.Numberformat.Format = "0.0%";
                detailSheet.Cells[detailRow, 8].Style.Numberformat.Format = "#,##0";

                // Định dạng màu cho trạng thái
                if (transaction.IsPaid)
                {
                    detailSheet.Cells[detailRow, 9].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    detailSheet.Cells[detailRow, 9].Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
                }
                else
                {
                    detailSheet.Cells[detailRow, 9].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    detailSheet.Cells[detailRow, 9].Style.Fill.BackgroundColor.SetColor(Color.LightPink);
                }

                detailRow++;
            }

            // Tự động điều chỉnh độ rộng cột cho trang chi tiết
            detailSheet.Cells[detailSheet.Dimension.Address].AutoFitColumns();

            // Thêm border cho bảng chi tiết
            using (var range = detailSheet.Cells[1, 1, detailRow - 1, 9])
            {
                range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            }

            // Chuyển đổi package thành mảng byte
            return package.GetAsByteArray();
        }

        public async Task<bool> CalculateCommissions(DateTime? startDate, DateTime? endDate)
        {
            var orders = await _orderRepo.AsQueryable()
                .Where(o => o.OrderStatus == Enums.EOrder.Completed)
                .ToListAsync();

            if (startDate.HasValue && endDate.HasValue)
            {
                orders = orders
                    .Where(o => o.CreatedDate >= startDate && o.CreatedDate <= endDate)
                    .ToList();
            }

            foreach (var order in orders)
            {
                await CalculateAndSaveCommissionForOrder(order.Id);
            }

            return true;
        }

        #region Commission Config

        public async Task<decimal> GetCommissionConfigAsync()
        {
            var commissionConfig = await _commonRepository
                .AsQueryable()
                .FirstOrDefaultAsync(x => x.Name == "CommissionConfig");

            if (commissionConfig == null || string.IsNullOrEmpty(commissionConfig.Content))
                return 0m;

            try
            {
                return JsonConvert.DeserializeObject<decimal>(commissionConfig.Content);
            }
            catch
            {
                return 0m;
            }
        }

        public async Task<int> AddOrUpdateCommissionRateAsync(decimal percentage)
        {
            var commissionConfig = await _commonRepository
             .AsQueryable()
             .FirstOrDefaultAsync(x => x.Name == "CommissionConfig");
            if (commissionConfig == null)
            {
                commissionConfig = new Common
                {
                    Name = "CommissionConfig",
                    Content = JsonConvert.SerializeObject(percentage)
                };
                _commonRepository.Add(commissionConfig);
            }
            else
            {
                commissionConfig.Content = JsonConvert.SerializeObject(percentage);
                _commonRepository.Update(commissionConfig);
            }

            return await unitOfWork.SaveChangesAsync();
        }

        #endregion
    }
}
