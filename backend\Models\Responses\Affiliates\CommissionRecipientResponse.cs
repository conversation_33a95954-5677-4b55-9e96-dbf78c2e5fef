namespace MiniAppCore.Models.Responses.Affiliates
{
    public class CommissionRecipientResponse
    {
        public string ReferrerZaloId { get; set; } = string.Empty;
        public string? ReferralCode { get; set; }
        public string DisplayName { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? Avatar { get; set; }
        public decimal TotalCommission { get; set; }
        public decimal PaidCommission { get; set; }
        public decimal PendingCommission { get; set; }
        public int ReferralCount { get; set; }
    }
}