﻿@using MiniAppCore.Entities.Products
@using MiniAppCore.Entities.SystemConfiguration
@model MiniAppCore.Entities.SystemConfiguration.Account;
@{
    // danh sách chi nhánh đã được họn

    // role đc chọn của ng đó

    // roles
    var optionsDict = new Dictionary<string, string>
{
{ "1", "Thống kê" },
// { "2", "Quản lý tin tức" },
// { "3", "Quản lý chi nhánh" },
{ "4", "Quản lý sản phẩm" },
// { "5", "Quản lý ưu đãi" },
{ "6", "Quản lý đơn hàng" },
// { "7", "Quản lý voucher" },
// { "8", "Quản lý người dùng" },
// { "9", "Vòng quay may mắn" },
// { "10", "Cài đặt chung" },
// { "11", "Quản lý nhân viên" },
};

    // danh sách chi dã chọn
    List<EmployeeBranch> employeeBranches = ViewBag.EmployeeBranches ?? new List<EmployeeBranch>();
    var selectedBranches = employeeBranches.ToList().Select(x => x.BranchId).ToList();
    var selectedRoles = employeeBranches.Where(x => !string.IsNullOrEmpty(x.ViewPermissions)) // Bỏ qua null hoặc empty
    .SelectMany(x => x.ViewPermissions.Split(",", StringSplitOptions.RemoveEmptyEntries)) // Tách và gộp tất cả các quyền
    .Select(x => x.Trim()) // Xoá khoảng trắng 2 đầu
    .Distinct() // Loại bỏ trùng
    .ToList();
}
<style>
    .select2-selection.select2-selection--multiple {
        height: fit-content !important;
    }
</style>

<div class="modal-content modal-xl" style="@(User.IsInRole("EMPLOYEE") ? "pointer-events: none;" : "")">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <form>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Email</label>
                        <input id="email" type="text" class="form-control" placeholder="Nhập email" value="@Model.Email" />
                        <div class="help-block with-errors"></div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label>Số điện thoại</label>
                        <input id="phoneNumber" type="text" class="form-control" placeholder="Nhập số điện thoại" value="@Model.PhoneNumber" />
                        <div class="help-block with-errors"></div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label>Tên nhân viên</label>
                        <input id="displayName" type="text" class="form-control" placeholder="Tên nhân viên" value="@Model.DisplayName" />
                        <div class="help-block with-errors"></div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label>Mật khẩu</label>
                        <input id="password" type="password" class="form-control" placeholder="Nhập mật khẩu" value="@Model.Password" />
                        <div class="help-block with-errors"></div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label>Tên vai trò <span style="color:red">*</span></label>
                        <select id="role" class="form-control" disabled>
                            @* <option value="ADMIN" selected="@(Model.RoleName == "ADMIN")">Admin</option> *@
                            <option value="EMPLOYEE" selected>Nhân viên</option>
                        </select>
                        <div class="help-block with-errors"></div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label>Chi nhánh</label>
                        <select id="branches" class="form-control">
                            @* @foreach (var item in ViewBag.Branches) *@
                            @* { *@
                            @*         <option value="@item.Id" selected="@(selectedBranches.Contains(item.Id))">@item.Name</option> *@
                            @* } *@
                        </select>
                        <div class="help-block with-errors"></div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group">
                        <label>Quyền</label>
                        <select id="roles" class="form-control" multiple>
                            @* <option value="all">Tất cả</option> *@
                            @foreach (var dict in optionsDict)
                            {
                                <option value="@dict.Key" selected="@(selectedRoles.Contains(dict.Key))">@dict.Value</option>
                            }
                        </select>
                        <div class="help-block with-errors"></div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (User.IsInRole("ADMIN"))
        {
            @if (string.IsNullOrEmpty(Model.Id))
            {
                <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
            }
            <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
        }
    </div>
</div>
<script>
    $(document).ready(function () {
        $("#roles").select2({
            dropdownParent: $("#modal-employee")
        });

        $("#branches").select2({
            dropdownParent: $("#modal-employee")
        });

        $("#password").on('change', () => { window.isChangePassword = true; })
    });
</script>