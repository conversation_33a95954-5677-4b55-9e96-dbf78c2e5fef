﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Offers;
using MiniAppCore.Entities.Products;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Models.Responses.Products;

namespace MiniAppCore.Services.Offers.Promotions
{
    public class PromotionService(IUnitOfWork unitOfWork, IMapper mapper, IHttpContextAccessor httpContextAccessor) : Service<Promotion>(unitOfWork), IPromotionService
    {
        private readonly IRepository<Product> _productRepo = unitOfWork.GetRepository<Product>();
        private readonly IRepository<PromotionItem> _promotionItemRepo = unitOfWork.GetRepository<PromotionItem>();
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public async Task<int> CreateAsync(PromotionRequest request)
        {
            var promotion = mapper.Map<Promotion>(request);

            if (request.Products.Any())
            {
                var productPromotions = request.Products.Select(item => new PromotionItem
                {
                    IsGift = false,
                    Quantity = item.Quantity,
                    ProductId = item.ProductId,
                    PromotionId = promotion.Id,
                }).ToList();

                _promotionItemRepo.AddRange(productPromotions);
            }

            if (request.Gifts.Any())
            {
                var productPromotions = request.Gifts.Select(item => new PromotionItem
                {
                    IsGift = true,
                    Quantity = item.Quantity,
                    ProductId = item.ProductId,
                    PromotionId = promotion.Id,
                }).ToList();
                _promotionItemRepo.AddRange(productPromotions);
            }
            return await base.CreateAsync(promotion);
        }

        public async Task<int> UpdateAsync(string id, PromotionRequest request)
        {
            var promotion = await GetByIdAsync(id);
            if (promotion == null)
            {
                throw new Exception("Promotion not found");
            }

            mapper.Map(request, promotion);

            // Lấy danh sách sản phẩm hiện tại
            var currentPromotionItems = await _promotionItemRepo.AsQueryable()
                .Where(pi => pi.PromotionId == promotion.Id)
                .ToListAsync();

            // Danh sách sản phẩm mới
            var productBuys = request.Products.Select(item => new PromotionItem
            {
                PromotionId = promotion.Id,
                ProductId = item.ProductId,
                Quantity = item.Quantity,
                IsGift = false
            }).ToList();

            var productGifts = request.Gifts.Select(gift => new PromotionItem
            {
                PromotionId = promotion.Id,
                ProductId = gift.ProductId,
                Quantity = gift.Quantity,
                IsGift = true
            }).ToList();

            // Xóa sản phẩm không còn
            var itemsToRemove = currentPromotionItems
                .Where(pi => !productBuys.Any(n => n.ProductId == pi.ProductId) ||
                             !productGifts.Any(g => g.ProductId == pi.ProductId))
                .ToList();
            _promotionItemRepo.DeleteRange(itemsToRemove);

            // Cập nhật hoặc thêm sản phẩm và sản phẩm tặng
            await UpdateOrAddItems(productBuys, currentPromotionItems, false); // Thêm sản phẩm
            await UpdateOrAddItems(productGifts, currentPromotionItems, true);  // Thêm sản phẩm tặng

            return await base.UpdateAsync(promotion);
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var existingPromotion = await GetByIdAsync(id);
            if (existingPromotion == null)
            {
                throw new Exception("Promotion not found");
            }

            // xóa những sản phẩm trong khuyến mãi
            var existingPromotionItems = await _promotionItemRepo.AsQueryable().Where(x => x.PromotionId == id).ToListAsync(); // new QueryBuilder<PromotionItem>().And(p => p.PromotionId == existingPromotion.Id));
            _promotionItemRepo.DeleteRange(existingPromotionItems);

            return await base.DeleteAsync(existingPromotion);
        }

        public async Task<List<ProductResponse>> GetProductGiftByProductId(string productId)
        {
            // Lấy danh sách ID các chương trình khuyến mãi đang hoạt động
            var _promotionItemQuery = _promotionItemRepo.AsQueryable();

            var activePromotionIds = await _repository.AsQueryable()
                .Where(p => p.IsActive && p.StartDate <= DateTime.Now && p.ExpiryDate >= DateTime.Now && _promotionItemQuery.Any(x => x.ProductId == productId))
                .Select(p => p.Id)
                .ToListAsync();

            if (!activePromotionIds.Any()) return new List<ProductResponse>();

            // Lấy danh sách sản phẩm tặng trong các chương trình khuyến mãi đang hoạt động
            var products = await _productRepo.AsQueryable()
                .Where(p => _promotionItemQuery.Any(pi => pi.IsGift && activePromotionIds.Contains(pi.PromotionId) && pi.ProductId == p.Id))
                .ToListAsync();

            return products.Select(gift => new ProductResponse
            {
                Id = gift.Id,
                Name = gift.Name,
                OriginalPrice = gift.Price,
                DiscountPrice = 0, // Nếu cần thiết
                Images = string.IsNullOrEmpty(gift.Images)
                    ? new List<string>()
                    : gift.Images.Split(",").Select(x =>
                    {
                        var trimmed = x.Trim();
                        if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                            return trimmed;
                        else
                            return $"{hostUrl}/uploads/images/products/{trimmed}";
                    }).ToList()
            }).ToList();
        }

        public async Task<List<ProductResponse>> GetProductBuyByPromotionId(string promotionId)
        {
            var products = await _productRepo.AsQueryable()
               .Where(p => _promotionItemRepo.AsQueryable()
                   .Any(pi => !pi.IsGift && pi.PromotionId == promotionId && pi.ProductId == p.Id))
               .ToListAsync();

            return products.Select(gift => new ProductResponse
            {
                Id = gift.Id,
                Name = gift.Name,
                OriginalPrice = gift.Price,
                DiscountPrice = 0, // Nếu cần thiết
                Images = string.IsNullOrEmpty(gift.Images)
                   ? new List<string>()
                   : gift.Images.Split(",").Select(x =>
                    {
                        var trimmed = x.Trim();
                        if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                            return trimmed;
                        else
                            return $"{hostUrl}/uploads/images/products/{trimmed}";
                    }).ToList()
            }).ToList();
        }

        public async Task<List<ProductResponse>> GetProductGiftByPromotionId(string promotionId)
        {
            var products = await _productRepo.AsQueryable()
               .Where(p => _promotionItemRepo.AsQueryable()
                   .Any(pi => pi.IsGift && pi.PromotionId == promotionId && pi.ProductId == p.Id))
               .ToListAsync();

            return products.Select(gift => new ProductResponse
            {
                Id = gift.Id,
                Name = gift.Name,
                OriginalPrice = gift.Price,
                DiscountPrice = 0, // Nếu cần thiết
                Images = string.IsNullOrEmpty(gift.Images)
                   ? new List<string>()
                   : gift.Images.Split(",").Select(x =>
                    {
                        var trimmed = x.Trim();
                        if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                            return trimmed;
                        else
                            return $"{hostUrl}/uploads/images/products/{trimmed}";
                    }).ToList()
            }).ToList();
        }

        private Task UpdateOrAddItems(IEnumerable<PromotionItem> newItems, List<PromotionItem> currentItems, bool isGift)
        {
            foreach (var newItem in newItems)
            {
                var existingItem = currentItems
                    .FirstOrDefault(pi => pi.ProductId == newItem.ProductId && pi.IsGift == isGift);

                if (existingItem != null)
                {
                    existingItem.Quantity = newItem.Quantity; // Cập nhật số lượng
                    _promotionItemRepo.Update(existingItem);
                }
                else
                {
                    _promotionItemRepo.Add(newItem); // Thêm sản phẩm mới
                }
            }
            return Task.CompletedTask;
        }
    }
}
