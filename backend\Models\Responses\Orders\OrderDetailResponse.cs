﻿using MiniAppCore.Entities.Memberships;

namespace MiniAppCore.Models.Responses.Orders
{
    public class OrderDetailResponse : OrderResponse
    {
        public long PointUsage { get; set; }
        public List<OrderItemResponse> OrderDetails { get; set; } = new List<OrderItemResponse>();
        public List<OrderVoucherRepsone> Vouchers { get; set; } = new List<OrderVoucherRepsone>();
        public MembershipVAT? Vat { get; set; } 
    }

    public class OrderItemResponse
    {
        public long Quantity { get; set; }
        public string? Note { get; set; }
        public string? ProductId { get; set; }
        public string? ProductName { get; set; }
        public decimal OriginalPrice { get; set; } // Giá gốc
        public decimal DiscountPrice { get; set; } // Giá sau giảm
        public bool IsDiscounted => DiscountPrice < OriginalPrice;
        public List<string> Images { get; set; } = new List<string>();
        public List<string> PropertyValues { get; set; } = new List<string>();
        public List<OrderItemResponse> Gifts { get; set; } = new List<OrderItemResponse>(); // danh sách quà tặng
    }

    public class OrderVoucherRepsone
    {
        public string? VoucherId { get; set; }
        public string? VoucherCode { get; set; }
        public string? VoucherName { get; set; }
    }
}
