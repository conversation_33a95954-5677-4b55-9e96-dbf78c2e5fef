﻿namespace MiniAppCore.Models.DTOs.LuckyWheels
{
    public class LuckyWheelDTO
    {
        public string? Id { get; set; }
        public bool IsActive { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public long RequiredPoints { get; set; }

        public string? ImageUrl { get; set; }
        public IFormFile? Image { get; set; }

        public DateTime StartDate { get; set; } = DateTime.Now;
        public DateTime ExpiryDate { get; set; } = DateTime.Now.AddDays(1);

        // Auto add spin points configuration
        public bool EnableAutoSpinPoints { get; set; } = false; // Có tự động cộng lượt chơi không
        public long SpinPointsToAdd { get; set; } = 0; // Số điểm spin sẽ cộng cho mỗi membership

        public string? GamePrizesString { get; set; }
        public List<GamePrizeConfigDTO> GamePrizes { get; set; } = new List<GamePrizeConfigDTO>(); // Danh sách giải thưởng
    }

    public class GamePrizeConfigDTO
    {
        public string? GamePrizeConfigId { get; set; }
        public string? GamePrizeName { get; set; }
        public required string GamePrizeId { get; set; }

        public float WinRate { get; set; }
        public short Ranking { get; set; }

        public int Position { get; set; }
        public int Quantity { get; set; }
        public int DailyLimit { get; set; }
    }
}
