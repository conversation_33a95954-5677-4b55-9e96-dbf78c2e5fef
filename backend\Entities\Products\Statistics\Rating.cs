﻿using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Products.Statistics
{
    public class Rating
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();

        public string? Images { get; set; }
        public string? Videos { get; set; }

        public int Point { get; set; }
        public string? Comment { get; set; }

        public string? UserUserZaloId { get; set; }
        public string? OrderId { get; set; }
        public string? ProductId { get; set; }
        public string? VariantId { get; set; }

        public bool IsAvailable { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
