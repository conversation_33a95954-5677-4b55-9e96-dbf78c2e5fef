﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Entities.Products;
using MiniAppCore.Entities.Products.ProductInfo;
using MiniAppCore.Entities.Products.Variants;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Queries;
using MiniAppCore.Services.Categories;
using OfficeOpenXml;

namespace MiniAppCore.Services.Products.ExportImports
{
    public class ExportImportProductService(IUnitOfWork unitOfWork, ICategoryService categoryService) : IExportImportProductService
    {
        private readonly IRepository<Product> _repository = unitOfWork.GetRepository<Product>();

        #region Export Products

        public async Task<byte[]> ExportProductsAsync(ProductQueryParams? queryParams)
        {
            // Start with a queryable
            var query = _repository.AsQueryable();
            var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();

            // Apply filters if query params are provided
            if (queryParams != null)
            {
                // Apply search filter
                if (!string.IsNullOrEmpty(queryParams.Keyword))
                {
                    query = query.Where(p => p.Name.Contains(queryParams.Keyword) ||
                                             p.Description.Contains(queryParams.Keyword));
                }

                // Apply category filter
                if (!string.IsNullOrEmpty(queryParams.CategoryId))
                {
                    var lstCategory = queryParams.CategoryId.Split(",").ToList();
                    query = query.Where(p => lstCategory.All(catId =>
                        productCategoryRepo.AsQueryable().Any(pc =>
                            pc.ProductId == p.Id && pc.CategoryId == catId
                        )
                    ));
                }

                // Apply price range filter
                if (queryParams.MinPrice.HasValue)
                {
                    query = query.Where(p => p.Price >= queryParams.MinPrice.Value);
                }

                if (queryParams.MaxPrice.HasValue)
                {
                    query = query.Where(p => p.Price <= queryParams.MaxPrice.Value);
                }

                // Apply stock status filter
                if (queryParams.StockStatus != null && queryParams.StockStatus.Any())
                {
                    query = query.Where(p => queryParams.StockStatus.Contains(p.Status));
                }

                // Apply IsGift filter
                if (queryParams.IsGift.HasValue)
                {
                    query = query.Where(p => p.IsGift == queryParams.IsGift.Value);
                }

                // Apply branch filter if applicable
                if (queryParams.BranchIds != null && queryParams.BranchIds.Any())
                {
                    var stockRepo = unitOfWork.GetRepository<ProductStock>();
                    query = query.Where(p => stockRepo.AsQueryable().Any(st => st.ProductId == p.Id && st.BranchId == queryParams.BranchId));
                    if (queryParams != null && queryParams.StockStatus.Any())
                    {
                        query = query.Where(p => queryParams.StockStatus.Contains(p.Status));
                    }
                }
            }

            // Load filtered products
            var products = await query.ToListAsync();

            // Continue with the rest of your existing code to load related data
            // Get variants separately and group by product ID
            var variantRepo = unitOfWork.GetRepository<Variant>();
            var variants = await variantRepo.AsQueryable().ToListAsync();
            var variantsByProductId = variants.GroupBy(v => v.ProductId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Get variant values separately and group by variant ID
            var variantValueRepo = unitOfWork.GetRepository<VariantValue>();
            var variantValues = await variantValueRepo.AsQueryable().ToListAsync();
            var valuesByVariantId = variantValues.GroupBy(vv => vv.VariantId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Get product categories separately and group by product ID
            var productCategories = await productCategoryRepo.AsQueryable().ToListAsync();
            var categoriesByProductId = productCategories.GroupBy(pc => pc.ProductId)
                .ToDictionary(g => g.Key, g => g.ToList());

            // Get categories for lookup
            var categoryRepo = unitOfWork.GetRepository<Category>();
            var categories = await categoryRepo.AsQueryable().ToListAsync();
            var categoryDict = categories.ToDictionary(c => c.Id, c => c.Name);

            // Get all properties and property values for lookup
            var propertyRepo = unitOfWork.GetRepository<Property>();
            var properties = await propertyRepo.AsQueryable().ToListAsync();
            var propertyDict = properties.ToDictionary(p => p.Id, p => p.Name);

            var propertyValueRepo = unitOfWork.GetRepository<PropertyValue>();
            var propertyValues = await propertyValueRepo.AsQueryable().ToListAsync();
            var propertyValueDict = propertyValues.ToDictionary(pv => pv.Id, pv => new PropertyValue { PropertyId = pv.PropertyId, Value = pv.Value });

            // Generate Excel file using the collected data
            return GenerateExcelFile(products, variantsByProductId, valuesByVariantId,
                categoriesByProductId, categoryDict, propertyDict, propertyValueDict);
        }

        private byte[] GenerateExcelFile(
            List<Product> products,
            Dictionary<string, List<Variant>> variantsByProductId,
            Dictionary<string, List<VariantValue>> valuesByVariantId,
            Dictionary<string, List<ProductCategory>> categoriesByProductId,
            Dictionary<string, string> categoryDict,
            Dictionary<string, string> propertyDict,
            Dictionary<string, PropertyValue> propertyValueDict)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Products");

            // Set up headers
            worksheet.Cells[1, 1].Value = "Tên sản phẩm";
            worksheet.Cells[1, 2].Value = "Tên thuộc tính 1";
            worksheet.Cells[1, 3].Value = "Giá trị thuộc tính 1";
            worksheet.Cells[1, 4].Value = "Tên thuộc tính 2";
            worksheet.Cells[1, 5].Value = "Giá trị thuộc tính 2";
            worksheet.Cells[1, 6].Value = "Tên thuộc tính 3";
            worksheet.Cells[1, 7].Value = "Giá trị thuộc tính 3";
            worksheet.Cells[1, 8].Value = "Giá";
            worksheet.Cells[1, 9].Value = "Tồn kho";
            worksheet.Cells[1, 10].Value = "Ảnh (URL, phân cách bằng dấu phẩy)";
            worksheet.Cells[1, 11].Value = "Danh mục (phân cách bằng dấu phẩy)";
            worksheet.Cells[1, 12].Value = "Mô tả";

            // Format headers
            using (var range = worksheet.Cells[1, 1, 1, 12])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            int row = 2;

            // Add product data
            foreach (var product in products)
            {
                // Get product categories
                string categoriesString = GetCategoriesString(product.Id, categoriesByProductId, categoryDict);

                // Check if product has variants
                if (variantsByProductId.TryGetValue(product.Id, out var productVariants) && productVariants.Any())
                {
                    // Process each variant
                    foreach (var variant in productVariants)
                    {
                        row = AddVariantToWorksheet(
                            worksheet, row, product, variant,
                            valuesByVariantId, propertyDict, propertyValueDict, categoriesString);
                    }
                }
                else
                {
                    // Process product without variants
                    row = AddProductToWorksheet(worksheet, row, product, categoriesString);
                }
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            return package.GetAsByteArray();
        }

        private string GetCategoriesString(
            string productId,
            Dictionary<string, List<ProductCategory>> categoriesByProductId,
            Dictionary<string, string> categoryDict)
        {
            if (!categoriesByProductId.TryGetValue(productId, out var productCategoryList))
                return string.Empty;

            var categoryNames = new List<string>();
            foreach (var pc in productCategoryList)
            {
                if (categoryDict.TryGetValue(pc.CategoryId, out var categoryName))
                {
                    categoryNames.Add(categoryName);
                }
            }

            return string.Join(", ", categoryNames);
        }

        private int AddVariantToWorksheet(
            ExcelWorksheet worksheet,
            int row,
            Product product,
            Variant variant,
            Dictionary<string, List<VariantValue>> valuesByVariantId,
            Dictionary<string, string> propertyDict,
            Dictionary<string, PropertyValue> propertyValueDict,
            string categoriesString)
        {
            // Get variant values
            var variantValueList = valuesByVariantId.TryGetValue(variant.Id, out var values)
                ? values : new List<VariantValue>();

            // Sort and limit to 3 properties
            var sortedVariantValues = variantValueList
                .OrderBy(vv => vv.PropertyId)
                .Take(3)
                .ToList();

            // Fill in product name
            worksheet.Cells[row, 1].Value = product.Name;

            // Initialize all property cells with empty strings
            for (int i = 0; i < 3; i++)
            {
                worksheet.Cells[row, 2 + i * 2].Value = string.Empty; // Property name
                worksheet.Cells[row, 3 + i * 2].Value = string.Empty; // Property value
            }

            // Fill in variant properties
            for (int i = 0; i < sortedVariantValues.Count; i++)
            {
                var vv = sortedVariantValues[i];
                if (propertyDict.TryGetValue(vv.PropertyId, out var propName) &&
                    propertyValueDict.TryGetValue(vv.PropertyValueId, out var propValueInfo))
                {
                    worksheet.Cells[row, 2 + i * 2].Value = propName;
                    worksheet.Cells[row, 3 + i * 2].Value = propValueInfo.Value;
                }
            }

            // Fill in variant details
            worksheet.Cells[row, 8].Value = variant.Price;
            worksheet.Cells[row, 9].Value = variant.Quantity;
            worksheet.Cells[row, 10].Value = !string.IsNullOrEmpty(product.Images) ? product.Images : string.Empty;
            worksheet.Cells[row, 11].Value = categoriesString;
            worksheet.Cells[row, 12].Value = !string.IsNullOrEmpty(product.Description) ? product.Description : string.Empty;

            return row + 1;
        }

        private int AddProductToWorksheet(
            ExcelWorksheet worksheet,
            int row,
            Product product,
            string categoriesString)
        {
            // Fill in product name
            worksheet.Cells[row, 1].Value = product.Name;

            // Set all property cells to empty
            for (int i = 0; i < 3; i++)
            {
                worksheet.Cells[row, 2 + i * 2].Value = string.Empty;
                worksheet.Cells[row, 3 + i * 2].Value = string.Empty;
            }

            // Fill in basic product details
            worksheet.Cells[row, 8].Value = product.Price;
            worksheet.Cells[row, 9].Value = 0; // No specific stock for non-variant products
            worksheet.Cells[row, 10].Value = !string.IsNullOrEmpty(product.Images) ? product.Images : string.Empty;
            worksheet.Cells[row, 11].Value = categoriesString;
            worksheet.Cells[row, 12].Value = !string.IsNullOrEmpty(product.Description) ? product.Description : string.Empty;

            return row + 1;
        }

        #endregion

        #region Import Products

        public async Task<int> ImportProductsAsync(IFormFile file)
        {
            // Validate file is not null
            if (file == null || file.Length == 0)
                throw new CustomException("No file was uploaded.");

            // Check file extension (allow only Excel files)
            var fileExt = Path.GetExtension(file.FileName).ToLower();
            if (fileExt != ".xlsx" && fileExt != ".xls")
                throw new CustomException("Only Excel files (.xlsx or .xls) are allowed.");

            var listProduct = new List<Product>();
            var productCategories = new Dictionary<string, List<string>>(); // Product ID -> Category IDs

            // Get all categories for lookup
            var allCategories = await categoryService.GetAllAsync();
            var categoryNameToId = allCategories.ToDictionary(
                c => c.Name.ToLower().Trim(), // Use lowercase to make comparison case-insensitive
                c => c.Id,
                StringComparer.OrdinalIgnoreCase
            );

            // Load existing properties from database
            var propertyRepo = unitOfWork.GetRepository<Property>();
            var existingProperties = await propertyRepo.AsQueryable().ToListAsync();
            var propertyCache = existingProperties.ToDictionary(
                p => p.Name.ToLower().Trim(),
                p => p,
                StringComparer.OrdinalIgnoreCase
            );

            // Load existing property values
            var propertyValueRepo = unitOfWork.GetRepository<PropertyValue>();
            var existingPropertyValues = await propertyValueRepo.AsQueryable().ToListAsync();
            var propertyValueCache = new Dictionary<string, Dictionary<string, string>>(StringComparer.OrdinalIgnoreCase);

            // Group property values by property ID and value
            foreach (var pv in existingPropertyValues)
            {
                var property = existingProperties.FirstOrDefault(p => p.Id == pv.PropertyId);
                if (property != null)
                {
                    var propName = property.Name.ToLower().Trim();
                    if (!propertyValueCache.ContainsKey(propName))
                    {
                        propertyValueCache[propName] = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                    }
                    propertyValueCache[propName][pv.Value.ToLower().Trim()] = pv.Id;
                }
            }

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0; // Reset position to the beginning of the stream

                using (var package = new ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null)
                        throw new CustomException("The Excel file does not contain any worksheets.");

                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    if (rowCount <= 1) // Only header row or empty
                        throw new CustomException("The Excel file does not contain any product data.");

                    // Track products by name to group variants
                    var productsByName = new Dictionary<string, Product>(StringComparer.OrdinalIgnoreCase);
                    var variantsByProductId = new Dictionary<string, List<ProductVariant>>();

                    // First pass: scan unique product names (not variants)
                    var uniqueProductNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                    const int maxProductLimit = 100;

                    for (int row = 2; row <= rowCount; row++)
                    {
                        var productName = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                        if (!string.IsNullOrEmpty(productName) && !uniqueProductNames.Contains(productName))
                        {
                            uniqueProductNames.Add(productName);
                        }
                    }

                    // Check product limit before processing
                    if (uniqueProductNames.Count > maxProductLimit)
                        throw new CustomException(400,
                            $"Chỉ được import tối đa {maxProductLimit} sản phẩm cơ bản mỗi lần (không tính biến thể). " +
                            $"File của bạn có {uniqueProductNames.Count} sản phẩm.");

                    // Start from row 2 (assuming row 1 is the header)
                    for (int row = 2; row <= rowCount; row++)
                    {
                        var productName = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                        var prop1Name = worksheet.Cells[row, 2].Value?.ToString()?.Trim();
                        var prop1Value = worksheet.Cells[row, 3].Value?.ToString()?.Trim();
                        var prop2Name = worksheet.Cells[row, 4].Value?.ToString()?.Trim();
                        var prop2Value = worksheet.Cells[row, 5].Value?.ToString()?.Trim();
                        var prop3Name = worksheet.Cells[row, 6].Value?.ToString()?.Trim();
                        var prop3Value = worksheet.Cells[row, 7].Value?.ToString()?.Trim();

                        var priceString = worksheet.Cells[row, 8].Value?.ToString();
                        var stockString = worksheet.Cells[row, 9].Value?.ToString();
                        var imageLinks = worksheet.Cells[row, 10].Value?.ToString()?.Trim();
                        var categories = worksheet.Cells[row, 11].Value?.ToString()?.Trim();
                        var description = worksheet.Cells[row, 12].Value?.ToString()?.Trim();

                        // Skip row if name is empty (required field)
                        if (string.IsNullOrEmpty(productName))
                            continue;

                        // Parse price with culture-invariant format
                        if (!decimal.TryParse(priceString, System.Globalization.NumberStyles.Any,
                            System.Globalization.CultureInfo.InvariantCulture, out decimal price))
                            price = 0; // Default price if parsing fails

                        // Parse stock quantity
                        if (!int.TryParse(stockString, out int stock))
                            stock = 0; // Default stock if parsing fails

                        // Create or get product
                        Product product;
                        if (!productsByName.TryGetValue(productName, out product))
                        {
                            // Create new product
                            product = new Product
                            {
                                Id = Guid.NewGuid().ToString("N"),
                                Name = productName,
                                Description = description ?? string.Empty,
                                Price = price, // Base price (can be overridden by variants)
                                Status = Enums.EProduct.InStock,
                                IsGift = false,
                                IsGlobalProduct = true,
                                LikeCount = 0,
                                ViewCount = 0,
                                BoughtCount = 0,
                                ReviewCount = 0,
                                ReviewPoint = 0,
                                Images = !string.IsNullOrWhiteSpace(imageLinks) ? imageLinks : string.Empty
                            };

                            productsByName[productName] = product;
                            listProduct.Add(product);
                            variantsByProductId[product.Id] = new List<ProductVariant>();

                            // Process categories if provided
                            if (!string.IsNullOrWhiteSpace(categories))
                            {
                                var categoryList = new List<string>();
                                var categoryNameList = categories.Split(',')
                                    .Select(c => c.Trim().ToLower())
                                    .Where(c => !string.IsNullOrWhiteSpace(c));

                                foreach (var catName in categoryNameList)
                                {
                                    if (categoryNameToId.TryGetValue(catName, out var categoryId))
                                    {
                                        categoryList.Add(categoryId);
                                    }
                                }

                                if (categoryList.Any())
                                {
                                    productCategories[product.Id] = categoryList;
                                }
                            }
                        }

                        // Process variant if any properties are specified
                        if (!string.IsNullOrWhiteSpace(prop1Name) && !string.IsNullOrWhiteSpace(prop1Value))
                        {
                            var variant = new ProductVariant
                            {
                                Price = price,
                                Stock = stock,
                                Properties = new List<VariantProperty>()
                            };

                            // Process property 1
                            AddVariantProperty(variant, prop1Name, prop1Value, propertyCache, propertyValueCache);

                            // Process property 2 if provided
                            if (!string.IsNullOrWhiteSpace(prop2Name) && !string.IsNullOrWhiteSpace(prop2Value))
                            {
                                AddVariantProperty(variant, prop2Name, prop2Value, propertyCache, propertyValueCache);
                            }

                            // Process property 3 if provided
                            if (!string.IsNullOrWhiteSpace(prop3Name) && !string.IsNullOrWhiteSpace(prop3Value))
                            {
                                AddVariantProperty(variant, prop3Name, prop3Value, propertyCache, propertyValueCache);
                            }

                            variantsByProductId[product.Id].Add(variant);
                        }
                    }

                    // Save data to database efficiently
                    await SaveImportData(listProduct, propertyCache.Values, propertyValueCache,
                        productCategories, variantsByProductId);

                    return listProduct.Count;
                }
            }
        }

        private async Task SaveImportData(
            List<Product> products,
            IEnumerable<Property> properties,
            Dictionary<string, Dictionary<string, string>> propertyValueCache,
            Dictionary<string, List<string>> productCategories,
            Dictionary<string, List<ProductVariant>> variantsByProductId)
        {
            // Save properties first
            // await SaveProperties(properties);

            // Save property values
            await SavePropertyValues(propertyValueCache);

            // Save products
            _repository.AddRange(products);
            await unitOfWork.SaveChangesAsync();

            // Save product categories
            if (productCategories.Any())
            {
                await SaveProductCategories(productCategories);
            }

            // Save variants
            await SaveProductVariants(variantsByProductId);
        }

        private void AddVariantProperty(
            ProductVariant variant,
            string propName,
            string propValue,
            Dictionary<string, Property> propertyCache,
            Dictionary<string, Dictionary<string, string>> propertyValueCache)
        {
            var propNameLower = propName.ToLower().Trim();
            var propValueLower = propValue.ToLower().Trim();

            // Get property (but don't create if doesn't exist)
            if (!propertyCache.TryGetValue(propNameLower, out var property))
            {
                // Skip this property as it doesn't exist in the database
                return;
            }

            // Create or get property value
            if (!propertyValueCache.ContainsKey(propNameLower))
            {
                propertyValueCache[propNameLower] = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            }

            if (!propertyValueCache[propNameLower].TryGetValue(propValueLower, out var propertyValueId))
            {
                // This is a new value for an existing property
                propertyValueId = Guid.NewGuid().ToString("N");
                propertyValueCache[propNameLower][propValueLower] = propertyValueId;
            }

            // Add to variant
            variant.Properties.Add(new VariantProperty
            {
                PropertyId = property.Id,
                PropertyValueId = propertyValueId
            });
        }

        private async Task SaveProperties(IEnumerable<Property> properties)
        {
            var propertyRepo = unitOfWork.GetRepository<Property>();
            var propertyList = properties.ToList();

            if (propertyList.Any())
            {
                propertyRepo.AddRange(propertyList);
                await unitOfWork.SaveChangesAsync();
            }
        }

        private async Task SavePropertyValues(Dictionary<string, Dictionary<string, string>> propertyValueCache)
        {
            if (!propertyValueCache.Any())
                return;

            var propertyRepo = unitOfWork.GetRepository<Property>();
            var propertyValueRepo = unitOfWork.GetRepository<PropertyValue>();
            var allPropertyValues = new List<PropertyValue>();

            // Get existing property values to avoid duplicates
            var existingPropertyValues = await propertyValueRepo.AsQueryable().ToListAsync();
            var existingValueIds = existingPropertyValues.Select(pv => pv.Id).ToHashSet();

            foreach (var propEntry in propertyValueCache)
            {
                var propertyName = propEntry.Key;
                var property = await propertyRepo.AsQueryable()
                    .FirstOrDefaultAsync(p => p.Name.ToLower() == propertyName.ToLower());

                if (property != null)
                {
                    foreach (var valueEntry in propEntry.Value)
                    {
                        // Only add property values that don't already exist
                        if (!existingValueIds.Contains(valueEntry.Value))
                        {
                            allPropertyValues.Add(new PropertyValue
                            {
                                Id = valueEntry.Value,
                                PropertyId = property.Id,
                                Value = valueEntry.Key
                            });
                        }
                    }
                }
            }

            if (allPropertyValues.Any())
            {
                propertyValueRepo.AddRange(allPropertyValues);
                await unitOfWork.SaveChangesAsync();
            }
        }

        private async Task SaveProductCategories(Dictionary<string, List<string>> productCategories)
        {
            var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();
            var productCategoryEntities = new List<ProductCategory>();

            foreach (var entry in productCategories)
            {
                foreach (var categoryId in entry.Value)
                {
                    productCategoryEntities.Add(new ProductCategory
                    {
                        ProductId = entry.Key,
                        CategoryId = categoryId
                    });
                }
            }

            if (productCategoryEntities.Any())
            {
                productCategoryRepo.AddRange(productCategoryEntities);
                await unitOfWork.SaveChangesAsync();
            }
        }

        private async Task SaveProductVariants(Dictionary<string, List<ProductVariant>> variantsByProductId)
        {
            var variantRepo = unitOfWork.GetRepository<Variant>();
            var variantValueRepo = unitOfWork.GetRepository<VariantValue>();
            var allVariants = new List<Variant>();
            var allVariantValues = new List<KeyValuePair<string, List<VariantProperty>>>();

            // First, create all variants with their IDs
            foreach (var productEntry in variantsByProductId)
            {
                var productId = productEntry.Key;
                foreach (var variantData in productEntry.Value)
                {
                    var variantId = Guid.NewGuid().ToString("N");

                    var variant = new Variant
                    {
                        Id = variantId,
                        ProductId = productId,
                        Price = variantData.Price,
                        Quantity = variantData.Stock,
                        Status = Enums.EProduct.InStock // Always set to InStock
                    };

                    allVariants.Add(variant);
                    allVariantValues.Add(new KeyValuePair<string, List<VariantProperty>>(variantId, variantData.Properties));
                }
            }

            // Save all variants in one batch
            if (allVariants.Any())
            {
                variantRepo.AddRange(allVariants);
                await unitOfWork.SaveChangesAsync();
            }

            // Then create all variant values
            var variantValuesList = new List<VariantValue>();
            foreach (var pair in allVariantValues)
            {
                string variantId = pair.Key;
                var properties = pair.Value;

                foreach (var prop in properties)
                {
                    variantValuesList.Add(new VariantValue
                    {
                        VariantId = variantId,
                        PropertyId = prop.PropertyId,
                        PropertyValueId = prop.PropertyValueId
                    });
                }
            }

            // Save all variant values in one batch
            if (variantValuesList.Any())
            {
                variantValueRepo.AddRange(variantValuesList);
                await unitOfWork.SaveChangesAsync();
            }
        }

        #endregion
    }

    #region Helper Classes

    internal class ProductVariant
    {
        public decimal Price { get; set; }
        public int Stock { get; set; }
        public List<VariantProperty> Properties { get; set; } = new();
    }

    internal class VariantProperty
    {
        public string PropertyId { get; set; }
        public string PropertyValueId { get; set; }
    }

    #endregion
}
