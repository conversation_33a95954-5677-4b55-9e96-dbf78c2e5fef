﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableOrder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "PaymentMethod",
                table: "Orders",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEBSmpdo1nkxChADeB9dxwQ4Vf7YnuMqk3UyTWPjbBD67Iu7ETGPsGehxKiEAGa+d6A==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PaymentMeth<PERSON>",
                table: "Orders");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEMFtEsDrhIPIHRWIFMJiWEa1ObXTP4r0pFzQovIyTLd7bdQnpL8H71y2l3+yy5XzkQ==");
        }
    }
}
