﻿using AutoMapper;
using MiniAppCore.Entities.Articles;
using MiniAppCore.Entities.Bookings;
using MiniAppCore.Entities.Branches;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Entities.ETM;
using MiniAppCore.Entities.Events;
using MiniAppCore.Entities.Events.Sponsors;
using MiniAppCore.Entities.Events.SponsorshipTiers;
using MiniAppCore.Entities.FormCustoms;
using MiniAppCore.Entities.LuckyWheels;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Offers;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Entities.Offers.Vouchers;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Entities.Products;
using MiniAppCore.Entities.Products.Brands;
using MiniAppCore.Entities.Products.Statistics;
using MiniAppCore.Entities.Products.Variants;
using MiniAppCore.Entities.Settings;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Enums;
using MiniAppCore.Models.DTOs;
using MiniAppCore.Models.DTOs.Events;
using MiniAppCore.Models.DTOs.Events.Sponsors;
using MiniAppCore.Models.DTOs.InvoiceTemplates;
using MiniAppCore.Models.DTOs.LuckyWheels;
using MiniAppCore.Models.DTOs.Products;
using MiniAppCore.Models.DTOs.Promotions;
using MiniAppCore.Models.DTOs.Properties;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Models.Requests.Articles;
using MiniAppCore.Models.Requests.BookingItems;
using MiniAppCore.Models.Requests.Bookings;
using MiniAppCore.Models.Requests.Branches;
using MiniAppCore.Models.Requests.Brands;
using MiniAppCore.Models.Requests.Categories;
using MiniAppCore.Models.Requests.GamePrizes;
using MiniAppCore.Models.Requests.Memberships;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Models.Requests.OmniTools;
using MiniAppCore.Models.Requests.Orders;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Models.Requests.Ranks;
using MiniAppCore.Models.Responses.Articles;
using MiniAppCore.Models.Responses.BookingItem;
using MiniAppCore.Models.Responses.Bookings;
using MiniAppCore.Models.Responses.Categories;
using MiniAppCore.Models.Responses.Events;
using MiniAppCore.Models.Responses.Gamification.GamePrizes;
using MiniAppCore.Models.Responses.Gamification.LuckyWheels;
using MiniAppCore.Models.Responses.Memberships;
using MiniAppCore.Models.Responses.Orders;
using MiniAppCore.Models.Responses.Orders.Carts;
using MiniAppCore.Models.Responses.Products;
using MiniAppCore.Models.Responses.Products.Ratings;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Models.Responses.Vouchers;

namespace MiniAppCore.Base.Common
{
    public class Mapper : Profile
    {
        public Mapper()
        {
            #region Article

            CreateMap<ArticleRequest, Article>()
                .ForMember(x => x.Id, opt => opt.Ignore())
                .ForMember(x => x.Images, opt => opt.Ignore())
                .ForMember(x => x.BannerImage, opt => opt.Ignore());

            CreateMap<Article, ArticleResponse>()
               .ForMember(x => x.Images, opt => opt.Ignore())
               .ForMember(x => x.BannerImage, opt => opt.Ignore());

            #endregion

            #region Branch

            CreateMap<BranchRequest, Branch>()
                .ForMember(x => x.Id, opt => opt.Ignore())
                .ForMember(x => x.Image, opt => opt.Ignore());

            #endregion

            #region Product

            // create or update product 
            CreateMap<ProductRequest, Product>()
                 .ForMember(x => x.Images, opt => opt.Ignore());

            // product respponse
            CreateMap<Product, ProductResponse>()
                .ForMember(x => x.Images, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.OriginalPrice, opt => opt.MapFrom(src => src.Price))
                .ForMember(dest => dest.DiscountPrice, opt => opt.MapFrom(src => src.Price));
            //.ForMember(dest => dest.Images, opt => opt.MapFrom(src => ConvertImages(src.Images)));

            CreateMap<Product, ProductDetailResponse>()
                .ForMember(x => x.Images, opt => opt.Ignore())
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.OriginalPrice, opt => opt.MapFrom(src => src.Price))
                .ForMember(dest => dest.DiscountPrice, opt => opt.MapFrom(src => src.Price));
            //.ForMember(dest => dest.Images, opt => opt.MapFrom(src => ConvertImages(src.Images)));

            CreateMap<PropertyDTO, Property>()
                .ForMember(x => x.Id, opt => opt.Ignore());

            #endregion

            #region Booking

            // Create or Update Booking
            CreateMap<BookingRequest, Booking>()
                .ForMember(x => x.Status, opt => opt.MapFrom(src => (EBooking)src.Status));

            // Booking Detail response
            CreateMap<(Booking booking, Membership membership, Branch branch), BookingResponse>()
                .ConstructUsing(src => new BookingResponse
                {
                    Id = src.booking.Id,
                    Status = (short)src.booking.Status,
                    UserZaloId = src.membership.UserZaloId,
                    Note = src.booking.Note,
                    BookingDate = src.booking.BookingDate,
                    UserZaloName = !string.IsNullOrEmpty(src.booking.Name) ? src.booking.Name : src.membership.UserZaloName,
                    PhoneNumber = src.membership.PhoneNumber,
                    CreatedDate = src.booking.CreatedDate,
                    Branch = src.branch
                });

            #endregion

            #region Booking Item

            // Booking Item
            CreateMap<BookingItemRequest, BookingItem>()
                .ForMember(dest => dest.Images, opt => opt.Ignore())
                .ForMember(dest => dest.BookedCount, opt => opt.MapFrom(src => src.BoughtCount));

            // Booking Item Response
            CreateMap<BookingItem, BookingItemResponse>()
                .ForMember(dest => dest.Images, opt => opt.Ignore())
                //.ForMember(dest => dest.Description, opt => opt.MapFrom(src => Regex.Replace(src.Description ?? string.Empty, "<.*?>", "")))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (short)src.Status))
                .ForMember(dest => dest.OriginalPrice, opt => opt.MapFrom(src => src.Price));

            #endregion

            #region Order

            // Create or Update Order
            CreateMap<OrderRequest, Order>()
                .ForMember(dst => dst.DeliveryAddress, opt => opt.MapFrom(src => src.Address))
                .ForMember(dst => dst.OrderStatus, opt => opt.MapFrom(src => (EOrder)src.OrderStatus))
                .ForMember(dst => dst.PaymentStatus, opt => opt.MapFrom(src => (EPayment)src.PaymentStatus));

            CreateMap<AdminCreateOrderRequset, Order>()
                .ForMember(dst => dst.DeliveryAddress, opt => opt.MapFrom(src => src.Address))
                .ForMember(dst => dst.PaymentMethod, opt => opt.MapFrom(src => src.PaymentMethod));

            // Create or Update Order Detail
            CreateMap<OrderDetailRequest, OrderDetail>();

            // Order Detail Mapping
            CreateMap<(Order order, Membership membership), OrderResponse>()
                .ConstructUsing(src => new OrderResponse
                {
                    Note = src.order.Note,
                    OrderId = src.order.Id,
                    TotalAmount = src.order.Total,
                    DiscountAmount = src.order.DiscountAmount,
                    UserZaloId = src.membership != null ? src.membership.UserZaloId : "Không khả dụng",
                    UserZaloName = src.membership != null ? src.membership.UserZaloName : "Không khả dụng",
                    PhoneNumber = src.membership != null ? src.membership.PhoneNumber : "Không khả dụng",
                    ShippingFee = src.order.ShippingFee,
                    Address = src.order.DeliveryAddress,
                    PaymentMethod = src.order.PaymentMethod,
                    OrderStatus = (short)src.order.OrderStatus,
                    PaymentStatus = (short)src.order.PaymentStatus,
                    CreatedDate = src.order.CreatedDate,
                });

            CreateMap<(Order order, Membership membership), OrderDetailResponse>()
                .ConstructUsing(src => new OrderDetailResponse
                {
                    Note = src.order.Note,
                    OrderId = src.order.Id,
                    TotalAmount = src.order.Total,
                    DiscountAmount = src.order.DiscountAmount,
                    ShippingFee = src.order.ShippingFee,
                    UserZaloId = src.membership.UserZaloId,
                    UserZaloName = src.membership.UserZaloName,
                    PhoneNumber = src.membership.PhoneNumber,
                    Address = src.order.DeliveryAddress,
                    PaymentMethod = src.order.PaymentMethod,
                    OrderStatus = (short)src.order.OrderStatus,
                    PaymentStatus = (short)src.order.PaymentStatus,
                    CreatedDate = src.order.CreatedDate,
                });

            #endregion

            #region Invoice Template

            CreateMap<InvoiceTemplateDTO, InvoiceTemplate>()
                .ForMember(dest => dest.Id, opt => opt.Ignore());

            CreateMap<InvoiceTemplate, InvoiceTemplateDTO>();

            #endregion

            #region Category

            CreateMap<CategoryRequest, Category>()
                .ForMember(x => x.Images, opt => opt.Ignore());

            CreateMap<Category, Models.Responses.Categories.CategoryResponse>()
               .ForMember(dest => dest.Images, opt => opt.Ignore())
               .ConstructUsing((src, ctx) =>
               {
                   var hostUrl = ctx.Items["HostUrl"] as string;
                   return new CategoryResponse
                   {
                       Id = src.Id,
                       Name = src.Name,
                       HexColor = src.HexColor,
                       Description = src.Description,
                       CreatedDate = src.CreatedDate,
                       Images = src.Images?.Split(',').Select(x => $"{hostUrl}/uploads/images/categories/{x}").ToList() ?? new List<string>()
                   };
               });

            #endregion

            #region Custom Form

            // CustomForm -> FormCustomResponse
            CreateMap<CustomForm, FormCustomResponse>()
                .ForMember(dest => dest.InputFields, opt => opt.Ignore()) // Được xử lý riêng
                .ForMember(dest => dest.FormId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.FormName, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.FormTitle, opt => opt.MapFrom(src => src.Title))
                .ForMember(dest => dest.CheckboxTextColor, opt => opt.MapFrom(src => src.TextColor))
                .ForMember(dest => dest.SubmitButtonColor, opt => opt.MapFrom(src => src.ButtonColor))
                .ForMember(dest => dest.FormBackgroundColor, opt => opt.MapFrom(src => src.BackgroundColor));

            // CustomFormAttribute -> ExtendInfoFormResponse
            CreateMap<CustomFormAttribute, ExtendInfoFormResponse>()
                .ForMember(dest => dest.Options, opt => opt.Ignore()) // xử lý giá trị của thuộc tính sau
                .ForMember(dest => dest.Attribute, opt => opt.MapFrom(src => src.Attribute))
                .ForMember(dest => dest.AttributeName, opt => opt.MapFrom(src => src.AttributeLabel))
                .ForMember(dest => dest.DisplayOrder, opt => opt.MapFrom(src => src.DislayOrder));

            // CustomFormRequest -> CustomForm
            CreateMap<CustomFormRequest, CustomForm>()
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.Title))
                .ForMember(dest => dest.TextColor, opt => opt.MapFrom(src => src.TextColor))
                .ForMember(dest => dest.ButtonText, opt => opt.MapFrom(src => src.ButtonText))
                .ForMember(dest => dest.ButtonColor, opt => opt.MapFrom(src => src.ButtonColor))
                .ForMember(dest => dest.BackgroundColor, opt => opt.MapFrom(src => src.BackgroundColor));

            // CustomFormAttributeRequest -> CustomFormAttribute
            CreateMap<CustomFormAttributeRequest, CustomFormAttribute>()
                .ForMember(dest => dest.Min, opt => opt.MapFrom(src => src.Min))
                .ForMember(dest => dest.Max, opt => opt.MapFrom(src => src.Max))
                .ForMember(dest => dest.Attribute, opt => opt.MapFrom(src => src.Attribute))
                .ForMember(dest => dest.DislayOrder, opt => opt.MapFrom(src => src.DislayOrder))
                .ForMember(dest => dest.AttributeLabel, opt => opt.MapFrom(src => src.AttributeLabel))
                .ForMember(dest => dest.AttributeValue, opt => opt.MapFrom(src => src.AttributeValue));

            #endregion

            #region Membership

            CreateMap<ProfileDTO, Membership>();

            CreateMap<MembershipDTO, Membership>()
                .ForMember(dest => dest.UserZaloName, opt => opt.MapFrom(src => src.DisplayName));

            // Đăng ký thành viên
            CreateMap<RegisterMember, Membership>()
                .ForMember(dest => dest.DisplayName, opt => opt.Ignore());

            CreateMap<MembershipAddressRequest, MembershipAddress>()
                .ForMember(dest => dest.UserZaloId, opt => opt.Ignore())
                .ForMember(dest => dest.WardId, opt => opt.MapFrom(src => src.Ward))
                .ForMember(dest => dest.ProvinceId, opt => opt.MapFrom(src => src.City))
                .ForMember(dest => dest.DistrictId, opt => opt.MapFrom(src => src.District))
                .ForMember(dest => dest.StreetLine, opt => opt.MapFrom(src => src.StreetLine));

            CreateMap<MembershipVATRequest, MembershipVAT>()
                .ForMember(dest => dest.UserZaloId, opt => opt.Ignore())
                .ForMember(dest => dest.WardId, opt => opt.MapFrom(src => src.Ward?? ""))
                .ForMember(dest => dest.ProvinceId, opt => opt.MapFrom(src => src.City??""))
                .ForMember(dest => dest.DistrictId, opt => opt.MapFrom(src => src.District?? ""))
                .ForMember(dest => dest.StreetLine, opt => opt.MapFrom(src => src.StreetLine?? ""));


            CreateMap<RankRequest, Rank>()
             .ForMember(x => x.Image, opt => opt.Ignore());

            #endregion

            #region Brand

            CreateMap<BrandRequest, Brand>()
                .ForMember(x => x.Images, opt => opt.Ignore());

            #endregion

            #region Rating

            CreateMap<RatingDTO, Rating>()
                .ForMember(x => x.Images, opt => opt.Ignore())
                .ForMember(x => x.Videos, opt => opt.Ignore());

            CreateMap<(Membership membership, OrderDetailReview review), ProductReviewDetailResponse>()
                .ForMember(dest => dest.CustomerName, opt => opt.MapFrom(src => src.membership.UserZaloName))
                .ForMember(dest => dest.CustomerPhone, opt => opt.MapFrom(src => src.membership.PhoneNumber))
                .ForMember(dest => dest.CustomerId, opt => opt.MapFrom(src => src.membership.Id))
                .ForMember(dest => dest.CustomerAvatar, opt => opt.MapFrom(src => src.membership.Avatar))
                .ForMember(dest => dest.RatingDate, opt => opt.MapFrom(src => src.review.CreatedDate))
                .ForMember(dest => dest.Star, opt => opt.MapFrom(src => src.review.ReviewPoint))
                .ForMember(dest => dest.Review, opt => opt.MapFrom(src => src.review.ReviewContent))
                .ForMember(dest => dest.IsShow, opt => opt.MapFrom(src => src.review.IsShow))
                .ForMember(dest => dest.RatingId, opt => opt.MapFrom(src => src.review.Id))
                .ForMember(dest => dest.CustomerEmail, opt => opt.Ignore())
                .ForMember(dest => dest.IsRating, opt => opt.Ignore())
                .ForMember(dest => dest.OrderId, opt => opt.Ignore())
                .ForMember(dest => dest.OrderDetailId, opt => opt.Ignore())
                .ForMember(dest => dest.Images, opt => opt.Ignore())
                .ForMember(dest => dest.Videos, opt => opt.Ignore());

            #endregion

            #region Voucher

            CreateMap<VoucherRequest, Voucher>()
                .ForMember(dest => dest.VoucherType, opt => opt.MapFrom(src => (EVoucherType)src.ApplyType))
                .ForMember(dest => dest.DiscountType, opt => opt.MapFrom(src => (EDiscountType)src.DiscountType));

            CreateMap<Voucher, VoucherResponse>()
                .ForMember(dst => dst.Status, opt => opt.MapFrom(src => src.IsActive))
                .ForMember(dst => dst.DiscountType, opt => opt.MapFrom(src => (short)src.DiscountType))
                .ForMember(dst => dst.DiscountValue, opt => opt.MapFrom(src => src.DiscountValue));

            CreateMap<Voucher, VoucherDetailResponse>()
                .ForMember(dest => dest.VoucherType, opt => opt.MapFrom(src => (short)src.VoucherType))
                .ForMember(dest => dest.DiscountType, opt => opt.MapFrom(src => (short)src.DiscountType));

            #endregion

            #region Cart

            CreateMap<(Cart cartItem, Product product), CartItemResponse>()
                 .ConstructUsing(src => new CartItemResponse
                 {
                     Note = src.cartItem.Note,
                     ProductId = src.product.Id,
                     CartItemId = src.cartItem.Id,
                     ProductName = src.product.Name,
                     Quantity = src.cartItem.Quantity,
                     VariantId = src.cartItem.VariantId,
                     OriginalPrice = src.product.Price,
                     DiscountPrice = src.product.Price,
                 });

            CreateMap<Product, CartItemGift>()
                .ForMember(x => x.Images, opt => opt.Ignore())
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.OriginalPrice, opt => opt.MapFrom(src => src.Price))
                .ForMember(dest => dest.DiscountPrice, opt => opt.MapFrom(src => src.Price - src.Price));

            CreateMap<ProductResponse, CartItemGift>()
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Images, opt => opt.MapFrom(src => src.Images))
                .ForMember(dest => dest.ProductName, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.OriginalPrice, opt => opt.MapFrom(src => src.OriginalPrice))
                .ForMember(dest => dest.DiscountPrice, opt => opt.MapFrom(src => 0));

            #endregion

            #region Address

            // Uncomment if needed
            // CreateMap<MembershipAddressDTO, MembershipAddress>()
            //     .ForMember(x => x.Id, opt => opt.Ignore())
            //     .ForMember(x => x.CreatedDate, opt => opt.Ignore());

            #endregion

            #region Discount & Promotion

            // Uncomment if needed
            CreateMap<Discount, DiscountRequest>()
                .ForMember(dest => dest.Products, opt => opt.Ignore());

            CreateMap<DiscountRequest, Discount>()
                .ForMember(dest => dest.Id, opt => opt.Ignore());

            CreateMap<Promotion, PromotionRequest>()
                .ForMember(dest => dest.Products, opt => opt.Ignore())
                .ForMember(dest => dest.Gifts, opt => opt.Ignore());

            CreateMap<PromotionRequest, Promotion>()
                .ForMember(dest => dest.Id, opt => opt.Ignore());

            CreateMap<Promotion, PromotionDTO>()
                .ForMember(dest => dest.Products, opt => opt.Ignore());

            #endregion

            #region Lucky Wheel & Game Prize

            // LuckyWheel -> LuckyWheelDTO (khi get)
            CreateMap<LuckyWheel, LuckyWheelDTO>()
                .ForMember(dest => dest.Image, opt => opt.Ignore())
                .ForMember(dest => dest.ImageUrl, opt => opt.Ignore());

            // LuckyWheelDTO -> LuckyWheel (khi create/update)
            CreateMap<LuckyWheelDTO, LuckyWheel>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Image, opt => opt.Ignore());

            // LuckyWheel -> LuckyWheelResponse (khi trả về cho client)
            CreateMap<LuckyWheel, LuckyWheelResponse>();


            // GamePrizeRequest -> GamePrize
            CreateMap<GamePrizeRequest, GamePrize>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Image, opt => opt.Ignore())
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type));

            // GamePrizeConfigDTO -> GamePrizeConfig
            CreateMap<GamePrizeConfigDTO, GamePrizeConfig>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.GameId, opt => opt.Ignore());

            CreateMap<(GamePrize prize, GamePrizeConfig config), GamePrizeResponse>()
               .ConstructUsing(src => new GamePrizeResponse
               {
                   Id = src.prize.Id,
                   Name = src.prize.Name,
                   ImageUrl = src.prize.Image,
                   Type = src.prize.Type,
                   Position = src.config.Position,
               });

            CreateMap<(GamePrize prize, GamePrizeConfig config), GamePrizeDetailResponse>()
              .ConstructUsing(src => new GamePrizeDetailResponse
              {
                  Id = src.prize.Id,
                  Name = src.prize.Name,
                  Value = src.prize.Value,
                  ImageUrl = src.prize.Image,
                  Type = src.prize.Type,
                  Position = src.config.Position,
                  // chưa tính tới việc thứ tự của giải thưởng
                  Ranking = 0,
                  RankingLabel = string.Empty,
              });

            CreateMap<(GamePrize prize, GamePrizeConfig config), GamePrizeConfigDTO>()
                .ConstructUsing(src => new GamePrizeConfigDTO
                {
                    GamePrizeId = src.prize.Id,
                    GamePrizeName = src.prize.Name,

                    WinRate = src.config.WinRate,
                    Ranking = src.config.Ranking,
                    Position = src.config.Position,
                    DailyLimit = src.config.DailyLimit,
                    Quantity = src.config.Quantity,

                    GamePrizeConfigId = src.config.Id,
                });

            CreateMap<(GamePrize prize, GamePrizeConfig config), ConfiguredGamePrizeDTO>()
                .ConstructUsing(src => new ConfiguredGamePrizeDTO
                {
                    Id = src.prize.Id,
                    Name = src.prize.Name,
                    Type = src.prize.Type,
                    ImageUrl = src.prize.Image,
                    WinRate = src.config.WinRate,
                    Quantity = src.config.Quantity,
                    DailyLimit = src.config.DailyLimit,

                    // tùy chọn
                    ReferenceId = src.prize.ReferenceId,
                });

            // claim reward response
            CreateMap<(GamePrize prize, SpinHistory history), ClaimRewardResponse>()
                .ConstructUsing(src => new ClaimRewardResponse
                {
                    Id = src.prize.Id,
                    Name = src.prize.Name,
                    Type = src.prize.Type,
                    ImageUrl = src.prize.Image,
                    Description = src.prize.Description,
                    RequestStatus = src.history.ClaimStatatus,

                    Value = src.prize.Value,
                    HistoryId = src.history.Id,
                });

            #endregion

            #region Campaign & Event Template

            CreateMap<EventTemplateRequest, EventTemplate>();

            CreateMap<ZaloUidConfigRequest, EventTemplate>();

            #endregion

            #region Survey

            CreateMap<Survey, SurveyDTO>()
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (short)src.Status));

            CreateMap<SurveyDTO, Survey>()
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => (ESurvey)src.Status));

            CreateMap<SurveyAnswer, SurveyAnswerDTO>()
                .ForMember(dest => dest.QuestionId, opt => opt.MapFrom(src => src.SurveyQuestionId));

            CreateMap<SurveyAnswer, AnswerResponse>()
                .ForMember(dest => dest.QuestionId, opt => opt.MapFrom(src => src.SurveyQuestionId))
                .ForMember(dest => dest.AnswerId, opt => opt.MapFrom(src => src.Id));

            // Define mapping for (SurveySubmission, anonymous type with membership data)
            CreateMap<(SurveySubmission submission, Membership membership), SurveySubmissionResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.submission.Id))
                .ForMember(dest => dest.SurveyId, opt => opt.MapFrom(src => src.submission.SurveyId))
                .ForMember(dest => dest.UserZaloId, opt => opt.MapFrom(src => src.submission.UserZaloId))
                .ForMember(dest => dest.SubmissionDate, opt => opt.MapFrom(src => src.submission.CreatedDate))
                // Map from dynamic membership object
                .ForMember(dest => dest.UserZaloName, opt => opt.MapFrom(src =>
                    src.membership == null ? "Khách vãng lai" : src.membership.UserZaloName))
                .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src =>
                    src.membership == null ? "" : src.membership.PhoneNumber));

            #endregion

            #region Survey Result

            CreateMap<(Survey survey, SurveySubmission submission, Membership membership), SurveyResultResponse>()
                .ForMember(dest => dest.SurveyId, opt => opt.MapFrom(src => src.survey.Id))
                .ForMember(dest => dest.Title, opt => opt.MapFrom(src => src.survey.Title))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.survey.Status.ToString()))
                .ForMember(dest => dest.IsDisplay, opt => opt.MapFrom(src => src.survey.IsDisplay))
                .ForMember(dest => dest.StartedDate, opt => opt.MapFrom(src => src.survey.StartedDate))
                .ForMember(dest => dest.EndDate, opt => opt.MapFrom(src => src.survey.EndDate))
                .ForMember(dest => dest.SubmissionId, opt => opt.MapFrom(src => src.submission.Id))
                .ForMember(dest => dest.UserZaloId, opt => opt.MapFrom(src => src.submission.UserZaloId))
                .ForMember(dest => dest.SubmissionDate, opt => opt.MapFrom(src => src.submission.CreatedDate))
                .ForMember(dest => dest.UserZaloName, opt => opt.MapFrom(src => src.membership != null ? src.membership.UserZaloName ?? "Khách vãng lai" : "Khách vãng lai"))
                .ForMember(dest => dest.DisplayName,
                    opt => opt.MapFrom(src =>
                        src.membership != null
                            ? (string.IsNullOrWhiteSpace(src.membership.DisplayName) ? "Khách vãng lai" : src.membership.DisplayName)
                            : "Khách vãng lai"))
                .ForMember(dest => dest.PhoneNumber,
                    opt => opt.MapFrom(src =>
                        src.membership != null
                            ? (string.IsNullOrWhiteSpace(src.membership.PhoneNumber) ? "Không có" : src.membership.PhoneNumber)
                            : "Không có"))
                .ForMember(dest => dest.TotalSection, opt => opt.Ignore())
                .ForMember(dest => dest.Sections, opt => opt.Ignore());

            CreateMap<SurveySection, SectionResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.TitleSection, opt => opt.MapFrom(src => src.TitleSection))
                .ForMember(dest => dest.DisplayOrder, opt => opt.MapFrom(src => src.DisplayOrder))
                .ForMember(dest => dest.ListQuestion, opt => opt.Ignore());

            CreateMap<SurveyQuestion, QuestionResponse>()
                .ForMember(dest => dest.QuestionId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type))
                .ForMember(dest => dest.QuestionTitle, opt => opt.MapFrom(src => src.QuestionTitle))
                .ForMember(dest => dest.IsRequied, opt => opt.MapFrom(src => src.IsRequired))
                .ForMember(dest => dest.DisplayOrder, opt => opt.MapFrom(src => src.DisplayOrder))
                .ForMember(dest => dest.ListOption, opt => opt.Ignore())
                .ForMember(dest => dest.ListQuestionLikert, opt => opt.Ignore())
                .ForMember(dest => dest.UserResponses, opt => opt.Ignore());

            CreateMap<SurveyLikertQuestion, QuestionLikertResponse>()
                .ForMember(dest => dest.QuestionLikertId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.QuestionLikertTitle, opt => opt.MapFrom(src => src.QuestionLikertTitle))
                .ForMember(dest => dest.OptionCount, opt => opt.MapFrom(src => src.OptionCount))
                .ForMember(dest => dest.DisplayOrder, opt => opt.MapFrom(src => src.DisplayOrder));

            CreateMap<SurveySubmissionDetail, SurveySubmissionDetailResponse>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.QuestionId, opt => opt.MapFrom(src => src.SurveyQuestionId))
                .ForMember(dest => dest.LikertQuestionId, opt => opt.MapFrom(src => src.SurveyLikertQuestionId))
                .ForMember(dest => dest.AnswerId, opt => opt.MapFrom(src => src.SurveyAnswerId))
                //.ForMember(dest => dest.LikertId, opt => opt.MapFrom(src => src.LikertId))
                .ForMember(dest => dest.InputValue, opt => opt.MapFrom(src => src.InputValue));

            #endregion

            #region Event

            // Sponsor
            CreateMap<SponsorshipTierDTO, SponsorshipTier>()
                .ForMember(dest => dest.Image, opt => opt.Ignore());

            CreateMap<SponsorDTO, Sponsor>()
                .ForMember(dest => dest.Image, opt => opt.Ignore());

            // Event
            CreateMap<EventDTO, Event>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.Banner, opt => opt.Ignore())
                .ForMember(dest => dest.Images, opt => opt.Ignore());

            // Event Response
            CreateMap<Event, EventResponse>()
                .ForMember(dest => dest.Images, opt => opt.MapFrom(src =>
                    string.IsNullOrEmpty(src.Images)
                        ? new List<string>()
                        : src.Images.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList()
                ))
                .ForMember(dest => dest.IsCheckIn, opt => opt.Ignore())
                .ForMember(dest => dest.IsRegister, opt => opt.Ignore());

            CreateMap<Event, EventDetailResponse>()
                .ForMember(dest => dest.Images, opt => opt.Ignore());

            CreateMap<(EventGift gift, Product product), EventGiftResponse>()
                .ForMember(dest => dest.ProductId, opt => opt.MapFrom(src => src.gift.ProductId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.product.Name))
                .ForMember(dest => dest.Images, opt => opt.Ignore())
                .ForMember(dest => dest.Price, opt => opt.MapFrom(src => src.product.Price))
                .ForMember(dest => dest.Quantity, opt => opt.MapFrom(src => src.gift.Quantity));

            CreateMap<(EventSponsor sponsorLink, Sponsor sponsor, SponsorshipTier? tier), EventSponsorResponse>()
                .ForMember(dest => dest.SponsorId, opt => opt.MapFrom(src => src.sponsorLink.SponsorId))
                .ForMember(dest => dest.SponsorName, opt => opt.MapFrom(src => src.sponsor.SponsorName))
                .ForMember(dest => dest.Introduction, opt => opt.MapFrom(src => src.sponsor.Introduction))
                .ForMember(dest => dest.Image, opt => opt.MapFrom(src => src.sponsor.Image))
                .ForMember(dest => dest.WebsiteURL, opt => opt.MapFrom(src => src.sponsor.WebsiteURL))
                .ForMember(dest => dest.TierId, opt => opt.MapFrom(src => src.sponsorLink.TierId))
                .ForMember(dest => dest.TierName, opt => opt.MapFrom((src, _) => src.tier != null ? src.tier.TierName : null))
                .ForMember(dest => dest.TierImage, opt => opt.MapFrom((src, _) => src.tier != null ? src.tier.Image : null));

            #endregion
        }
    }
}
