﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <h4 class="mb-0">Danh sách nhà tài trợ</h4>
            <button onclick="GetFormSponsor('')" type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-sponsor">
                <i class="las la-plus me-2"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">T<PERSON><PERSON> kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-3">
                        <label for="filter-active-status" class="form-label">Trạng thái hoạt động</label>
                        <select id="filter-active-status" class="form-control">
                            <option value="0">Tất cả</option>
                            <option value="1">Đang hoạt động</option>
                            <option value="2">Ngưng hoạt động</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="sponsor-table" class="table-responsive rounded mb-3">
            <table id="list-sponsor" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-sponsor" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-xl" style="width: 90vw"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListSponsor();

            $('#search').on('input', function () {
                table.ajax.reload();
            });

            $('#filter-active-status').on('change', function () {
                table.ajax.reload();
            });
        });

        function InitialEditor(id) {
            window.editor = new Quill(id, {
                theme: 'snow',
                modules: {
                    imageResize: {
                        displaySize: true,
                    },
                    toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered' }, { 'list': 'bullet' }],  // Thêm chỉnh list
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'align': [] }], // Căn lề (trái, phải, giữa, đều)
                        ['blockquote', 'code-block'], // Thêm blockquote và code block
                        ['image'],
                        ['link',], // Thêm liên kết
                        ['clean'] // Xóa định dạng
                    ]
                }
            });

            window.editor.on('text-change', function (delta, oldDelta, source) {
                const content = window.editor.root.innerHTML;
                if (content === "<p><br></p>") {
                    $("#preview-content").html("Nội dung tin tức");
                    return;
                }
                $("#preview-content").html(content);
            });
        }

        async function previewImage(input) {
            const file = input.files[0];
            if (!file || !file.type.startsWith("image/")) return;

            const isValid = await validateImageAspectRatio(file, 1, 1);
            if (!isValid.valid) {
                AlertResponse(`Ảnh phải có tỉ lệ 1:1. Kích thước hiện tại: ${isValid.actualWidth}x${isValid.actualHeight}`, "warning");
                $(input).val("");
                return;
            }

            const reader = new FileReader();
            reader.onload = function (e) {
                $('#preview-image').html(`
                    <div class="image-preview position-relative d-inline-block">
                        <span class="btn-preview-remove" onclick="removePreviewImage()">x</span>
                        <img src="${e.target.result}" class="rounded border" style="height: 120px; width: 120px; object-fit: cover;" />
                    </div>
                `);
            };
            reader.readAsDataURL(file);
        }

        function removePreviewImage() {
            $('#preview-image').empty();
            $('#image').val("");
        }

        async function validateImageAspectRatio(file, requiredWidth, requiredHeight) {
            return new Promise(resolve => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = e => {
                    const img = new Image();
                    img.src = e.target.result;
                    img.onload = () => {
                        const actualRatio = img.width / img.height;
                        const expectedRatio = requiredWidth / requiredHeight;
                        resolve({ valid: Math.abs(actualRatio - expectedRatio) <= 0.01, actualWidth: img.width, actualHeight: img.height });
                    };
                };
            });
        }

        function GetListSponsor() {
            table = new DataTable("#list-sponsor", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const status = $("#filter-active-status").val();

                    $.ajax({
                        url: '@Url.Action("GetAll", "Sponsors")',
                        type: 'GET',
                        data: {
                            page: page,
                            pageSize: data.length,
                            keyword: keyword,
                            activeStatus: status || 0
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => {
                                const imageHtml = item.image
                                    ? `<img src="${item.image}" style="height:40px;width:40px;object-fit:cover;border-radius:6px;margin-right:10px;" />`
                                    : `<div class="rounded-circle bg-secondary me-2" style="height:40px;width:40px;"></div>`;

                                const sponsorInfo = `
                                    <div class="d-flex align-items-center">
                                        ${imageHtml}
                                        <div><strong>${item.sponsorName}</strong></div>
                                    </div>`;

                                const website = item.websiteURL
                                    ? `<a href="${item.websiteURL}" target="_blank">${item.websiteURL}</a>`
                                    : '<span class="text-muted">Không có</span>';

                                const status = item.isActive ?
                                    `<div class="m-auto bg-success circle-active" title="Đang hoạt động"></div>` :
                                    `<div class="m-auto circle-inactive" title="Ngưng hoạt động"></div>`

                                const actions = `
                                    <div class="d-flex align-items-center justify-content-center list-action">
                                        <a onclick="GetFormSponsor('${item.id}')" class="badge badge-info mx-1" title="Chi tiết">
                                            <i class="ri-pencil-line fs-6"></i>
                                        </a>
                                        <a onclick="DeleteSponsor('${item.id}')" class="badge bg-warning mx-1" title="Xóa">
                                            <i class="ri-delete-bin-line fs-6"></i>
                                        </a>
                                    </div>`;

                                return {
                                    rowNum: data.start + index + 1,
                                    sponsorInfo,
                                    website,
                                    status,
                                    actions
                                };
                            });

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length,
                                    data: formattedData
                                });
                            }, 400);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "rowNum", className: 'text-center' },
                    { title: "Tên & Hình ảnh", data: "sponsorInfo" },
                    { title: "Website", data: "website" },
                    { title: "Trạng thái", data: "status", className: 'text-center' },
                    { title: "Thao tác", data: "actions", className: 'text-center' }
                ],
                language: {
                    lengthMenu: "Hiển thị _MENU_ dòng mỗi trang",
                    zeroRecords: "Không tìm thấy kết quả",
                    info: "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    infoEmpty: "Không có dữ liệu",
                    infoFiltered: "(lọc từ tổng số _MAX_ dòng)"
                }
            });
        }

        function GetActiveBadge(status) {
            if (status === true) {
                return '<span class="badge bg-success">Đang hoạt động</span>';
            } else {
                return '<span class="badge bg-secondary">Không hoạt động</span>';
            }
        }

        function GetFormSponsor(id) {
            const url = id ? `@Url.Action("Detail", "Sponsor")/${id}` : "@Url.Action("Create", "Sponsor")";
            $.get(url, data => {
                $("#modal-content").html(data);
                $("#modal-sponsor").modal("show");
            });
        }

        function DeleteSponsor(id) {
            if (!id) return;
            const url = `/api/sponsors/${id}`;
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const sponsorName = $('#sponsorName').val().trim();
            const websiteUrl = $('#websiteUrl').val().trim();
            const introduction = window.editor.root.innerHTML.trim();
            const isActive = $('#isActive').val() === 'true';

            const imageFile = $('#image')[0]?.files[0];
            const hasPreviewImage = $('#preview-image img').length > 0;

            if (!sponsorName) {
                AlertResponse("Vui lòng nhập tên nhà tài trợ.", "warning");
                return;
            }

            if (!imageFile && !hasPreviewImage) {
                AlertResponse("Vui lòng chọn ít nhất một hình ảnh hợp lệ.", "warning");
                return;
            }

            const formData = new FormData();
            formData.append("SponsorName", sponsorName);
            formData.append("WebsiteURL", websiteUrl);
            formData.append("Introduction", introduction);
            formData.append("IsActive", isActive);
            if (imageFile) formData.append("Image", imageFile);

            const url = id ? `/api/sponsors/${id}` : '/api/sponsors';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url, type: method, data: formData,
                processData: false, contentType: false,
                success: res => {
                    if (res.code === 0) {
                        AlertResponse(res.message, 'success');
                        table.ajax.reload(null, false);
                        $('#modal-sponsor').modal('hide');
                    } else {
                        AlertResponse(res.message, 'warning');
                    }
                },
                error: () => AlertResponse("Lỗi máy chủ", "error")
            });
        }
    </script>
}
