﻿//using AutoMapper;
//using MiniAppCore.Entities.Products.Brands;
//using MiniAppCore.Helpers;
//using MiniAppCore.Models.DTOs.Products;
//using MiniAppCore.Models.Requests.Common;

//namespace MiniAppCore.Services.Products.Brands
//{
//    public class BrandService : BaseService<Brand, int>, IBrandService
//    {
//        private readonly IMapper _mapper;
//        private readonly IWebHostEnvironment _env;
//        public BrandService(IBrandRepo repo, IMapper mapper, IWebHostEnvironment env) : base(repo)
//        {
//            _mapper = mapper;
//            _env = env;
//        }

//        public async Task Add(BrandDTO dto)
//        {
//            Brand brand = _mapper.Map<Brand>(dto);
//            brand.Images = await ProcessUpload(dto.files);
//            await _repo.AddAsync(brand);
//        }

//        public async Task Update(BrandDTO dto)
//        {
//            var brand = await _repo.FindByIdAsync(dto.Id);

//            if (brand == null)
//            {
//                throw new Exception("Brand not found!");
//            }

//            _mapper.Map(dto, brand);

//            if (dto.files != null && dto.files.Any())
//            {
//                string newFiles = await ProcessUpload(dto.files);

//                if (!string.IsNullOrEmpty(newFiles))
//                {
//                    if (!string.IsNullOrEmpty(brand.Images))
//                    {
//                        var existingImages = brand.Images.Split(",").ToList();
//                        var newImageFiles = newFiles.Split(",").ToList();

//                        existingImages.AddRange(newImageFiles);

//                        brand.Images = string.Join(",", existingImages.Distinct());
//                    }
//                    else
//                    {
//                        brand.Images = newFiles;
//                    }
//                }
//            }

//            if (dto.RemovedOldImages != null && dto.RemovedOldImages.Any())
//            {
//                RemoveOldImage(string.Join(",", dto.RemovedOldImages));

//                var remainingImages = brand.Images?
//                    .Split(",")
//                    .Where(image => !dto.RemovedOldImages.Contains(image))
//                    .ToList();

//                brand.Images = string.Join(",", remainingImages);
//            }
//            await _repo.UpdateAsync(brand);
//        }

//        public override async Task<PageResult<Brand>> GetPaged(BaseQueryParameters query)
//        {
//            var brands = _repo.AsQueryAble();
//            var count = brands.Count();

//            var queryBuilder = new QueryBuilder<Brand>();

//            if (!string.IsNullOrEmpty(query.Keyword))
//            {
//                queryBuilder = queryBuilder.And(p => p.Name.Contains(query.Keyword) ||
//                                                     p.Description.Contains(query.Keyword));
//            }

//            var items = await _repo.FindWithConditionsAsync(queryBuilder);
//            var filterCount = items.Count();

//            if (query.Page != 1 || query.PageSize != 20)
//            {
//                items = items.Skip((query.Page - 1) * query.PageSize).Take(query.PageSize);
//            }

//            return new PageResult<Brand>()
//            {
//                Data = items.ToList(),
//                TotalItems = count,
//                PageSize = query.PageSize,
//                PageNumber = query.PageSize,
//                FilteredItems = filterCount
//            };
//        }

//        private async Task<string> ProcessUpload(List<IFormFile>? files)
//        {
//            var stringFiles = string.Empty;
//            if (files != null)
//            {
//                var savePath = Path.Combine(_env.WebRootPath, "images/brands");
//                var fileResult = await FileHandler.SaveFiles(files, savePath);
//                stringFiles = string.Join(",", fileResult);
//            }
//            return stringFiles;
//        }

//        private void RemoveOldImage(string listImage)
//        {
//            var images = listImage.Split(",").Select(x => Path.Combine(_env.WebRootPath, "images/brands", x)).ToList();
//            FileHandler.RemoveFiles(images);
//        }
//    }
//}
