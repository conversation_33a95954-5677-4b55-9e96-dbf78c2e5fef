﻿using MiniAppCore.Enums;

namespace MiniAppCore.Models.Requests.GamePrizes
{
    public class GamePrizeRequest
    {
        public string? Name { get; set; } // tên phần thưởng
        public string? Value { get; set; } // giá trị phần thưởng
        public string? Description { get; set; }
        public IFormFile? Image { get; set; } // ảnh phần thưởng

        public EPrizeType Type { get; set; } = EPrizeType.None; // loại phần thưởng
        public string? Metadata { get; set; } // thông tin mở rộng thêm của phần thưởng dành cho prize type khác nhau
        public string? ReferenceId { get; set; } // Id tham chiếu tới record trong table nếu như nó là phần thưởng hoặc sản phẩm
    }
}
