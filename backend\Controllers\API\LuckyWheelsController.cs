﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.LuckyWheels;
using MiniAppCore.Models.Requests.LuckyWheels;
using MiniAppCore.Services.Gamifications.GamePrizes;
using MiniAppCore.Services.Gamifications.LuckyWheels;
using Newtonsoft.Json;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class LuckyWheelsController(ILogger<LuckyWheelsController> logger, ILuckyWheelService luckyWheelService, IGamePrizeService gamePrizeService) : ControllerBase
    {
        #region Mini App API

        [HttpGet("Available")]
        [AllowAnonymous]
        public async Task<IActionResult> GetLuckyWheelsAvailabel([FromQuery] RequestQuery query)
        {
            try
            {
                var result = await luckyWheelService.GetPage(query);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo mới vòng quay thành công!",
                    result.Data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception)
            {
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Internal Server Errors",
                });
            }
        }

        [HttpGet("{luckyWheelId}/Info")]
        public async Task<IActionResult> GetLuckyWheel(string luckyWheelId)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value;
                if (string.IsNullOrWhiteSpace(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await luckyWheelService.GetActiveLuckyWheel(userZaloId, luckyWheelId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception)
            {
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Internal Server Errors",
                });
            }
        }

        [HttpGet("{luckyWheelId}/Prizes")]
        public async Task<IActionResult> GetGamePrize(string luckyWheelId)
        {
            try
            {
                var gamePrizes = await gamePrizeService.GetGamePrizesDetailByGameId(luckyWheelId);
                gamePrizes.ToList().ForEach(x => x.ImageUrl = $"{Request.Scheme}://{HttpContext.Request.Host}/uploads/images/gamePrizes/{x.ImageUrl}");
                gamePrizes = gamePrizes.Where(x => x.Type != Enums.EPrizeType.None).ToList();
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = gamePrizes
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception)
            {
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Internal Server Errors",
                });
            }
        }

        [HttpPost("{luckyWheelId}/Spin")]
        public async Task<IActionResult> SpinLuckyWheel(string luckyWheelId)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value;
                if (string.IsNullOrWhiteSpace(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await luckyWheelService.PlayLuckyWheel(userZaloId, luckyWheelId, false);
                result.ImageUrl = $"{Request.Scheme}://{Request.Host}/uploads/images/gamePrizes/{result.ImageUrl}";
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error spinning lucky wheel");
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Internal Server Errors",
                });
            }
        }

        [HttpGet("{luckyWheelId}/History")]
        public async Task<IActionResult> GetHistory([FromQuery] RequestQuery query, string luckyWheelId,
                                                    [FromQuery] bool? resultType, [FromQuery] short? type,
                                                    [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                var isAdmin = User.IsInRole("ADMIN") || User.IsInRole("SUPER_ADMIN");
                if (userZaloId == null && !isAdmin)
                {
                    return Unauthorized();
                }
                var result = await luckyWheelService.GetSpinHistory(query, isAdmin, userZaloId, resultType, type, startDate, endDate, luckyWheelId);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting spin history.");
                return StatusCode(500, new { Code = 0, Message = "Internal server error." });
            }
        }

        [HttpGet("Claims/{historyId}")]
        public async Task<IActionResult> GetStatusRequest(string historyId)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value;
                if (string.IsNullOrWhiteSpace(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await luckyWheelService.GetClaimStatusReward(userZaloId, historyId);
                result.ImageUrl = $"{Request.Scheme}://{Request.Host}/uploads/images/gamePrizes/{result.ImageUrl}";
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = result,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error spinning lucky wheel");
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Internal Server Errors",
                });
            }
        }

        [HttpPost("Claims/{historyId}")]
        public async Task<IActionResult> RegisterClaimReward(string historyId, [FromBody] RegisterRewardRequest registerRequest)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(x => x.Type == "UserZaloId")?.Value;
                if (string.IsNullOrWhiteSpace(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await luckyWheelService.RegisterClaimReward(userZaloId, historyId, registerRequest);
                result.ImageUrl = $"{Request.Scheme}://{Request.Host}/uploads/images/gamePrizes/{result.ImageUrl}";
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error spinning lucky wheel");
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Internal Server Errors",
                });
            }
        }

        #endregion

        #region Admin method 

        [HttpGet]
        [Authorize(Roles = "ADMIN, SUPER_ADMIN")]
        public async Task<IActionResult> GetPagedWheels([FromQuery] RequestQuery requestQuery)
        {
            try
            {
                var result = await luckyWheelService.GetPage(requestQuery);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo mới vòng quay thành công!",
                    result.Data,
                    result.TotalPages,
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while updating the card.");
                return StatusCode(500, new { Code = 0, Message = "Internal server error." });
            }
        }

        [HttpPost]
        [Authorize(Roles = "ADMIN, SUPER_ADMIN")]
        public async Task<IActionResult> CreateWheel([FromForm] LuckyWheelDTO luckyWheel)
        {
            try
            {
                if (!string.IsNullOrEmpty(luckyWheel.GamePrizesString))
                {
                    luckyWheel.GamePrizes = JsonConvert.DeserializeObject<List<GamePrizeConfigDTO>>(luckyWheel.GamePrizesString) ?? new List<GamePrizeConfigDTO>();
                }
                await luckyWheelService.CreateAsync(luckyWheel);
                return Ok(new
                {
                    Code = 0,
                    Message = "success"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while updating the card.");
                return StatusCode(500, new { Code = 0, Message = "Internal server error." });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN, SUPER_ADMIN")]
        public async Task<IActionResult> UpdateWheel(string id, [FromForm] LuckyWheelDTO luckyWheel)
        {
            try
            {
                if (!string.IsNullOrEmpty(luckyWheel.GamePrizesString))
                {
                    luckyWheel.GamePrizes = JsonConvert.DeserializeObject<List<GamePrizeConfigDTO>>(luckyWheel.GamePrizesString) ?? new List<GamePrizeConfigDTO>();
                }
                await luckyWheelService.UpdateAsync(id, luckyWheel);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thành công!"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while updating the card.");
                return StatusCode(500, new { Code = 0, Message = "Internal server error." });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "ADMIN, SUPER_ADMIN")]
        public async Task<IActionResult> DeleteWheel(string id)
        {
            try
            {
                await luckyWheelService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "success"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while updating the card.");
                return StatusCode(500, new { Code = 0, Message = "Internal server error." });
            }
        }

        [HttpGet("History")]
        [Authorize(Roles = "ADMIN, SUPER_ADMIN")]
        public async Task<IActionResult> GetHistoryAdmin([FromQuery] RequestQuery query,
                                                    [FromQuery] short? type, [FromQuery] bool? resultType,
                                                    [FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                var result = await luckyWheelService.GetSpinHistory(query, true, string.Empty, resultType, type, startDate, endDate, string.Empty);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while getting spin history.");
                return StatusCode(500, new { Code = 0, Message = "Internal server error." });
            }
        }

        [HttpPut("UpdateClaimStatus")]
        [Authorize(Roles = "ADMIN, SUPER_ADMIN")]
        public async Task<IActionResult> UpdateClaimStatus([FromBody] UpdateClaimRewardRequest request)
        {
            try
            {
                await luckyWheelService.UpdateClaimHistoryStatus(request.Id, (Enums.ETransaction)request.Status);
                return Ok(new
                {
                    Code = 0,
                    Message = "success"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred while updating the card.");
                return StatusCode(500, new { Code = 0, Message = "Internal server error." });
            }
        }

        #endregion
    }

    public class UpdateClaimRewardRequest
    {
        public string? Id { get; set; }
        public short Status { get; set; }
    }
}