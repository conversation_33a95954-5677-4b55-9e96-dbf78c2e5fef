﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Models.Common;

namespace MiniAppCore.Models.Queries
{
    public class BranchQueryParameters : RequestQuery, IRequestQuery
    {
        public string? City { get; set; }
        public string? District { get; set; }
        public string? Ward { get; set; }

        public string? ProvinceId { get; set; }
        public string? DistrictId { get; set; }
        public string? WardId { get; set; }

        public bool? IsActive { get; set; }
    }
}
