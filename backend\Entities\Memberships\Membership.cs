﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Memberships
{
    public class Membership : BaseEntity
    {
        public required string Avatar { get; set; }
        public required string MiniAppId { get; set; }
        public required string UserZaloId { get; set; }
        public required string PhoneNumber { get; set; }
        public required string UserZaloName { get; set; }
        public string? DisplayName { get; set; }

        public long SpinPoint { get; set; }
        public long UsingPoint { get; set; }
        public long RankingPoint { get; set; }

        public string? Job { get; set; }
        public string? Source { get; set; }
        public string? Address { get; set; }
        public string? RankingId { get; set; }
        public string? ReferralCode { get; set; }
        public string? UserZaloIdByOA { get; set; }

        public string? ReferrerId { get; set; }

        public DateTime? DateOfBirth { get; set; }
    }
}
