﻿using MiniAppCore.Entities.Settings;

namespace MiniAppCore.Models.Responses.Memberships
{
    public class MembershipResponse
    {
        public string? UserZaloId { get; set; }
        public string? PhoneNumber { get; set; }
        public string? UserZaloName { get; set; }
        public string? DisplayName { get; set; }
        public string? RankingId { get; set; }
        public Rank? Rank { get; set; }

        public long SpinPoint { get; set; }
        public long UsingPoint { get; set; }
        public long RankingPoint { get; set; }

        public decimal TotalCommission { get; set; }

        public DateTime? DateOfBirth { get; set; }
        public List<ExtendInfoFormResponseWithValue> InputFields { get; set; } = new();
    }

    // Kế thừa từ class đã có để thêm trường Value
    public class ExtendInfoFormResponseWithValue : ExtendInfoFormResponse
    {
        public string? Value { get; set; }
    }
}
