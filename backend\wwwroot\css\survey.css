﻿/* <PERSON><PERSON><PERSON><PERSON> lập c<PERSON> bản */
:root {
	--primary: #4e73df;
	--primary-light: #eaefff;
	--success: #1cc88a;
	--danger: #e74a3b;
	--warning: #f6c23e;
	--info: #36b9cc;
	--dark: #2c3e50;
	--muted: #858796;
	--light: #f8f9fc;
	--white: #fff;
	--border: #e3e6f0;
	--shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
	--shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
	--border-radius: 0.35rem;
}

.survey-form-container {
	max-width: 100%;
	color: #444;
	font-size: 0.95rem;
}

/* Card và Section styling */
.survey-section {
	border: none !important;
	border-radius: var(--border-radius) !important;
	box-shadow: var(--shadow-sm);
	margin-bottom: 1.75rem;
	overflow: hidden;
	transition: all 0.25s ease;
	background-color: var(--white);
}

.survey-section:hover {
	box-shadow: var(--shadow);
}

.section-header {
	background: linear-gradient(to right, #f8f9fc, #f1f3fa);
	padding: 1rem 1.25rem;
	border-bottom: 1px solid var(--border);
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.section-title {
	font-size: 1.1rem;
	font-weight: 600;
	color: var(--dark);
	margin: 0;
	display: flex;
	align-items: center;
}

.section-title .section-number {
	background-color: var(--primary);
	color: var(--white);
	border-radius: 50%;
	width: 32px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
	font-size: 0.9rem;
	box-shadow: 0 2px 4px rgba(78, 115, 223, 0.25);
}

.section-content {
	padding: 1.5rem;
	background-color: var(--white);
}

/* Question styling */
.question-card {
	border: 1px solid var(--border) !important;
	border-radius: var(--border-radius) !important;
	padding: 1.25rem;
	margin-bottom: 1.25rem;
	transition: all 0.2s ease;
	background-color: var(--white);
	position: relative;
}

.question-card:hover {
	box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
}

.question-header {
	display: flex;
	align-items: flex-start;
	margin-bottom: 1rem;
}

.question-number {
	background-color: var(--primary);
	color: var(--white);
	border-radius: 50%;
	width: 28px;
	height: 28px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 12px;
	font-size: 0.9rem;
	flex-shrink: 0;
	box-shadow: 0 2px 4px rgba(78, 115, 223, 0.25);
}

.question-title {
	font-weight: 500;
	color: var(--dark);
	flex-grow: 1;
	line-height: 1.5;
}

/* Badge styling */
.badges {
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.question-badge {
	padding: 0.35rem 0.7rem;
	font-size: 0.7rem;
	font-weight: 500;
	border-radius: 20px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	white-space: nowrap;
}

.badge.bg-danger {
	background-color: var(--danger) !important;
}

.badge.bg-info {
	background-color: var(--info) !important;
}

/* Button styling */
.btn-action {
	width: 32px;
	height: 32px;
	padding: 0;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	border-radius: var(--border-radius);
	transition: all 0.2s;
	margin-left: 0.35rem;
	font-size: 0.9rem;
	border-width: 1px;
}

.btn-action:hover {
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
	transform: translateY(-2px);
}

.btn-action i {
	font-size: 0.875rem;
}

/* "Add" containers */
.add-container {
	background: linear-gradient(to right, #f7f8fc, #edf0f8);
	border-radius: var(--border-radius);
	padding: 1.5rem;
	margin-top: 1.5rem;
	border: 1px solid #e9ecf3;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.add-container h6 {
	font-weight: 600;
	color: var(--primary);
	margin-bottom: 1rem;
	display: flex;
	align-items: center;
}

.add-container h6 i {
	margin-right: 0.5rem;
	font-size: 1.2rem;
}

.form-control,
.form-select {
	border-radius: 4px;
	padding: 0.5rem 0.75rem;
	border: 1px solid #d1d9e6;
	transition: all 0.2s;
	font-size: 0.9rem;
}

.form-control:focus,
.form-select:focus {
	border-color: var(--primary);
	box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Quill Editor */
.quill-editor-container {
	border-radius: var(--border-radius);
	overflow: hidden;
	border: 1px solid #d1d9e6;
	margin-bottom: 1rem;
}

.ql-toolbar.ql-snow {
	border-color: #d1d9e6;
	border-top-left-radius: var(--border-radius);
	border-top-right-radius: var(--border-radius);
	background-color: #f8f9fc;
	border-bottom: 1px solid #e9ecf5;
}

.ql-container.ql-snow {
	border-color: #d1d9e6;
	border-bottom-left-radius: var(--border-radius);
	border-bottom-right-radius: var(--border-radius);
	min-height: 120px;
}

/* Required Switch */
.required-switch-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0.75rem 1rem;
	margin: 0.75rem 0;
	border-top: 1px solid var(--border);
	border-bottom: 1px solid var(--border);
	background-color: #fcfcfc;
	border-radius: var(--border-radius);
}

.required-switch {
	position: relative;
	display: inline-block;
	width: 50px;
	height: 24px;
}

.required-switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

.required-slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	transition: 0.4s;
	border-radius: 24px;
}

.required-slider:before {
	position: absolute;
	content: "";
	height: 16px;
	width: 16px;
	left: 4px;
	bottom: 4px;
	background-color: white;
	transition: 0.4s;
	border-radius: 50%;
}

input:checked + .required-slider {
	background-color: var(--danger);
}

input:focus + .required-slider {
	box-shadow: 0 0 1px var(--danger);
}

input:checked + .required-slider:before {
	transform: translateX(26px);
}

.required-label {
	margin-right: 10px;
	font-size: 0.85rem;
	font-weight: 500;
	color: var(--muted);
}

.required-label i {
	color: var(--danger);
}

.form-check.form-switch .form-check-input {
	width: 36px;
	height: 20px;
	margin-top: 0;
	margin-right: 8px;
	cursor: pointer;
	background-image: none !important;
	position: relative;
	background-color: #ccc;
	border-color: #ccc;
}

.form-check.form-switch .form-check-input:before {
	content: "";
	position: absolute;
	width: 14px;
	height: 14px;
	left: 3px;
	top: 2px;
	background-color: white;
	transition: transform 0.2s ease;
	border-radius: 50%;
}

.form-check.form-switch .form-check-input:checked:before {
	transform: translateX(16px);
}

.form-check.form-switch .form-check-input:checked {
	background-color: #4e73df;
	border-color: #4e73df;
}

.form-check.form-switch {
	display: flex;
	align-items: center;
}

/* Required Checkbox Container */
.required-checkbox-container {
	margin-top: 12px;
	padding-top: 12px;
	padding-left: 20px;
	border-top: 1px solid #e3e6f0;
}

.required-checkbox-container .form-check-input:checked {
	background-color: #e74a3b !important;
	border-color: #e74a3b !important;
}

/* Allow Input Container */
.allow-input-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 10px;
	padding: 0.4rem 0.6rem;
	border-radius: 0.25rem;
	min-width: 100px;
	text-align: center;
}

.allow-input-container .form-check-label {
	font-size: 0.8rem;
	margin-bottom: 4px;
	color: #6c757d;
}

.allow-input-container .form-check.form-switch {
	justify-content: center;
}

.allow-input-container .form-check-input {
	margin: 0 auto;
}

/* Hover effect for switches */
.required-switch-container:hover {
	background-color: #f5f5f5;
}

/* Options containers */
.options-container {
	background-color: var(--light);
	border-radius: var(--border-radius);
	padding: 1.2rem;
	margin-top: 1rem;
	border: 1px solid #eaecf3;
}

.options-list {
	/* max-height: 300px; */
	overflow-y: auto;
}

.option-item {
	background-color: var(--white);
	border-radius: var(--border-radius);
	padding: 0.75rem;
	margin-bottom: 0.75rem;
	border: 1px solid #e6e6e6;
	transition: all 0.2s;
	display: flex;
	align-items: center;
}

.option-item:hover {
	border-color: #bbbbbb;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Likert question styling */
.likert-scale-container {
	background-color: var(--white);
	border-radius: var(--border-radius);
	padding: 1rem;
	margin-top: 0.75rem;
	border: 1px solid #e6e6e6;
}

.likert-question-item {
	background-color: var(--light);
	border-radius: var(--border-radius);
	padding: 1rem;
	margin-bottom: 1rem;
	border: 1px solid #eaecf3;
}

.likert-scale {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.likert-option {
	text-align: center;
	flex: 1;
}

/* Form-related styling */
.form-label {
	font-weight: 500;
	color: var(--dark);
	margin-bottom: 0.5rem;
}

.form-check-input:checked {
	background-color: var(--primary);
	border-color: var(--primary);
}

/* Alert styling */
.alert {
	border-radius: var(--border-radius);
	padding: 0.75rem 1rem;
	margin-bottom: 1rem;
	border: none;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.alert-info {
	color: #0c5460;
	background-color: #d1ecf1;
	border-color: #bee5eb;
}

/* Modal styling */
.modal-content {
	border: none;
	border-radius: var(--border-radius);
	box-shadow: var(--shadow);
}

.modal-header {
	background-color: #f8f9fc;
	border-bottom: 1px solid var(--border);
}

.modal-footer {
	border-top: 1px solid var(--border);
}

/* Label styles */
.form-check-label {
	cursor: pointer;
}
