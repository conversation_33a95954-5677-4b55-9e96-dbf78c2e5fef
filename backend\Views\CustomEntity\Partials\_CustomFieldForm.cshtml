@model MiniAppCore.Features.CustomEntity.Handlers.CustomFieldRequest
@{
    var availableEntities = ViewBag.AvailableEntities as List<string>;
    var dataTypes = ViewBag.DataTypes as List<SelectListItem>;
    var title = ViewBag.Title as string;
    var actionType = ViewBag.ActionType as string;
    var actionUrl = actionType == "Create" ? Url.Action("Create", "CustomEntity") : Url.Action("Edit", "CustomEntity", new { id = Model?.Id });
}

<div class="modal-content">
    <div class="modal-header">
        <h4 class="modal-title">@title</h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>

    <form id="customFieldForm" action="@actionUrl" method="post" novalidate>
        @if (actionType == "Edit")
        {
            <input type="hidden" asp-for="Id" />
        }

        <div class="modal-body">
            <div class="form-group mb-3">
                <label asp-for="EntityName" class="form-label">Entity Name <span class="text-danger">*</span></label>
                <select asp-for="EntityName" class="form-select" required>
                    <option value="">-- Chọn Entity --</option>
                    @if (availableEntities != null)
                    {
                        @foreach (var entity in availableEntities)
                        {
                            <option value="@entity" selected="@(entity == Model?.EntityName)">@entity</option>
                        }
                    }
                </select>
                <div class="invalid-feedback">Vui lòng chọn entity.</div>
            </div>

            <div class="form-group mb-3">
                <label asp-for="FieldName" class="form-label">Field Name (Tên kỹ thuật) <span class="text-danger">*</span></label>
                <input asp-for="FieldName" type="text" class="form-control" placeholder="Ví dụ: CustomDescription" required pattern="^[A-Za-z][A-Za-z0-9]*$" />
                <small class="form-text text-muted">Tên kỹ thuật tiếng Anh, không dấu, không khoảng trắng, bắt đầu bằng chữ cái.</small>
                <div class="invalid-feedback">Tên field không hợp lệ.</div>
            </div>

            <div class="form-group mb-3">
                <label asp-for="FieldNameDisplay" class="form-label">Tên hiển thị <span class="text-danger">*</span></label>
                <input asp-for="FieldNameDisplay" type="text" class="form-control" placeholder="Ví dụ: Mô tả thêm" required />
                <small class="form-text text-muted">Tên hiển thị cho người dùng, có thể sử dụng tiếng Việt có dấu.</small>
                <div class="invalid-feedback">Tên hiển thị không được để trống.</div>
            </div>

            <div class="form-group mb-3">
                <label asp-for="DataType" class="form-label">Data Type <span class="text-danger">*</span></label>
                <select asp-for="DataType" class="form-select" required>
                    <option value="">-- Chọn kiểu dữ liệu --</option>
                    @if (dataTypes != null)
                    {
                        @foreach (dynamic dataType in dataTypes)
                        {
                            <option value="@dataType.Value" selected="@(dataType.Value == Model?.DataType)">@dataType.Text</option>
                        }
                    }
                </select>
                <div class="invalid-feedback">Vui lòng chọn kiểu dữ liệu.</div>
            </div>

            <div class="form-group">
                <div class="form-check form-switch">
                    <input asp-for="IsRequired" type="checkbox" />
                    <label asp-for="IsRequired" class="form-check-label">
                        Bắt buộc nhập
                    </label>
                </div>
                <small class="form-text text-muted">Nếu được chọn, field này sẽ bắt buộc phải nhập khi tạo/cập nhật entity.</small>
            </div>

            <div class="alert alert-info mt-3">
                <h6><i class="fas fa-info-circle"></i> Lưu ý:</h6>
                <ul class="mb-0 small">
                    <li><strong>Field Name (Tên kỹ thuật):</strong> Tên tiếng Anh, không dấu, không khoảng trắng, dùng để lưu trữ trong cơ sở dữ liệu.</li>
                    <li><strong>Tên hiển thị:</strong> Tên hiển thị cho người dùng, có thể sử dụng tiếng Việt có dấu và khoảng trắng.</li>
                    <li>Sau khi tạo, bạn có thể sử dụng field này trong các form tương ứng.</li>
                    <li><strong>Khuyến nghị:</strong> Sử dụng "Cấu hình Custom Fields" để quản lý nhiều fields cùng lúc cho một entity.</li>
                </ul>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
            <button type="submit" class="btn btn-primary">
                <i class="ri-save-line me-1"></i>
                @(actionType == "Create" ? "Tạo mới" : "Cập nhật")
            </button>
        </div>
    </form>
</div>

@await Html.PartialAsync("Scripts/_CustomFieldFormScripts")
