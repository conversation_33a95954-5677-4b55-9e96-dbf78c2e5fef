﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Danh sách voucher</h4>
                @* <p class="mb-0"> *@
                @*     Danh sách mã giảm giá trình bày một cách hiệu quả và cung cấp không gian<br /> *@
                @*     liệt kê các ưu đãi của bạn dành cho khách hàng theo cách hấp dẫn nhất. *@
                @* </p> *@
            </div>
            <div>
                <button type="button" class="btn btn-success mt-2" onclick="GetFormGiftVoucher()">
                    <i class="ri ri-gift-line"></i>Tặng voucher
                </button>
                <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" onclick="HandleExportData()"><i class="ri-file-excel-line"></i>Xuất Excel</button>
                @if (User.IsInRole("ADMIN"))
                {
                    <button type="button" class="btn btn-success mt-2" data-bs-toggle="modal" data-bs-target="#importVoucherModal">
                        <i class="ri-add-line mr-2"></i>Nhập Excel
                    </button>
                    <button type="button" class="btn btn-primary mt-2" onclick="GetFormVoucher()">
                        <i class="ri-add-line mr-2"></i>Thêm mới
                    </button>
                }
            </div>
        </div>
    </div>
    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label for="filter-type" class="form-label">Loại voucher</label>
                        <select id="filter-type" class="form-select">
                            <option value="">Tất cả</option>
                            <option value="1">Giảm giá đơn hàng</option>
                            <option value="2">Giảm giá sản phẩm</option>
                            <option value="3">Giảm giá vận chuyển</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="filter-isActive" class="form-label">Trạng thái</label>
                        <select id="filter-isActive" class="form-select">
                            <option value="">Tất cả</option>
                            <option value="true">Đang hoạt động</option>
                            <option value="false">Ngưng hoạt động</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="voucher-table" class="table-responsive rounded mb-3">
            <table id="list-voucher" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-voucher" class="modal fade" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<div id="modal-giftVoucher" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-dialog-centered modal-xl"></div>
</div>

@await Html.PartialAsync("_VoucherImportModal")

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListVoucher();

            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#filter-isActive').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#search').on('input', search);
        });

        function GetListVoucher() {
            table = new DataTable("#list-voucher", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const type = $("#filter-type").val();
                    const isActive = $("#filter-isActive").val();
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetPage", "Vouchers")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            type: type,
                            isActive: isActive
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                index: index + 1, // Số thứ tự
                                code: item.code, // Mã voucher
                                // name: item.name, // Tên voucher
                                name: `
                                  <div>
                                    <div>${item.name}</div>
                                    <div class="text-muted small">${item.code}</div>
                                  </div>
                                `,
                                discountValue: item.discountType == 0 ?
                                    `${item.discountValue}%` :
                                    item.discountValue?.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }),
                                maxDiscount: item.maxDiscountAmount?.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) || 'Không giới hạn',
                                minimumOrderValue: item.minimumOrderValue?.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) || '0 ₫',
                                startDate: FormatDateTime(item.startDate),
                                endDate: FormatDateTime(item.endDate),
                                expiryDate: FormatDateTime(item.expiryDate),
                                quantity: item.quantity == -1 ? 'Không giới hạn' : item.quantity,
                                actions: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                <a onclick="GetFormVoucher('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                    <i class="ri-edit-line fs-6"></i>
                                                </a>
                                                <a onclick="DeleteVoucher('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                    <i class="ri-delete-bin-line fs-6"></i>
                                                </a>
                                              </div>`,
                                isActive: item.status ?
                                    `<div class="m-auto bg-success circle-active" title="Đang hoạt động"></div>` :
                                    `<div class="m-auto circle-inactive" title="Ngưng hoạt động"></div>`
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        },
                        error: function (error) {
                            $("#spinner").hide();
                            AlertResponse("Đã xảy ra lỗi khi tải dữ liệu!", "error");
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "index", className: 'text-center' },
                    { title: "Tên voucher", data: "name", className: 'text-center' },
                    // { title: "Mô tả", data: "description" },
                    // { title: "Số điểm yêu cầu", data: "pointRequired" },
                    // { title: "Số lượng", data: "quantity", className: 'text-center' },
                    // { title: "Loại áp dụng", data: "applyType", className: 'text-center' },
                    { title: "Giá trị", data: "discountValue", className: 'text-center' },
                    { title: "Giảm giá tối đa", data: "maxDiscount", className: 'text-center' },
                    { title: "Trạng thái", data: "isActive", className: 'text-center' },
                    { title: "Ngày bắt đầu", data: "startDate", className: 'text-center' },
                    { title: "Ngày kết thúc", data: "endDate", className: 'text-center' },
                    { title: "Ngày hết hạn", data: "expiryDate", className: 'text-center' },
                    { title: "Thao tác", data: "actions", className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data text-header-sm');
        }

        function GetFormVoucher(id) {
            const url = id ? '@Url.Action("Detail", "Voucher")' + `/${id}` : '@Url.Action("Create", "Voucher")'
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-voucher").modal("toggle");

                    // $("#products").select2({
                    //     placeholder: "Chọn sản phẩm",
                    //     allowClear: true,
                    //     width: '100%',
                    //     templateResult: format,
                    //     templateSelection: format
                    //     minimumResultsForSearch: 0,
                    //     dropdownParent: $("#modal-voucher")
                    // });

                    if (!id) {
                        InitValidator();
                    }

                    // $('#startDate').on('blur', function () {
                    //     ValidateFormFromTo("Thông báo", "Ngày hết hạn không được nhỏ hơn ngày bắt đầu!");
                    // });

                    // $('#expiryDate').on('blur', function () {
                    //     ValidateFormFromTo("Thông báo", "Ngày hết hạn không được nhỏ hơn ngày bắt đầu!");
                    // });

                    toggleProductSelection($("#applyAll").val());
                }
            })
        }

        function GetFormGiftVoucher(id) {
            $.ajax({
                url: '@Url.Action("FormGiftVoucher", "Voucher")',
                type: 'GET',
                success: function (data) {
                    $("#modal-giftVoucher #modal-content").html(data);
                    $("#modal-giftVoucher").modal("toggle");

                    $("#users").select2();
                    $("#vouchers").select2();
                }
            })
        }

        function DeleteVoucher(id) {
            const url = `/api/vouchers/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            // 🔹 Lấy giá trị từ form
            const applyAll = $("#applyAll").val() === "true";
            const isActive = $("#isActive").val() === "true";
            const isExchange = $("#isExchange").val() === "true";

            const code = $("#code").val()?.trim();
            const name = $("#name").val()?.trim();
            const description = $("#descr").val()?.trim();

            const applyType = parseInt($("#applyType").val()) || 0;
            const discountType = $("#discountType").val()?.trim();
            const discountValue = parseFloat(InputValidator.parseCurrency($("#discountValue").val()?.trim())) || 0;

            const pointRequired = parseInt(InputValidator.parseCurrency($("#pointRequired").val())) || 0;
            const quantity = parseInt($("#quantity").val()) || -1;

            const maxDiscount = parseFloat(InputValidator.parseCurrency($("#maxDiscount").val())) || 0;
            const minimumOrderValue = parseFloat(InputValidator.parseCurrency($("#minimumOrderValue").val())) || 0;
            const rankingPoint = parseInt(InputValidator.parseCurrency($("#rankingPoint").val())) || 0;
            const exchangeTimes = parseInt(InputValidator.parseCurrency($("#exchangeTimes").val())) || 1;
            const expiry = parseInt($("#expiry").val()) || 0;
            const products = $("#products").val() || [];

            // 📅 Lấy và kiểm tra ngày tháng
            const startDate = $('#startDate').val();
            const endDate = $('#endDate').val();
            const expiryDate = $('#expiryDate').val();

            if (!moment(startDate, moment.ISO_8601, true).isValid() ||
                !moment(endDate, moment.ISO_8601, true).isValid() ||
                !moment(expiryDate, moment.ISO_8601, true).isValid()) {
                AlertResponse("📅 Ngày không hợp lệ! Vui lòng kiểm tra lại!", "error");
                return;
            }

            // 🔹 Danh sách lỗi (nếu có)
            let errors = [];

            if (!code) errors.push("⚠ Vui lòng nhập tên voucher!");
            if (!description) errors.push("⚠ Vui lòng nhập Mô tả!");

            // 🛠 Kiểm tra logic ngày tháng
            if (moment(startDate).isSameOrAfter(endDate)) {
                errors.push("❌ Ngày kết thúc phải lớn hơn ngày bắt đầu!");
            }
            if (moment(endDate).isSameOrAfter(expiryDate)) {
                errors.push("❌ Ngày hết hạn phải lớn hơn ngày kết thúc!");
            }
            if (moment(startDate).isSameOrAfter(expiryDate)) {
                errors.push("❌ Ngày hết hạn phải lớn hơn ngày bắt đầu!");
            }

            // ❌ Nếu có lỗi, hiển thị tất cả lỗi
            if (errors.length > 0) {
                AlertResponse(errors.join("\n"), "error");
                return;
            }

            // 📌 Tạo object dữ liệu
            const data = {
                Code: code,
                Name: name,
                Description: description,
                PointRequired: pointRequired,
                Quantity: quantity,

                ApplyType: applyType,
                DiscountType: discountType,
                DiscountValue: discountValue, // Có thể cần sửa nếu giảm giá theo % hoặc số tiền

                MaxDiscountAmount: maxDiscount, // Đúng theo `VoucherRequest`
                MinimumOrderValue: minimumOrderValue,
                StartDate: startDate,
                EndDate: endDate,
                ExpiryDate: expiryDate,
                RankingPoint: rankingPoint,
                ExchangeTimes: exchangeTimes,
                Expiry: expiry,
                IsActive: isActive,
                IsExchange: isExchange,
                IsAllProducts: applyAll, // Sửa từ `IsAllProducts` thành `ApplyAll`
                ProductIds: products // Đảm bảo gửi đúng danh sách sản phẩm
            };

            const url = id ? `/api/vouchers/${id}` : '/api/vouchers';
            const method = id ? 'PUT' : 'POST';

            // 🚀 Gửi AJAX request
            $.ajax({
                url: url,
                type: method,
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'error');
                    }
                    $("#modal-voucher").modal("toggle");
                },
                error: function () {
                    AlertResponse("❌ Có lỗi xảy ra khi kết nối đến máy chủ!", "error");
                }
            });
        }

        function SendGift() {
            const users = $("#users").val();
            const vouchers = $("#vouchers").val();

            const data = {
                users,
                vouchers
            }

            $.ajax({
                url: '@Url.Action("SendGift", "Vouchers")',
                method: 'POST',
                contentType: "application/json",
                data: JSON.stringify(data),
                success: function (res) {
                    $("#modal-giftVoucher").modal("toggle");
                    const data = res.data;

                    for (const mess of data) {
                        $.toast({
                            heading: 'Thông báo',
                            text: mess,
                            hideAfter: 3000,
                            allowToastClose: false,
                            showHideTransition: 'fade',
                            icon: 'info',
                            position: 'bottom-right',
                        });
                    }
                    table.ajax.reload(null, false);
                }
            });
        }

        function format(state) {
            if (!state.id) return state.text;

            var imageUrl = window.location.origin + "/images/products/" + $(state.element).data('image').split(',')[0];

            var $container = $(`<span>
                    <img src="${imageUrl}" style="width: 35px; max-width: 100%; height: auto; margin-right: 10px; margin-top: 2px; margin-bottom: 2px; border-radius: 5px;" alt="${state.text}" />
                    <span>${state.text}</span>
                </span>`);

            return $container;
        }

        function toggleProductSelection(value) {
            const productSelection = $("#productSelection");
            if (value === "true") {
                productSelection.hide();
            } else {
                productSelection.show();
            }
        }

        function HandleExportData() {
            const apiUrl = `/api/Vouchers/ExportVouchers`;
            $.ajax({
                url: apiUrl,
                type: 'GET',
                xhrFields: {
                    responseType: 'blob' // Để nhận file từ server
                },
                success: function (response, status, xhr) {
                    // Lấy tên file từ header response
                    const timestamp = moment().format("YYYYMMDD_HHmmss");
                    const fileName = `Vouchers_${timestamp}.xlsx`;
                    // Tạo đường dẫn tải file
                    const url = window.URL.createObjectURL(new Blob([response]));
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    a.remove();
                    AlertResponse("Xuất file thành công!", "success");
                },
                error: function (xhr) {
                    console.log(xhr)
                    AlertResponse(xhr.responseJSON?.message || "Đã xảy ra lỗi. Vui lòng thử lại sau.", "error");
                },
                complete: function () {
                    $("#modal-export").modal("toggle");
                }
            });
        }
    </script>
}
