﻿namespace MiniAppCore.Models.Responses.Affiliates
{
    public class CommissionTransactionResponse
    {
        public string? Avatar { get; set; }
        public string? UserZaloId { get; set; }
        public string? UserZaloName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? ReferralCode { get; set; }

        public string? OrderId { get; set; }
        public decimal TotalOrder { get; set; }
        public decimal TotalCommission { get; set; }

        public DateTime CreatedDate { get; set; }

        public bool IsPaid { get; set; } = false; // true nếu đã được trả hoa hồng
    }

    class GroupedCommission
    {
        public string? UserZaloId { get; set; }
        public string? UserZaloName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? ReferralCode { get; set; }
        public string? Avatar { get; set; }
        public decimal TotalCommission { get; set; }
    }


    public class UserZaloNode
    {
        public string? UserZaloId { get; set; }
        public string? UserZaloName { get; set; }
        public string? PhoneNumber { get; set; }
        public List<UserZaloNode> Children { get; set; } = new List<UserZaloNode>();
    }

}
