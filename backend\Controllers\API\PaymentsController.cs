using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Helpers;
using MiniAppCore.Services.Orders;
using Newtonsoft.Json.Linq;
using System.Web;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    public class PaymentsController(ILogger<PaymentsController> logger, IConfiguration configuration, IOrderService orderService) : ControllerBase
    {
        [HttpPost("payment-handler")]
        public async Task<IActionResult> ZaloPaymentHandler([FromBody] PaymentInfo info)
        {
            try
            {
                WriteToFile("ZaloPaymentHandler.txt", System.Text.Json.JsonSerializer.Serialize(info));

                var extraDataDecoded = HttpUtility.UrlDecode(info.Data.ExtraData);
                var transId = JObject.Parse(extraDataDecoded ?? "{}")["myTransactionId"]?.ToString() ?? string.Empty;

                var macObject = new Dictionary<string, object?>()
                {
                    {"appId", info.Data.AppId },
                    {"amount", info.Data.Amount },
                    {"description", info.Data.Description},
                    {"orderId", info.Data.OrderId },
                    {"message", info.Data.Message },
                    {"resultCode", info.Data.ResultCode },
                    {"transId", info.Data.TransId },
                };

                string strMacObj = string.Join("&", macObject.Select(kvp =>
                {
                    return $"{kvp.Key}={kvp.Value?.ToString()}";
                }));

                var dataForMac = Tools.ComputeHmacSha256(configuration["MiniAppSettings:PrivateKey"], strMacObj);

                if (dataForMac == info.Mac && info.Data.ResultCode == 1)
                {
                    // var transId = JObject.Parse(extraDataDecoded ?? "{}")["myTransactionId"]?.ToString() ?? string.Empty;
                    transId = info.Data.MerchantTransId ?? transId;
                    await orderService.UpdateStatusAsync(info.Data.MerchantTransId ?? transId, paymentMethod: info.Data.Method, paymentStatus: Enums.EPayment.Paid);
                }

                return Ok(new
                {
                    ReturnCode = 1,
                    ReturnMessage = "Xử lý đơn hàng thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return Ok(new
                {
                    ReturnCode = 0,
                    ReturnMessage = "Xử lý đơn hàng thất bại!"
                });
            }
        }

        [HttpPost("notify")]
        public async Task<IActionResult> Notify([FromBody] PaymentInfo info)
        {
            try
            {
                WriteToFile("Notify.txt", $"[BODY] {System.Text.Json.JsonSerializer.Serialize(info)}");

                var appId = info.Data.AppId;
                var orderId = info.Data.OrderId;
                var method = info.Data.Method;

                var data = $"appId={appId}&orderId={orderId}&method={method}";
                var reqmac = Tools.ComputeHmacSha256(configuration["MiniAppSettings:PrivateKey"], data);

                if (reqmac != info.Mac || string.IsNullOrEmpty(orderId) || string.IsNullOrEmpty(method))
                {
                    return Ok(new
                    {
                        returnCode = 0,
                        returnMessage = "Dữ liệu notify không hợp lệ!"
                    });
                }
                var paymentMethod = method.Contains("COD") ? "1" : "2";
                await orderService.UpdateStatusAsync(orderId, paymentMethod: paymentMethod);

                return Ok(new
                {
                    returnCode = 1,
                    returnMessage = "Xử lí thành công!"
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex.Message);
                return Ok(new
                {
                    returnCode = 0,
                    returnMessage = "Xử lý đơn hàng thất bại!"
                });
            }
        }

        private void WriteToFile(string fileName, string content)
        {
            try
            {
                var logPath = Path.Combine(Directory.GetCurrentDirectory(), "Logs");
                if (!Directory.Exists(logPath))
                    Directory.CreateDirectory(logPath);

                var fullPath = Path.Combine(logPath, fileName);
                System.IO.File.AppendAllText(fullPath, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {content}{Environment.NewLine}");
            }
            catch (Exception ex)
            {
                // Nếu ghi log lỗi cũng lỗi luôn thì thôi bỏ qua
                Console.WriteLine($"Logging failed: {ex.Message}");
            }
        }
    }

    public class PaymentInfo
    {
        public required TransactionInfo Data { get; set; }
        public required string Mac { get; set; }
        public string? OverallMac { get; set; }
    }

    public class TransactionInfo
    {
        public string? AppId { get; set; }
        public string? OrderId { get; set; }
        public string? TransId { get; set; }
        public string? Method { get; set; }
        public long? TransTime { get; set; }
        public string? MerchantTransId { get; set; }
        public int Amount { get; set; }
        public string? Description { get; set; }
        public int ResultCode { get; set; }
        public string? Message { get; set; }
        public string? ExtraData { get; set; }
    }
    public class DataInfo
    {
        public string? AppId { get; set; }
        public string? OrderId { get; set; }
        public string? Method { get; set; }
        public string? Mac { get; set; }
    }

}
