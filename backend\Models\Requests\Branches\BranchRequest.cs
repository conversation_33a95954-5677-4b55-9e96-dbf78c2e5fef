﻿namespace MiniAppCore.Models.Requests.Branches
{
    public class BranchRequest
    {
        // Thông tin cơ bản
        public required string Name { get; set; }
        public required string PhoneNumber { get; set; }
        public bool Status { get; set; } = true;

        // Địa chỉ
        public string? StreetLine { get; set; }
        public required string ProvinceId { get; set; }
        public string? ProvinceName { get; set; }
        public required string DistrictId { get; set; }
        public string? DistrictName { get; set; }
        public required string WardId { get; set; }
        public string? WardName { get; set; }
        public string? FullAddress { get; set; }

        // Tọa độ Google Maps
        public required string Latitude { get; set; }
        public required string Longitude { get; set; }
        public string? GoogleMapURL { get; set; }

        // Thời gian hoạt động
        public DateTime OpeningTime { get; set; } = DateTime.Today.AddHours(8);
        public DateTime ClosingTime { get; set; } = DateTime.Today.AddHours(22);
        public bool IsOpenHoliday { get; set; } = true;

        // <PERSON><PERSON>i đặt chi nhánh
        public bool IsDefault { get; set; } = false;

        // Mô tả và hình ảnh
        public string? Description { get; set; }
        public List<IFormFile>? files { get; set; }
    }
}
