﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;

namespace MiniAppCore.Entities.Surveys
{
    public class Survey : BaseEntity
    {
        public string? Title { get; set; }

        public ESurvey Status { get; set; }
        public short DisplayOrder { get; set; }

        public bool IsDisplay { get; set; }

        public DateTime EndDate { get; set; }
        public DateTime StartedDate { get; set; }
    }
}
