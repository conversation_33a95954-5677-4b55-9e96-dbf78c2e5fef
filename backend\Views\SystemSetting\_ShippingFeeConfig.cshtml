﻿@model MiniAppCore.Entities.Orders.ShippingFeeConfig
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body" style="@(User.IsInRole("EMPLOYEE") ? "pointer-events: none;" : "")">
        <div class="card-body">
            <form data-toggle="validator">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Giá trị đơn hàng tối thiểu</label>
                            <input id="minOrderValue"                 
                                   class="form-control"
                                   type="text"
                                   value="@(Model.MinOrderValue.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   oninput="InputValidator.currency(this,{min:0})"
                                   placeholder="Nhập giá trị đơn hàng tối thiểu."
                                   data-error-message="Nhập giá trị đơn hàng tối thiểu."                  
                                   required />

                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Giá trị đơn hàng tối đa</label>
                            <input 
                                id="maxOrderValue"
                                   class="form-control"
                                min="0"
                                   type="text"
                                   value="@(Model.MaxOrderValue.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   oninput="InputValidator.currency(this,{min:0})"
                                placeholder="Nhập giá trị đơn hàng tối đa." 
                                data-error-message="Nhập giá trị đơn hàng tối đa." 
                                required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Phí vận chuyển <span style="color:red">*</span></label>
                            <input id="shippingFee" type="text" 
                                min="0" 
                                class="form-control"
                                   value="@(Model.ShippingFee.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Nhập phí vận chuyển."
                                   oninput="InputValidator.currency(this,{min:0})"
                                   data-error-message="Vui lòng nhập giá bán." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
    </div>
</div>