﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Events.Sponsors;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events.Sponsors;

namespace MiniAppCore.Services.Events.Sponsors
{
    public interface ISponsorService : IService<Sponsor>
    {
        Task<int> CreateSponsorAsync(SponsorDTO data);
        Task<int> UpdateSponsorAsync(string id, SponsorDTO data);
        Task<int> DeleteSponsorAsync(string id);

        Task<PagedResult<Sponsor>> GetPaged(RequestQuery query, short activeStatus);
        Task<Sponsor?> GetById(string id);

        Task<List<Sponsor>> GetListByIdsAsync(IEnumerable<string> ids);
    }

}
