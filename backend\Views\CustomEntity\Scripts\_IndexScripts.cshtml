<script>
    const selectedEntity = "@Html.Raw(string.IsNullOrEmpty(ViewBag.SelectedEntity) ? "" : $"{ViewBag.SelectedEntity}")";

    $(document).ready(function () {
        initializeDataTable();
        setupEventHandlers();
    });

    function setupEventHandlers() {
        // Debounced search to reduce requests while typing
        $('#search').on('input', search);

        // Entity filter for the "All Entities" view
        $('#entityFilter').on('change', function () {
            if (table) table.ajax.reload();
        });

        $('#btn-refresh').on('click', function () {
            if (table) table.ajax.reload();
        });
    }

    function initializeDataTable() {
        const isAllEntitiesView = !selectedEntity;

        table = new DataTable("#customFieldsTable", {
            searching: false,
            ordering: false,
            responsive: true,
            pageLength: 10,
            serverSide: true,
            ajax: function (data, callback, settings) {
                const page = Math.floor(data.start / data.length) + 1;
                const keyword = $("#search").val() || '';
                const entityName = selectedEntity || $("#entityFilter").val() || '';

                $('#spinner').show();

                $.ajax({
                    url: '@Url.Action("GetPage", "CustomEntity")',
                    type: 'GET',
                    data: {
                        page: page,
                        pageSize: data.length,
                        keyword: keyword,
                        entityName: entityName
                    },
                    success: function (response) {
                        if (!response.success) {
                            console.error('API Error:', response.message);
                            AlertResponse(response.message || 'Lỗi tải dữ liệu.', 'error');
                            callback({
                                draw: data.draw,
                                recordsTotal: 0,
                                recordsFiltered: 0,
                                data: []
                            });
                            return;
                        }

                        const formattedData = response.data.map((item) => ({
                            entityNameDisplay: `<span class="badge bg-secondary bg-soft">${escapeHtml(item.entityName)}</span>`,
                            fieldNameDisplay: `
                                   <div class="mb-1">
                                        <div class="text-muted small">Tên hiển thị</div>
                                        <div class="fw-bold text-primary">${escapeHtml(item.fieldNameDisplay || item.fieldName)}</div>
                                        ${item.fieldNameDisplay
                                            ? `<div class="text-muted small mt-1">Tên kỹ thuật: <code>${escapeHtml(item.fieldName)}</code></div>`
                                            : ''}
                                   </div>
                            `,
                            dataTypeDisplay: `<span class="badge bg-info bg-soft">${getDataTypeIcon(item.dataType)} ${escapeHtml(item.dataType)}</span>`,
                            isRequiredDisplay: item.isRequired
                                ? '<span class="badge bg-danger bg-soft">Bắt buộc</span>'
                                : '<span class="badge bg-light text-dark">Tùy chọn</span>',
                            createdDate: FormatDate(item.createdDate),
                            actions: createActionButtons(item.id, item.fieldNameDisplay || item.fieldName)
                        }));

                        setTimeout(() => {
                            callback({
                                draw: data.draw,
                                recordsTotal: response.totalCount,
                                recordsFiltered: response.totalCount,
                                data: formattedData
                            });
                        }, 300);
                    },
                    error: function (xhr, status, error) {
                        console.error('DataTable AJAX Error:', { xhr, status, error });
                        AlertResponse('Lỗi kết nối đến máy chủ. Vui lòng thử lại.', 'error');
                        callback({
                            draw: data.draw,
                            recordsTotal: 0,
                            recordsFiltered: 0,
                            data: []
                        });
                    },
                    complete: function () {
                        $('#spinner').hide();
                    }
                });
            },
            columns: [
                { title: "Entity", data: "entityNameDisplay", name: "entityName", visible: isAllEntitiesView },
                { title: "Tên Field", data: "fieldNameDisplay", name: "fieldName" },
                { title: "Kiểu dữ liệu", data: "dataTypeDisplay", name: "dataType", className: 'text-center' },
                { title: "Bắt buộc", data: "isRequiredDisplay", name: "isRequired", className: 'text-center' },
                { title: "Ngày tạo", data: "createdDate", name: "createdDate", className: 'text-center' },
                { title: "Thao tác", data: "actions", name: "actions", className: 'text-center', orderable: false }
            ],
            language: {
                "lengthMenu": "Hiển thị _MENU_ dòng",
                "zeroRecords": "Không tìm thấy dữ liệu phù hợp",
                "info": "Hiển thị _START_ - _END_ của _TOTAL_ mục",
                "infoEmpty": "Không có dữ liệu",
                "infoFiltered": "(lọc từ _MAX_ mục)",
                "processing": "Đang xử lý...",
            },
        });

        $(table.table().header()).addClass('light light-data');
    }

    // Utility functions
    function escapeHtml(text) {
        if (text === null || typeof text === 'undefined') return '';
        const map = {
            '&': '&',
            '<': '<',
            '>': '>',
            '"': '"',
            "'": '&#39;'
        };
        return text.toString().replace(/[&<>"']/g, (m) => map[m]);
    }

    function getDataTypeIcon(dataType) {
        const icons = {
            'string': '<i class="ri-text"></i>',
            'textarea': '<i class="ri-file-text-line"></i>',
            'int': '<i class="ri-hashtag"></i>',
            'decimal': '<i class="ri-number-9"></i>',
            'bool': '<i class="ri-checkbox-line"></i>',
            'datetime': '<i class="ri-calendar-2-line"></i>',
            'date': '<i class="ri-calendar-line"></i>',
            'email': '<i class="ri-at-line"></i>',
            'phone': '<i class="ri-phone-line"></i>',
            'url': '<i class="ri-links-line"></i>',
            'file': '<i class="ri-attachment-2"></i>',
            'image': '<i class="ri-image-line"></i>',
            'select': '<i class="ri-arrow-down-s-line"></i>',
            'multiselect': '<i class="ri-list-check-2"></i>'
        };
        return icons[dataType?.toLowerCase()] || '<i class="ri-settings-3-line"></i>';
    }

    function createActionButtons(id, fieldName) {
        // Escape quotes properly for onclick attributes
        const safeFieldName = fieldName.replace(/'/g, "\\'").replace(/"/g, '\\"');

        return `
            <div class="btn-group btn-group-sm">
                <button onclick="editCustomField('${id}')" class="btn btn-outline-primary" title="Chỉnh sửa '${escapeHtml(fieldName)}'">
                    <i class="ri-edit-line"></i>
                </button>
                <button onclick="deleteCustomField('${id}', '${safeFieldName}')" class="btn btn-outline-danger" title="Xóa '${escapeHtml(fieldName)}'">
                    <i class="ri-delete-bin-line"></i>
                </button>
            </div>
        `;
    }

    // Modal and CRUD operations
    function editCustomField(id) {
        // Use the correct route format with path parameter
        const baseUrl = '@Url.Action("Edit", "CustomEntity")';
        const editUrl = baseUrl.endsWith('/') ? `${baseUrl}${id}` : `${baseUrl}/${id}`;

        $.get(editUrl)
            .done(function (data) {
                $('#modalContent').html(data);
                $('#modal-dialog').removeClass('modal-xl').addClass('modal-lg');
                $('#customFieldModal').modal('show');
            })
            .fail(function (xhr) {
                const message = xhr.responseJSON?.message || 'Không thể tải form chỉnh sửa. Vui lòng thử lại.';
                if (typeof AlertResponse === 'function') {
                    AlertResponse(message, 'error');
                } else {
                    alert(message);
                }
                console.error('Edit form load failed:', xhr);
            });
    }

    function deleteCustomField(id, fieldName) {
        ConfirmAlert(
            'Xác nhận xóa', // Title
            'Bạn có chắc chắn muốn xóa custom field "' + fieldName + '"?\nLưu ý: Tất cả dữ liệu liên quan sẽ bị xóa.',
            'warning',
            '@Url.Action("Delete", "CustomEntity")/' + id, // URL
            'POST', // Method
            null,
            table,
            function (response) {
                console.log("Xóa thành công:", response);
            }
        );
    }
</script>
