﻿namespace MiniAppCore.Models.Requests.Offers
{
    public class PromotionRequest
    {
        public string? Name { get; set; }
        public string? Description { get; set; }
        public decimal TotalAmount { get; set; }
        public string? ExtraGift { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime? StartDate { get; set; }
        public DateTime? ExpiryDate { get; set; }

        public List<PromotionItemRequest> Gifts { get; set; } = new List<PromotionItemRequest>();
        public List<PromotionItemRequest> Products { get; set; } = new List<PromotionItemRequest>();
    }

    public class PromotionItemRequest
    {
        public int Quantity { get; set; }
        public required string ProductId { get; set; }
    }
}
