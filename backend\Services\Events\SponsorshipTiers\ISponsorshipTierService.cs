﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Events.SponsorshipTiers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events.Sponsors;

namespace MiniAppCore.Services.Events.SponsorshipTiers
{
    public interface ISponsorshipTierService : IService<SponsorshipTier>
    {
        Task<int> CreateTierAsync(SponsorshipTierDTO data);
        Task<int> UpdateTierAsync(string id, SponsorshipTierDTO data);
        Task<int> DeleteTierAsync(string id);

        Task<PagedResult<SponsorshipTier>> GetPaged(RequestQuery query, short activeStatus);
        Task<SponsorshipTier?> GetById(string id);

        Task<List<SponsorshipTier>> GetListByIdsAsync(IEnumerable<string> ids);
    }
}
