﻿@model MiniAppCore.Models.Responses.Surveys.QuestionResponse

<style>
    .likert-question-item {
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 1rem;
        background-color: #f8f9fa;
    }

    .likert-scale {
        gap: 0.75rem;
    }

    .likert-option .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .likert-option label {
        font-weight: 500;
        font-size: 0.9rem;
    }

    .likert-option .form-check-input {
        cursor: default;
    }
</style>

<div class="question-card card">
    <div class="card-body">
        <div class="question-title d-flex justify-content-between align-items-start">
            <div class="me-2 flex-grow-1">@Html.Raw(Model.QuestionTitle)</div>
            @await Html.PartialAsync("SurveySubmission/_QuestionTypeBadgePartial", Model.Type)
            @if (Model.IsRequied)
            {
                <span class="badge bg-danger question-badge" title="Bắt buộc">
                    <i class="ri-asterisk me-1"></i> B<PERSON><PERSON> buộc
                </span>
            }
        </div>

        @foreach (var likert in Model.ListQuestionLikert)
        {
            @if (Model.UserResponses?.Any() == true)
            {
                var selectedValue = Model.UserResponses.FirstOrDefault(r => r.LikertQuestionId == likert.QuestionLikertId)?.AnswerId;

                <div class="likert-question-item">
                    <div class="mb-2 fw-semibold">@likert.QuestionLikertTitle</div>
                    <div class="likert-scale d-flex justify-content-between align-items-center">
                        @foreach (var option in Model.ListOption.OrderBy(o => o.DisplayOrder))
                        {
                            <div class="likert-option text-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" disabled
                                    @(selectedValue == option.AnswerId ? "checked" : "")>
                                    <label class="form-check-label">@option.Key</label>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        }
    </div>
</div>
