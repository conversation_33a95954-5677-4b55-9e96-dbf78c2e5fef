﻿<script>
    // Declare getFilterValues in the global scope
    let getFilterValues;

    $(document).ready(function () {
        // Initialize Select2 for all selects
        $('#filter-category').select2({
            placeholder: '<PERSON>ọn danh mục',
            allowClear: true
        });

        $('#filter-branch').select2({
            placeholder: 'Chọn chi nhánh',
            allowClear: true
        });

        $('#filter-stock').select2({
            placeholder: 'Tình trạng kho',
            allowClear: false
        });

        $('#filter-gift').select2({
            placeholder: 'Quà tặng',
            allowClear: false
        });

        $('#filter-supplier').select2({
            placeholder: 'Chọn nhà cung cấp',
            allowClear: false
        });

        $('#filter-brand').select2({
            placeholder: 'Chọn thương hiệu',
            allowClear: false
        });

        $('#filter-status').select2({
            placeholder: 'Trạng thái hiển thị',
            allowClear: false
        });

        $('#filter-sort').select2({
            placeholder: 'Sắp xếp theo',
            minimumResultsForSearch: Infinity
        });

        // Handle filter collapse toggle icon
        $('#filterBody').on('show.bs.collapse', function () {
            $('#filterCollapseBtn i').removeClass('ri-arrow-down-s-line').addClass('ri-arrow-up-s-line');
        });

        $('#filterBody').on('hide.bs.collapse', function () {
            $('#filterCollapseBtn i').removeClass('ri-arrow-up-s-line').addClass('ri-arrow-down-s-line');
        });

        // Load API data
        loadCategories();
        loadBranches();

        // Search button click event
        $('#btn-search').click(function () {
            applyFilters();
        });

        // Enter key in search field
        $('#search').keypress(function (e) {
            if (e.which === 13) {
                applyFilters();
                return false;
            }
        });

        // Apply basic filters
        $('#btn-apply-filters').click(function () {
            applyFilters();
        });

        // Apply advanced filters
        $('#btn-apply-advanced-filters').click(function () {
            applyFilters();
        });

        // Reset basic filters
        $('#btn-reset-filters').click(function () {
            resetBasicFilters();
            applyFilters(); // Apply filters after reset to update UI
        });

        // Reset all filters
        $('#btn-reset-all-filters').click(function () {
            resetAllFilters();
            applyFilters(); // Apply filters after reset to update UI
        });

        // Clear all active filters
        $('#clearAllFilters').click(function () {
            resetAllFilters();
            applyFilters();
        });

        // Remove individual filter
        $(document).on('click', '.filter-badge .btn-close', function () {
            const filterType = $(this).closest('.filter-badge').data('type');
            removeFilter(filterType);
            applyFilters();
        });

        // Filter change events to update UI
        $('#filter-category, #filter-branch, #filter-supplier, #filter-brand, #filter-stock, #filter-status, #filter-gift, input[name="rating"], #filter-sort').on('change', function () {
            // Optional: Auto-apply filters on change
            // applyFilters();
        });

        // Price input events
        $('#min-price, #max-price').on('input', function () {
            // validatePriceRange();
        });

        // Load categories from API
        function loadCategories() {
            $.ajax({
                url: '/api/categories',
                method: 'GET',
                success: function (response) {
                    const data = response.data || [];
                    const categorySelect = $('#filter-category');
                    categorySelect.empty();
                    data.forEach(category => {
                        categorySelect.append(new Option(category.name, category.id));
                    });
                    // Initialize select2 again to refresh options
                    categorySelect.trigger('change');
                },
                error: function (error) {
                    console.error('Failed to load categories:', error);
                }
            });
        }

        // Load branches from API
        function loadBranches() {
            $.ajax({
                url: '/api/branches',
                method: 'GET',
                success: function (response) {
                    const data = response.data || [];
                    const branchSelect = $('#filter-branch');
                    branchSelect.empty();
                    // Don't add "Tất cả" for multiple select
                    data.forEach(branch => {
                        branchSelect.append(new Option(branch.name, branch.id));
                    });
                    // Initialize select2 again to refresh options
                    branchSelect.trigger('change');
                },
                error: function (error) {
                    console.error('Failed to load branches:', error);
                }
            });
        }

        // Apply filters and update UI
        function applyFilters() {
            const filters = getFilterValues();
            // console.log('Applying filters:', filters);

            // Show active filters
            updateActiveFilters(filters);

            // Reload the products table with new filters
            if (typeof table !== 'undefined') {
                table.ajax.reload();
            }

            // Show the active filters section if there are any filters applied
            if (hasActiveFilters(filters)) {
                $('#activeFilters').removeClass('d-none');
            } else {
                $('#activeFilters').addClass('d-none');
            }
        }

        // Check if any filters are active
        function hasActiveFilters(filters) {
            return !!(
                filters.search ||
                (filters.category && filters.category.length > 0) ||
                (filters.branch && filters.branch.length > 0) ||
                filters.minPrice ||
                filters.maxPrice ||
                (filters.stock && filters.stock !== '0') ||
                (filters.gift && filters.gift !== 'all') ||
                filters.supplier ||
                filters.brand ||
                filters.status ||
                filters.rating ||
                filters.dateFrom ||
                filters.dateTo ||
                (filters.sort && filters.sort !== 'newest')
            );
        }

        // Update active filters display
        function updateActiveFilters(filters) {
            const activeFilters = [];

            // Check each filter and add badges for active ones
            if (filters.search) {
                activeFilters.push(createFilterBadge('search', 'Tìm kiếm', filters.search));
            }

            if (filters.category && filters.category.length > 0) {
                const categoryNames = $('#filter-category option:selected').map(function () {
                    return $(this).text();
                }).get().join(', ');
                activeFilters.push(createFilterBadge('category', 'Danh mục', categoryNames));
            }

            if (filters.branch && filters.branch.length > 0) {
                const branchNames = $('#filter-branch option:selected').map(function () {
                    return $(this).text();
                }).get().join(', ');
                activeFilters.push(createFilterBadge('branch', 'Chi nhánh', branchNames));
            }

            if (filters.minPrice || filters.maxPrice) {
                const priceText = `${filters.minPrice || '0'}đ - ${filters.maxPrice ? filters.maxPrice + "đ" : "Không giới hạn"}`;
                activeFilters.push(createFilterBadge('price', 'Giá', priceText));
            }

            if (filters.stock && filters.stock !== '0') {
                const stockMap = { '0': 'Tất cả', '1': 'Còn hàng', '2': 'Hết hàng', '3': 'Ngừng kinh doanh' };
                activeFilters.push(createFilterBadge('stock', 'Tình trạng', stockMap[filters.stock]));
            }

            if (filters.gift && filters.gift !== 'all') {
                const giftMap = { 'all': 'Tất cả', 'true': 'Là quà tặng', 'false': 'Không phải quà tặng' };
                activeFilters.push(createFilterBadge('gift', 'Quà tặng', giftMap[filters.gift]));
            }

            if (filters.supplier) {
                const supplierName = $('#filter-supplier option:selected').text();
                activeFilters.push(createFilterBadge('supplier', 'Nhà cung cấp', supplierName));
            }

            if (filters.brand) {
                const brandName = $('#filter-brand option:selected').text();
                activeFilters.push(createFilterBadge('brand', 'Thương hiệu', brandName));
            }

            if (filters.status) {
                const statusMap = { 'published': 'Đang hiển thị', 'draft': 'Đang ẩn' };
                activeFilters.push(createFilterBadge('status', 'Trạng thái', statusMap[filters.status]));
            }

            if (filters.rating) {
                activeFilters.push(createFilterBadge('rating', 'Đánh giá', `${filters.rating}+ sao`));
            }

            if (filters.dateFrom || filters.dateTo) {
                let dateText = '';
                if (filters.dateFrom && filters.dateTo) {
                    dateText = `${filters.dateFrom} đến ${filters.dateTo}`;
                } else if (filters.dateFrom) {
                    dateText = `Từ ${filters.dateFrom}`;
                } else {
                    dateText = `Đến ${filters.dateTo}`;
                }
                activeFilters.push(createFilterBadge('date', 'Thời gian', dateText));
            }

            if (filters.sort && filters.sort !== 'newest') {
                const sortMap = {
                    'oldest': 'Cũ nhất',
                    'price_asc': 'Giá tăng dần',
                    'price_desc': 'Giá giảm dần',
                    'name_asc': 'Tên A-Z',
                    'name_desc': 'Tên Z-A'
                };
                activeFilters.push(createFilterBadge('sort', 'Sắp xếp', sortMap[filters.sort]));
            }

            // Update the badges container
            const badgesContainer = $('#activeFilterBadges');
            badgesContainer.empty();

            activeFilters.forEach(badge => {
                badgesContainer.append(badge);
            });
        }

        // Create a filter badge HTML
        function createFilterBadge(type, label, value) {
            return `<div class="filter-badge badge bg-light text-dark d-flex align-items-center" data-type="${type}">
                        <span class="me-1">${label}:</span>
                        <span class="fw-medium">${value}</span>
                        <button type="button" class="btn-close btn-close-sm ms-2" aria-label="Close"></button>
                    </div>`;
        }

        // Remove a specific filter
        function removeFilter(type) {
            switch (type) {
                case 'search':
                    $('#search').val('');
                    break;
                case 'category':
                    $('#filter-category').val(null).trigger('change');
                    break;
                case 'branch':
                    $('#filter-branch').val(null).trigger('change');
                    break;
                case 'price':
                    $('#min-price, #max-price').val('');
                    break;
                case 'stock':
                    $('#filter-stock').val('0').trigger('change');
                    break;
                case 'gift':
                    $('#filter-gift').val('all').trigger('change');
                    break;
                case 'supplier':
                    $('#filter-supplier').val('').trigger('change');
                    break;
                case 'brand':
                    $('#filter-brand').val('').trigger('change');
                    break;
                case 'status':
                    $('#filter-status').val('').trigger('change');
                    break;
                case 'rating':
                    $('input[name="rating"]').prop('checked', false);
                    break;
                case 'date':
                    $('#date-from, #date-to').val('');
                    break;
                case 'sort':
                    $('#filter-sort').val('newest').trigger('change');
                    break;
            }
        }

        // Validate price range
        function validatePriceRange() {
            const minPrice = parseInt($('#min-price').val()) || 0;
            const maxPrice = parseInt($('#max-price').val()) || 0;

            if (maxPrice > 0 && minPrice > maxPrice) {
                $('#max-price').val(minPrice);
            }
        }

        // Define getFilterValues function and assign it to the global variable
        getFilterValues = function () {
            return {
                search: $('#search').val().trim(),
                category: $('#filter-category').val() || [],
                branch: $('#filter-branch').val() || [],
                minPrice: $('#min-price').val().trim(),
                maxPrice: $('#max-price').val().trim(),
                stock: $('#filter-stock').val(),
                gift: $('#filter-gift').val(),
                supplier: $('#filter-supplier').val(),
                brand: $('#filter-brand').val(),
                status: $('#filter-status').val(),
                rating: $('input[name="rating"]:checked').val(),
                dateFrom: $('#date-from').val(),
                dateTo: $('#date-to').val(),
                sort: $('#filter-sort').val()
            };
        }

        // Reset basic filters only
        function resetBasicFilters() {
            $('#search').val('');
            $('#filter-category').val(null).trigger('change');
            $('#filter-branch').val(null).trigger('change');
            $('#min-price, #max-price').val('');
            $('#filter-stock').val('0').trigger('change');
            $('#filter-gift').val('all').trigger('change');
        }

        // Reset all filters
        function resetAllFilters() {
            // Reset basic filters
            resetBasicFilters();

            // Reset advanced filters
            $('#filter-supplier').val('').trigger('change');
            $('#filter-brand').val('').trigger('change');
            $('#filter-status').val('').trigger('change');
            $('input[name="rating"]').prop('checked', false);
            $('#date-from, #date-to').val('');
            $('#filter-sort').val('newest').trigger('change');

            // Update UI
            $('#activeFilters').addClass('d-none');
            $('#activeFilterBadges').empty();
        }

    });
</script>