﻿namespace MiniAppCore.Models.DTOs.Surveys
{
    public class SurveyQuestionDTO
    {
        public bool IsRequired { get; set; }

        public string? Id { get; set; }
        public string? Type { get; set; }
        public string? SectionId { get; set; }
        public string? QuestionTitle { get; set; }

        public short DisplayOrder { get; set; }

        public List<SurveyAnswerDTO> Answers { get; set; } = new List<SurveyAnswerDTO>(); // Answers của câu hỏi
        public List<SurveyLikertQuestionDTO> LikertQuestions { get; set; } = new List<SurveyLikertQuestionDTO>();
    }

    public class SurveyLikertQuestionDTO
    {
        public short OptionCount { get; set; }
        public string? QuestionLikertId { get; set; }
        public string? QuestionLikertTitle { get; set; }
    }

    public class SurveyAnswerDTO
    {
        public string? Id { get; set; }
        public string? Key { get; set; }
        public string? Value { get; set; }
        public string? QuestionId { get; set; }

        public bool IsInput { get; set; }
        public short DisplayOrder { get; set; }
    }
}
