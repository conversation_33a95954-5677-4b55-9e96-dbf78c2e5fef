/*! Toasty.js - v1.5.0 - 2018-05-04
* https://jakim.me/Toasty.js/
* Copyright (c) 2015-2018 <PERSON><PERSON><PERSON>; Licensed MIT */

!function(){"use strict";function a(){var b={},c=!1,d=0,e=arguments.length;"[object Boolean]"===Object.prototype.toString.call(arguments[0])&&(c=arguments[0],d++);for(d;d<e;d++){var f=arguments[d];!function(d){for(var e in d)!0===Object.prototype.hasOwnProperty.call(d,e)&&(c&&"[object Object]"===Object.prototype.toString.call(d[e])?b[e]=a(!0,b[e],d[e]):b[e]=d[e])}(f)}return b}function b(a){return document.createElement(a||"div")}function c(a){return a.parentElement||a.parentNode}function d(a){return new RegExp("(^|\\s+)"+a+"(\\s+|$)")}function e(a,b){return(document.documentElement.classList?function(a,b){return a.classList.contains(b)}:function(a,b){return!(!a||!a.className)&&a.className.match(d(b))})(a,b)}function f(a){var b=arguments;return!(b.length<=1||"object"!=typeof a)&&(document.documentElement.classList?function(a,b){for(var c=1;c<b.length;c++)"string"==typeof b[c]&&a.classList.add(b[c]);return a}:function(a,b){for(var c=1;c<b.length;c++)e(a,b[c])||"string"!=typeof b[c]||(a.className+=(a.className?" ":"")+b[c]);return a})(a,b)}function g(a){var b=arguments;return!(b.length<=1||"object"!=typeof a)&&(document.documentElement.classList?function(a,b){for(var c=1;c<b.length;c++)"string"==typeof b[c]&&a.classList.remove(b[c]);return a}:function(a,b){for(var c=1;c<b.length;c++)e(a,b[c])&&"string"==typeof b[c]&&(a.className=a.className.replace(d(b[c]),"$2"));return a})(a,b)}function h(a,b,c,d){if("addEventListener"in a)try{a.addEventListener(b,c,d)}catch(e){if("object"!=typeof c||!c.handleEvent)throw e;a.addEventListener(b,function(a){c.handleEvent.call(c,a)},d)}else"attachEvent"in a&&("object"==typeof c&&c.handleEvent?a.attachEvent("on"+b,function(){c.handleEvent.call(c)}):a.attachEvent("on"+b,c));return a}function i(a,b,c,d){if("removeEventListener"in a)try{a.removeEventListener(b,c,d)}catch(e){if("object"!=typeof c||!c.handleEvent)throw e;a.removeEventListener(b,function(a){c.handleEvent.call(c,a)},d)}else"detachEvent"in a&&("object"==typeof c&&c.handleEvent?a.detachEvent("on"+b,function(){c.handleEvent.call(c)}):a.detachEvent("on"+b,c));return a}function j(){var a,c=b("transitionElement"),d={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(a in d)if(void 0!==c.style[a])return d[a]}function k(a,b,c){return b=b||c.duration,0==b&&(b=a.length*(p/2)),Math.floor(b)}function l(a,b){for(var c in a)if(!0===a.hasOwnProperty(c))switch(typeof a[c]){case"object":l(a[c],b);break;case"string":for(var d in b)!0===b.hasOwnProperty(d)&&(a[c]=a[c].replace(d,b[d]))}return a}var m=["fade","slideLeftFade","slideLeftRightFade","slideRightFade","slideRightLeftFade","slideUpFade","slideUpDownFade","slideDownFade","slideDownUpFade","pinItUp","pinItDown"],n={classname:"toast",transition:"fade",insertBefore:!0,duration:4e3,enableSounds:!1,autoClose:!0,progressBar:!1,sounds:{info:"./dist/sounds/info/1.mp3",success:"./dist/sounds/success/1.mp3",warning:"./dist/sounds/warning/1.mp3",error:"./dist/sounds/error/1.mp3"},onShow:function(a){},onHide:function(a){},prependTo:document.body.childNodes[0]},o={container:"{:class-name}-container",mainwrapp:"{:class-name}-wrapper",toasts:{info:"{:class-name}--info",success:"{:class-name}--success",warning:"{:class-name}--warning",error:"{:class-name}--error"},animate:{init:"{:transition}-init",show:"{:transition}-show",hide:"{:transition}-hide"},progressbar:"{:class-name}-progressbar",playerclass:"{:class-name}-soundplayer"},p=100,q=function(a,d,e,g){var i=e[a],j=f(b("audio"),g);h(j,"ended",function(){var a=c(this);this.remove(),a.childNodes.length<1&&c(a).remove()}),j.setAttribute("autoplay","autoplay"),j.innerHTML='<source src="'+i+'" type="audio/mpeg"/><embed hidden="true" autoplay="false" loop="false" src="'+i+'" />',c(d).appendChild(j)},r=function(a,b,c,d,e,g,k){var l=0,m=function(b){i(b.target,b.type,m,!1),"function"==typeof k&&k(a)},n=function(){var c=j();void 0!==c?h(b,c,m,!1):"function"==typeof k&&k(a),f(b,d.show)},o=c.childNodes;o=o[!0===g?0:o.length],c.insertBefore(b,o),function(a,b){clearTimeout(l),l=setTimeout(a,b)}(n,p)},s=function(a,b,d,e,g){var k=0,l=function(b){i(b.target,b.type,l,!1),m(),"function"==typeof g&&g(a)},m=function(){var a=c(b);b.remove(),a.childNodes.length<1&&c(a).remove()},n=function(){var c=j();void 0!==c?h(b,c,l,!1):(m(),"function"==typeof g&&g(a)),f(b,e.hide)};!function(a,b){clearTimeout(k),k=setTimeout(a,b)}(n,10*p+d)},t=function(a,b,c,d,e){var i=function(f){f.stopPropagation(),g(b,e),s(a,b,0,c,d)};f(b,e),h(b,"click",i)},u=function(a,c,d,e){var g=0,h=function(){var g=f(b("div"),e.progressbar,e.progressbar+"--"+a);c.appendChild(g);var h=0,i=0,j=setInterval(function(){h++,i=Math.round(1e3*h/d),i>100?clearInterval(j):g.style.width=i+"%"},10)};!function(a,b){clearTimeout(g),g=setTimeout(a,b)}(h,10*p)},v=function(b,c){return"string"==typeof c&&(b.classmap[c]=a(!0,o,{}),b.classmap[c]=l(b.classmap[c],{"{:class-name}":b.settings.classname,"{:transition}":c})),c},w=function(a){if(this.settings={},this.classmap={},this.configure("object"==typeof a?a:{}),"object"==typeof m)for(var b in m)!0===m.hasOwnProperty(b)&&v(this,m[b])};w.prototype.configure=function(b){return this.settings=a(!0,n,this.settings,b),this},w.prototype.transition=function(a){return this.settings.transition=v(this,a),this},w.prototype.toast=function(a,d,e){var g=this.classmap,h=this.settings;!1===g.hasOwnProperty(h.transition)&&v(this,h.transition);var i=g[h.transition],j=null;j="string"==typeof h.transition?document.querySelector("."+i.container+"--"+h.transition):document.querySelector("."+i.container);var k=!!j;if(k)j=j.querySelector("."+i.mainwrapp);else{j=f(b("div"),i.container,i.container+"--"+h.transition);var l=f(b("div"),i.mainwrapp);j.appendChild(j=l)}var m=f(b("div"),h.classname,i.toasts[a],i.animate.init);return m.innerHTML=d,k||document.body.insertBefore(c(j),h.prependTo),1==h.enableSounds&&q(a,j,h.sounds,i.playerclass),r(a,m,j,i.animate,0,h.insertBefore,h.onShow),1==h.autoClose?s(a,m,e,i.animate,h.onHide):t(a,m,i.animate,h.onHide,"close-on-click"),1==h.progressBar&&1==h.autoClose&&u(a,m,e,i),this},w.prototype.info=function(a,b){b=k(a,b,this.settings),this.toast("info",a,b)},w.prototype.success=function(a,b){b=k(a,b,this.settings),this.toast("success",a,b)},w.prototype.warning=function(a,b){b=k(a,b,this.settings),this.toast("warning",a,b)},w.prototype.error=function(a,b){b=k(a,b,this.settings),this.toast("error",a,b)},"remove"in Element.prototype||(Element.prototype.remove=function(){this.parentNode&&this.parentNode.removeChild(this)}),window.Toasty=w}(window,document);