﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Offers;
using MiniAppCore.Services.Offers.Promotions;
using MiniAppCore.Services.Products;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class PromotionController : Controller
    {
        private readonly IPromotionService _promotionService;
        private readonly IProductService _productService;

        public PromotionController(IPromotionService promotionService, IProductService productService)
        {
            _promotionService = promotionService;
            _productService = productService;
        }
        public IActionResult Index()
        {
            return View();
        }
        [HttpGet("Promotion/Create")]
        public async Task<IActionResult> Create()
        {
            var promotion = new Promotion()
            {
                Id = string.Empty,
                Name = string.Empty,
                Description = string.Empty,
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới chương trình khuyến mãi";

            var products = (await _productService.GetAllAsync()).ToList();
            return PartialView("_Promotion", (promotion, products, new List<string>(), new List<string>()));
        }

        [HttpGet("Promotion/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Title = "Cập nhật chương trình khuyến mãi";
            ViewBag.Button = "Cập nhật";

            var result = await _promotionService.GetByIdAsync(id);
            var gifts = (await _promotionService.GetProductGiftByPromotionId(id)).Select(x => x.Id).ToList();
            var products = (await _promotionService.GetProductBuyByPromotionId(id)).Select(x => x.Id).ToList();

            var allProducts = (await _productService.GetAllAsync()).ToList();
            return PartialView("_Promotion", (result, allProducts, products, gifts));
        }
    }
}
