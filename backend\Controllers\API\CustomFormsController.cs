﻿using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Memberships;
using MiniAppCore.Services.Memberships.CustomForms;

namespace MiniAppCore.Controllers.API
{
    [Route("api/[controller]")]
    [ApiController]
    public class CustomFormsController(
        ILogger<CustomFormsController> logger,
        ICustomFormService customFormService) : ControllerBase
    {
        [HttpGet]
        public async Task<IActionResult> GetPage([FromQuery] RequestQuery query)
        {
            try
            {
                var result = await customFormService.GetPage(query);
                return Ok(new
                {
                    Code = 0,
                    Message = "Success",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError("Error occurred while retrieving custom forms: {Message}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] CustomFormRequest request)
        {
            try
            {
                var result = await customFormService.CreateAsync(request);
                return Ok(new
                {
                    Code = 0,
                    Message = "Custom form created successfully",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError("Error occurred while creating custom form: {Message}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] CustomFormRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(new
                    {
                        Code = -1,
                        Message = "Invalid model state",
                        Data = ModelState
                    });

                var result = await customFormService.UpdateAsync(id, request);
                return Ok(new
                {
                    Code = 0,
                    Message = "Custom form updated successfully",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError("Error occurred while updating custom form with ID {FormId}: {Message}", id, ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var result = await customFormService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Custom form deleted successfully",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError("Error occurred while deleting custom form with ID {FormId}: {Message}", id, ex.Message);
                return StatusCode(500);
            }
        }
    }
}
