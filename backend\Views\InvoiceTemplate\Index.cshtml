﻿<div class="row">
    <!-- Header Section -->
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> sách mẫu hóa đơn</h4>
            </div>
            <!-- Add Invoice Template Button -->
            <button type="button" class="btn btn-primary mt-2" onclick="GetFormTemplate()">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">T<PERSON><PERSON> kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12" style="z-index: 1">
        <div id="spinner" class="text-center my-4"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-template" class="data-table table table-bordered table-hover mb-0">
            </table>
        </div>
    </div>
</div>

<div id="modal-template" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-dialog-centered modal-xl" style="max-width: 90vw;"></div>
</div>

@section Scripts {
    <!--TinyMCE-->
    <script src="https://cdn.jsdelivr.net/npm/tinymce@7.7.2/tinymce.min.js"></script>
    <script>
        const options = {
            dropdownParent: $("#modal-template")
        }
        var tinymce;
        function reloadTable() {
            const element = event.target || event.srcElement;

            if (element && element.id && element.id.includes('filter')) {
                table.ajax.reload();
            }
        }

        $(document).ready(function () {
            GetListTemplate();
            $('#search').on('input', search);
        });

        function GetListTemplate() {
            table = new DataTable("#list-template", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const pageSize = data.length;
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetPage", "InvoiceTemplates")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                0: index + 1, // STT
                                1: item.name, // Tên
                                2: item.isDefault ? 'Có' : 'Không',
                                3: FormatDateTime(item.createdDate),
                                4: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                <a onclick="GetFormTemplate('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                    <i class="ri-edit-line fs-6 mr-0"></i>
                                                </a>
                                                <a onclick="DeleteTemplate('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                    <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                </a>
                                            </div>`,
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên", data: 1 },
                    { title: "Mặc định", data: 2 },
                    { title: "Ngày tạo", data: 3 },
                    { title: "Thao tác", data: 4, className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormTemplate(id) {
            if (tinymce && tinymce.get('content')) {
                tinymce.get('content').remove();
            }

            const url = id ? `@Url.Action("Detail", "InvoiceTemplate")/${id}` : "@Url.Action("Create", "InvoiceTemplate")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-template").modal("toggle");
                }
            })
        }

        function DeleteTemplate(id) {
            const url = `/api/InvoiceTemplates/${id}`
            DeleteItem(url);
        }

        function InitialEditor() {
            tinymce.init({
                selector: '#content',
                height: 350,
                menubar: false,
                plugins: [
                    'lists', 'link', 'image', 'table', 'code'
                ],
                toolbar: `
                            undo redo |
                            formatselect fontsizeselect |
                            bold italic underline strikethrough |
                            forecolor backcolor |
                            alignleft aligncenter alignright alignjustify |
                            bullist numlist outdent indent |
                            blockquote code |
                            image link table | removeformat
                        `,
                fontsize_formats: '8px 10px 12px 13px 14px 16px 18px 20px 24px 28px 32px 36px 48px 72px',
                content_style: `
                            body { font-family:Arial,sans-serif; font-size:14px; }
                            table { width: 100%; border-collapse: collapse; }
                            th, td { border: 1px solid #000; padding: 6px; } `,
                setup: function (editor) {
                    editor.on('Change KeyUp', function () {
                        const html = editor.getContent();
                        document.getElementById('preview').innerHTML = html;
                    });
                }
            });
        }

        function HandleSaveOrUpdate(id) {
            $(".error-message").text("");

            const fields = ["name"];

            const data = fields.reduce((obj, field) => {
                obj[field] = $(`#${field}`).val().trim();
                return obj;
            }, {});

            data.name = $("#name").val().trim();
            data.isDefault = $("#isDefault").val() === "true";
            data.content = tinymce.get('content').getContent(); // lấy HTML từ TinyMCE

            const requiredFields = {
                name: "Tên mẫu hóa đơn là bắt buộc."
            };

            for (const [field, message] of Object.entries(requiredFields)) {
                if (!data[field]) {
                    $(`#error-${field}`).text(message);
                }
            }

            for (const [field, message] of Object.entries(requiredFields)) {
                if (!data[field]) {
                    AlertResponse(message, 'warning');
                    return;
                }
            }

            const isNew = !id || id.trim() === "";
            const url = !isNew ? `/api/InvoiceTemplates/${id}` : '/api/InvoiceTemplates';
            const method = !isNew ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                data: JSON.stringify(data),
                contentType: 'application/json',
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'warning')
                    }
                    $("#modal-template").modal("toggle");
                },
                error: function (err) {
                    AlertResponse("Lỗi máy chủ, vui lòng thử lại sau!", 'error')
                }
            });
        }

        function ClearForm() {
            const url = "@Url.Action("Create", "InvoiceTemplate")";

            $.ajax({
                url: url,
                type: "GET",
                success: function (data) {
                    $("#modal-content").html(data);
                },
                error: function () {
                    console.error("Lỗi khi reset form từ route: " + url);
                }
            });
        }

    </script>
}
