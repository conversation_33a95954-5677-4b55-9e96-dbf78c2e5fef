﻿@using MiniAppCore.Entities.Memberships
@using MiniAppCore.Entities.Offers.Vouchers

<div class="modal-content border-0">
    <div class="modal-header bg-gradient-primary text-white border-0">
        <h5 class="modal-title fw-bold" id="exampleModalLabel">
            <i class="fas fa-gift me-2"></i>Tặng voucher
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>

    <div class="modal-body p-0">
        <div class="p-4">
            <!-- Gift Mode Selection -->
            <div class="mb-4">
                <h6 class="text-uppercase text-muted fw-bold mb-3 small">Chọn chế độ tặng</h6>
                <div class="gift-mode-selector d-flex gap-3">
                    <div class="gift-mode-option flex-fill">
                        <input type="radio" name="giftMode" id="giftModeTag" value="tag" checked class="d-none">
                        <label for="giftModeTag" class="gift-mode-label d-flex align-items-center justify-content-center gap-2 py-3 rounded-3">
                            <i class="fas fa-tags"></i>
                            <span>Tặng theo tag</span>
                        </label>
                    </div>
                    <div class="gift-mode-option flex-fill">
                        <input type="radio" name="giftMode" id="giftModeUser" value="user" class="d-none">
                        <label for="giftModeUser" class="gift-mode-label d-flex align-items-center justify-content-center gap-2 py-3 rounded-3">
                            <i class="fas fa-users"></i>
                            <span>Tặng Khách hàng cụ thể</span>
                        </label>
                    </div>
                </div>
            </div>

            <hr class="my-4 bg-light">

            <!-- Recipients Selection -->
            <div class="mb-4">
                <h6 class="text-uppercase text-muted fw-bold mb-3 small">Chọn người nhận</h6>

                <div id="tagSelectionSection">
                    <div class="form-floating mb-2">
                        <select id="tags" class="form-select custom-select" multiple>
                            <option value="all">Tất cả khách hàng</option>
                        </select>
                        <label><i class="fas fa-tag me-1"></i>Chọn Tag</label>
                    </div>
                    <div class="form-text text-muted fst-italic small">
                        Chọn "Tất cả khách hàng" để gửi đến toàn bộ khách hàng
                    </div>
                </div>

                <div id="userSelectionSection" style="display:none">
                    <div class="form-floating mb-2">
                        <select id="users" class="form-select custom-select my-2" multiple>
                        </select>
                        <label><i class="fas fa-user me-1"></i>Chọn khách hàng</label>
                    </div>
                    <div class="form-text text-muted fst-italic small">
                        Có thể chọn nhiều Khách hàng
                    </div>
                </div>
            </div>

            <hr class="my-4 bg-light">

            <!-- Voucher Selection -->
            <div class="mb-4">
                <h6 class="text-uppercase text-muted fw-bold mb-3 small">Chọn voucher</h6>
                <div class="form-floating mb-2">
                    <select id="vouchers" class="form-select custom-select my-2" multiple>
                    </select>
                    <label><i class="fas fa-ticket-alt me-1"></i>Chọn Voucher</label>
                </div>
                <div class="form-text text-muted fst-italic small">
                    Chọn một hoặc nhiều voucher để tặng
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer border-0 bg-light p-3">
        <button type="button" class="btn btn-light fw-semibold" data-bs-dismiss="modal">
            <i class="fas fa-times me-1"></i>Đóng
        </button>
        <button type="button" class="btn btn-primary fw-semibold" onclick="SendGift()">
            <i class="fas fa-paper-plane me-1"></i>Tặng voucher
        </button>
    </div>
</div>

<style>
    /* Modern custom styling */
    .gift-mode-label {
        border: 2px solid #e9ecef;
        background: #f8f9fa;
        color: #6c757d;
        transition: all 0.2s ease;
        cursor: pointer;
        font-weight: 500;
    }

    input[type="radio"]:checked+.gift-mode-label {
        border-color: var(--bs-primary);
        background: rgba(13, 110, 253, 0.05);
        color: var(--bs-primary);
    }

    .gift-mode-label:hover {
        border-color: #ced4da;
        background: #f1f3f5;
    }

    /* Enhanced Select2 styling */
    .select2-container--default .select2-selection--multiple {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem !important;
        min-height: 58px !important;
        padding: 0.5rem 0.75rem !important;
        padding-top: 1.625rem !important;
        margin-top: 5px;
    }

    .select2-container--default.select2-container--focus .select2-selection--multiple {
        border-color: #86b7fe !important;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        border: 1px solid #e9ecef !important;
        border-radius: 0.25rem !important;
        padding: 0.25rem 0.5rem !important;
        margin: 0.25rem !important;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #f1f3f5 !important;
        color: #212529 !important;
    }

    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #e9ecef !important;
    }

    .select2-dropdown {
        border: 1px solid #ced4da !important;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        border-radius: 0.375rem !important;
        overflow: hidden;
    }

    .form-floating>.select2-container--default {
        height: auto !important;
    }

    .bg-gradient-primary {
        /* background: linear-gradient(90deg, #0d6efd, #0b5ed7) !important; */
    }

    .modal-content {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .form-text {
        margin-top: 0.25rem;
    }
</style>

<script>
    $(document).ready(function () {
        // Initialize select2 for all dropdowns with enhanced styling
        $('#tags, #users, #vouchers').select2({
            placeholder: 'Tìm kiếm...',
            allowClear: true,
            width: '100%',
            dropdownParent: $('#giftVoucherModal'),
        });

        // Toggle between tag and user selection modes
        $('input[name="giftMode"]').change(function () {
            const mode = $(this).val();
            if (mode === 'tag') {
                $('#tagSelectionSection').fadeIn(200);
                $('#userSelectionSection').fadeOut(200);
            } else {
                $('#tagSelectionSection').fadeOut(200);
                $('#userSelectionSection').fadeIn(200);
            }
        });

        // Load tags dynamically with enhanced styling
        $('#tags').select2({
            ajax: {
                url: '/api/Tags',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        keyword: params.term,
                        page: 1,
                        pageSize: 20
                    };
                },
                processResults: function (res) {
                    // Add "All" tag at the beginning
                    var results = [{
                        id: 'all',
                        text: 'Tất cả khách hàng hàng',
                    }];

                    // Add other tags
                    if (res.data) {
                        res.data.forEach(function (item) {
                            results.push({
                                id: item.id,
                                text: item.name
                            });
                        });
                    }

                    return {
                        results: results
                    };
                },
                cache: true
            },
            templateResult: formatTag,
            templateSelection: formatTagSelection
        });

        // Format tag options in dropdown
        function formatTag(tag) {
            if (!tag.id) {
                return tag.text;
            }

            if (tag.id === 'all') {
                return $('<div class="d-flex align-items-center py-1"><span class="badge bg-primary me-2 px-2 py-1"><i class="fas fa-users"></i></span>' + tag.text + '</div>');
            }

            return $('<div class="d-flex align-items-center py-1"><span class="badge bg-secondary me-2 px-2 py-1"><i class="fas fa-tag"></i></span>' + tag.text + '</div>');
        }

        // Format selected tags
        function formatTagSelection(tag) {
            if (!tag.id) return tag.text;

            if (tag.id === 'all') {
                return $('<span><i class="fas fa-users me-1"></i> ' + tag.text + '</span>');
            }
            return $('<span><i class="fas fa-tag me-1"></i> ' + tag.text + '</span>');
        }

        // Load users dynamically
        $('#users').select2({
            ajax: {
                url: '/api/memberships',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        keyword: params.term,
                        page: 1,
                        pageSize: 20
                    };
                },
                processResults: function (res) {
                    return {
                        results: res.data.map(function (item) {
                            return {
                                id: item.userZaloId,
                                text: item.userZaloName || item.userZaloId,
                                avatar: item.userZaloAvatar
                            };
                        })
                    };
                },
                cache: true
            },
            templateResult: formatUser,
            templateSelection: formatUserSelection
        });

        // Format user options in dropdown
        function formatUser(user) {
            if (!user.id) {
                return user.text;
            }

            return $('<div class="d-flex align-items-center py-1"><span class="badge bg-info me-2 px-2 py-1"><i class="fas fa-user"></i></span>' + user.text + '</div>');
        }

        // Format selected users
        function formatUserSelection(user) {
            if (!user.id) return user.text;
            return $('<span><i class="fas fa-user me-1"></i> ' + user.text + '</span>');
        }

        // Load vouchers dynamically
        $('#vouchers').select2({
            ajax: {
                url: '/api/vouchers',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        keyword: params.term,
                        page: 1,
                        pageSize: 20,
                        isActive: true
                    };
                },
                processResults: function (res) {
                    return {
                        results: res.data.map(function (item) {
                            return {
                                id: item.id,
                                text: item.code,
                                discount: item.discount,
                                type: item.voucherType
                            };
                        })
                    };
                },
                cache: true
            },
            templateResult: formatVoucher,
            templateSelection: formatVoucherSelection
        });

        // Format voucher options in dropdown
        function formatVoucher(voucher) {
            if (!voucher.id) {
                return voucher.text;
            }

            return $('<div class="d-flex align-items-center py-1"><span class="badge bg-success me-2 px-2 py-1"><i class="fas fa-ticket-alt"></i></span>' + voucher.text + '</div>');
        }

        // Format selected vouchers
        function formatVoucherSelection(voucher) {
            if (!voucher.id) return voucher.text;
            return $('<span><i class="fas fa-ticket-alt me-1"></i> ' + voucher.text + '</span>');
        }
    });

    // Function to send gift vouchers
    function SendGift() {
        const mode = $('input[name="giftMode"]:checked').val();
        const voucherIds = $('#vouchers').val();

        if (!voucherIds || voucherIds.length === 0) {
            swal("Lỗi", "Vui lòng chọn ít nhất một voucher", "error");
            return;
        }

        let url, data;

        if (mode === 'tag') {
            const tagIds = $('#tags').val();
            if (!tagIds || tagIds.length === 0) {
                swal("Lỗi", "Vui lòng chọn ít nhất một tag", "error");
                return;
            }

            url = '/api/vouchers/send-gift-by-tags';
            data = {
                tagIds: tagIds,
                voucherIds: voucherIds
            };
        } else {
            const userZaloIds = $('#users').val();
            if (!userZaloIds || userZaloIds.length === 0) {
                swal("Lỗi", "Vui lòng chọn ít nhất một khách hàng", "error");
                return;
            }

            url = '/api/vouchers/sendGift';
            data = {
                userZaloIds: userZaloIds,
                voucherIds: voucherIds
            };
        }

        // Show confirmation before sending
        swal({
            title: "Xác nhận",
            text: "Bạn có chắc chắn muốn tặng voucher?",
            icon: "warning",
            buttons: {
                cancel: {
                    text: "Hủy",
                    value: null,
                    visible: true,
                    className: "btn-light"
                },
                confirm: {
                    text: "Tặng voucher",
                    value: true,
                    visible: true,
                    className: "btn-primary"
                }
            }
        }).then((confirmed) => {
            if (!confirmed) return;

            // Show loading indicator
            const $btn = $('.modal-footer .btn-primary').prop('disabled', true);
            const originalText = $btn.html();
            $btn.html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Đang xử lý...');

            $.ajax({
                url: url,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function (response) {
                    // Restore button
                    $btn.prop('disabled', false).html(originalText);

                    if (response.code === 0 || response.code === 200) {
                        // Success case
                        $('#giftVoucherModal').modal('hide');
                        swal("Thành công", response.message || "Tặng voucher thành công!", "success");
                        // If there are messages, display them in a summary modal
                        if (response.messages && Array.isArray(response.messages) && response.messages.length > 0) {
                            showResultSummary(response.messages);
                        } else if (response.data && Array.isArray(response.data) && response.data.length > 0) {
                            showResultSummary(response.data);
                        }

                        // Reload table if it exists
                        if (typeof table !== 'undefined') {
                            table.ajax.reload(null, false);
                        }
                    } else {
                        // Error case - Check both "messages" and "messsages" (with typo in API)
                        if (response.messsages && Array.isArray(response.messsages) && response.messsages.length > 0) {
                            // Show the first message as the main alert
                            swal("Lỗi", response.messsages[0], "error");
                        } else if (response.messages && Array.isArray(response.messages) && response.messages.length > 0) {
                            // Show the first message as the main alert
                            swal("Lỗi", response.messages[0], "error");
                        } else if (response.message) {
                            swal("Lỗi", response.message, "error");
                        } else {
                            swal("Lỗi", "Có lỗi xảy ra khi tặng voucher", "error");
                        }
                    }
                },
                error: function () {
                    // Restore button
                    $btn.prop('disabled', false).html(originalText);
                    swal("Lỗi", "Có lỗi xảy ra khi gửi yêu cầu", "error");
                }
            });
        });
    }

    // Function to display gift results in a summary modal
    function showResultSummary(messages) {
        if (!messages || messages.length === 0) return;

        // Count success and failure messages
        const successMessages = messages.filter(msg => msg.includes('thành công'));
        const failureMessages = messages.filter(msg => !msg.includes('thành công'));

        // Create a modal with the results
        let modalHtml = `
        <div class="modal fade" id="giftResultModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-lg">
                <div class="modal-content border-0">
                    <div class="modal-header bg-gradient-primary text-white border-0">
                        <h5 class="modal-title fw-bold">
                            <i class="fas fa-check-circle me-2"></i>Kết quả tặng voucher
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="p-4">
                            <div class="d-flex justify-content-between mb-4">
                                <div class="text-success d-flex align-items-center gap-2">
                                    <span class="badge bg-success px-2 py-1"><i class="fas fa-check-circle"></i></span>
                                    <span><strong>${successMessages.length}</strong> thành công</span>
                                </div>
                                <div class="text-danger d-flex align-items-center gap-2">
                                    <span class="badge bg-danger px-2 py-1"><i class="fas fa-times-circle"></i></span>
                                    <span><strong>${failureMessages.length}</strong> thất bại</span>
                                </div>
                            </div>`;

        if (successMessages.length > 0) {
            modalHtml += `
                            <div class="card mb-4 shadow-sm">
                                <div class="card-header bg-light py-3">
                                    <h6 class="m-0 text-success d-flex align-items-center gap-2">
                                        <i class="fas fa-check-circle"></i>Thành công
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">`;

            successMessages.forEach(msg => {
                modalHtml += `
                                        <div class="list-group-item py-2 px-3 d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success me-2"></i>${msg}
                                        </div>`;
            });

            modalHtml += `
                                    </div>
                                </div>
                            </div>`;
        }

        if (failureMessages.length > 0) {
            modalHtml += `
                            <div class="card shadow-sm">
                                <div class="card-header bg-light py-3">
                                    <h6 class="m-0 text-danger d-flex align-items-center gap-2">
                                        <i class="fas fa-times-circle"></i>Thất bại
                                    </h6>
                                </div>
                                <div class="card-body p-0">
                                    <div class="list-group list-group-flush" style="max-height: 200px; overflow-y: auto;">`;

            failureMessages.forEach(msg => {
                modalHtml += `
                                        <div class="list-group-item py-2 px-3 d-flex align-items-center">
                                            <i class="fas fa-times-circle text-danger me-2"></i>${msg}
                                        </div>`;
            });

            modalHtml += `
                                    </div>
                                </div>
                            </div>`;
        }

        modalHtml += `
                        </div>
                    </div>
                    <div class="modal-footer border-0 bg-light">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Đóng</button>
                    </div>
                </div>
            </div>
        </div>`;

        // Remove any existing modal
        $('#giftResultModal').remove();

        // Add the modal to the page and show it
        $('body').append(modalHtml);

        // Initialize and show the modal
        if (typeof bootstrap !== 'undefined') {
            const resultModal = new bootstrap.Modal(document.getElementById('giftResultModal'));
            resultModal.show();
        } else {
            $('#giftResultModal').modal('show');
        }
    }
</script>
