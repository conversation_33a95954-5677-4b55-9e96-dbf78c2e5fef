﻿﻿<div id="importVoucherModal" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fw-bold">Import Voucher</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="importTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="template-tab" data-bs-toggle="tab" data-bs-target="#template" type="button" role="tab" aria-controls="template" aria-selected="true">
                            <i class="bi bi-file-earmark-excel me-1"></i>Tải file mẫu
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab" aria-controls="upload" aria-selected="false">
                            <i class="bi bi-upload me-1"></i>Upload file
                        </button>
                    </li>
                </ul>

                <div class="tab-content p-3" id="importTabsContent">
                    <!-- Tab tải file mẫu -->
                    <div class="tab-pane fade show active" id="template" role="tabpanel" aria-labelledby="template-tab">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Mẫu file import voucher</h5>
                                <p class="text-muted">Tải file mẫu để nhập thông tin voucher theo định dạng yêu cầu</p>

                                <div class="text-center mb-4">
                                    <a href="@Url.Action("ImportTemplateExcel", "Voucher")" class="btn btn-primary">
                                        <i class="bi bi-download me-2"></i>Tải file mẫu
                                    </a>
                                </div>

                                <div class="alert alert-info">
                                    <h6 class="alert-heading"><i class="bi bi-info-circle me-2"></i>Yêu cầu về file import</h6>
                                    <ul class="mb-0">
                                        <li>File phải có định dạng Excel (.xlsx)</li>
                                        <li>Không thay đổi tên các cột trong file</li>
                                        <li>Nhập đúng định dạng dữ liệu theo yêu cầu</li>
                                    </ul>
                                </div>

                                <h6 class="mt-3">Cấu trúc file</h6>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-sm">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Cột</th>
                                                <th>Mô tả</th>
                                                <th>Định dạng</th>
                                                <th>Bắt buộc</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Name</td>
                                                <td>Tên voucher</td>
                                                <td>Text</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                            <tr>
                                                <td>Code</td>
                                                <td>Mã voucher</td>
                                                <td>Text</td>
                                                <td>
                                                    <span class="badge bg-primary">Có</span>
                                                    <span class="badge bg-primary">Unique</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Description</td>
                                                <td>Mô tả</td>
                                                <td>Text</td>
                                                <td><span class="badge bg-secondary">Không</span></td>
                                            </tr>
                                            <tr>
                                                <td>DiscountValue</td>
                                                <td>Giá trị giảm giá</td>
                                                <td>Số</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                            <tr>
                                                <td>PointRequired</td>
                                                <td>Điểm yêu cầu</td>
                                                <td>Số nguyên</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                            <tr>
                                                <td>StartDate</td>
                                                <td>Ngày bắt đầu</td>
                                                <td>dd/MM/yyyy</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                            <tr>
                                                <td>EndDate</td>
                                                <td>Ngày kết thúc</td>
                                                <td>dd/MM/yyyy</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                            <tr>
                                                <td>ExpiryDate</td>
                                                <td>Ngày hết hạn</td>
                                                <td>dd/MM/yyyy</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                            <tr>
                                                <td>DiscountType</td>
                                                <td>Loại giảm giá</td>
                                                <td>Percentage/FixedAmount</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                            <tr>
                                                <td>Quantity</td>
                                                <td>Số lượng voucher có thể phát hành</td>
                                                <td>Số nguyên</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                            <tr>
                                                <td>ExchangeTimes</td>
                                                <td>Số lần người dùng có thể đổi voucher</td>
                                                <td>Số nguyên</td>
                                                <td><span class="badge bg-primary">Có</span></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab upload file -->
                    <div class="tab-pane fade" id="upload" role="tabpanel" aria-labelledby="upload-tab">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Upload file import voucher</h5>
                                <p class="text-muted">Chọn file Excel đã điền thông tin voucher</p>

                                <form id="importVoucherForm" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="voucherFile" class="form-label">Chọn file Excel (.xlsx)</label>
                                        <input type="file" id="voucherFile" name="file" class="form-control" accept=".xlsx">
                                    </div>
                                    <div id="fileInfo" class="mb-3 d-none">
                                        <div class="alert alert-success py-2">
                                            <i class="bi bi-file-earmark-excel me-2"></i>
                                            <span id="fileName"></span>
                                        </div>
                                    </div>
                                </form>

                                <div class="alert alert-warning" hidden>
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>Lưu ý:</strong> Hệ thống sẽ kiểm tra tính hợp lệ của dữ liệu trước khi import.
                                </div>
                            </div>
                        </div>

                        <!-- Add this new error message results section -->
                        <div id="importResultSection" class="mt-3 d-none">
                            <div class="card border-warning">
                                <div class="card-header bg-warning bg-opacity-10">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">
                                            <i class="bi bi-exclamation-triangle me-2"></i>Danh sách lỗi
                                        </h6>
                                        <span class="badge bg-warning text-dark" id="errorCount">0</span>
                                    </div>
                                </div>
                                <div class="card-body p-0">
                                    <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                        <table class="table table-sm table-hover mb-0">
                                            <thead class="table-light">
                                                <tr>
                                                    <th class="ps-3">Dòng</th>
                                                    <th>Lỗi</th>
                                                </tr>
                                            </thead>
                                            <tbody id="errorMessagesList">
                                                <!-- Error messages will be populated here -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-success mt-3 d-none" id="importSuccessMsg">
                                <i class="bi bi-check-circle me-2"></i>
                                <pre>Đã import thành công <span id="importCount">0</span> voucher</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="importBtn" disabled>
                    <i class="bi bi-upload me-1"></i>Import
                </button>
            </div>
        </div>
    </div>
</div>
