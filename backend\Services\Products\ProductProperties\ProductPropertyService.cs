﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Products.Variants;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.DTOs.Properties;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Models.Responses.Products;

namespace MiniAppCore.Services.Products.Variants
{
    public class ProductPropertyService(IUnitOfWork unitOfWork, IMapper mapper) : Service<Property>(unitOfWork), IProductPropertyService
    {
        private readonly IRepository<Variant> _variantRepo = unitOfWork.GetRepository<Variant>();
        private readonly IRepository<VariantValue> _variantValueRepo = unitOfWork.GetRepository<VariantValue>();

        private readonly IRepository<PropertyValue> _propertyValueRepo = unitOfWork.GetRepository<PropertyValue>();

        private readonly IRepository<Entities.Products.Product> _productRepo = unitOfWork.GetRepository<Entities.Products.Product>();

        public async Task<int> CreateAsync(PropertyDTO model)
        {
            var property = mapper.Map<Property>(model);
            property.CreatedDate = DateTime.Now;
            property.UpdatedDate = DateTime.Now;

            if (model.Options.Any())
            {
                var propertyValues = model.Options.Select(o => new PropertyValue
                {
                    Id = Guid.NewGuid().ToString("N"),
                    Value = o.Value ?? string.Empty,
                    PropertyId = property.Id,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now
                }).ToList();

                _propertyValueRepo.AddRange(propertyValues);
            }

            return await base.CreateAsync(property);
        }

        public async Task<int> UpdateAsync(string id, PropertyDTO model)
        {
            var property = await base.GetByIdAsync(id);
            if (property == null)
            {
                throw new CustomException("Không tìm thấy thuộc tính");
            }

            mapper.Map(model, property);
            property.UpdatedDate = DateTime.Now;

            // Cập nhật danh sách Options
            var existingOptions = _propertyValueRepo.AsQueryable().Where(pv => pv.PropertyId == id).ToList();

            // Xóa các Options không còn trong danh sách mới
            var optionsToRemove = existingOptions
                .Where(o => model.Options.All(m => m.PropertyValueId != o.Id))
                .ToList();
            _propertyValueRepo.DeleteRange(optionsToRemove);

            // Thêm hoặc cập nhật các Options mới
            foreach (var option in model.Options)
            {
                var existingOption = existingOptions.FirstOrDefault(o => o.Id == option.PropertyValueId);
                if (existingOption != null)
                {
                    existingOption.Value = option.Value ?? string.Empty;
                    existingOption.UpdatedDate = DateTime.Now;
                }
                else
                {
                    _propertyValueRepo.Add(new PropertyValue
                    {
                        Id = Guid.NewGuid().ToString("N"),
                        Value = option.Value ?? string.Empty,
                        PropertyId = id,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    });
                }
            }

            return await base.UpdateAsync(property);
        }

        public async Task<PropertyDTO> GetDetailProperty(string propertyId)
        {
            // Lấy property từ database
            var property = await base.GetByIdAsync(propertyId);
            if (property == null)
            {
                throw new CustomException(200, "Không tìm thấy thuộc tính!");
            }
            // Lấy danh sách giá trị của property này
            var propertyValues = await _propertyValueRepo.AsQueryable().Where(pv => pv.PropertyId == propertyId).ToListAsync();
            // Mapping sang DTO
            var propertyDTO = new PropertyDTO
            {
                Id = propertyId,
                Name = property.Name,
                Description = property.Description,
                MaxSelection = property.MaxSelection,
                IsMultipleChoice = property.IsMultipleChoice,
                Options = propertyValues.Select(pv => new PropertyValueDTO
                {
                    PropertyValueId = pv.Id,
                    Value = pv.Value
                }).ToList()
            };

            return propertyDTO;
        }

        public async Task<Dictionary<string, decimal>> GetPriceByVariantIds(IEnumerable<string> variantIds)
        {
            // ✅ Lấy danh sách Variant từ DB
            var variants = await _variantRepo
                .AsQueryable()
                .Where(v => variantIds.Contains(v.Id))
                .ToListAsync();

            // ✅ Mapping VariantId -> Price
            return variants.ToDictionary(v => v.Id, v => v.Price);
        }

        public async Task<Dictionary<string, List<string>>> GetPropertyValuesByVariantIds(IEnumerable<string> variantIds)
        {
            // ✅ Lấy danh sách VariantValue từ DB
            var variantValues = await unitOfWork.GetRepository<VariantValue>()
                .AsQueryable()
                .Where(vv => variantIds.Contains(vv.VariantId))
                .ToListAsync();

            // ✅ Lấy danh sách PropertyValue từ DB
            var propertyValues = await unitOfWork.GetRepository<PropertyValue>()
                .AsQueryable()
                .ToDictionaryAsync(pv => pv.Id, pv => pv.Value);

            // ✅ Mapping VariantId -> PropertyValues
            return variantValues
                .Where(vv => propertyValues.ContainsKey(vv.PropertyValueId))
                .GroupBy(vv => vv.VariantId)
                .ToDictionary(g => g.Key, g => g.Select(vv => propertyValues[vv.PropertyValueId]).ToList());
        }

        public async Task<(List<ProductVariantResponse>, List<ProductVariantDetailResponse>)> GetProductVariants(string productId)
        {
            // Kiểm tra sản phẩm có tồn tại không
            await ValidateProductExists(productId);

            // Lấy danh sách Variant của sản phẩm
            var variants = await _variantRepo.AsQueryable().Where(x => x.ProductId == productId).ToListAsync();
            if (variants == null || !variants.Any())
            {
                return (new List<ProductVariantResponse>(), new List<ProductVariantDetailResponse>());
            }

            // Lấy danh sách VariantValue dựa trên VariantId
            var variantIds = variants.Select(v => v.Id).ToList();
            var variantValues = await _variantValueRepo.AsQueryable().Where(x => variantIds.Contains(x.VariantId)).ToListAsync();
            var variantValueIds = variantValues.Select(vv => vv.PropertyValueId).ToList();

            // Lấy danh sách Property và PropertyValue liên quan
            var propertyIds = variantValues.Select(vv => vv.PropertyId).Distinct().ToList();
            var properties = await _repository.AsQueryable().Where(x => propertyIds.Contains(x.Id)).ToListAsync();

            var propertyValues = await _propertyValueRepo.AsQueryable().Where(x => propertyIds.Contains(x.PropertyId) && variantValueIds.Contains(x.Id)).ToListAsync();

            // Nhóm dữ liệu theo thuộc tính (Property)
            var options = properties.Select(property => new ProductVariantResponse
            {
                PropertyId = property.Id,
                OptionName = property.Name,
                IsMultipleChoice = property.IsMultipleChoice,
                MaxSelection = property.MaxSelection,
                Variants = propertyValues.Where(pv => pv.PropertyId == property.Id)
                    .Select(pv => new VariantRepsonse
                    {
                        PropertyValueId = pv.Id,
                        PropertyValueName = pv.Value,
                    }).ToList()
            }).ToList();

            // Xây dựng danh sách ProductVariantDetailResponse
            var variantDetails = variants.Select(variant => new ProductVariantDetailResponse
            {
                //Stock = variant.Stock, // Số lượng tồn kho
                Quantity = variant.Quantity, // Số lượng tồn kho
                Status = (short)variant.Status, // Trạng thái sản phẩm
                OriginalPrice = variant.Price, // Giá gốc

                Price = variant.Price, // Giá giảm
                DiscountPrice = variant.Price, // Giá giảm

                VariantId = variant.Id, // ID biến thể
                PropertyValueIds = variantValues
                    .Where(vv => vv.VariantId == variant.Id)
                    .Select(vv => vv.PropertyValueId)
                    .ToList()
            }).ToList();

            return (options, variantDetails);
        }

        #region Products Variants 

        public async Task<int> DeleteProductVariants(string productId)
        {
            // Lấy danh sách biến thể của sản phẩm
            var variants = await _variantRepo.AsQueryable().Where(x => x.ProductId == productId).ToListAsync();
            if (variants == null || !variants.Any())
            {
                return 0;
            }
            var variantIds = variants.Select(v => v.Id).ToList();
            var variantValues = await _variantValueRepo.AsQueryable().Where(x => variantIds.Contains(x.VariantId)).ToListAsync();

            // Xóa biến thể và giá trị biến thể
            if (variants.Any()) _variantRepo.DeleteRange(variants);
            if (variantValues.Any()) _variantValueRepo.DeleteRange(variantValues);

            return variants.Count() + variantValues.Count();
        }

        public async Task<int> CreateProductVariants(string productId, List<ProductVariantRequest> variants)
        {
            if (variants == null || !variants.Any())
                throw new CustomException(200, "Danh sách biến thể không được trống.");

            // Kiểm tra sản phẩm có tồn tại không
            //await ValidateProductExists(productId);

            // mapping propertyId thiếu bằng PropertyValueId trong request
            foreach (var item in variants)
            {
                await MappingPropertyIdFromPropertyValueId(item.Properties);
            }

            // Tạo đối tượng xử lý
            var result = new ProductVariantProcessingResult();

            // Xử lý danh sách biến thể & giá trị biến thể
            ProcessVariant(new ProductVariantProcessingInput(productId, variants, new List<Variant>(), new List<VariantValue>()), result);

            // Thêm vào DB
            if (result.NewVariants.Any()) _variantRepo.AddRange(result.NewVariants);
            if (result.NewVariantValues.Any()) _variantValueRepo.AddRange(result.NewVariantValues);

            //return await unitOfWork.SaveChangesAsync();
            return result.NewVariants.Count() + result.NewVariantValues.Count();
        }

        public async Task<int> UpdateProductVariants(string productId, List<ProductVariantRequest> variants)
        {
            // if (variants == null || !variants.Any())
            //     throw new CustomException(200, "Danh sách biến thể không được trống.");

            // Kiểm tra sản phẩm có tồn tại không
            await ValidateProductExists(productId);

            // Lấy danh sách biến thể và giá trị biến thể hiện tại
            var existingVariants = await GetExistingVariants(productId);
            var existingVariantIds = existingVariants.Select(v => v.Id).ToList();
            var allExistingVariantValues = await GetExistingVariantValues(existingVariantIds);

            // mapping propertyId thiếu bằng PropertyValueId trong request
            foreach (var item in variants)
            {
                await MappingPropertyIdFromPropertyValueId(item.Properties);
            }

            // Gom nhóm dữ liệu đầu vào
            var input = new ProductVariantProcessingInput(productId, variants, existingVariants, allExistingVariantValues);

            // Xử lý danh sách biến thể & giá trị biến thể
            var result = new ProductVariantProcessingResult();
            ProcessVariant(input, result);

            // Cập nhật vào DB
            if (result.NewVariants.Any()) _variantRepo.AddRange(result.NewVariants); // thêm mới variant
            if (result.UpdatedVariants.Any()) _variantRepo.UpdateRange(result.UpdatedVariants); // cập nhật variant
            if (result.RemovedVariants.Any()) _variantRepo.DeleteRange(result.RemovedVariants); // xóa variant

            if (result.NewVariantValues.Any()) _variantValueRepo.AddRange(result.NewVariantValues); // thêm mới variant value
            if (result.RemovedVariantValues.Any()) _variantValueRepo.DeleteRange(result.RemovedVariantValues); // xóa variant value

            //return await unitOfWork.SaveChangesAsync(); // Commit sau

            // Tổng số entity sẽ thay đổi:
            int totalChanges = result.NewVariants.Count()
                             + result.UpdatedVariants.Count()
                             + result.RemovedVariants.Count()
                             + result.NewVariantValues.Count()
                             + result.RemovedVariantValues.Count();

            return totalChanges;
        }

        private async Task MappingPropertyIdFromPropertyValueId(IEnumerable<VariantProperty> variantProperties)
        {
            var propertyValueIds = variantProperties.Select(x => x.PropertyValueId).ToList();
            var propertyIds = await _propertyValueRepo.AsQueryable()
                                                 .Where(x => propertyValueIds.Contains(x.Id))
                                                 .ToListAsync();

            // Tạo dictionary để tra cứu nhanh
            var propertyValueDict = propertyIds.ToDictionary(x => x.Id, x => x.PropertyId);

            foreach (var variantProperty in variantProperties)
            {
                if (propertyValueDict.TryGetValue(variantProperty.PropertyValueId, out var propertyId))
                {
                    variantProperty.PropertyId = propertyId;
                }
            }
        }

        private async Task<List<Variant>> GetExistingVariants(string productId)
        {
            return await _variantRepo.AsQueryable().Where(v => v.ProductId == productId).ToListAsync();
        }

        private async Task<List<VariantValue>> GetExistingVariantValues(List<string> existingVariantIds)
        {
            return await _variantValueRepo.AsQueryable().Where(vv => existingVariantIds.Contains(vv.VariantId)).ToListAsync();
        }

        #endregion

        #region Validate product, property, value

        private async Task ValidateProductExists(string productId)
        {
            var productExists = await _productRepo.AsQueryable().AnyAsync(p => p.Id == productId);
            if (!productExists)
                throw new CustomException(200, "Sản phẩm không tồn tại.");
        }

        private async Task ValidatePropertyAndValue(string? propertyId, string? propertyValueId)
        {
            if (!string.IsNullOrEmpty(propertyId))
            {
                var propertyExists = await _repository.AsQueryable().AnyAsync(p => p.Id == propertyId);
                if (!propertyExists)
                    throw new CustomException(200, $"PropertyId {propertyId} không hợp lệ.");
            }

            if (!string.IsNullOrEmpty(propertyValueId))
            {
                var propertyValueExists = await _propertyValueRepo.AsQueryable().AnyAsync(pv => pv.Id == propertyValueId);
                if (!propertyValueExists)
                    throw new CustomException(200, $"PropertyValueId {propertyValueId} không hợp lệ.");
            }
        }

        #endregion

        #region process variant

        private Variant CreateVariant(string productId, ProductVariantRequest variantRequest)
        {
            return new Variant
            {
                ProductId = productId,
                Price = variantRequest.Price,
                Status = variantRequest.Status,
                Quantity = variantRequest.Quantity,
            };
        }

        private VariantValue CreateVariantValue(string variantId, string propertyId, string propertyValueId)
        {
            return new VariantValue
            {
                VariantId = variantId,
                PropertyId = propertyId,
                PropertyValueId = propertyValueId,
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };
        }

        private void ProcessVariant(ProductVariantProcessingInput input, ProductVariantProcessingResult result)
        {
            var requestVariantIds = new HashSet<string>();

            foreach (var variantRequest in input.Variants)
            {
                if (!string.IsNullOrEmpty(variantRequest.VariantId) && !variantRequest.VariantId.Contains("-")) // Nếu có VariantId => cập nhật
                {
                    var existingVariant = input.ExistingVariants.FirstOrDefault(v => v.Id == variantRequest.VariantId);
                    if (existingVariant != null)
                    {
                        // Cập nhật biến thể nếu đã tồn tại
                        existingVariant.Price = variantRequest.Price;
                        existingVariant.Status = variantRequest.Status;
                        existingVariant.Quantity = variantRequest.Quantity;
                        existingVariant.UpdatedDate = DateTime.Now;

                        result.UpdatedVariants.Add(existingVariant); // Thêm vào danh sách variant được cập nhật
                        requestVariantIds.Add(existingVariant.Id);

                        // Xử lý giá trị biến thể
                        ProcessVariantValues(existingVariant.Id, variantRequest.Properties, input.ExistingVariantValues, result);
                    }
                }
                else // Nếu không có VariantId => tạo mới
                {
                    var newVariant = CreateVariant(input.ProductId, variantRequest);
                    result.NewVariants.Add(newVariant);
                    requestVariantIds.Add(newVariant.Id);

                    // Thêm danh sách biến thể
                    if (variantRequest.Properties.Any())
                    {
                        // Tạo danh sách thuộc tính cho biến thể mới
                        var newVariantValues = variantRequest.Properties.Select(x => CreateVariantValue(newVariant.Id, x.PropertyId, x.PropertyValueId)).ToList();

                        result.NewVariantValues.AddRange(newVariantValues);
                    }
                }
            }

            // Xác định các biến thể bị xóa
            result.RemovedVariants = input.ExistingVariants.Where(v => !requestVariantIds.Contains(v.Id)).ToList();
        }

        private void ProcessVariantValues(string variantId, List<VariantProperty> properties, List<VariantValue> existingVariantValues, ProductVariantProcessingResult result)
        {
            if (!properties.Any()) return;

            // Danh sách thuộc tính từ request
            var requestProperties = properties
                .Select(p => new { p.PropertyId, p.PropertyValueId })
                .ToHashSet(); // Chuyển thành HashSet để tìm kiếm nhanh

            // Lọc ra những giá trị thuộc tính hiện có của biến thể
            var existingValuesForVariant = existingVariantValues
                .Where(vv => vv.VariantId == variantId)
                .ToDictionary(vv => new { vv.PropertyId, vv.PropertyValueId });

            // Tạo danh sách thuộc tính mới (chưa tồn tại)
            var newVariantValues = properties
                .Where(x => !existingValuesForVariant.ContainsKey(new { x.PropertyId, x.PropertyValueId }))
                .Select(x => CreateVariantValue(variantId, x.PropertyId, x.PropertyValueId)).ToList();

            // Xác định VariantValues bị xóa (tồn tại trong existing nhưng không có trong request)
            var removedVariantValues = existingValuesForVariant
                .Where(ev => !requestProperties.Contains(ev.Key)) // Nếu không có trong request thì xóa
                .Select(ev => ev.Value) // Lấy object VariantValue
                .ToList();

            // Thêm vào kết quả xử lý
            result.NewVariantValues.AddRange(newVariantValues);
            result.RemovedVariantValues.AddRange(removedVariantValues);
        }

        #endregion
    }

    #region internal processor

    internal class ProductVariantProcessingInput
    {
        public string ProductId { get; }

        public List<ProductVariantRequest> Variants { get; } // YC biến thể từ request

        public List<Variant> ExistingVariants { get; } // tất biến thể đã có
        public List<VariantValue> ExistingVariantValues { get; } // tất cả giá trị của biến thể

        public ProductVariantProcessingInput(string productId, List<ProductVariantRequest> variants, List<Variant> existingVariants, List<VariantValue> existingVariantValues)
        {
            ProductId = productId;
            Variants = variants;
            ExistingVariants = existingVariants;
            ExistingVariantValues = existingVariantValues;
        }
    }

    internal class ProductVariantProcessingResult
    {
        public List<Variant> NewVariants { get; } = new();
        public List<Variant> UpdatedVariants { get; } = new();
        public List<Variant> RemovedVariants { get; set; } = new();

        public List<VariantValue> NewVariantValues { get; } = new();
        public List<VariantValue> RemovedVariantValues { get; } = new();
    }

    #endregion
}
