﻿namespace MiniAppCore.Entities.Products.Variants
{
    public class PropertyValue
    {
        public string Id { get; set; } = Guid.NewGuid().ToString("N");
        public required string Value { get; set; }
        public required string PropertyId { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
