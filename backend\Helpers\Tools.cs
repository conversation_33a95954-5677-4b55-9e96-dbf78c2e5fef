﻿using HtmlAgilityPack;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Enums;
using MiniAppCore.Models.DTOs.Payments;
using Newtonsoft.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace MiniAppCore.Helpers
{
    public class Tools
    {
        private static readonly Random random = new Random();
        private static string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        private static List<string> _specialEndings = new List<string> { "ch", "sh", "s", "x", "z" };

        #region "Adress Text"

        public static async Task<string?> GetFullAddressByIds(string cityId, string districtId, string wardId)
        {
            try
            {

                string apiLocationUrl = "https://open.oapi.vn/location";
                // Gọi API để lấy danh sách provinces
                var provincesResponse = await GetLocationData($"{apiLocationUrl}/provinces?page=0&size=100");
                var city = provincesResponse?.FirstOrDefault(p => p["id"].ToString() == cityId)?["name"]?.ToString();

                // Gọi API để lấy danh sách districts theo cityId
                var districtsResponse = await GetLocationData($"{apiLocationUrl}/districts/{cityId}?page=0&size=100");
                var district = districtsResponse?.FirstOrDefault(d => d["id"].ToString() == districtId)?["name"]?.ToString();

                // Gọi API để lấy danh sách wards theo districtId
                var wardsResponse = await GetLocationData($"{apiLocationUrl}/wards/{districtId}?page=0&size=100");
                var ward = wardsResponse?.FirstOrDefault(w => w["id"].ToString() == wardId)?["name"]?.ToString();

                // Nếu không tìm thấy bất kỳ thành phần nào, trả về null
                if (string.IsNullOrEmpty(city) || string.IsNullOrEmpty(district) || string.IsNullOrEmpty(ward))
                {
                    return null;
                }

                return $"{ward}, {district}, {city}";
            }
            catch
            {
                return null;
            }
        }

        private static async Task<List<Dictionary<string, object>>?> GetLocationData(string endpoint)
        {
            var _httpClient = new HttpClient();
            var response = await _httpClient.GetAsync(endpoint);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeObject<Dictionary<string, object>>(content);

                if (result != null && result.ContainsKey("data"))
                {
                    return JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(result["data"].ToString());
                }
            }
            return null;
        }

        #endregion

        #region "Generate random string"

        public static string GenerateCode(int length)
        {
            StringBuilder result = new StringBuilder(length);
            for (int i = 0; i < length; i++)
            {
                result.Append(chars[random.Next(chars.Length)]);
            }
            return result.ToString();
        }

        #endregion

        #region "Convert Image Path"

        public static List<string> GetListImagePath(string? images, string contentPath)
        {
            if (string.IsNullOrEmpty(images))
            {
                return new List<string>();
            }
            var path = images.Split(",").Select(item => $"{contentPath}{item}").ToList();
            return path;
        }

        #endregion

        #region "Convert Word"
        /// <summary>
        /// Chuyển đổi danh sách các từ từ số ít sang số nhiều.
        /// </summary>
        /// <param name="singularWords">Danh sách từ số ít</param>
        /// <returns>Danh sách từ số nhiều</returns>
        public static string ConvertToPlural(string singularWord)
        {
            string result;
            if (EndsWithSpecialEnding(singularWord))
            {
                result = singularWord + "es";
            }
            else
            {
                result = singularWord + "s";
            }
            return result;
        }

        /// <summary>
        /// Kiểm tra từ có kết thúc bằng một trong các hậu tố đặc biệt hay không.
        /// </summary>
        /// <param name="word">Từ cần kiểm tra</param>
        /// <returns>True nếu từ kết thúc bằng hậu tố đặc biệt, ngược lại False</returns>
        private static bool EndsWithSpecialEnding(string word)
        {
            foreach (var ending in _specialEndings)
            {
                if (word.EndsWith(ending, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }
            return false;
        }
        #endregion

        #region "Zalo checkout SDK"

        public static string CreateMac(CheckOutDTO checkout, string PrivateKey)
        {
            string strItems = JsonConvert.SerializeObject(checkout.Items);
            string strExtraData = JsonConvert.SerializeObject(checkout.ExtraData);

            Dictionary<string, string> obj = new Dictionary<string, string>(){
                {"amount", checkout.Amount.ToString()},
                {"desc", checkout.Desc ?? ""},
                {"extradata", strExtraData},
                {"item", strItems},
            };

            var macData = string.Join("&", obj.Select(x => $"{x.Key}={x.Value}"));

            return ComputeHmacSha256(PrivateKey, macData);
        }

        public static string ComputeHmacSha256(string? key, string data)
        {
            if (string.IsNullOrEmpty(key))
            {
                return string.Empty;
            }

            using (HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(key)))
            {
                byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
                return Convert.ToHexString(hashValue).ToLower();
            }
        }

        #endregion

        #region "Calculation"

        public static double CalculateDistance(double startLatitude, double startLongitude, double destLatitude, double destLongitude)
        {
            const double EarthRadiusKm = 6371;

            double dLat = DegreesToRadians(destLatitude - startLatitude);
            double dLon = DegreesToRadians(destLongitude - startLongitude);

            // Convert starting and destination points to radians
            startLatitude = DegreesToRadians(startLatitude);
            destLatitude = DegreesToRadians(destLatitude);

            // Haversine formula
            double a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                       Math.Cos(startLatitude) * Math.Cos(destLatitude) *
                       Math.Sin(dLon / 2) * Math.Sin(dLon / 2);
            double c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

            return EarthRadiusKm * c;
        }

        private static double DegreesToRadians(double degrees)
        {
            return degrees * Math.PI / 180.0;
        }

        public static void CalculateDiscount(decimal price, Discount discount, out decimal discountValue, out decimal discountAmount, out decimal discountPrice, out bool hasDiscount)
        {
            discountValue = 0;
            discountAmount = 0;
            discountPrice = price;
            hasDiscount = false;

            if (discount != null && discount.DiscountValue > 0 && discount.IsActive)
            {
                DateTime now = DateTime.Now;

                if (now < discount.StartDate || now > discount.ExpiryDate)
                {
                    return;
                }

                discountValue = discount.DiscountValue;
                hasDiscount = true;

                if (discount.Type == (short)DiscountType.PERCENTAGE)
                {
                    discountAmount = price * (discount.DiscountValue / 100);

                    if (discount.MaxDiscountAmount > 0 && discountAmount > discount.MaxDiscountAmount)
                    {
                        discountAmount = discount.MaxDiscountAmount;
                    }
                }
                else if (discount.Type == (short)DiscountType.FLAT)
                {
                    discountAmount = discount.DiscountValue;
                }

                discountPrice = price - discountAmount;

                discountPrice = Math.Max(discountPrice, 0);
            }
        }

        public static string GetVariantKey(dynamic variant)
        {
            var properties = (IEnumerable<dynamic>)variant.Properties;

            var sortedProperties = properties.OrderBy(p => p.Name).ToList();

            var propertyKeys = sortedProperties.Select(p =>
            {
                var options = (IEnumerable<dynamic>)p.Options;

                var selectedOption = options.FirstOrDefault(o => o.IsSelected);

                return selectedOption != null ? $"{p.PropertyId}-{selectedOption.PropertyItemId}" : null;
            }).Where(key => key != null).ToList();

            return string.Join("-", propertyKeys);
        }

        #endregion

        #region "Summarize Html"

        public static string SummarizeHtmlContent(string html, int maxLength)
        {
            if (string.IsNullOrWhiteSpace(html)) return string.Empty;

            var doc = new HtmlDocument();
            doc.LoadHtml(html);
            string textContent = HtmlEntity.DeEntitize(doc.DocumentNode.InnerText).Trim();
            textContent = Regex.Replace(textContent, @"\s+", " ");
            textContent = textContent.Replace("\\\"", "\"");
            if (textContent.Length <= maxLength)
            {
                return textContent;
            }
            int lastSpaceIndex = textContent.LastIndexOf(' ', maxLength);
            return lastSpaceIndex > 0 ? textContent.Substring(0, lastSpaceIndex) + "..." : textContent.Substring(0, maxLength) + "...";
        }

        public static string ExtractSortingKey(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) return value;

            var sizeMatch = Regex.Match(value, @"^(\d+(?:x\d+)*)$");
            if (sizeMatch.Success)
            {
                return string.Join("-", sizeMatch.Groups[1].Value.Split('x').Select(n => n.PadLeft(5, '0')));
            }

            var numberWithUnitMatch = Regex.Match(value, @"^(\d+)([a-zA-Z]+)$");
            if (numberWithUnitMatch.Success)
            {
                return numberWithUnitMatch.Groups[1].Value.PadLeft(5, '0') + "-" + numberWithUnitMatch.Groups[2].Value;
            }

            var numberMatch = Regex.Match(value, @"^(\d+)$");
            if (numberMatch.Success)
            {
                return numberMatch.Groups[1].Value.PadLeft(5, '0');
            }

            return value;
        }

        #endregion

        #region Image Helper

        public static string NormalizeImageUrl(string hostUrl, string? image, string folder)
        {
            if (string.IsNullOrWhiteSpace(image)) return string.Empty;
            return image.StartsWith("http", StringComparison.OrdinalIgnoreCase)
                ? image
                : $"{hostUrl}/{folder}/{image}";
        }

        public static string GetImage(string hostUrl, string? banner, string path)
        {
            return NormalizeImageUrl(hostUrl, banner, path);
        }

        public static List<string> GetImages(string hostUrl, string? images, string path)
        {
            return (images ?? "")
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(img => NormalizeImageUrl(hostUrl, img, path))
                .ToList();
        }

        #endregion
    }

}
