﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Memberships;
using System.Linq;
using System.Net;

namespace MiniAppCore.Services.Memberships.VAT
{
    public class MembershipVATService(IUnitOfWork unitOfWork, IMapper mapper) : Service<MembershipVAT>(unitOfWork),IMembershipVATService
    {
        public async Task<PagedResult<MembershipVAT>> GetMembershipVATUserZaloId(string userUserZaloId, RequestQuery query)
        {
            var membershipVAT = _repository.AsQueryable().Where(x => x.UserZaloId == userUserZaloId);
            if (!string.IsNullOrEmpty(query.Keyword))
            {

            }
            var totalItems = await membershipVAT.CountAsync();
            var totalPage = (int)Math.Ceiling((double)totalItems / query.PageSize);
            var items = await membershipVAT.OrderBy(x => x.CreatedDate).Skip(query.Skip).Take(query.PageSize).ToListAsync();
            return new PagedResult<MembershipVAT>()
            {
                Data = items,
                TotalPages = totalPage
            };
        }

        public async Task<MembershipVAT?> GetDefaultMembershipVAT(string userZaloId)
        {
            // Tìm địa chỉ mặc định
            var defaultVAT= await _repository.AsQueryable()
                                                  .AsNoTracking()
                                                  .FirstOrDefaultAsync(x => x.UserZaloId == userZaloId && x.IsDefault);

            if (defaultVAT != null)
            {
                return defaultVAT;
            }

            // Nếu không có địa chỉ mặc định, tìm địa chỉ đầu tiên và đánh dấu là mặc định
            var firstVAT= await _repository.AsQueryable()
                                                .AsNoTracking()
                                                .OrderBy(x => x.CreatedDate)
                                                .FirstOrDefaultAsync(x => x.UserZaloId == userZaloId);
            if (firstVAT != null)
            {
                firstVAT.IsDefault = true;
            }

            return firstVAT;
        }

        public async Task<int> CreateAsync(string userZaloId, MembershipVATRequest entity)
        {            
            var address = mapper.Map<MembershipVAT>(entity);
            var (wardName, districtName, provinceName) = ("", "", "");
            if (entity.Type != "Personal")
            {
                ValidateAddress(entity);
                (wardName, districtName, provinceName) = await GetLocationNamesAsync(entity);
                address.FullAddress = FormatFullAddress(address.StreetLine, wardName, districtName, provinceName);
            }
            else
            {
                address.FullAddress = "";
            }
            address.UserZaloId = userZaloId;
            

            if (entity.IsDefault)
            {
                await EnsureSingleDefaultAsync(userZaloId, address.Id);
            }

            if (!(await _repository.AsQueryable().AnyAsync(x => x.UserZaloId == userZaloId)))
            {
                entity.IsDefault = true;
            }

            return await base.CreateAsync(address);
        }

        public async Task<int> UpdateAsync(string id, MembershipVATRequest entity)
        {
            ValidateAddress(entity);

            var exist = await GetByIdAsync(id) ?? throw new NotFoundException(404, "Không tìm thấy địa chỉ này!");

            //var (wardName, districtName, provinceName) = await GetLocationNamesAsync(entity);
            
            var (wardName, districtName, provinceName) = ("", "", "");
            mapper.Map(entity, exist);
            if (entity.Type != "Personal")
            {
                (wardName, districtName, provinceName) = await GetLocationNamesAsync(entity);
                exist.FullAddress = FormatFullAddress(exist.StreetLine, wardName, districtName, provinceName);
            }
            else
            {
                exist.FullAddress = "";
            }
            
            if (entity.IsDefault)
            {
                await EnsureSingleDefaultAsync(exist.UserZaloId, exist.Id);
            }

            return await base.UpdateAsync(exist);
        }

        private void ValidateAddress(MembershipVATRequest entity)
        {
            if (string.IsNullOrEmpty(entity.City) || string.IsNullOrEmpty(entity.Ward) || string.IsNullOrEmpty(entity.District))
            {
                throw new CustomException(400, "Địa chỉ không hợp lệ! Vui lòng kiểm tra lại!");
            }
        }

        private async Task<(string wardName, string districtName, string provinceName)> GetLocationNamesAsync(MembershipVATRequest entity)
        {
            var city = await Locator.GetCityByLocationId(entity.City);
            var district = await Locator.GetDistrictByLocationId(entity.City, entity.District);
            var ward = await Locator.GetWardByLocationId(entity.District, entity.Ward);

            return (ward?.Name ?? "", district?.Name ?? "", city?.Name ?? "");
        }

        private string FormatFullAddress(string street, string ward, string district, string province)
        {
            return $"{street}, {ward}, {district}, {province}".Trim(',', ' ');
        }

        private async Task EnsureSingleDefaultAsync(string userZaloId, string? excludedId = null)
        {
            var otherVAT = _repository.AsQueryable()
                                      .Where(x => x.UserZaloId == userZaloId && (excludedId == null || x.Id != excludedId))
                                      .ToList();

            if (otherVAT.Any(x => x.IsDefault))
            {
                otherVAT.ForEach(x => x.IsDefault = false);
                _repository.UpdateRange(otherVAT);
            }
            await unitOfWork.SaveChangesAsync();
        }
    }
}
