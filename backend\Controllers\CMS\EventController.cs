﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Events;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Services.Events;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class EventController(IHttpContextAccessor httpContextAccessor, IEventService eventService) : Controller
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Create()
        {
            var model = new Event
            {
                Id = string.Empty,
                Title = string.Empty,
                Content = string.Empty,
                StartTime = DateTime.Now,
                EndTime = DateTime.Now.AddDays(7)
            };

            ViewBag.Title = "Thêm sự kiện mới";
            ViewBag.Button = "Lưu";

            return PartialView("_Event", model);
        }

        public async Task<IActionResult> Detail(string id)
        {
            var model = await eventService.GetByIdAsync(id);

            if (model == null) throw new CustomException(1, "Sự kiện không khả dụng");

            ViewBag.Gifts = await eventService.GetGifts(id);
            ViewBag.Sponsors = await eventService.GetSponsors(id);

            ViewBag.Images = Tools.GetImages(hostUrl, model.Images, "uploads/images/events");
            ViewBag.Banner = Tools.GetImage(hostUrl, model.Banner, "uploads/images/events");

            ViewBag.Title = "Cập nhật sự kiện";
            ViewBag.Button = "Cập nhật";

            return PartialView("_Event", model);
        }

        public async Task<IActionResult> Participants(string id)
        {
            var model = await eventService.GetByIdAsync(id);

            if (model == null) throw new CustomException(1, "Sự kiện không khả dụng");

            ViewBag.Banner = Tools.GetImage(hostUrl, model.Banner, "uploads/images/events");

            return View("Participants", model);
        }
    }

}
