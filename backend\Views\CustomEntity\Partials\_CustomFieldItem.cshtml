@model MiniAppCore.Features.CustomEntity.Handlers.CustomFieldRequest
@{
    var index = ViewData["Index"] as int? ?? 0;
    var dataTypes = ViewData["DataTypes"] as List<object>;
    var fieldPrefix = $"Fields[{index}]";
    var fieldIdPrefix = $"Fields_{index}_";
}

<div class="card border shadow-sm field-item">
    <div class="card-header bg-light d-flex justify-content-between align-items-center py-2">
        <div class="d-flex align-items-center">
            <div class="drag-handle me-2 text-muted" style="cursor: move;" title="Kéo để sắp xếp">
                <i class="ri-drag-move-fill"></i>
            </div>
            <strong class="field-number text-primary me-2">#@(index + 1)</strong>
            <h6 class="mb-0 fw-bold">
                @if (!string.IsNullOrEmpty(Model?.Id))
                {
                    <span class="badge bg-info-subtle text-info-emphasis rounded-pill me-1">Hiện có</span>
                }
                else
                {
                    <span class="badge bg-success-subtle text-success-emphasis rounded-pill me-1">Mới</span>
                }
            </h6>
        </div>
        <button type="button" class="btn btn-sm btn-outline-danger remove-field-btn" title="Xóa field này">
            <i class="ri-delete-bin-line"></i>
        </button>
    </div>
    <div class="card-body">
        @if (!string.IsNullOrEmpty(Model?.Id))
        {
            <input type="hidden" name="@(fieldPrefix).Id" value="@Model.Id" />
        }
        <input type="hidden" name="@(fieldPrefix).DisplayOrder" value="@index" class="display-order-input" />

        <div class="row g-3">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="@(fieldIdPrefix)FieldName" class="form-label fw-semibold">
                        <i class="ri-text-spacing me-1"></i>Field Name (Tên kỹ thuật) <span class="text-danger">*</span>
                    </label>
                    <input id="@(fieldIdPrefix)FieldName" name="@(fieldPrefix).FieldName" value="@Model?.FieldName" type="text" class="form-control field-name-input" placeholder="ViDu: MoTaThem"
                        required />
                    <div class="invalid-feedback">Tên field không hợp lệ hoặc đã tồn tại.</div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-group">
                    <label for="@(fieldIdPrefix)FieldNameDisplay" class="form-label fw-semibold">
                        <i class="ri-eye-line me-1"></i>Tên hiển thị <span class="text-danger">*</span>
                    </label>
                    <input id="@(fieldIdPrefix)FieldNameDisplay" name="@(fieldPrefix).FieldNameDisplay" value="@Model?.FieldNameDisplay" type="text" class="form-control" placeholder="Ví dụ: Mô tả thêm"
                        required />
                    <div class="invalid-feedback">Tên hiển thị không được để trống.</div>
                </div>
            </div>
        </div>

        <div class="row g-3 mt-2">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="@(fieldIdPrefix)DataType" class="form-label fw-semibold">
                        <i class="ri-code-s-slash-line me-1"></i>Data Type <span class="text-danger">*</span>
                    </label>
                    <select id="@(fieldIdPrefix)DataType" name="@(fieldPrefix).DataType" class="form-select data-type-select" required>
                        <option value="">-- Chọn kiểu dữ liệu --</option>
                        @if (dataTypes != null)
                        {
                            foreach (dynamic dataType in dataTypes)
                            {
                                <option value="@dataType.Value" selected="@(dataType.Value == Model?.DataType)">@dataType.Icon @dataType.Text</option>
                            }
                        }
                    </select>
                    <div class="invalid-feedback">Vui lòng chọn kiểu dữ liệu.</div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="form-check form-switch">
                    <input id="@(fieldIdPrefix)IsRequired" name="@(fieldPrefix).IsRequired" type="checkbox" class="form-check-input" value="true" @(Model?.IsRequired == true ? "checked" : "")>
                    <label for="@(fieldIdPrefix)IsRequired" class="form-check-label">Bắt buộc nhập</label>
                </div>
            </div>
        </div>
    </div>
</div>
