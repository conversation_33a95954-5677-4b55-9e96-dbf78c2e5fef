﻿@using MiniAppCore.Entities.Categories
@model (List<string> selectedCategories, IEnumerable<Category> allCategories)
<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> mục sản phẩm</h4>
                <p class="mb-0">
                    Thêm các danh mục sản phẩm để tối ưu quản lý
                </p>
            </div>
            <button onclick="GetFormCategory('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-category">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!-- Category -->
    <div class="col-lg-12">
        <div class="shadow-none">
            <div class="modal-content shadow-lg p-3 mb-5 rounded border-none">
                <div class="modal-header">
                    <h5 class="modal-title">Thứ tự hiển thị các danh mục ở trang chủ</h5>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <select id="categoryHome" class="form-control" multiple>
                            @foreach (var item in Model.selectedCategories)
                            {
                                var category = Model.allCategories.FirstOrDefault(x => x.Id == item);
                                if (category != null)
                                {
                                    <option value="@category.Id" selected="selected">@category.Name</option>
                                }
                            }

                            @foreach (var item in Model.allCategories)
                            {
                                if (!Model.selectedCategories.Contains(item.Id))
                                {
                                    <option value="@item.Id">@item.Name</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div id="button-group-category" class="d-flex my-1 justify-content-end px-3">
                    <button class="mx-1 btn btn-primary" onclick="HandleSaveCategoryHome()">Lưu thay đổi</button>
                </div>
            </div>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="quick-update-container" style="display: none !important;">
            <div class="form-group col-3 d-block">
                <button class="btn btn-danger w-50" id="btn-quick-update-status" onclick="handleQuickUpdate()">
                    <i class="ri-delete-bin-line"></i>
                    <span>Xóa</span>
                </button>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0; z-index: -1;"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-categories" class="data-table table mb-0 tbl-server-info"> </table>
        </div>
    </div>
</div>

<div id="modal-category" class="modal fade" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<div id="modal-pullCategory" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-pullContent" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        let removedOldImages = [];
        let newImages = [];
        let currentImages = 0;
        const catgoryDatadefault = {};

        $(document).ready(function () {
            GetListCategory();

            $("#categoryHome").select2({
                placeholder: 'Chọn danh mục hiển thị trang chủ'
            }).on("select2:select", function (evt) {
                var id = evt.params.data.id;

                var element = $(this).children("option[value="+id+"]");

                moveElementToEndOfParent(element);

                $(this).trigger("change");
            });

            var ele=$("#categoryHome").parent().find("ul.select2-selection__rendered");
            ele.sortable({
                containment: 'parent',
                update: function() {
                    orderSortedValues();
                }
            });

            $('#search').on('input', search);

            //inititalCategoryConfig();
        });

        function orderSortedValues() {
            var value = ''
            $("#categoryHome").parent().find("ul.select2-selection__rendered").children("li[title]").each(function(i, obj){

                var element = $("#categoryHome").children('option').filter(function () { return $(this).html() == obj.title });
                moveElementToEndOfParent(element)
            });
        }

        function moveElementToEndOfParent(element) {
            var parent = element.parent();

            element.detach();

            parent.append(element);
        }

        $(document).on("click", ".btn-preview-remove", function () {
            const fullImageUrl = $(this).data("url");
            const imageUrl = fullImageUrl.split("/").pop();

            $(this).parent().remove();

            if (!removedOldImages.includes(imageUrl)) {
                removedOldImages.push(imageUrl);
            }
        });

        function ClearForm() {
            $("form").trigger("reset");
            ShowPreview();
        }

        function ShowPreview(event) {
            if (!event) return;

            const files = event.target.files;
            // Kiểm tra tất cả file phải là ảnh
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (!file.type.startsWith("image/")) {
                    AlertResponse("Chỉ được upload ảnh!", "warning");
                    event.target.value = ""; // Reset input
                    return;
                }
            }
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.readAsDataURL(file);
                    reader.onload = (e) => {
                        const previewItem = $(`
                                <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height: 170px;">
                                    <img src="${e.target.result}" class="card-img-top h-100" alt="Hình ảnh" style="object-fit:contain" />
                                    <span class="btn-preview-remove">x</span>
                                </div>`);

                        previewItem.find('.btn-preview-remove').click(function () {
                            $(this).parent().remove();
                            const currentFiles = Array.from(event.target.files);
                            const newFiles = currentFiles.filter(f => f !== file);
                        });

                        newImages.push(file);
                        $("#preview").append(previewItem);
                    };
                }
            }
        }

        function HandleSaveCategoryHome() {
            const categoryId = $('#categoryHome').val();
            $.ajax({
                url: '@Url.Action("CreateOrUpdateCategoryHome", "SystemSettings")',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(categoryId),
                success: function (response) {
                    if (response.code === 0) {
                        $('.cancelButton').attr('hidden', true);
                        $('.saveButton').attr('hidden', true);
                        AlertResponse(response.message, "success");
                    } else {
                        AlertResponse("Cập nhật thất bại! Vui lòng thử lại!", "warning");
                    }
                },
                error: function (xhr, status, error) {
                    AlertResponse("Đã xảy ra lỗi khi gọi API.", "error");
                }
            });
        }

        function GetListCategory() {
            table = new DataTable("#list-categories", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    // const type = $("#filter-type").val();
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetPage", "Categories")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword,
                            type: "sanpham"
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                select: `<input type="checkbox" class="category-checkbox" data-category-id="${item.id}" onchange="showQuickUpdate()" />`,
                                0: data.start + index + 1,
                                1: `<div class="d-flex align-items-center">
                                                                    <!-- <img src="${item.images[0] ? item.images[0] : "/images/no-image-1.png"}" style="background-color: #f0f0f0; padding: 2px; border-radius: 8px;" class="img-fluid rounded avatar-50 mr-3" alt="image" /> -->
                                                                    ${item.name}
                                                                </div>`,
                                2: `${item.listChild.map((child) => `<p>${child.name}</p>`).join('')}`,

                                3:`<div class="text-center">
                                        ${item.orderPriority}
                                    </div>`,
                                4: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                    <a onclick="GetFormCategory('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                        <i class="ri-edit-line fs-6 mr-0"></i>
                                    </a>
                                    <a onclick="DeleteCategory('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                        <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                    </a>
                                </div>`,
                                5: item.hexColor ? item.hexColor.toUpperCase() : "",
                                6: `<input type="color" id="head" name="head" value="${item.hexColor}" disabled />`,
                                id: item.id
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "<input type='checkbox' id='selectAll' onclick='handleCheckTableRow()' />", data: "select", className: 'text-center select-checkbox' },
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên", data: 1 },
                    // { title: "Mã màu", data: 6, className: "text-center" },
                    { title: "Danh mục con", data: 2 },
                    { title: "Thứ tự hiển thị (Miniapp)", data: 3 },
                    { title: "Thao tác", data: 4 },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');

            $('#list-categories tbody').on('click', 'td.select-checkbox', function (event) {
                // Nếu click trực tiếp vào checkbox thì không làm gì cả
                if ($(event.target).is('input[type="checkbox"]')) return;

                // Tìm checkbox trong cell
                const checkbox = $(this).find('input[type="checkbox"]');

                // Đảo trạng thái của checkbox (nếu đang checked thì bỏ check và ngược lại)
                checkbox.prop('checked', !checkbox.prop('checked'));
                showQuickUpdate()
            });
        }

        function handleCheckTableRow() {
            const isSelectAll = $("#selectAll").is(":checked");
            if (isSelectAll) {
                $("input[data-category-id]").prop("checked", true);
                $("#quick-update-container").show();
            } else {
                $("input[data-category-id]").prop("checked", false);
                $("#quick-update-container").hide();
            }
        }

        function showQuickUpdate() {
            const checkedRows = $(".category-checkbox:checked");
            if (checkedRows.length > 0) {
                $("#quick-update-container").show();
            } else {
                $("#quick-update-container").hide();
            }
        }

        function handleQuickUpdate() {
            const checkedCats = [];
            $(".category-checkbox:checked").each(function () {
                checkedCats.push($(this).data("category-id"));
            });

            if (checkedCats.length === 0) {
                AlertResponse("Vui lòng chọn ít nhất một danh mục để cập nhật", "warning");
                return;
            }

            ConfirmAlert(
                "Xác nhận", 
                "Bạn có muốn xóa những danh mục được chọn, mọi thao tác sẽ không thể hoàn lại", 
                "warning",
                "/api/categories/QuickUpdate",
                "POST",
                JSON.stringify(checkedCats),
                table,
                function(response) {
                    $("#quick-update-container").hide();
                }
            );
        }

        function GetFormCategory(id) {
            const url = id ? `@Url.Action("Detail", "Category")/${id}` : "@Url.Action("Create", "Category")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-category").modal("toggle");

                    $("#type").select2();

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function DeleteCategory(id) {
            const url = `/api/categories/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const formData = new FormData();

            formData.append("id", id);
            formData.append("isGlobal", true);
            formData.append("type", "sanpham");
            formData.append("name", $("#name").val()?.trim());
            formData.append("hexColor", $("#hexColor").val());
            formData.append("description", $("#description").val());

            formData.append("orderPriority", $("#orderPriority").val()?.trim());
            // formData.append("isGlobal", $("#isGlobalCategorySelect").val()?.trim() === "true");

            // const branchIds = $("#branchId").val();
            // branchIds.map(item => {formData.append("branchIds", item)});

            if (!$("#name").val().trim()) {
                AlertResponse("Tên danh mục đang trống, vui lòng nhập tên danh mục!", 'warning');
                return;
            }

            //Danh mục con
            var flag = 0;
            const categoryList = [];
            const categoryElements = document.querySelectorAll("[id^='category-child-']");
                categoryElements.forEach(element => {
                    // Lấy các giá trị từ các input trong mỗi div
                    const categoryChildId = $(`#Id_${element.id.split('-')[2]}`).val();
                    const createdDate = $(`#CreatedDate_${element.id.split('-')[2]}`).val();
                    const updatedDate = $(`#UpdatedDate_${element.id.split('-')[2]}`).val();
                    const name = $(`#name_${element.id.split('-')[2]}`).val();
                    const description = $(`#discription_${element.id.split('-')[2]}`).val();
                    const orderPriority = $(`#orderPriority_${element.id.split('-')[2]}`).val();
                    if (!name || orderPriority == "") {
                        flag = 1;
                        return; 
                    }

                    const categoryChild = {
                        Id: categoryChildId,
                        CreatedDate: createdDate,
                        UpdatedDate: updatedDate,
                        Name: name,
                        Description: description,
                        OrderPriority: orderPriority,
                        categoryId: id
                    };
                    categoryList.push(categoryChild);
                });


                    if(flag == 1){
                        AlertResponse("Vui lòng điền đầy đủ thông tin: Tên danh mục con và Thứ tự.", "warning");
                        return;
                    }

                    formData.append("listChildJson", JSON.stringify(categoryList));

                    

            // handle image
            {
                const files = $('#pics')[0].files;
                if (removedOldImages.length === currentImages) {
                    if (files.length === 0) {
                        // AlertResponse("Bạn cần ít nhất một hình ảnh!", "warning");
                        // return;
                    }
                }

                if (id) {
                    if (files.length == 0) {
                        // AlertResponse('Bạn vui lòng tải hình ảnh lên!', 'error');
                        // return;
                    }
                }

                for (let i = 0; i < files.length; i++) {
                    formData.append('images', files[i]);
                }

                if (removedOldImages.length > 0) {
                    for (const removedImage of removedOldImages) {
                        formData.append('removedOldImages', removedImage);
                    }
                }
            }


            const url = id ? `/api/categories/${id}` : '/api/categories';
            const method = id ? 'PUT' : 'POST'

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    }
                    $("#modal-category").modal("toggle");
                },
                error: function (err) {
                    AlertResponse('Thêm mới danh mục thất bại!', 'error')
                }
            });
        }
    </script>
}
