﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;

namespace MiniAppCore.Entities.Offers.Vouchers
{
    public class Voucher : BaseEntity
    {
        public required string Name { get; set; }
        public required string Code { get; set; }
        public required string Description { get; set; }

        public decimal MaxDiscountAmount { get; set; }
        public decimal MinimumOrderValue { get; set; }

        public bool IsActive { get; set; }
        public bool IsExchange { get; set; }
        public bool IsAllProducts { get; set; }

        public long Quantity { get; set; }
        public long RankingPoint { get; set; }
        public long ExchangeTimes { get; set; }
        public long PointRequired { get; set; }

        public decimal DiscountValue { get; set; }
        public EVoucherType VoucherType { get; set; }
        public EDiscountType DiscountType { get; set; }

        public DateTime EndDate { get; set; } = DateTime.Today.AddHours(24);
        public DateTime StartDate { get; set; } = DateTime.Now;

        public DateTime ExpiryDate { get; set; } = DateTime.Today.AddHours(48);
        public long ExpiryDateTimeStamps { get; set; } = new DateTimeOffset(DateTime.Today.AddHours(48)).ToUnixTimeMilliseconds();
    }
}
