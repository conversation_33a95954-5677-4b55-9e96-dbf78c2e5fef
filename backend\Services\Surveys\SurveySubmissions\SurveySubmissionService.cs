﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Surveys;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Services.Surveys.Sections;
using MiniAppCore.Services.Surveys.SurveySubmissionExcels;
using MiniAppCore.Services.Surveys.UserSurveys;

namespace MiniAppCore.Services.Surveys.Responses
{
    public class SurveySubmissionService(
        IUnitOfWork unitOfWork,
        IMapper mapper,
        ISurveySectionService sectionService,
        ISurveySubmissionExcelService excelService) : Service<SurveySubmission>(unitOfWork), ISurveySubmissionService
    {
        private readonly IRepository<Survey> _surveyRepo = unitOfWork.GetRepository<Survey>();
        private readonly IRepository<SurveyQuestion> _surveyQuestionRepo = unitOfWork.GetRepository<SurveyQuestion>();
        private readonly IRepository<SurveySubmissionDetail> _surveyResponseDetail = unitOfWork.GetRepository<SurveySubmissionDetail>();

        private readonly IRepository<Membership> _membershipRepo = unitOfWork.GetRepository<Membership>();
        public async Task<int> SubmitSurveyAsync(string userZaloId, SurveySubmissionRequest surveySubmissionRequest)
        {
            var submission = new SurveySubmission
            {
                SurveyId = surveySubmissionRequest.SurveyId,
                UserZaloId = userZaloId,
                CreatedDate = DateTime.Now
            };

            var answersIds = surveySubmissionRequest.Answers.Select(x => x.QuestionId).ToList();
            var surveyQuestions = await _surveyQuestionRepo.AsQueryable().Where(x => answersIds.Contains(x.Id)).ToDictionaryAsync(question => question.Id, question => question); ;

            foreach (var answer in surveySubmissionRequest.Answers)
            {
                var questionType = surveyQuestions.TryGetValue(answer.QuestionId!, out var question) ? question.Type : string.Empty;

                // câu hỏi là likert
                if (questionType == "likert")
                {
                    foreach (var likertAnswer in answer.LikertAnswers)
                    {
                        var detail = new SurveySubmissionDetail
                        {
                            SurveySubmissionId = submission.Id,
                            SurveyQuestionId = answer.QuestionId,

                            InputValue = likertAnswer.LikertValue, // Value của SurveAnswerId
                            SurveyAnswerId = likertAnswer.AnswerId, // // SurveyAnswerId 
                            SurveyLikertQuestionId = likertAnswer.LikertQuestionId, // Id câu hỏi trong bảng likertQuesion
                        };
                        _surveyResponseDetail.Add(detail);
                    }
                }
                // câu hỏi là choice/multiple choice/dropdown
                else if (questionType == "singleChoice" || questionType == "multiChoice" || questionType == "dropDown")
                {
                    foreach (var option in answer.OptionInputs)
                    {
                        var detail = new SurveySubmissionDetail
                        {
                            SurveySubmissionId = submission.Id,
                            SurveyQuestionId = answer.QuestionId,
                            SurveyAnswerId = option.AnswerId,
                            InputValue = option.InputValue
                        };
                        _surveyResponseDetail.Add(detail);
                    }
                }
                // câu hỏi là paragraph/time/date/ thì lưu dữ liệu input
                else
                {
                    var detail = new SurveySubmissionDetail
                    {
                        SurveySubmissionId = submission.Id,
                        SurveyQuestionId = answer.QuestionId,
                        InputValue = answer.InputValue
                    };
                    _surveyResponseDetail.Add(detail);
                }
            }
            return await CreateAsync(submission);
        }

        public async Task<int> SyncDataFromVisitorIdToUserZaloId(string visitorId, string userZaloId)
        {
            var updatedCount = await _repository.AsQueryable()
                        .Where(x => x.UserZaloId == visitorId)
                        .ExecuteUpdateAsync(setters => setters
                            .SetProperty(x => x.UserZaloId, userZaloId));
            return updatedCount;
        }

        public async Task<PagedResult<SurveySubmissionResponse>> GetSurveyHistoryAsync(RequestQuery requestQuery, string? surveyId, DateTime? fromDate, DateTime? toDate)
        {
            var query = _repository.AsQueryable().AsNoTracking();
            if (!string.IsNullOrEmpty(surveyId))
            {
                query = query.Where(x => x.SurveyId == surveyId);
            }

            if (!string.IsNullOrEmpty(requestQuery.Keyword))
            {
                var keyword = requestQuery.Keyword.Trim();
                var membershipQuery = _membershipRepo.AsQueryable()
                                            .Where(x => x.UserZaloId.Contains(keyword) ||
                                                        x.PhoneNumber.Contains(keyword) ||
                                                        x.UserZaloName.Contains(keyword))
                                            .Select(x => x.UserZaloId);
                query = query.Where(x => membershipQuery.Contains(x.UserZaloId));
            }

            if (fromDate.HasValue)
            {
                query = query.Where(x => x.CreatedDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(x => x.CreatedDate <= toDate.Value);
            }

            // Apply pagination
            var count = await query.CountAsync();
            var totalPages = (int)Math.Ceiling(count / (double)requestQuery.PageSize);
            var items = await query
                .OrderByDescending(x => x.CreatedDate)
                .Skip(requestQuery.Skip)
                .Take(requestQuery.PageSize)
                .ToListAsync();

            // danh sách surveys
            var surveyIds = items.Select(x => x.SurveyId).ToList();
            var surveys = await _surveyRepo.AsQueryable().Where(x => surveyIds.Contains(x.Id)).ToListAsync();

            // danh sách thông tin người làm khảo sát
            var membershipUserZaloIds = items.Select(x => x.UserZaloId).ToList();
            var memberships = await _membershipRepo.AsQueryable()
                                        .Where(x => membershipUserZaloIds.Contains(x.UserZaloId))
                                        //.Select(x => new { x.UserZaloId, x.UserZaloName, x.PhoneNumber })
                                        .ToListAsync();
            var result = items.Select(submission =>
            {
                var membership = memberships.FirstOrDefault(m => m.UserZaloId == submission.UserZaloId);
                var historyResponse = mapper.Map<SurveySubmissionResponse>((submission, membership));

                var survey = surveys.FirstOrDefault(x => x.Id == submission.SurveyId);
                historyResponse.SurveyTitle = survey?.Title ?? "-";
                return historyResponse;
            }).ToList();

            return new PagedResult<SurveySubmissionResponse>
            {
                Data = result,
                TotalPages = totalPages
            };
        }

        public async Task<SurveyResultResponse> GetSurveyResultAsync(string submissionId)
        {
            var submission = await _repository.FindByIdAsync(submissionId)
                              ?? throw new CustomException(404, "Không tìm thấy lượt nộp khảo sát!");

            var survey = await _surveyRepo.FindByIdAsync(submission.SurveyId ?? "")
                         ?? throw new CustomException(404, "Không tìm thấy khảo sát liên quan!");

            var membership = await _membershipRepo.AsQueryable().Where(m => m.UserZaloId == submission.UserZaloId).FirstOrDefaultAsync();

            var sections = await sectionService.GetSectionsBySurveyIdAsync(survey.Id);
            var submissionDetails = await _surveyResponseDetail.AsQueryable()
                                        .Where(d => d.SurveySubmissionId == submission.Id)
                                        .ToListAsync();

            return BuildSurveyResultResponse(survey, submission, membership, sections, submissionDetails);
        }

        public async Task<byte[]> ExportSurveysToExcelAsync(List<string> surveyIds)
        {
            if (surveyIds == null || !surveyIds.Any())
                throw new CustomException("Vui lòng chọn một bài khảo sát.");

            var submissions = await _repository.AsQueryable()
                .Where(s => !string.IsNullOrEmpty(s.SurveyId) && surveyIds.Contains(s.SurveyId))
                .ToListAsync();

            if (!submissions.Any())
                throw new CustomException(404, "Không tìm thấy lượt nộp khảo sát nào phù hợp.");

            var surveyIdSet = submissions.Select(s => s.SurveyId).Distinct().ToList();
            var submissionIds = submissions.Select(s => s.Id).ToList();
            var userZaloIds = submissions.Select(s => s.UserZaloId).Distinct().ToList();

            var surveys = await _surveyRepo.AsQueryable().Where(s => surveyIdSet.Contains(s.Id)).ToListAsync();
            var memberships = await _membershipRepo.AsQueryable().Where(m => userZaloIds.Contains(m.UserZaloId)).ToListAsync();
            var submissionDetails = await _surveyResponseDetail.AsQueryable().Where(d => !string.IsNullOrEmpty(d.SurveySubmissionId) && submissionIds.Contains(d.SurveySubmissionId)).ToListAsync();

            var results = new List<SurveyResultResponse>();

            foreach (var submission in submissions)
            {
                var survey = surveys.FirstOrDefault(s => s.Id == submission.SurveyId);
                if (survey == null) continue;

                // Lấy các sections riêng biệt cho mỗi submission
                var sections = await sectionService.GetSectionsBySurveyIdAsync(survey.Id);

                var membership = memberships.FirstOrDefault(m => m.UserZaloId == submission.UserZaloId);
                var details = submissionDetails.Where(d => d.SurveySubmissionId == submission.Id).ToList();

                var result = BuildSurveyResultResponse(survey, submission, membership, sections, details);
                results.Add(result);
            }

            return excelService.ExportToExcel(results);
        }

        private SurveyResultResponse BuildSurveyResultResponse(
            Survey survey,
            SurveySubmission submission,
            Membership? membership,
            List<SectionResponse> sections,
            List<SurveySubmissionDetail> submissionDetails)
        {
            var result = mapper.Map<SurveyResultResponse>((survey, submission, membership));

            foreach (var section in sections)
            {
                foreach (var question in section.ListQuestion)
                {
                    var relevantDetails = submissionDetails
                        .Where(d => d.SurveyQuestionId == question.QuestionId ||
                                    (question.Type?.ToLower() == "likert" &&
                                     question.ListQuestionLikert.Any(l => l.QuestionLikertId == d.SurveyLikertQuestionId)))
                        .ToList();

                    question.UserResponses = mapper.Map<List<SurveySubmissionDetailResponse>>(relevantDetails);
                }
            }

            result.Sections = sections;
            result.TotalSection = sections.Count;
            return result;
        }
    }
}
