﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;

namespace MiniAppCore.Entities.Memberships
{
    public class MembershipVoucher : BaseEntity
    {
        public required string MembershipId { get; set; }
        public required string VoucherId { get; set; }
        public long Quantity { get; set; }
        public EOffers Status { get; set; }
        public DateTime ExpiryDate { get; set; }
        public long ExpiryDateTimeStamps { get; set; } = DateTimeOffset.Now.ToUnixTimeMilliseconds();
    }
}
