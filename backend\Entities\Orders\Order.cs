﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;

namespace MiniAppCore.Entities.Orders
{
    public class Order : BaseEntity
    {
        public required string UserZaloId { get; set; }
        public string? Note { get; set; }
        public string? CancelReason { get; set; }
        public long PointUsage { get; set; }
        public string? DeliveryAddress { get; set; }
        public string? VoucherCode { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal ShippingFee { get; set; }
        public decimal Total { get; set; }

        public EOrder OrderStatus { get; set; }
        public EOrder DeliveryStatus { get; set; }
        public EPayment PaymentStatus { get; set; }

        public string? BranchId { get; set; }
        public string? ZaloOrderId { get; set; }
        public string? BookingId { get; set; }
        public string? PaymentMethod { get; set; }
        public string? MembershipAddressId { get; set; }
        public string? MembershipVATId { get; set; }
    }
}
