﻿@model MiniAppCore.Entities.Commons.ActionButtonConfig;
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    <div class="modal-body" style="@(User.IsInRole("EMPLOYEE") ? "pointer-events: none;" : "")">
        <form id="actionButtonForm">
            <input type="hidden" id="id" value="@Model.Id" />
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label>Tiêu đề <span class="text-danger">*</span></label>
                        <input id="title" type="text" class="form-control" value="@Model.Title" placeholder="Nhập tiêu đề..." required>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label>Loại nút <span class="text-danger">*</span></label>
                        <select id="type" class="form-control" required>
                            <option value="phone" selected="@(Model.Type == "phone")">Phone (Gọi điện)</option>
                            <option value="webview" selected="@(Model.Type == "webview")">Webview (Mở trang web)</option>
                            <option value="email" selected="@(Model.Type == "email")">Email</option>
                            <option value="direct" selected="@(Model.Type == "direct")">Direct (Liên kết trực tiếp)</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label>Nhóm nút <span class="text-danger">*</span></label>
                        <select id="category" class="form-control" required>
                            <option value="shortcut" selected="@(Model.Category == "shortcut")">Shortcut (Phím tắt)</option>
                            <option value="button" selected="@(Model.Category == "button")">Button (Nút chính)</option>
                            <option value="floatingBubble" selected="@(Model.Category == "floatingBubble")">Floating Bubble (Nút nổi)</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label>Thứ tự hiển thị</label>
                        <input id="sortOrder" type="number" min="0" class="form-control" value="@Model.SortOrder" placeholder="Thứ tự hiển thị...">
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group mb-3">
                        <label>Mô tả/URL/Số điện thoại <span class="text-danger">*</span></label>
                        <input id="description" type="text" class="form-control" value="@Model.Description" placeholder="Nhập mô tả, URL hoặc số điện thoại..." required>
                        <small id="typeHelp" class="form-text text-muted"></small>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group mb-3">
                        <label>Trạng thái hoạt động</label>
                        <select id="isActive" class="form-control" required>
                            <option value="true" selected="@(Model.IsActive)">Hoạt động</option>
                            <option value="false" selected="@(!Model.IsActive)">Ngưng hoạt động</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="form-group mb-3">
                        <label>Hình ảnh <span class="text-danger">*</span></label>
                        <input type="file" id="buttonImage" name="images" class="form-control" accept="image/*"
                               onchange="handleImagePreview(this, { width: 1, height: 1 }, null, '#preview', {isSingleImage: true, defaultImagePath: '/images/no-image-2.jpg'})">
                    </div>
                </div>

                <div class="col-md-12">
                    <div id="preview" class="mt-2">
                        @if (!string.IsNullOrEmpty(Model.Image))
                        {
                            var images = Model.Image.Split(",").Select(x => $"/uploads/images/actionButtons/{x}");
                            foreach (var item in images)
                            {
                                <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                    <img src="@item" class="card-img-top h-100" alt="Alternate Text" style="object-fit:contain" />
                                    <span class="btn-preview-remove" data-url="@item">x</span>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (User.IsInRole("ADMIN"))
        {
            <button type="button" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
        }
    </div>
</div>

<script>
    $(document).ready(function () {
        // Initialize select2 if available
        if ($.fn.select2) {
            $("#type, #category").select2({
                dropdownParent: $("#modal-action-button")
            });
        }

        // Update help text based on selected type
        updateTypeHelp();

        // Type change handler
        $("#type").change(function () {
            updateTypeHelp();
        });
    });

    // Update help text based on selected type
    function updateTypeHelp() {
        const type = $("#type").val();
        let helpText = "";

        switch (type) {
            case "phone":
                helpText = "Nhập số điện thoại (ví dụ: 0912345678)";
                break;
            case "webview":
                helpText = "Nhập URL đầy đủ (ví dụ: https://example.com)";
                break;
            case "email":
                helpText = "Nhập email (ví dụ: <EMAIL>)";
                break;
            case "direct":
                helpText = "Nhập đường dẫn đến trang trong ứng dụng";
                break;
            default:
                helpText = "Nhập thông tin phù hợp với loại nút đã chọn";
        }

        $("#typeHelp").text(helpText);
    }
</script>