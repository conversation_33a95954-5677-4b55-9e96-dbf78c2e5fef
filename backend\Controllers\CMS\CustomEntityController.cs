using MediatR;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using MiniAppCore.Features.CustomEntity.Handlers;
using MiniAppCore.Models.Common;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class CustomEntityController(IMediator mediator) : Controller
    {
        // Constants cho available entities
        private static readonly HashSet<string> AvailableEntities = new()
        {
            "Product", "Category", "Article", "Event",
            "Booking", "Order", "Membership", "Branch"
        };

        private static readonly Dictionary<string, string> DataTypeOptions = new()
        {
            ["string"] = "Văn bản",
            ["int"] = "Số nguyên",
            ["decimal"] = "Số thập phân",
            ["bool"] = "Boolean (Đúng/Sai)",
            ["datetime"] = "Ngày giờ",
            ["date"] = "Ngày",
            ["email"] = "Email",
            ["phone"] = "Số điện thoại",
            ["url"] = "Đường dẫn URL",
            ["textarea"] = "Văn bản dài",
            ["select"] = "Danh sách lựa chọn",
            ["multiselect"] = "Đa lựa chọn",
            ["file"] = "File đính kèm",
            ["image"] = "Hình ảnh"
        };

        public async Task<IActionResult> Index(string? entityName = null)
        {
            var customFields = await mediator.Send(new GetAllCustomFieldsQuery
            {
                EntityName = entityName
            });

            // Pass available entities to all partials
            ViewBag.AvailableEntities = AvailableEntities.ToList();
            ViewBag.SelectedEntity = entityName;
            ViewBag.DebugInfo = $"Total Custom Fields: {customFields.Count}, Available Entities: {string.Join(", ", AvailableEntities)}";

            return View("~/Views/CustomEntity/Index.cshtml", customFields);
        }

        [HttpGet("CustomEntity/GetPage")]
        public async Task<IActionResult> GetPage(int page = 1, int pageSize = 10, string? sortBy = "createdDate", bool isDescending = true, string? keyword = null, string? entityName = null)
        {
            try
            {
                var customFields = await mediator.Send(new GetAllCustomFieldsQuery { EntityName = entityName });
                var query = customFields.AsQueryable();

                // Apply filtering
                if (!string.IsNullOrEmpty(keyword))
                {
                    var lowerKeyword = keyword.ToLowerInvariant();
                    query = query.Where(f =>
                        (f.FieldName != null && f.FieldName.ToLowerInvariant().Contains(lowerKeyword)) ||
                        (f.FieldNameDisplay != null && f.FieldNameDisplay.ToLowerInvariant().Contains(lowerKeyword)) ||
                        (f.EntityName != null && f.EntityName.ToLowerInvariant().Contains(lowerKeyword)) ||
                        (f.DataType != null && f.DataType.ToLowerInvariant().Contains(lowerKeyword)));
                }

                // Apply sorting
                var sortedQuery = (sortBy?.ToLowerInvariant(), isDescending) switch
                {
                    ("fieldname", false) => query.OrderBy(f => f.FieldName),
                    ("fieldname", true) => query.OrderByDescending(f => f.FieldName),
                    ("entityname", false) => query.OrderBy(f => f.EntityName),
                    ("entityname", true) => query.OrderByDescending(f => f.EntityName),
                    ("datatype", false) => query.OrderBy(f => f.DataType),
                    ("datatype", true) => query.OrderByDescending(f => f.DataType),
                    ("isrequired", false) => query.OrderBy(f => f.IsRequired),
                    ("isrequired", true) => query.OrderByDescending(f => f.IsRequired),
                    ("createddate", false) => query.OrderBy(f => f.CreatedDate),
                    ("createddate", true) => query.OrderByDescending(f => f.CreatedDate),
                    _ => query.OrderByDescending(f => f.CreatedDate) // Default sort
                };

                // Apply pagination
                var totalCount = sortedQuery.Count();
                var pagedData = sortedQuery
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return Json(new { success = true, data = pagedData, totalCount });
            }
            catch (Exception ex)
            {
                // Ensure a consistent error response format
                return Json(new { success = false, message = ex.Message, data = new List<object>(), totalCount = 0 });
            }
        }

        [HttpGet("CustomEntity/Create")]
        public IActionResult Create(string? entityName = null)
        {
            PopulateViewBagForForm(entityName, "Thêm Custom Field", "Create");
            var model = new CustomFieldRequest { EntityName = entityName, IsRequired = false };
            return PartialView("~/Views/CustomEntity/Partials/_CustomFieldForm.cshtml", model);
        }

        [HttpPost("CustomEntity/Create")]
        public async Task<IActionResult> Create(CustomFieldRequest model)
        {
            if (!ModelState.IsValid)
            {
                PopulateViewBagForForm(model.EntityName, "Thêm Custom Field", "Create");
                return PartialView("~/Views/CustomEntity/Partials/_CustomFieldForm.cshtml", model);
            }
            return await HandleAction(async () =>
            {
                await mediator.Send(new CreateCustomFieldCommand { CustomField = model });
                return JsonSuccess("Tạo custom field thành công!");
            });
        }

        [HttpGet("CustomEntity/Edit/{id}")]
        public async Task<IActionResult> Edit(string id)
        {
            return await HandleAction(async () =>
            {
                var customField = await mediator.Send(new GetCustomFieldByIdQuery { Id = id });
                var model = new CustomFieldRequest
                {
                    Id = customField.Id,
                    EntityName = customField.EntityName,
                    FieldName = customField.FieldName,
                    DataType = customField.DataType,
                    IsRequired = customField.IsRequired
                };
                PopulateViewBagForForm(customField.EntityName, "Chỉnh sửa Custom Field", "Edit");
                return PartialView("~/Views/CustomEntity/Partials/_CustomFieldForm.cshtml", model);
            });
        }

        [HttpPost("CustomEntity/Edit/{id}")]
        public async Task<IActionResult> Edit(string id, CustomFieldRequest model)
        {
            if (!ModelState.IsValid)
            {
                PopulateViewBagForForm(model.EntityName, "Chỉnh sửa Custom Field", "Edit");
                return PartialView("~/Views/CustomEntity/Partials/_CustomFieldForm.cshtml", model);
            }
            return await HandleAction(async () =>
            {
                await mediator.Send(new UpdateCustomFieldCommand { Id = id, CustomField = model });
                return JsonSuccess("Cập nhật custom field thành công!");
            });
        }

        [HttpPost("CustomEntity/Delete/{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            return await HandleAction(async () =>
            {
                await mediator.Send(new DeleteCustomFieldCommand { Id = id });
                return JsonSuccess("Xóa custom field thành công!");
            });
        }

        [HttpGet("CustomEntity/GetEntityFieldsOverview")]
        public async Task<IActionResult> GetEntityFieldsOverview(string entityName)
        {
            return await HandleAction(async () =>
            {
                var customFields = await mediator.Send(new GetAllCustomFieldsQuery { EntityName = entityName });
                ViewBag.EntityName = entityName;
                return PartialView("~/Views/CustomEntity/Partials/_EntityFieldsOverview.cshtml", customFields);
            });
        }

        [HttpGet("CustomEntity/ConfigureEntity")]
        public async Task<IActionResult> ConfigureEntity(string entityName)
        {
            return await HandleAction(async () =>
            {
                var configuration = await mediator.Send(new GetEntityConfigurationQuery { EntityName = entityName });
                ViewBag.DataTypes = GetDataTypes();
                return PartialView("~/Views/CustomEntity/Partials/_EntityConfiguration.cshtml", configuration);
            });
        }

        [HttpPost("CustomEntity/SaveEntityConfiguration")]
        public async Task<IActionResult> SaveEntityConfiguration(EntityConfigurationRequest model)
        {
            if (string.IsNullOrEmpty(model.EntityName))
            {
                return JsonError("Entity name is required");
            }

            foreach (var field in model.Fields)
            {
                field.EntityName = model.EntityName;
            }

            return await HandleAction(async () =>
            {
                await mediator.Send(new SaveEntityConfigurationCommand { Configuration = model });
                return JsonSuccess($"Cấu hình custom fields cho {model.EntityName} đã được lưu thành công!");
            });
        }

        [HttpGet("CustomEntity/GetByEntity/{entityName}")]
        public async Task<IActionResult> GetByEntity(string entityName)
        {
            return await HandleAction(async () =>
            {
                var customFields = await mediator.Send(new GetAllCustomFieldsQuery { EntityName = entityName });
                return JsonSuccess(data: customFields);
            });
        }

        #region Private Helper Methods

        private void PopulateViewBagForForm(string? selectedEntity, string title, string actionType)
        {
            ViewBag.AvailableEntities = AvailableEntities.ToList();
            ViewBag.SelectedEntity = selectedEntity;
            ViewBag.DataTypes = GetDataTypes();
            ViewBag.Title = title;
            ViewBag.ActionType = actionType;
        }

        private List<SelectListItem> GetDataTypes()
        {
            return DataTypeOptions
                .Select(kvp => new SelectListItem
                {
                    Value = kvp.Key,
                    Text = kvp.Value
                })
                .ToList();
        }

        private async Task<IActionResult> HandleAction(Func<Task<IActionResult>> action)
        {
            try
            {
                return await action();
            }
            catch (Exception ex)
            {
                return HandleError(ex);
            }
        }

        private JsonResult JsonSuccess(string message = "", object? data = null)
        {
            return Json(new { success = true, message, data });
        }

        private JsonResult JsonError(string message, object? data = null)
        {
            return Json(new { success = false, message, data });
        }

        private JsonResult HandleError(Exception ex, object? data = null)
        {
            // Log the exception here if needed
            return JsonError(ex.Message, data);
        }

        #endregion
    }
}
