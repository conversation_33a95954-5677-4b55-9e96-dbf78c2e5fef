﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddTableForSurvey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "874f4774f21d4922a12961678b1f1f02");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "03c36eee653341b284e49d8acbdb3b41");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "061671a5dec74fada354c71824fd359c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "063d060bfac1458db5ec51c5f41a2061");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "18c4ae2535d14b81b71212bdae43bf12");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1a29f76567584c1e92f0e1261382601c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "237eb5c9fd1c417f9d7008138ff91217");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2ae0bade39e149fab1177195cdab1496");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "30ee0d6781074db483f96e996108384d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3ed91c1e4d4d4ed3aeaa3bf9695dd900");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "480c81fd007345d0beb755698c1355cf");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "55600ef3adb04cfea21a5ea7c9a768c5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5653565d8b044d45893d57af12f79ab3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5a591da9b40a4fb99fa61d8a42b38dbd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5a75f814f1a0479c8be404248bbd70d1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5b49105184624f83924bba5fd91008e3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6173466f23444035a33a282baaea18d6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "655c2407e1504eb1b9cd19fdaf68f5ac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6822b352cfff4663b13e22c47d20cafc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "799be08d2060403cb3cb42faca19e69b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7a50f8dca7da4418b3efb6ec398debcc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8aa6ed54a7644a9bbbc1cf32dda8cac0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "92b8bd26485341b282c8805e26655659");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9d8f64861206470c810002f2d8c12ff6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a95cef0986494fa6aa0d1438347e6eaa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d0bfcf7b9ede40f0bfe1f510eb87a181");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "db81730f672b4f14ba195230706bd9e1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dc97579455684ccda91150a0d87af9c3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e25961ff3ab5406ca8fc080bdfeb5061");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e2f498f035a041da8d8b03cf64ef88dc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ee85841d527d4b18a8bba316364239ab");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ef051d958ef24398b416ee67e5553b6a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f3667d7d449b4d368a699756bd004f2f");

            migrationBuilder.CreateTable(
                name: "SurveyAnswers",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    QuestionId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    Key = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Value = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsInput = table.Column<bool>(type: "bit", nullable: false),
                    DisplayOrder = table.Column<short>(type: "smallint", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SurveyAnswers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SurveyLikertQuestions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    QuestionId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    QuestionLikertTitle = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OptionCount = table.Column<short>(type: "smallint", nullable: false),
                    DisplayOrder = table.Column<short>(type: "smallint", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SurveyLikertQuestions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SurveyQuestions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    SectionId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    QuestionTitle = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsRequired = table.Column<bool>(type: "bit", nullable: false),
                    DisplayOrder = table.Column<short>(type: "smallint", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SurveyQuestions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Surveys",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    DisplayOrder = table.Column<short>(type: "smallint", nullable: false),
                    IsDisplay = table.Column<bool>(type: "bit", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    StartedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Surveys", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SurveySections",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    SurveyId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    TitleSection = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DisplayOrder = table.Column<byte>(type: "tinyint", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SurveySections", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SurveySubmissionDetails",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    QuestionId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    LikertQuestionId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    AnswerId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    LikertId = table.Column<string>(type: "nvarchar(36)", maxLength: 36, nullable: true),
                    InputValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SurveySubmissionDetails", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SurveySubmissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    SurveyId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserZaloId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SurveySubmissions", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEM14RtKG2Ryd/InsqsoYhez7l8hq4QzDxUYc4pN3FNS4iaDX7VmqKToYWAfbiMYwHg==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "81a373a311bb43478b33985e89769f44", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 25, 10, 4, 49, 716, DateTimeKind.Local).AddTicks(1860), "FeaturesButton", new DateTime(2025, 4, 25, 10, 4, 49, 716, DateTimeKind.Local).AddTicks(1874) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "0028f9a96cd54eda90bdedbff373aee5", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6880), true, "AffiliateList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6881), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "02d319f8412847d1ae9c9454a2d6c676", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6960), true, "CustomForm", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6961), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "0544a4c5ef4c4564b7a85dbc790b545d", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6929), true, "Rank", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6930), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "084a69e40f394d0181e1b4b20da55515", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6958), true, "ShippingFeeConfig", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6959), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "1625aaae5c784a9c86388a8648a5636b", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6956), true, "MembershipExtendDefaults", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6957), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2ecab6c982024583b5f49d9e725e38b9", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6883), true, "ArticleCategory", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6883), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "3e5ac835f4144678867e04034a6f8698", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6931), true, "Tag", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6932), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "3ea6ad07d8c8429ca5009f20aabe614d", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6913), true, "OrderList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6914), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "4597dc7121a048669425c54f446c97c0", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6918), true, "DiscountList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6918), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "4751924500f84bb8bba5c442bc685289", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6906), true, "ProductList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6906), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "6cb0151bfff44710919fbdfed3f8f872", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6916), true, "InvoiceTemplate", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6916), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "6fba4aeea1f04ab2a90b8e6d4e2019e8", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6897), true, "Brand", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6897), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "790a5c455ac747398bfb19224c8f5c7d", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6875), true, "BranchList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6877), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "7d59c2351a08488ab34b720d5a633cda", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6909), true, "BookingItem", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6910), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "861ca78ddbd74b78bad27cacc87350ca", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6936), true, "History", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6936), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "95e5543aeec34151837c9209770ab5f1", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6911), true, "BookingList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6912), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "9a0293dd0d7f4221b4eba5df48bd3dd9", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6938), true, "GamePrize", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6938), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "ad42f43ac01f43b395c953884b638c8a", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6948), true, "EventTemplateHistory", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6948), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "ad62819acb0049e7aa9b05902656d4ed", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6942), true, "CampaignHistory", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6942), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "b8b84e612f2d49cebc465300779d872b", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6954), true, "EnableFeatures", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6955), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "b942e67f50ee4ae5a252f874689ae94f", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6920), true, "Promotion", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6920), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "b94444fa448244ad8ac5fe5a07b2b3dc", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(1564), true, "Overview", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6162), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "c3d3f16b87e64742bd03905fb5fff8ec", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6895), true, "Category", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6895), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c931bd9c8f9a4b218df2b0223b4c15cb", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6950), true, "TemplateUidList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6950), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "dd1cfc19786944d88e24ae7e2317cc6e", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6952), true, "GeneralSetting", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6952), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "e764657be616457db25beac8f10a0e89", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6926), true, "MembershipList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6927), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "eafb0caa76004a308fa39442b4ef26ea", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6946), true, "EventTemplateList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6946), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "f20c17f859884827ac26f8eb4e759dc3", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6940), true, "CampaignList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6940), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "f2717f81f90e48f8bdd7b511152e110c", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6899), true, "ProductProperty", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6899), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "f4d6ab54209a44ecbf0ae2dc8445ddbc", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6934), true, "GameList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6934), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "f662f57f6eb349b58ca581a0dfff361b", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6922), true, "VoucherList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6922), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "fd69a329ae1b44a680bd09a16c5cee4f", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6885), true, "ArticleList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6885), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_SurveySections_SurveyId",
                table: "SurveySections",
                column: "SurveyId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SurveyAnswers");

            migrationBuilder.DropTable(
                name: "SurveyLikertQuestions");

            migrationBuilder.DropTable(
                name: "SurveyQuestions");

            migrationBuilder.DropTable(
                name: "Surveys");

            migrationBuilder.DropTable(
                name: "SurveySections");

            migrationBuilder.DropTable(
                name: "SurveySubmissionDetails");

            migrationBuilder.DropTable(
                name: "SurveySubmissions");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "81a373a311bb43478b33985e89769f44");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0028f9a96cd54eda90bdedbff373aee5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "02d319f8412847d1ae9c9454a2d6c676");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0544a4c5ef4c4564b7a85dbc790b545d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "084a69e40f394d0181e1b4b20da55515");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1625aaae5c784a9c86388a8648a5636b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2ecab6c982024583b5f49d9e725e38b9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3e5ac835f4144678867e04034a6f8698");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3ea6ad07d8c8429ca5009f20aabe614d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4597dc7121a048669425c54f446c97c0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4751924500f84bb8bba5c442bc685289");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6cb0151bfff44710919fbdfed3f8f872");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6fba4aeea1f04ab2a90b8e6d4e2019e8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "790a5c455ac747398bfb19224c8f5c7d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7d59c2351a08488ab34b720d5a633cda");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "861ca78ddbd74b78bad27cacc87350ca");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "95e5543aeec34151837c9209770ab5f1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9a0293dd0d7f4221b4eba5df48bd3dd9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ad42f43ac01f43b395c953884b638c8a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ad62819acb0049e7aa9b05902656d4ed");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b8b84e612f2d49cebc465300779d872b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b942e67f50ee4ae5a252f874689ae94f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b94444fa448244ad8ac5fe5a07b2b3dc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c3d3f16b87e64742bd03905fb5fff8ec");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c931bd9c8f9a4b218df2b0223b4c15cb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dd1cfc19786944d88e24ae7e2317cc6e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e764657be616457db25beac8f10a0e89");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "eafb0caa76004a308fa39442b4ef26ea");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f20c17f859884827ac26f8eb4e759dc3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f2717f81f90e48f8bdd7b511152e110c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f4d6ab54209a44ecbf0ae2dc8445ddbc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f662f57f6eb349b58ca581a0dfff361b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fd69a329ae1b44a680bd09a16c5cee4f");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEN8bO8/olGX6e2WFSs8WuqNS6s588x1kGhvfGdkqwrto3Du6hT3ckLX1xzhNMPHziQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "874f4774f21d4922a12961678b1f1f02", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 24, 13, 38, 53, 607, DateTimeKind.Local).AddTicks(8641), "FeaturesButton", new DateTime(2025, 4, 24, 13, 38, 53, 607, DateTimeKind.Local).AddTicks(8659) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "03c36eee653341b284e49d8acbdb3b41", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8317), true, "BranchList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8319), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "061671a5dec74fada354c71824fd359c", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8423), true, "BookingList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8423), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "063d060bfac1458db5ec51c5f41a2061", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8352), true, "ProductList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8352), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "18c4ae2535d14b81b71212bdae43bf12", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8420), true, "BookingItem", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8420), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "1a29f76567584c1e92f0e1261382601c", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8347), true, "Brand", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8348), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "237eb5c9fd1c417f9d7008138ff91217", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8350), true, "ProductProperty", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8350), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "2ae0bade39e149fab1177195cdab1496", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8335), true, "ArticleList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8335), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "30ee0d6781074db483f96e996108384d", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8434), true, "Promotion", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8434), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "3ed91c1e4d4d4ed3aeaa3bf9695dd900", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8473), true, "ShippingFeeConfig", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8473), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "480c81fd007345d0beb755698c1355cf", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8425), true, "OrderList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8425), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "55600ef3adb04cfea21a5ea7c9a768c5", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8345), true, "Category", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8345), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "5653565d8b044d45893d57af12f79ab3", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8462), true, "TemplateUidList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8463), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "5a591da9b40a4fb99fa61d8a42b38dbd", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8450), true, "History", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8450), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "5a75f814f1a0479c8be404248bbd70d1", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(2535), true, "Overview", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(7603), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "5b49105184624f83924bba5fd91008e3", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8430), true, "InvoiceTemplate", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8430), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "6173466f23444035a33a282baaea18d6", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8442), true, "Rank", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8442), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "655c2407e1504eb1b9cd19fdaf68f5ac", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8432), true, "DiscountList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8432), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "6822b352cfff4663b13e22c47d20cafc", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8458), true, "EventTemplateList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8458), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "799be08d2060403cb3cb42faca19e69b", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8322), true, "AffiliateList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8322), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "7a50f8dca7da4418b3efb6ec398debcc", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8452), true, "GamePrize", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8452), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "8aa6ed54a7644a9bbbc1cf32dda8cac0", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8460), true, "EventTemplateHistory", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8461), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "92b8bd26485341b282c8805e26655659", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8475), true, "CustomForm", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8475), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "9d8f64861206470c810002f2d8c12ff6", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8454), true, "CampaignList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8454), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "a95cef0986494fa6aa0d1438347e6eaa", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8456), true, "CampaignHistory", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8456), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d0bfcf7b9ede40f0bfe1f510eb87a181", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8444), true, "Tag", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8445), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "db81730f672b4f14ba195230706bd9e1", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8436), true, "VoucherList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8437), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "dc97579455684ccda91150a0d87af9c3", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8468), true, "EnableFeatures", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8469), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "e25961ff3ab5406ca8fc080bdfeb5061", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8439), true, "MembershipList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8439), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "e2f498f035a041da8d8b03cf64ef88dc", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8446), true, "GameList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8447), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "ee85841d527d4b18a8bba316364239ab", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8325), true, "ArticleCategory", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8325), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "ef051d958ef24398b416ee67e5553b6a", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8471), true, "MembershipExtendDefaults", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8471), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "f3667d7d449b4d368a699756bd004f2f", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8465), true, "GeneralSetting", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8465), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" }
                });
        }
    }
}
