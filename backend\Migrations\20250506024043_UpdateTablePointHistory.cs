﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTablePointHistory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "5a884f2ab9a3476eae8434a7d1f3139f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "03356be917084ddd921b74965112322f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0dd2ecbe15e745529f0141a0e55b68a9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "11be882c4d65474c9729fc00e8d645a4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "22aca0c646c9492487765a29a7626153");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "326914d2edeb4db18e39571f774ac696");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "34c5535e977f40c789804f5401db8f6a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3c7b751825884733b430665209df8c8a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "47aceb4906dd411d93171fa4c4383d1f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4849ab59ab86442ab6861ae0d00e685e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "56f4c87e959b4b62a96e4da9a85a90e3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "57db3ce3d4d94431929854c37a3b8b0f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "698a05b1701649c6b220881e3423f513");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6c9a334343504a49aa3277d51857dc6b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "705c6ae7106f4e4f9b14ae29b5e298c2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8a3c0336749640fc974c3816d9a9b96c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8c67354059d74d049aa09eb8b14c7378");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8d8f056682fe45b3b8035dc587a8361c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8e98ed303fe74d10bb2c09e583def6c3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "90a5d4b3747b4d5bb5e46299e2265c2b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "93e97d2505294f71b7895d1bd200d3c8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9441f3a7fec24da996f5eeb0000f122f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ba71fb0cd8b490d8fff808a1e2ade53");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a06f0cae65df48128e6c08fe9fabae49");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b0217dcb83274e90a9d2711b09115513");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b1f2f584e7804e6f9d7ccdf535aed271");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b3b9033a748d4b19885711d5e6d7d0b6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c699db41fc784fa0ae17a7bc71e61cb8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c9cc66fc9ad249c5a835ad0ea6704b85");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cad9981232694d0b8279c3ef655e8879");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d22e5042338646ddbd190d372256c82b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d47c072d9db04a02ab5bf6f5f12145e3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d54793c063e143a8af713598c2a36a64");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fc1654455318420aad7295919e20b71c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fc213fddede74fd8b643c1c2e002fafa");

            migrationBuilder.AddColumn<string>(
                name: "ReferenceId",
                table: "PointHistories",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEJ4O5bMNitKXFToS7XHWO2AHZhjjc0jy6VPgTjp3mWWjwij0hp0kWt87XrUfYBv9gw==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "f5a6f3f71fed42569a0d5ab455034926", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 6, 9, 40, 42, 650, DateTimeKind.Local).AddTicks(5343), "FeaturesButton", new DateTime(2025, 5, 6, 9, 40, 42, 650, DateTimeKind.Local).AddTicks(5346) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "02c180315c1a49c9b3f6febfb3b89164", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5001), true, "GameList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5002), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "03f7d1e77d9a48129877d18b124cadf9", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5013), true, "EventTemplateList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5014), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "05198f6674f34f4c99fd86970ccd42aa", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5003), true, "History", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5004), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "10b9c5cc3d524a0a9c90e3e1b9fcfe92", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4907), true, "Brand", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4908), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "136e558715874c4cb99176b2d205e22c", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4892), true, "AffiliateList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4892), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "1aa3926f799f49c69fcce18de58f820a", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4916), true, "ProductList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4916), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "1ba8d5730c304f55a8055b90e9a20a3b", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4999), true, "SurveyList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5000), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "1c1648f7cded4e6a807ffd12ea2168a6", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4887), true, "BranchList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4888), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "1ecc23eb61d54f22889533c13d16e444", new DateTime(2025, 5, 6, 9, 40, 42, 648, DateTimeKind.Local).AddTicks(9217), true, "Overview", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(3898), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "253d7e92276d46b6932664d82d216551", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4983), true, "Promotion", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4983), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "3a56f9dd017f43d59194373d21ce2600", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4972), true, "BookingItem", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4972), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "3ad0c65b89724d399427d190b7e55db4", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5019), true, "GeneralSetting", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5020), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "4024fd5f767c4f61bdb66fe3509f9ca0", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4997), true, "History", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4997), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "43507a58519d4d56a68f33eeb31786bc", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5024), true, "MembershipExtendDefaults", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5024), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "47fea9eb900c4b58bd31aec61a22a260", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5006), true, "GamePrize", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5006), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "4d8a4037ff6543c682d0f02ee6441fc1", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4978), true, "InvoiceTemplate", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4979), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "60757f782b8341e4b92630a1a7391e8b", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4993), true, "Rank", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4993), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "7b6cd2a56af14751a06e96fdebfbd573", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4981), true, "DiscountList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4981), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "7ebc267b356441f28562b1766603deb2", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5017), true, "TemplateUidList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5018), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "80b64e533851415d86e231d5b376a469", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5027), true, "ShippingFeeConfig", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5027), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "871b004cbbd247f3af00ff7edac3cc5c", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5030), true, "CustomForm", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5030), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8ec72bdb335a4cd9b9a57dac9d7c122e", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4995), true, "Tag", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4995), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "965d39f34b104a71940e8ce0aeace423", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4896), true, "ArticleList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4896), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "9910174b4bcc4bffab332b119069deb5", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5015), true, "EventTemplateHistory", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5016), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "9bc3025ae3604734b547bf7522618a1c", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4985), true, "VoucherList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4985), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "9cd7be1f1822427ba87455e76a9a1a97", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4974), true, "BookingList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4975), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "a91675a2859b470aa8f30ac55a3be5ef", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4910), true, "ProductProperty", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4910), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "b34df1c70d5b4e1c827fbbb70ac4766b", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5021), true, "EnableFeatures", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5022), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "b952cea0287c4658b2dff060aade4f70", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4905), true, "Category", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4906), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c773d86a8bc548ca8c2ba350a5353f66", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5009), true, "CampaignList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5009), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d038b9980cdf4b158073d6138bde798f", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5011), true, "CampaignHistory", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(5012), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d46538406b524f1ab43d6e73a236ea0e", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4989), true, "MembershipList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4989), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "e56395c770b542778de23b529004755a", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4976), true, "OrderList", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4977), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "f6e14c5891ad48f792b3bc12fa7c490a", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4894), true, "ArticleCategory", new DateTime(2025, 5, 6, 9, 40, 42, 649, DateTimeKind.Local).AddTicks(4894), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_PointHistories_ReferenceId",
                table: "PointHistories",
                column: "ReferenceId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_PointHistories_ReferenceId",
                table: "PointHistories");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "f5a6f3f71fed42569a0d5ab455034926");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "02c180315c1a49c9b3f6febfb3b89164");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "03f7d1e77d9a48129877d18b124cadf9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "05198f6674f34f4c99fd86970ccd42aa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "10b9c5cc3d524a0a9c90e3e1b9fcfe92");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "136e558715874c4cb99176b2d205e22c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1aa3926f799f49c69fcce18de58f820a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1ba8d5730c304f55a8055b90e9a20a3b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1c1648f7cded4e6a807ffd12ea2168a6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1ecc23eb61d54f22889533c13d16e444");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "253d7e92276d46b6932664d82d216551");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3a56f9dd017f43d59194373d21ce2600");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3ad0c65b89724d399427d190b7e55db4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4024fd5f767c4f61bdb66fe3509f9ca0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "43507a58519d4d56a68f33eeb31786bc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "47fea9eb900c4b58bd31aec61a22a260");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4d8a4037ff6543c682d0f02ee6441fc1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "60757f782b8341e4b92630a1a7391e8b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7b6cd2a56af14751a06e96fdebfbd573");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7ebc267b356441f28562b1766603deb2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "80b64e533851415d86e231d5b376a469");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "871b004cbbd247f3af00ff7edac3cc5c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8ec72bdb335a4cd9b9a57dac9d7c122e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "965d39f34b104a71940e8ce0aeace423");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9910174b4bcc4bffab332b119069deb5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9bc3025ae3604734b547bf7522618a1c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9cd7be1f1822427ba87455e76a9a1a97");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a91675a2859b470aa8f30ac55a3be5ef");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b34df1c70d5b4e1c827fbbb70ac4766b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b952cea0287c4658b2dff060aade4f70");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c773d86a8bc548ca8c2ba350a5353f66");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d038b9980cdf4b158073d6138bde798f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d46538406b524f1ab43d6e73a236ea0e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e56395c770b542778de23b529004755a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f6e14c5891ad48f792b3bc12fa7c490a");

            migrationBuilder.DropColumn(
                name: "ReferenceId",
                table: "PointHistories");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOE+NTsEce+W3r6dX1DoPJMuwWDZisOsvDnmq2tcusTYmRs+NahBOC5pxOgHJXNIKQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "5a884f2ab9a3476eae8434a7d1f3139f", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 5, 16, 12, 13, 600, DateTimeKind.Local).AddTicks(2938), "FeaturesButton", new DateTime(2025, 5, 5, 16, 12, 13, 600, DateTimeKind.Local).AddTicks(2941) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "03356be917084ddd921b74965112322f", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3804), true, "ProductProperty", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3805), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "0dd2ecbe15e745529f0141a0e55b68a9", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3814), true, "BookingList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3814), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "11be882c4d65474c9729fc00e8d645a4", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3787), true, "AffiliateList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3788), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "22aca0c646c9492487765a29a7626153", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3915), true, "ShippingFeeConfig", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3915), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "326914d2edeb4db18e39571f774ac696", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3827), true, "MembershipList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3827), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "34c5535e977f40c789804f5401db8f6a", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3830), true, "Rank", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3830), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "3c7b751825884733b430665209df8c8a", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3838), true, "SurveyList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3838), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "47aceb4906dd411d93171fa4c4383d1f", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3840), true, "GameList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3840), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "4849ab59ab86442ab6861ae0d00e685e", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3905), true, "EventTemplateHistory", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3905), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "56f4c87e959b4b62a96e4da9a85a90e3", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3816), true, "OrderList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3817), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "57db3ce3d4d94431929854c37a3b8b0f", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3836), true, "History", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3836), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "698a05b1701649c6b220881e3423f513", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3821), true, "DiscountList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3821), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "6c9a334343504a49aa3277d51857dc6b", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3897), true, "CampaignList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3897), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "705c6ae7106f4e4f9b14ae29b5e298c2", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3913), true, "MembershipExtendDefaults", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3913), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8a3c0336749640fc974c3816d9a9b96c", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3911), true, "EnableFeatures", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3911), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8c67354059d74d049aa09eb8b14c7378", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3800), true, "Category", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3800), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "8d8f056682fe45b3b8035dc587a8361c", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3907), true, "TemplateUidList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3907), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "8e98ed303fe74d10bb2c09e583def6c3", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3825), true, "VoucherList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3825), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "90a5d4b3747b4d5bb5e46299e2265c2b", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3802), true, "Brand", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3802), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "93e97d2505294f71b7895d1bd200d3c8", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3819), true, "InvoiceTemplate", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3819), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "9441f3a7fec24da996f5eeb0000f122f", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3895), true, "GamePrize", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3895), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "9ba71fb0cd8b490d8fff808a1e2ade53", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3792), true, "ArticleList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3792), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "a06f0cae65df48128e6c08fe9fabae49", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3892), true, "History", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3892), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "b0217dcb83274e90a9d2711b09115513", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3806), true, "ProductList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3807), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "b1f2f584e7804e6f9d7ccdf535aed271", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3775), true, "BranchList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3777), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "b3b9033a748d4b19885711d5e6d7d0b6", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3899), true, "CampaignHistory", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3899), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "c699db41fc784fa0ae17a7bc71e61cb8", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3903), true, "EventTemplateList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3903), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "c9cc66fc9ad249c5a835ad0ea6704b85", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3823), true, "Promotion", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3823), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "cad9981232694d0b8279c3ef655e8879", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3790), true, "ArticleCategory", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3790), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "d22e5042338646ddbd190d372256c82b", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3809), true, "BookingItem", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3810), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "d47c072d9db04a02ab5bf6f5f12145e3", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3918), true, "CustomForm", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3918), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "d54793c063e143a8af713598c2a36a64", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3909), true, "GeneralSetting", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3909), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "fc1654455318420aad7295919e20b71c", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3834), true, "Tag", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3834), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "fc213fddede74fd8b643c1c2e002fafa", new DateTime(2025, 5, 5, 16, 12, 13, 598, DateTimeKind.Local).AddTicks(8697), true, "Overview", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3063), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" }
                });
        }
    }
}
