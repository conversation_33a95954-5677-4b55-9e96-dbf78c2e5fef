﻿@model MiniAppCore.Models.DTOs.Surveys.SurveySectionDTO

<div class="section-item survey-section" data-section-id="@Model.Id">
    <div class="section-header">
        <div class="section-title">
            <span class="section-number">@Model.DisplayOrder</span>
            <span>@Model.TitleSection</span>
        </div>
        <div class="section-actions">
            <button class="btn btn-sm btn-outline-primary btn-action" type="button" data-bs-toggle="collapse" data-bs-target="#<EMAIL>" aria-expanded="true">
                <i class="ri-arrow-down-s-line"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger btn-action" onclick="SurveyModule.removeSection('@Model.Id')">
                <i class="ri-delete-bin-line fs-6"></i>
            </button>
        </div>
    </div>

    <div class="collapse show section-content" id="<EMAIL>">
        <!-- Questions Container -->
        <div class="questions-container">
            @if (Model.Questions == null || !Model.Questions.Any())
            {
                <div class="alert alert-info">
                    <i class="ri-information-line me-1"></i> Chưa có câu hỏi nào trong phần này.
                </div>
            }
            else
            {
                foreach (var question in Model.Questions.OrderBy(q => q.DisplayOrder))
                {
                    <partial name="_QuestionPartial" model="question" />
                }
            }
        </div>

        <!-- Add New Question -->
        <div class="add-container mt-4">
            <h6><i class="ri-add-circle-line"></i>Thêm câu hỏi mới</h6>
            <div class="form-group mb-3">
                <label class="form-label">Nội dung câu hỏi <span class="text-danger">*</span></label>
                <div class="quill-question-editor quill-editor-container"></div>
            </div>
            <div class="row g-2">
                <div class="col-md-8">
                    <select class="form-select new-question-type">
                        <option value="date">Ngày</option>
                        <option value="time">Thời gian</option>
                        <option value="paragraph">Đoạn văn</option>
                        <option value="multiChoice">Nhiều lựa chọn</option>
                        <option value="singleChoice">Một lựa chọn</option>
                        <option value="likert">Thang đo Likert</option>
                        <option value="dropDown">Dropdown</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-primary w-100" onclick="SurveyModule.addQuestionToSection('@Model.Id')">
                        <i class="ri-add-line me-1"></i> Thêm câu hỏi
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>