﻿using MiniAppCore.Enums;

namespace MiniAppCore.Models.DTOs.LuckyWheels
{
    // Chi tiết cấu hình giải thưởng của một lucky wheel, etc.
    public class ConfiguredGamePrizeDTO
    {
        public string? Id { get; set; } // Id của game prize 
        public string? Name { get; set; } // Tên giải thưởng
        public string? ImageUrl { get; set; } // Hình ảnh giải thưởng
        public string? ReferenceId { get; set; }
        public EPrizeType Type { get; set; } // Loại giải thưởng
        public float WinRate { get; set; }

        public int Quantity { get; set; }
        public int DailyLimit { get; set; }
    }
}
