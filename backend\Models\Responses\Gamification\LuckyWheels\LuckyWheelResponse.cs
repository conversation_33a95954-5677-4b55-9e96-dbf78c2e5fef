using MiniAppCore.Models.Responses.Gamification.GamePrizes;

namespace MiniAppCore.Models.Responses.Gamification.LuckyWheels
{
    public class LuckyWheelResponse
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }

        public long RemainingSpins { get; set; }

        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }

        public List<GamePrizeResponse> Prizes { get; set; } = new List<GamePrizeResponse>();
    }
}