﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Models.Common;
using MiniAppCore.Services.Memberships;
using MiniAppCore.Services.Memberships.Addresses;
using MiniAppCore.Services.Memberships.Ranks;
using MiniAppCore.Services.Memberships.VAT;
using MiniAppCore.Services.Tags;
using MiniAppCore.Services.Affiliates.Referrals;
using QRCoder;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class MembershipController(IConfiguration configuration, IMembershipService membershipService, IMembershipAddressService membershipAddressService, IRankService rankService, ITagService tagService, IMembershipVATService membershipVATService, IReferralService referralService) : Controller
    {
        public async Task<IActionResult> Index()
        {
            var ranks = await rankService.GetAllAsync();
            return View(ranks);
        }

        [HttpGet("Membership/Create")]
        public async Task<IActionResult> Createmembership()
        {
            var membership = new Membership()
            {
                Id = string.Empty,
                UserZaloId = string.Empty,
                UserZaloName = string.Empty,
                ReferralCode = string.Empty,
                PhoneNumber = string.Empty,
                Avatar = string.Empty,
                MiniAppId = string.Empty
            };

            //var ranks = await _settingService.GetRanks();
            ViewBag.Ranks = await rankService.GetAllAsync();

            ViewBag.Title = "Thêm mới khách hàng";
            ViewBag.Button = "Thêm mới";
            return PartialView("Views/membership/_membership.cshtml", membership);
        }

        [HttpGet("Membership/{id}")]
        public async Task<IActionResult> Detailmembership(string id)
        {
            var membership = await membershipService.GetByIdAsync(id);
            //var ranks = await _settingService.GetRanks();
            //var url = await _membershipService.GetUserReferralUrl(id);

            //var userCommission = await _commissionService.GetReceivedAmount(id);

            //var profileCommission = userCommission.Select(c => Convert.ToDecimal(c.Value)).Sum();
            //var withDrawAmount = await _withdrawRequestService.GetMembershipWithDrawAmount(id);

            //if (withDrawAmount > 0)
            //{
            //    profileCommission -= withDrawAmount;
            //}

            // Tạo mã QR từ zaloUrl
            var source = configuration["MiniAppSettings:Source"];
            var prefix = configuration["MiniAppSettings:ZaloUrl"];
            var url = $"{prefix}{membership?.MiniAppId}/home?ref={membership?.ReferralCode}&source={source}";
            QRCodeGenerator qrGenerator = new QRCodeGenerator();
            QRCodeData qrCodeData = qrGenerator.CreateQrCode(url, QRCodeGenerator.ECCLevel.Q);
            PngByteQRCode qrCode = new PngByteQRCode(qrCodeData);
            byte[] qrCodeAsPng = qrCode.GetGraphic(20);

            // Chuyển mảng byte thành Base64 string
            var qrCodeBase64 = Convert.ToBase64String(qrCodeAsPng);
            ViewBag.ZaloUrl = url;
            ViewBag.QrCodeBase64 = qrCodeBase64;
            ViewBag.Ranks = await rankService.GetAllAsync();

            ViewBag.Addresses = (await membershipAddressService.GetAddressesByUserZaloId(membership?.UserZaloId, new RequestQuery()
            {
                Page = 1,
                PageSize = 1000
            })).Data;

            ViewBag.MembershipVAT = (await membershipVATService.GetMembershipVATUserZaloId(membership?.UserZaloId, new RequestQuery()
            {
                Page = 1,
                PageSize = 1000
            })).Data;

            var listMbEx = new List<MembershipExtend>();
            if (membership != null)
            {
                listMbEx = await membershipService.GetListMembershipExtendByUserZaloId(membership?.UserZaloId ?? "");
            }
            var listMbExDf = await membershipService.GetListMembershipExtendDefault();
            var seletedTags = await membershipService.GetTagsMembership(membership?.UserZaloId ?? "");

            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Chi tiết khách hàng";
            ViewBag.Tags = await tagService.GetAllAsync() ?? new List<Tag>();

            return PartialView("Views/Membership/Partials/_Membership.cshtml", (membership, listMbExDf, listMbEx, seletedTags));
        }

        /// <summary>
        /// Xem cây đa cấp của một thành viên
        /// </summary>
        /// <param name="userZaloId">ID Zalo của thành viên</param>
        /// <param name="maxDepth">Độ sâu tối đa (mặc định 3)</param>
        /// <returns>Partial view hiển thị cây đa cấp</returns>
        [HttpGet("Membership/ReferralTree/{userZaloId}")]
        public async Task<IActionResult> GetReferralTree(string userZaloId, int maxDepth = 3)
        {
            try
            {
                var tree = await referralService.GetReferralTreeAsync(userZaloId, maxDepth);
                if (tree == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy thành viên" });
                }

                var statistics = await referralService.GetReferralStatisticsAsync(userZaloId);

                ViewBag.Statistics = statistics;
                ViewBag.MaxDepth = maxDepth;

                return PartialView("Partials/_TreeReferral", tree);
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Lấy thống kê đa cấp của một thành viên
        /// </summary>
        /// <param name="userZaloId">ID Zalo của thành viên</param>
        /// <returns>JSON với thông tin thống kê</returns>
        [HttpGet("Membership/ReferralStatistics/{userZaloId}")]
        public async Task<IActionResult> GetReferralStatistics(string userZaloId)
        {
            try
            {
                var statistics = await referralService.GetReferralStatisticsAsync(userZaloId);
                if (statistics == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy thành viên" });
                }

                return Json(new
                {
                    success = true,
                    data = statistics
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Lấy danh sách thành viên được giới thiệu trực tiếp
        /// </summary>
        /// <param name="userZaloId">ID Zalo của thành viên giới thiệu</param>
        /// <returns>JSON với danh sách thành viên được giới thiệu trực tiếp</returns>
        [HttpGet("Membership/DirectReferrals/{userZaloId}")]
        public async Task<IActionResult> GetDirectReferrals(string userZaloId)
        {
            try
            {
                var referrals = await referralService.GetDirectReferralsAsync(userZaloId);
                return Json(new
                {
                    success = true,
                    data = referrals
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// Lấy đường dẫn từ gốc đến một thành viên trong cây đa cấp
        /// </summary>
        /// <param name="userZaloId">ID Zalo của thành viên</param>
        /// <returns>JSON với đường dẫn từ gốc đến thành viên</returns>
        [HttpGet("Membership/ReferralPath/{userZaloId}")]
        public async Task<IActionResult> GetReferralPath(string userZaloId)
        {
            try
            {
                var path = await referralService.GetReferralPathAsync(userZaloId);
                return Json(new
                {
                    success = true,
                    data = path
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        [HttpGet("Membership/TestReferralTree")]
        public IActionResult TestReferralTree()
        {
            return View();
        }
    }
}
