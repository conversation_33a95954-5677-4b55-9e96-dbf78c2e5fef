﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Services.Offers.Promotions;

namespace MiniAppCore.Controllers.API
{
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    [ApiController]
    public class PromotionsController(ILogger<PromotionsController> logger, IPromotionService promotionService) : ControllerBase
    {
        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult> GetAll([FromQuery] RequestQuery query)
        {
            try
            {
                var discounts = await promotionService.GetPage(query);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    discounts.Data,
                    discounts.TotalPages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost]
        public async Task<ActionResult> Create([FromBody] PromotionRequest model)
        {
            try
            {
                var result = await promotionService.CreateAsync(model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo chương trình giảm giá thành công!",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<ActionResult> GetById(string id)
        {
            try
            {
                var property = await promotionService.GetByIdAsync(id);
                if (property == null)
                {
                    return StatusCode(404, "Không tìm thấy chương trình giảm giá!");
                }
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = property
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<ActionResult> Update(string id, [FromBody] PromotionRequest model)
        {
            try
            {
                var article = await promotionService.UpdateAsync(id, model);

                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật chương trình giảm giá thành công!",
                    Data = article
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> Delete(string id)
        {
            try
            {
                await promotionService.DeleteByIdAsync(id);

                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa chương trình thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }
    }
}
