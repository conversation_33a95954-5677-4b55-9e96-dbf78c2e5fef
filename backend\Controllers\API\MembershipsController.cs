﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Memberships;
using MiniAppCore.Models.Responses.Affiliates;
using MiniAppCore.Models.Responses.Memberships;
using MiniAppCore.Services.Affiliates.Commissions;
using MiniAppCore.Services.Authencation;
using MiniAppCore.Services.Memberships;
using MiniAppCore.Services.Memberships.Addresses;
using MiniAppCore.Services.Memberships.CustomForms;
using MiniAppCore.Services.Memberships.Ranks;
using MiniAppCore.Services.Memberships.VAT;
using System.Net;

namespace MiniAppCore.Controllers.API
{
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController]
    [Route("api/[controller]")]
    public class MembershipsController(ILogger<MembershipsController> logger,
                                       IRankService rankService,
                                       IAuthencationService authencationService,
                                       IMembershipService membershipService,
                                       ICommissionService commissionService,
                                       ICustomFormService customFormService,
                                       IMembershipAddressService membershipAddressService,
                                       IMembershipVATService membershipVATService
                                       
        ) : ControllerBase
    {
        [AllowAnonymous]
        [HttpPost("share")]
        public async Task<IActionResult> ShareInfo(RegisterMember info, [FromQuery] string? source, [FromQuery] string? referralCode)
        {
            try
            {
                var result = await membershipService.RegisterMembership(info, source, referralCode);
                var membership = await membershipService.GetByUserZaloId(info.UserZaloId);
                var accessToken = await authencationService.LoginMiniApp(info.PhoneNumber, info.UserZaloId);
                //var rank = await _setttingService.GetRankById(result.RankId);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Token = accessToken,
                    Data = new
                    {
                        membership?.Id,
                        membership?.UserZaloId,
                        membership?.PhoneNumber,
                        membership?.Avatar,
                        membership?.ReferralCode,
                        membership?.SpinPoint,
                        membership?.UsingPoint,
                        membership?.RankingPoint,
                        //Rank = new
                        //{
                        //    rank?.Id,
                        //    rank?.Name,
                        //    rank?.Image
                        //},
                    },
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [AllowAnonymous]
        [HttpGet("FormRegister")]
        public async Task<IActionResult> FormRegister([FromQuery] string? formId)
        {
            try
            {
                var formCustomResponse = new FormCustomResponse()
                {
                    FormId = string.Empty,
                    FormName = string.Empty,
                    FormTitle = string.Empty,
                    CampaignName = string.Empty,
                    CheckboxTextColor = string.Empty,
                    SubmitButtonText = string.Empty,
                    SubmitButtonColor = string.Empty,
                    FormBackgroundColor = string.Empty,
                };
                if (!string.IsNullOrEmpty(formId))
                {
                    formCustomResponse = await customFormService.GetActiveCustomFormAsync(formId);
                }
                else
                {
                    formCustomResponse.InputFields = await membershipService.GetFormExtendInfo();
                }
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = formCustomResponse ?? new object()
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpGet("profile")]
        public async Task<IActionResult> Profile()
        {
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var membership = await membershipService.GetByUserZaloId(userZaloId);
                //var userRef = await _membershipService.GetUserReferral(parentId: membership.Id);
                //var userCommission = await _commissionService.GetReceivedAmount(membership.Id);

                var userCommssions = await commissionService.GetUserCommissionTransactions(userZaloId);
                var groupedCommissions = userCommssions
                   .GroupBy(t => t.UserZaloId)
                   .Select(g => new GroupedCommission
                   {
                       UserZaloId = g.Key,
                       UserZaloName = g.First().UserZaloName,
                       PhoneNumber = g.First().PhoneNumber,
                       ReferralCode = g.First().ReferralCode,
                       Avatar = g.First().Avatar,
                       TotalCommission = g.Sum(t => t.TotalCommission)
                   })
                   .ToList();
                var totalCommission = userCommssions.Where(x => !x.IsPaid).Sum(c => c.TotalCommission);
                var rank = await rankService.GetByIdAsync(membership?.RankingId ?? string.Empty);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = new
                    {
                        membership?.Id,
                        membership?.UserZaloId,
                        membership?.PhoneNumber,
                        membership?.SpinPoint,
                        membership?.UsingPoint,
                        membership?.UserZaloName,
                        DisplayName = membership?.UserZaloName,
                        Rank = new
                        {
                            Id = rank?.Id ?? Guid.NewGuid().ToString("N"),
                            Name = rank?.Name ?? "Thành viên mới",
                            Image = $"{Request.Scheme}://{Request.Host}/uploads/images/ranks/{rank?.Image}"  // "https://domain.com.vn"
                        },
                        membership?.RankingPoint,
                        membership?.ReferralCode,
                        ProfileCommission = groupedCommissions,
                        TotalCommission = totalCommission,
                        MembershipRef = new List<object>(),
                    }
                });
            }
            catch (NotFoundException ex)
            {
                return Ok(new
                {
                    ex.Code,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        [HttpGet("profileV2")]
        public async Task<IActionResult> ProfileV2()
        {
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var profile = await membershipService.GetMembershipProfile(userZaloId);
                if (profile != null)
                {
                    var userCommssions = await commissionService.GetUserCommissionTransactions(userZaloId);
                    var groupedCommissions = userCommssions
                       .GroupBy(t => t.UserZaloId)
                       .Select(g => new GroupedCommission
                       {
                           UserZaloId = g.Key,
                           UserZaloName = g.First().UserZaloName,
                           PhoneNumber = g.First().PhoneNumber,
                           ReferralCode = g.First().ReferralCode,
                           Avatar = g.First().Avatar,
                           TotalCommission = g.Sum(t => t.TotalCommission)
                       })
                       .ToList();

                    //profile.TotalCommission = groupedCommissions.Sum(c => c.TotalCommission);
                    var totalCommission = userCommssions.Where(x => !x.IsPaid).Sum(c => c.TotalCommission);
                    profile.TotalCommission = totalCommission;
                }
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = profile
                });
            }
            catch (NotFoundException ex)
            {
                return Ok(new
                {
                    ex.Code,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("profile")]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequest model)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                await membershipService.UpdateProfileAsync(userZaloId, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thông tin thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(500);
            }
        }

        #region Point History

        [HttpGet("PointHistory")]
        public async Task<IActionResult> GetPointHistory([FromQuery] RequestQuery queryParams)
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var result = await membershipService.GetPointHistoriesByUserZaloIdAsync(userZaloId, queryParams);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        #endregion

        #region Membership Rank

        [HttpGet("MyRank")]
        public async Task<IActionResult> MembershipRank()
        {
            try
            {
                var userZaloId = User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var membership = await membershipService.GetByUserZaloId(userZaloId);
                var rank = await rankService.GetMembershipRankResponseAsync(membership?.RankingId ?? string.Empty, membership?.RankingPoint ?? 0);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = rank
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        #endregion

        #region "ADDRESS FUNCTION"

        [HttpGet("MyAddress")]
        public async Task<IActionResult> MyAddress([FromQuery] RequestQuery query)
        {
            try
            {
                var UserZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                var result = await membershipAddressService.GetAddressesByUserZaloId(UserZaloId, query);
                // Kiểm tra nếu không có địa chỉ nào là mặc định
                if (!result.Data.Any(x => x.IsDefault))
                {
                    // Nếu không có, đánh dấu địa chỉ đầu tiên là mặc định chỉ trong bộ nhớ
                    var firstAddress = result.Data.OrderBy(x => x.CreatedDate).FirstOrDefault();
                    if (firstAddress != null)
                    {
                        firstAddress.IsDefault = true;
                    }
                }

                var data = result.Data.Select(x => new
                {
                    x.Id,
                    x.Note,
                    x.Name,
                    x.IsDefault,
                    x.PhoneNumber,
                    Details = new
                    {
                        City = x.ProvinceId,
                        District = x.DistrictId,
                        Ward = x.WardId,
                        x.StreetLine,
                        FullTextAddressSelect =
                            string.IsNullOrWhiteSpace(x.StreetLine)
                            ? x.FullAddress
                            : x.FullAddress.Replace($"{x.StreetLine},", "").Trim(),
                    },
                    x.FullAddress,
                });
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpGet("DefaultAddress")]
        public async Task<IActionResult> DefaultAddress()
        {
            try
            {
                var UserZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                var address = await membershipAddressService.GetDefaultAddress(UserZaloId);

                if (address == null)
                {
                    return Ok(new
                    {
                        Code = 1,
                        Message = "Không có dữ liệu",
                    });
                }

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = new
                    {
                        address.Id,
                        address.Note,
                        address.Name,
                        address.IsDefault,
                        address.PhoneNumber,
                        Details = new
                        {
                            City = address.ProvinceId,
                            District = address.DistrictId,
                            Ward = address.WardId,
                            address.StreetLine,
                            FullTextAddressSelect =
                            string.IsNullOrWhiteSpace(address.StreetLine)
                            ? address.FullAddress
                            : address.FullAddress.Replace($"{address.StreetLine},", "").Trim(),
                        },
                        address.FullAddress,
                    }
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpGet("Address/{id}")]
        public async Task<IActionResult> AddressDetail(string id)
        {
            try
            {
                var result = await membershipAddressService.GetByIdAsync(id);
                if (result == null) return Ok(new { Code = 0, Message = "Không tìm thấy dữ liệu." });
                var data = new
                {
                    result.Id,
                    result.Note,
                    result.Name,
                    result.FullAddress,
                    Details = new
                    {
                        result.Name,
                        result.IsDefault,
                        result.StreetLine,
                        Ward = result.WardId,
                        City = result.ProvinceId,
                        District = result.DistrictId,
                        FullTextAddressSelect =
                            string.IsNullOrWhiteSpace(result.StreetLine)
                            ? result.FullAddress
                        : result.FullAddress.Replace($"{result.StreetLine},", "").Trim(),
                    },
                };

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = data
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpPost("Address")]
        public async Task<IActionResult> CreateAddress([FromBody] MembershipAddressRequest address)
        {
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                var result = await membershipAddressService.CreateAsync(userZaloId, address);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo mới địa chỉ thành công.",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpPut("Address/{id}")]
        public async Task<IActionResult> UpdateAddress(string id, [FromBody] MembershipAddressRequest address)
        {
            try
            {
                var result = await membershipAddressService.UpdateAsync(id, address);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật địa chỉ thành công.",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpDelete("Address/{id}")]
        public async Task<IActionResult> DeleteAddress(string id)
        {
            try
            {

                await membershipAddressService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xoá địa chỉ thành công.",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        #endregion

        #region MembershipVAT

        [HttpGet("MyMembershipVAT")]
        public async Task<IActionResult> MyMembershipVAT([FromQuery] RequestQuery query)
        {
            try
            {
                var UserZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                var result = await membershipVATService.GetMembershipVATUserZaloId(UserZaloId, query);
                // Kiểm tra nếu không có địa chỉ nào là mặc định
                if (!result.Data.Any(x => x.IsDefault))
                {
                    // Nếu không có, đánh dấu địa chỉ đầu tiên là mặc định chỉ trong bộ nhớ
                    var firstAddress = result.Data.OrderBy(x => x.CreatedDate).FirstOrDefault();
                    if (firstAddress != null)
                    {
                        firstAddress.IsDefault = true;
                    }
                }

                var data = result.Data.Select(x => new
                {
                    x.Id,
                    x.TaxCode,
                    x.OwnerName,
                    x.IsDefault,
                    x.Email,
                    x.Type,
                    Details = new
                    {
                        City = x.ProvinceId,
                        District = x.DistrictId,
                        Ward = x.WardId,
                        x.StreetLine,
                        FullTextAddressSelect =
                            string.IsNullOrWhiteSpace(x.StreetLine)
                            ? x.FullAddress
                            : x.FullAddress.Replace($"{x.StreetLine},", "").Trim(),
                    },
                    x.FullAddress,
                });
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpGet("DefaultMembershipVAT")]
        public async Task<IActionResult> DefaultMembershipVAT()
        {
            try
            {
                var UserZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                var address = await membershipVATService.GetDefaultMembershipVAT(UserZaloId);

                if (address == null)
                {
                    return Ok(new
                    {
                        Code = 1,
                        Message = "Không có dữ liệu",
                    });
                }

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = new
                    {
                        address.Id,
                        address.TaxCode,
                        address.OwnerName,
                        address.IsDefault,
                        address.Email,
                        address.Type,
                        Details = new
                        {
                            City = address.ProvinceId,
                            District = address.DistrictId,
                            Ward = address.WardId,
                            address.StreetLine,
                            FullTextAddressSelect =
                            string.IsNullOrWhiteSpace(address.StreetLine)
                            ? address.FullAddress
                            : address.FullAddress.Replace($"{address.StreetLine},", "").Trim(),
                        },
                        address.FullAddress,
                    }
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpGet("MembershipVAT/{id}")]
        public async Task<IActionResult> MembershipVAT(string id)
        {
            try
            {
                var result = await membershipVATService.GetByIdAsync(id);
                if (result == null) return Ok(new { Code = 0, Message = "Không tìm thấy dữ liệu." });
                var data = new
                {
                    result.Id,
                    result.TaxCode,
                    result.OwnerName,
                    result.IsDefault,
                    result.Email,
                    result.Type,
                    Details = new
                    {
                        result.OwnerName,
                        result.IsDefault,
                        result.StreetLine,
                        Ward = result.WardId,
                        City = result.ProvinceId,
                        District = result.DistrictId,
                        FullTextAddressSelect =
                            string.IsNullOrWhiteSpace(result.StreetLine)
                            ? result.FullAddress
                        : result.FullAddress.Replace($"{result.StreetLine},", "").Trim(),
                    },
                };

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = data
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpPost("MembershipVAT")]
        public async Task<IActionResult> CreateMembershipVAT([FromBody] MembershipVATRequest address)
        {
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                var result = await membershipVATService.CreateAsync(userZaloId, address);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo mới thông tin VAT thành công.",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpPut("MembershipVAT/{id}")]
        public async Task<IActionResult> UpdateMembershipVAT(string id, [FromBody] MembershipVATRequest address)
        {
            try
            {
                var result = await membershipVATService.UpdateAsync(id, address);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thông tin VAT thành công.",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        [HttpDelete("MembershipVAT/{id}")]
        public async Task<IActionResult> DeleteMembershipVAT(string id)
        {
            try
            {

                await membershipVATService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xoá thông tin VAT thành công.",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal server error",
                });
            }
        }

        #endregion

        #region "ADMIN FUNCTION"

        [HttpGet]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> GetPage([FromQuery] UserQueryParameters query)
        {
            try
            {
                var result = await membershipService.GetPage(query);
                //var ranks = await _setttingService.GetRanks();
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpGet("Tree/{userZaloId}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> GetTreeReferral(string userZaloId, [FromQuery] int? maxDepth)
        {
            try
            {
                var details = await commissionService.GetUserCommissionTransactions(userZaloId);
                var userCommssions = await commissionService.GetUserCommissionTransactions(userZaloId);
                var groupedCommissions = userCommssions
                   .GroupBy(t => t.UserZaloId)
                   .Select(g => new GroupedCommission
                   {
                       UserZaloId = g.Key,
                       UserZaloName = g.First().UserZaloName,
                       PhoneNumber = g.First().PhoneNumber,
                       ReferralCode = g.First().ReferralCode,
                       Avatar = g.First().Avatar,
                       TotalCommission = g.Sum(t => t.TotalCommission)
                   })
                   .ToList();
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = groupedCommissions,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> UpdateMembership(string id, [FromBody] MembershipDTO dto)
        {
            try
            {
                await membershipService.UpdateAsync(id, dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thông tin khách hàng thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("Import")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> ImportMembership(IFormFile file)
        {
            try
            {
                var response = await membershipService.ImportMembershipList(file);

                if (response.Count > 0)
                {
                    return Ok(new
                    {
                        Code = 1,
                        Messages = "Xử lý thành công!",
                        Errors = response
                    });
                }

                return Ok(new
                {
                    Code = 0,
                    Messages = "Khởi tạo thành viên thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("Export")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> ExportMembership([FromBody] ExportMembershipRequest request)
        {
            try
            {
                var fileContent = await membershipService.ExportMemberships(request.StartDate, request.EndDate);
                var fileName = $"ThanhVien_{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                Response.Headers["Content-Disposition"] = $"attachment; filename={fileName}";
                return File(fileContent, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> DeleteMembership(string id)
        {
            try
            {
                await membershipService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa khách hàng thành công!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        #endregion


    }
}
