﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateLuckyWheelSimplifyAutoPoints : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "EnableAutoSpinPoints",
                table: "LuckyWheels",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<long>(
                name: "SpinPointsToAdd",
                table: "LuckyWheels",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EnableAutoSpinPoints",
                table: "LuckyWheels");

            migrationBuilder.DropColumn(
                name: "SpinPointsToAdd",
                table: "LuckyWheels");
        }
    }
}
