﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class CreateMembershipVATAndDatabaseSeeder : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "AspNetUserRoles",
                keyColumns: new[] { "RoleId", "UserId" },
                keyValues: new object[] { "12345678-90AB-CDEF-1234-567890ABCDEF", "09876543-21AB-CDEF-5678-90ABCDEF1234" });

            migrationBuilder.DeleteData(
                table: "AspNetUserRoles",
                keyColumns: new[] { "RoleId", "UserId" },
                keyValues: new object[] { "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0", "09876543-21AB-CDEF-5678-90ABCDEF1234" });

            migrationBuilder.DeleteData(
                table: "AspNetUserRoles",
                keyColumns: new[] { "RoleId", "UserId" },
                keyValues: new object[] { "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0", "ABCDEF12-3456-7890-ABCD-EF1234567890" });

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "ab67c03ee6c541ae9e6aeaa73e6fbc01");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "100b3d77fafc44009b5f3d3bf5ba3743");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "121e0cd4e9e9421f9235e176f8a7a83b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "249c38c9bd244bf69f539aaa1b3f4d06");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "24a22a91fde4415ea73009a6942351f4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2611a053b94a4e36892a07ea47b1e3b6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "26aa279c4c5b4457bdc5848635c40dac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2b3b6ca3b6d545b38f6ccf71cbb57655");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "30f6274a8e0144d291a2e7a1164137d1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "40b0a366eba044dfa9a77ea72d94eddc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "533f8dcafda74fd9aaa99cbab4aff95e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "55a8dc5077e94a259ab021c151050e82");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "62ce06b84bd14cd88e9d919ed4316590");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6b554834bf7340469fb2cec30e9f59e4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "70b320a5a8d948f39905d2031e1abbd8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7b5d5213709840e29b3032a5d3a26582");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "84e31336f8c84cd59c023b044310c4e8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8d942308f220407f8c04077f3d5a04bd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "957b7b02256542a89b83c0032569a6db");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9946ea89f80e42ee85ea3ddb90a57933");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a41a5eeedce0452e84f25725e4c168e1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a861681fdd0e491c9415d7c40251d9bb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "af0a2e91c04f45d9ad158cd32bbff151");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b8653872590c464cbd575934bfc4dce2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c041400916f24ef8847550fe62ae94c5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c5c0608ee334485fa9014f0853f439c4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cadee4e8e7334e0a8ad936381c41fabb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cf9c8d49affc40aaa29e4cc015c3f650");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d4be24ba33ec4f569cedf6c4a24816d5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d7caa70563ec4a9f80c992f717073cb3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dec7fab6ffd04fd288d4dd5e0eb2fd8a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e34be14105df43008684fd875818bf4c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e4e89cd1b0424fedb1bebba69dee0c13");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f265367fc04f48ec8874e66860236cdc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fc4e9a2546274d12ac00eb0d873d497c");

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "12345678-90AB-CDEF-1234-567890ABCDEF");

            migrationBuilder.DeleteData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0");

            migrationBuilder.DeleteData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234");

            migrationBuilder.DeleteData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "ABCDEF12-3456-7890-ABCD-EF1234567890");

            migrationBuilder.AddColumn<string>(
                name: "MembershipAddressId",
                table: "Orders",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MembershipVATId",
                table: "Orders",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsShow",
                table: "OrderDetailReviews",
                type: "bit",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "MembershipVATs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    UserZaloId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OwnerName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    TaxCode = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Type = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    StreetLine = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    WardId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DistrictId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    ProvinceId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    FullAddress = table.Column<string>(type: "nvarchar(350)", maxLength: 350, nullable: false),
                    IsDefault = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MembershipVATs", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MembershipVATs");

            migrationBuilder.DropColumn(
                name: "MembershipAddressId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "MembershipVATId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "IsShow",
                table: "OrderDetailReviews");

            migrationBuilder.InsertData(
                table: "AspNetRoles",
                columns: new[] { "Id", "ConcurrencyStamp", "Name", "NormalizedName" },
                values: new object[,]
                {
                    { "12345678-90AB-CDEF-1234-567890ABCDEF", "1", "SUPER_ADMIN", "SUPER_ADMIN" },
                    { "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0", "1", "ADMIN", "ADMIN" }
                });

            migrationBuilder.InsertData(
                table: "AspNetUsers",
                columns: new[] { "Id", "AccessFailedCount", "ConcurrencyStamp", "Email", "EmailConfirmed", "LockoutEnabled", "LockoutEnd", "NormalizedEmail", "NormalizedUserName", "PasswordHash", "PhoneNumber", "PhoneNumberConfirmed", "SecurityStamp", "TwoFactorEnabled", "UserName" },
                values: new object[,]
                {
                    { "09876543-21AB-CDEF-5678-90ABCDEF1234", 0, "afb6dce9-cebd-45fd-ab66-e2453e85c71a", "<EMAIL>", false, false, null, "<EMAIL>", "ICSG", "AQAAAAIAAYagAAAAEO8Wal3g0UsM6GSdmaGtoROVpAS4oV5SNFb3OllSCfzUiNfysmsNoc6fiaW54VPTbw==", "0000000000", false, "d928884b-3833-4d98-9fc1-46f334c5771e", false, "IncomSaiGon" },
                    { "ABCDEF12-3456-7890-ABCD-EF1234567890", 0, "0d7f89f6-30af-4a75-9c35-a5a58e2f829a", "<EMAIL>", false, false, null, "<EMAIL>", "MiniAppCore", "AQAAAAIAAYagAAAAEEtf/LLPy9rCLenxmCFlA5KzwWZgG1teOs++SBOrwRXkKm2JTdAb9V4LupFMQHhvSw==", "0000000000", false, "07599e3c-66ae-4283-9ee9-763775792ddf", false, "MiniAppCore" }
                });

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "ab67c03ee6c541ae9e6aeaa73e6fbc01", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 28, 16, 58, 19, 250, DateTimeKind.Local).AddTicks(5016), "FeaturesButton", new DateTime(2025, 5, 28, 16, 58, 19, 250, DateTimeKind.Local).AddTicks(5019) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "100b3d77fafc44009b5f3d3bf5ba3743", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7226), true, "MembershipList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7227), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "121e0cd4e9e9421f9235e176f8a7a83b", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7231), true, "Rank", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7231), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "249c38c9bd244bf69f539aaa1b3f4d06", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7245), true, "CampaignList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7245), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "24a22a91fde4415ea73009a6942351f4", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7224), true, "VoucherList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7225), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "2611a053b94a4e36892a07ea47b1e3b6", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7204), true, "ProductProperty", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7204), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "26aa279c4c5b4457bdc5848635c40dac", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7222), true, "Promotion", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7222), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "2b3b6ca3b6d545b38f6ccf71cbb57655", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7187), true, "ArticleCategory", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7188), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "30f6274a8e0144d291a2e7a1164137d1", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7253), true, "EventTemplateHistory", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7253), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "40b0a366eba044dfa9a77ea72d94eddc", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7243), true, "GamePrize", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7243), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "533f8dcafda74fd9aaa99cbab4aff95e", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7248), true, "CampaignHistory", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7249), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "55a8dc5077e94a259ab021c151050e82", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7259), true, "EnableFeatures", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7259), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "62ce06b84bd14cd88e9d919ed4316590", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7237), true, "SurveyList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7237), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "6b554834bf7340469fb2cec30e9f59e4", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7128), true, "BranchList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7130), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "70b320a5a8d948f39905d2031e1abbd8", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7184), true, "AffiliateList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7184), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "7b5d5213709840e29b3032a5d3a26582", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7255), true, "TemplateUidList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7255), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "84e31336f8c84cd59c023b044310c4e8", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7267), true, "CustomForm", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7267), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8d942308f220407f8c04077f3d5a04bd", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7239), true, "GameList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7239), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "957b7b02256542a89b83c0032569a6db", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7190), true, "ArticleList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7190), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "9946ea89f80e42ee85ea3ddb90a57933", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7263), true, "ShippingFeeConfig", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7263), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "a41a5eeedce0452e84f25725e4c168e1", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7235), true, "History", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7235), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "a861681fdd0e491c9415d7c40251d9bb", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(1023), true, "Overview", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(6386), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "af0a2e91c04f45d9ad158cd32bbff151", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7206), true, "ProductList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7206), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "b8653872590c464cbd575934bfc4dce2", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7216), true, "OrderList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7216), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "c041400916f24ef8847550fe62ae94c5", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7218), true, "InvoiceTemplate", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7218), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "c5c0608ee334485fa9014f0853f439c4", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7211), true, "BookingItem", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7212), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "cadee4e8e7334e0a8ad936381c41fabb", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7214), true, "BookingList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7214), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "cf9c8d49affc40aaa29e4cc015c3f650", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7257), true, "GeneralSetting", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7257), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "d4be24ba33ec4f569cedf6c4a24816d5", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7251), true, "EventTemplateList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7251), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d7caa70563ec4a9f80c992f717073cb3", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7261), true, "MembershipExtendDefaults", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7261), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "dec7fab6ffd04fd288d4dd5e0eb2fd8a", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7241), true, "History", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7241), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "e34be14105df43008684fd875818bf4c", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7202), true, "Brand", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7202), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "e4e89cd1b0424fedb1bebba69dee0c13", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7220), true, "DiscountList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7220), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "f265367fc04f48ec8874e66860236cdc", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7233), true, "Tag", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7233), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "fc4e9a2546274d12ac00eb0d873d497c", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7199), true, "Category", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7200), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" }
                });

            migrationBuilder.InsertData(
                table: "AspNetUserRoles",
                columns: new[] { "RoleId", "UserId" },
                values: new object[,]
                {
                    { "12345678-90AB-CDEF-1234-567890ABCDEF", "09876543-21AB-CDEF-5678-90ABCDEF1234" },
                    { "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0", "09876543-21AB-CDEF-5678-90ABCDEF1234" },
                    { "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0", "ABCDEF12-3456-7890-ABCD-EF1234567890" }
                });
        }
    }
}
