﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.FormCustoms
{
    public class CustomFormAttribute : BaseEntity
    {
        public required string CustomFormId { get; set; }
        public string? Type { get; set; } // kiểu thuộc tính
        public string? Attribute { get; set; } // key thuộc tính
        public string? AttributeLabel { get; set; } // label thuộc tính
        public string? AttributeValue { get; set; } // giá trị thuộc tính
        public string? DefaultValue { get; set; } // giá trị mặc định cho thuộc tính

        public bool IsActive { get; set; }

        public long Min { get; set; }
        public long Max { get; set; }
        public short DislayOrder { get; set; } // thứ tự hiển thị
    }
}
