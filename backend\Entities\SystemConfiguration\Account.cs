﻿using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.SystemConfiguration
{
    public class Account
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString("N").ToUpper();
        public string? EmployeeCode { get; set; }
        public required string Password { get; set; }
        public required string DisplayName { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public byte Gender { get; set; }
        public required string RoleName { get; set; }

        // Tiếp thị liên kết
        public string? ReferralCode { get; set; } = string.Empty;
        public int ReferralCount { get; set; }
        public byte CommissionRank { get; set; }
        public byte CommissionLevel { get; set; }

        public string? Address { get; set; }
        public string? ParentId { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
