﻿namespace MiniAppCore.Base.Interface
{
    public interface IReadOnlyRepository<T>
    {
        IQueryable<T> AsQueryable();
        Task<T?> FindByIdAsync(string id);
        Task<IEnumerable<T>> FindByIdsAsync(IEnumerable<string> idds);
    }

    public interface IWriteRepository<T>
    {
        void Add(T entity);
        void AddRange(IEnumerable<T> entities);

        void Update(T entity);
        void UpdateRange(IEnumerable<T> entities);
    }

    public interface IDeleteRepository<T>
    {
        void Delete(T entities);
        void DeleteRange(IEnumerable<T> entities);
        Task DeleteByIdAsync(string id);
    }

    public interface IRepository<T> :
                     IWriteRepository<T>,
                     IDeleteRepository<T>,
                     IReadOnlyRepository<T>
    {

    }
}
