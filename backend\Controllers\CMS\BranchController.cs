﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Branches;
using MiniAppCore.Services.Branches;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class BranchController(IBranchService branchService) : Controller
    {
        public async Task<IActionResult> Index()
        {
            return View();
        }

        [HttpGet("Branch/Create")]
        public IActionResult Create()
        {
            var product = new Branch()
            {
                Id = string.Empty,
                Name = string.Empty,
                PhoneNumber = string.Empty,
                Description = string.Empty,
                //Email = string.Empty,
                StreetLine = string.Empty,
                Longitude = string.Empty,
                Latitude = string.Empty,
                //Avatar = string.Empty,
                //City = string.Empty,
                //Ward = string.Empty,
                //District = string.Empty,
            };
            ViewBag.Title = "Thêm mới chi nhánh";
            ViewBag.Button = "Lưu";
            return PartialView("_Branch", product);
        }

        [HttpGet("Branch/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Title = "Cập nhật chi nhánh";
            ViewBag.Button = "Cập nhật";
            var result = await branchService.GetByIdAsync(id);

            if (result != null)
            {
                ViewBag.Maps = $"https://www.google.com/maps/@{result.Latitude},{result.Longitude},15z";
            }
            return PartialView("_Branch", result);
        }
    }
}
