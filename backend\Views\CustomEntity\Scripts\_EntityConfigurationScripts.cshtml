<script>
    // Entity Configuration Scripts
    document.addEventListener('DOMContentLoaded', function () {
        if (document.getElementById('entityConfigurationForm')) {
            initializeEntityConfiguration();
        }
    });

    function initializeEntityConfiguration() {
        const fieldsContainer = document.getElementById('fieldsContainer');
        const addFieldBtn = document.getElementById('addFieldBtn');
        const form = document.getElementById('entityConfigurationForm');

        updateFieldCount();
        toggleNoFieldsMessage();
        initializeSortable(fieldsContainer);

        addFieldBtn.addEventListener('click', addNewField);
        form.addEventListener('submit', saveEntityConfiguration);

        // Use event delegation for dynamically added fields
        fieldsContainer.addEventListener('click', function (e) {
            if (e.target && e.target.closest('.remove-field-btn')) {
                removeField(e.target.closest('.remove-field-btn'));
            }
        });

        fieldsContainer.addEventListener('input', function (e) {
            if (e.target && e.target.classList.contains('field-name-input')) {
                formatFieldName(e.target);
                
                // Auto-generate display name if it's empty
                const fieldWrapper = e.target.closest('.field-item-wrapper');
                const displayNameInput = fieldWrapper.querySelector('input[name*=".FieldNameDisplay"]');
                
                if (displayNameInput && !displayNameInput.value && e.target.value) {
                    // Convert camelCase/PascalCase to readable text
                    let displayName = e.target.value
                        .replace(/([A-Z])/g, ' $1') // Add space before capital letters
                        .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
                        .trim();
                    displayNameInput.value = displayName;
                }
            }
            validateEntityConfiguration(); // Validate on any input change
        });
    }

    function addNewField() {
        const template = document.getElementById('fieldTemplate').innerHTML;
        const newIndex = fieldsContainer.children.length;

        // Create a temporary div to hold the new HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = template.replace(/\[-1\]/g, `[${newIndex}]`).replace(/_-1_/g, `_${newIndex}_`);

        const newFieldWrapper = tempDiv.firstElementChild;
        newFieldWrapper.style.display = 'none';
        fieldsContainer.appendChild(newFieldWrapper);

        // Animate the new field
        $(newFieldWrapper).slideDown(300, function () {
            this.querySelector('.field-name-input')?.focus();
        });

        updateFieldIndices();
        toggleNoFieldsMessage();
        updateFieldCount();
        validateEntityConfiguration();
    }

    function removeField(button) {
        const fieldWrapper = button.closest('.field-item-wrapper');
        const isExistingField = fieldWrapper.querySelector('input[name*=".Id"]')?.value;

        const confirmAndRemove = () => {
            $(fieldWrapper).slideUp(300, function () {
                this.remove();
                updateFieldIndices();
                toggleNoFieldsMessage();
                updateFieldCount();
                validateEntityConfiguration();
            });
        };

        if (isExistingField) {
            // Replace with a more robust confirmation library if available (e.g., SweetAlert)
            if (confirm('Bạn có chắc chắn muốn xóa field này? Dữ liệu liên quan có thể bị ảnh hưởng.')) {
                confirmAndRemove();
            }
        } else {
            confirmAndRemove();
        }
    }

    function updateFieldIndices() {
        const items = fieldsContainer.querySelectorAll('.field-item-wrapper');
        items.forEach((item, index) => {
            // Update field number display
            const fieldNumber = item.querySelector('.field-number');
            if fieldNumber) fieldNumber.textContent = `#${index + 1}`;

            // Update names and IDs of all inputs, selects, and labels
            item.querySelectorAll('[name*="Fields["]').forEach(el => {
                el.name = el.name.replace(/\[\d+\]/, `[${index}]`);
                if (el.id) el.id = el.id.replace(/_\d+_/, `_${index}_`);
            });
            item.querySelectorAll('label[for*="Fields_"]').forEach(el => {
                el.htmlFor = el.htmlFor.replace(/_\d+_/, `_${index}_`);
            });
        });
    }

    function toggleNoFieldsMessage() {
        const hasFields = fieldsContainer.children.length > 0;
        document.getElementById('noFieldsMessage').style.display = hasFields ? 'none' : 'block';
    }

    function updateFieldCount() {
        const count = fieldsContainer.children.length;
        document.getElementById('fieldCount').textContent = count;
    }

    function initializeSortable(container) {
        if (typeof Sortable !== 'undefined' && container) {
            new Sortable(container, {
                animation: 150,
                handle: '.drag-handle',
                ghostClass: 'field-placeholder',
                onUpdate: function () {
                    updateFieldIndices();
                }
            });
        }
    }

    function saveEntityConfiguration(e) {
        e.preventDefault();
        const form = e.target;
        const submitBtn = document.getElementById('saveConfigBtn');

        if (!validateEntityConfiguration()) {
            // Optionally, show a generic error message
            return;
        }

        const originalBtnHtml = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status"></span>Đang lưu...`;

        const formData = new FormData(form);

        $.ajax({
            url: form.action,
            type: 'POST',
            data: $(form).serialize(), // jQuery serialize still useful here
            success: function (response) {
                if (response.success) {
                    $('#customFieldModal').modal('hide');
                    if (typeof AlertResponse === 'function') AlertResponse(response.message, "success");
                    const entityName = $('#entitySelector').val();
                    if (entityName) loadEntityData(entityName);
                } else {
                    if (typeof AlertResponse === 'function') AlertResponse(response.message, "error");
                }
            },
            error: function () {
                if (typeof AlertResponse === 'function') AlertResponse('Có lỗi xảy ra, vui lòng thử lại.', "error");
            },
            complete: function () {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnHtml;
            }
        });
    }

    function validateEntityConfiguration() {
        let isValid = true;
        const fieldNames = new Set();
        const saveBtn = document.getElementById('saveConfigBtn');
        const fields = fieldsContainer.querySelectorAll('.field-item-wrapper');

        if (fields.length === 0) {
            saveBtn.disabled = true;
            return true; // It's valid to have no fields, just can't save.
        }

        fields.forEach(field => {
            const fieldNameInput = field.querySelector('.field-name-input');
            const fieldNameDisplayInput = field.querySelector('input[name*=".FieldNameDisplay"]');
            const dataTypeSelect = field.querySelector('.data-type-select');

            // Reset validation states
            fieldNameInput.classList.remove('is-invalid');
            if (fieldNameDisplayInput) fieldNameDisplayInput.classList.remove('is-invalid');
            dataTypeSelect.classList.remove('is-invalid');

            const fieldName = fieldNameInput.value.trim();
            const fieldNameDisplay = fieldNameDisplayInput ? fieldNameDisplayInput.value.trim() : '';

            // Validate Field Name: required, pattern, and uniqueness
            if (!fieldName || !/^[A-Za-z][A-Za-z0-9]*$/.test(fieldName) || fieldNames.has(fieldName.toLowerCase())) {
                fieldNameInput.classList.add('is-invalid');

                // Add feedback if it doesn't exist
                let feedback = fieldNameInput.nextElementSibling;
                if (!feedback || !feedback.classList.contains('invalid-feedback')) {
                    feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    fieldNameInput.parentNode.appendChild(feedback);
                }
                if (!fieldName) {
                    feedback.textContent = 'Tên field là bắt buộc.';
                } else if (fieldNames.has(fieldName.toLowerCase())) {
                    feedback.textContent = 'Tên field phải là duy nhất.';
                } else {
                    feedback.textContent = 'Tên field không hợp lệ.';
                }

                isValid = false;
            } else {
                fieldNames.add(fieldName.toLowerCase());
            }

            // Validate Field Name Display: required
            if (fieldNameDisplayInput && !fieldNameDisplay) {
                fieldNameDisplayInput.classList.add('is-invalid');
                isValid = false;
            }

            // Validate Data Type: required
            if (!dataTypeSelect.value) {
                dataTypeSelect.classList.add('is-invalid');
                isValid = false;
            }
        });

        saveBtn.disabled = !isValid;
        return isValid;
    }

    function formatFieldName(input) {
        const value = input.value;
        let formatted = value.replace(/[^a-zA-Z0-9]/g, '');
        if (formatted) {
            // Capitalize first letter, but don't force case on the rest
            formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1);
        }
        if (formatted !== value) {
            input.value = formatted;
        }
    }
</script>
