﻿﻿﻿@using MiniAppCore.Entities.Branches
@using MiniAppCore.Entities.Products
@using MiniAppCore.Enums
@using MiniAppCore.Helpers
@using MiniAppCore.Models.DTOs.Products
@using MiniAppCore.Models.DTOs.Properties
@model MiniAppCore.Models.Responses.Products.ProductDetailResponse;
@using System.Linq

@{
    var listBranch = (List<Branch>)ViewBag.SelectedBranch ?? new List<Branch>();
    var containsBranch = User.Claims
        .Where(c => c.Type == "ViewPermission")
        .Any(c => c.Value.Contains("Branch"));
}

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body" style="@(User.IsInRole("EMPLOYEE") ? "pointer-events: none;" : "")">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.Id" />
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label>Tên sản phẩm <span style="color:red">*</span></label>
                            <input id="name" type="text" class="form-control" value="@Model.Name" placeholder="Ví dụ: Iphone X, v.v" data-error-message="Vui lòng nhập tên sản phẩm." maxlength="120" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Thứ tự hiển thị</label>
                            <input id="displayOrder"
                                   type="text"
                                   min="1"
                                   class="form-control"
                                   value="@(Model.DisplayOrder.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Nhập thứ tự hiển thị."
                                   data-error-message="Vui lòng nhập thứ tự hiển thị"
                                   oninput="InputValidator.currency(this)"
                                   required>
                        </div>
                    </div>

                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Loại sản phẩm</label>
                            <select id="isGift" class="form-control">
                                <option value="true" selected="@(Model.IsGift)">Quà tặng</option>
                                <option value="false" selected="@(!Model.IsGift)">Sản phẩm</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Danh mục <span style="color:red">*</span></label>
                            <select id="categories" class="selectpicker form-control" required multiple>
                                @* <option value="">--- Chọn danh mục --- </option> *@
                            </select>
                        </div>
                    </div>

                    @* Danh mục con *@
                    <div class="col-md-12">
                        <div id="selectedCategoryChildContainer" class="row">
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Giá bán <span style="color:red">*</span></label>
                            <input id="price" 
                                    type="text" 
                                    min="0" 
                                    class="form-control" 
                                    value="@(Model.OriginalPrice.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))" 
                                    placeholder="Nhập giá bán." oninput="InputValidator.currency(this)" 
                                    data-error-message="Vui lòng nhập giá bán." 
                                    required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Lượt bán</label>
                            <input id="boughtCount"
                                   type="text"
                                    min="0" 
                                    class="form-control"
                                    value="@(Model.BoughtCount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                    placeholder="Nhập lượt bán." 
                                    data-error-message="Vui lòng nhập lượt bán."
                                    oninput="InputValidator.currency(this)"
                                    required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3" hidden>
                        <div class="form-group">
                            <label>Lượt thích</label>
                            <input id="likeCount"
                                   type="text"
                                   min="0"
                                   class="form-control"
                                   value="@(Model.LikeCount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Nhập lượt thích."
                                   data-error-message="Vui lòng nhập lượt thích."
                                   oninput="InputValidator.currency(this)"
                                   required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Lượt đánh giá</label>
                            <input id="reviewCount"
                                   type="text"
                                   min="0"
                                   class="form-control"
                                   value="@(Model.ReviewCount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Nhập lượt đánh giá."
                                   data-error-message="Vui lòng nhập lượt đánh giá"
                                   oninput="InputValidator.currency(this)"
                                   required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Điểm đánh giá</label>
                            <input id="reviewPoint" type="number" min="0" class="form-control" max="5" value="@Model.ReviewPoint" 
                            placeholder="Nhập điểm đánh giá (0 - 5)"
                            data-error-message="Vui lòng nhập điểm đánh giá." 
                            step="0.1"
                            oninput="validateReviewPoint(this, 0, 5)"
                            required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label>Trạng thái hoạt động</label>
                            <select id="stockStatus" class="form-control" required>
                                <option value="@EProduct.InStock" selected="@(Model.Status == EProduct.InStock)">Còn hàng</option>
                                <option value="@EProduct.OutOfStock" selected="@(Model.Status == EProduct.OutOfStock)">Hết hàng</option>
                                <option value="@EProduct.Discontinued" selected="@(Model.Status == EProduct.Discontinued)">Ngưng hoạt động</option>
                            </select>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12 @(containsBranch ? "" : "d-none")">
                        <div class="form-group">
                            <label>Chọn chi nhánh</label>
                            <select id="isGlobalProductSelect" class="form-control">
                                <option value="true" selected="@Model.IsGlobalProduct">Tất cả chi nhánh</option>
                                <option value="false" selected="@(!Model.IsGlobalProduct)">Từng chi nhánh</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12 @(containsBranch ? "" : "d-none")" id="branchSelectContainer">
                        <div class="form-group">
                            <label>Chi nhánh</label>
                            <select id="branchId" class="form-control" multiple>
                                @if (listBranch.Any())
                                {
                                    foreach (Branch item in listBranch)
                                    {
                                        <option value="@item.Id" selected>@item.Name</option>
                                    }
                                }
                                @*  else
                                    {
                                        <option value="all" selected>Tất cả chi nhánh</option>
                                    } *@
                            </select>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12" hidden>
                        <div class="form-group">
                            <label>Thương hiệu</label>
                            <select id="brandId" class="form-control" required>
                                @* <option value="0" selected="@(Model.BrandId == 0)">Chọn thương hiệu</option> *@
                                @* @if (ViewBag.Brands != null) *@
                                @* { *@
                                @*         foreach (Brand item in ViewBag.Brands) *@
                                @*         { *@
                                @*                 <option value="@item.Id" selected="@(Model.BrandId == item.Id)">@item.Name</option> *@
                                @*         } *@
                                @* } *@
                            </select>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <label class="mb-0">Mô tả</label>
                                <div id="editor-counter">0/3000</div>
                            </div>
                            <div id="description" class="rounded-bottom" style="height: 250px" data-type="content">@Html.Raw(Model.Description)</div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Chọn phân loại</label>
                            <select id="propertySelect" class="selectpicker form-control" multiple>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div id="selectedPropertiesContainer" class="row">
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <label class="h6 mb-0">Giá sản phẩm theo từng loại <span style="color:red">*</span></label>
                        </div>
                        <div id="variantContainer">
                            <p>Không có biến thể cho sản phẩm này.</p>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Ảnh<span style=" color:red">*</span></label>
                            <input type="file" id="pics" class="form-control image-file" name="pic" accept="image/*" onchange="ShowPreview(event)" multiple>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div id="preview" class="row d-flex flex-nowrap" style="overflow-x: scroll; min-height: 250px;">
                            @if (Model.Images.Any())
                            {
                                foreach (var item in Model.Images)
                                {
                                    <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                        <img src="@item" class="card-img-top h-100" alt="Alternate Text" style="object-fit:contain" />
                                        <span class="btn-preview-remove" data-url="@item">x</span>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="submit" class="btn btn-primary" id="btn-save-product" onclick="HandleSaveOrUpdate('@Model.Id')">
            <span class="spinner-border spinner-border-sm me-2 d-none" id="btn-save-product-spinner" role="status" aria-hidden="true"></span>
            <span class="btn-save-product-text" data-button-text="@ViewBag.Button">@ViewBag.Button</span>
        </button>
    </div>
</div>

<script>
    variantIndex = 0;
    allProperties = @Html.Raw(Json.Serialize(ViewBag.Properties));

    $(document).ready(async () => {

        const categories = @Html.Raw(Json.Serialize(Model.Categories));

        GetCategoryByType('#categories', "#type", categories, {dropdownParent: $("#modal-product")});
        GetProperties('#propertySelect', null, @Html.Raw(Json.Serialize(Model.Options)), {dropdownParent: $("#modal-product")});

        currentImages = $("#preview .image-preview img").length;

        $("#isGlobalProductSelect").change(function () {
            if ($(this).val() === "true") {
                $("#branchSelectContainer").hide();
            } else {
                $("#branchSelectContainer").show();
            }
        });

        InitialEditor();

        const options = @Html.Raw(Json.Serialize(Model.Options));
        const variants = @Html.Raw(Json.Serialize(Model.Variants));

        const optionMap = BuildPropertyOptionMap(options, variants);
        
        for (const opt of options) {
            const selectedValueIds = optionMap[opt.propertyId] ? Array.from(optionMap[opt.propertyId]) : [];
            await RenderPropertyCard(opt.propertyId, selectedValueIds);
        }

        //Danh mục con


        const categoryChilds = @Html.Raw(Json.Serialize(Model.CategorieChilds));  
        for (const cate of categories) {
            const selectedValueIds = categoryChilds.length > 0 ? categoryChilds.map(item=>item.id) : [];
            await RenderCategoryChildCard(cate.id, selectedValueIds);
        }

        GenerateVariants(); // render lại các card variant
        setTimeout(() => {
            FillVariantData(variants);
        }, 500);
    });

    function BuildPropertyOptionMap(options, variants) {
        const map = {}; // { propertyId: Set(propertyValueId) }

        for (const variant of variants) {
            for (const valueId of variant.propertyValueIds) {
                const option = options
                    .flatMap(opt => opt.variants.map(v => ({ propertyId: opt.propertyId, valueId: v.propertyValueId })))
                    .find(v => v.valueId === valueId);

                if (option) {
                    if (!map[option.propertyId]) map[option.propertyId] = new Set();
                    map[option.propertyId].add(valueId);
                }
            }
        }

        return map;
    }

    $("#branchId").select2({
        // dropdownParent: $("#modal-product"),
        allowClear: false,
        minimumInputLength: 0,
        ajax: {
            url: '@Url.Action("GetPage", "Branches")' + "?isGetAll=true",
            dataType: 'json',
            delay: 250, // Thời gian trễ khi gõ tìm kiếm
            data: function (params) {
                return {
                    keyword: params.term, // Keyword tìm kiếm
                    page: params.page || 1,
                    pageSize: 10
                };
            },
            processResults: function (data, params) {
                params.page = params?.page || 1;
                // Map data
                const mappedData = data.data.map(function (item) {
                    return {
                        id: item['id'],
                        text: item['name']
                    };
                });
                
                let isAllBranchAdded = @Html.Raw((listBranch.Count == 0).ToString().ToLower());
                if (!isAllBranchAdded) {
                    // Thêm phần tử rỗng vào đầu mảng
                    mappedData.unshift({ id: 'all', text: 'Tất cả chi nhánh' });
                    isAllBranchAdded = true;
                }

                return {
                    results: mappedData,
                    pagination: {
                        more: (params.page * (10)) < (data.totalItems || 0)
                    }
                };
            },
            cache: true,
        }
    });
</script>