﻿﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON>h sách phần thưởng</h4>
                <p class="mb-0">
                    <PERSON>h sách phần thưởng được trình bày hiệu quả và cung cấp không gian để liệt kê các phần thưởng.
                </p>
            </div>
            <button type="button" class="btn btn-primary mt-2" onclick="GetFormWheelPrize()">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>
    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i><PERSON><PERSON> lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="prize-table" class="table-responsive rounded mb-3">
            <table id="list-prize" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-wheelPrize" class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>


@section Scripts {
    <script>
        // Search functionality
        $(document).ready(function () {
            $('#search').on('input', function () {
                table.ajax.reload(null, false);
            });

            GetListPrize();
        });

        function ShowPreview(event) {
            $("#preview").html("");
            const files = event.target.files;

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = (e) => {
                    const previewItem = $(`<div class="image-preview mx-2 card position-relative" style="max-width: 250px; height: 170px;">`);
                    const image = $(`<img src="${e.target.result}" class="card-img-top h-100" alt="Hình ảnh" style="object-fit:contain" />`);
                    previewItem.append(image);

                    const removeButton = $('<span class="btn-preview-remove">x</span>');
                    removeButton.click(function () {
                        $(this).parent().remove();
                        // Reset files input
                        const inputFile = $("#image")[0];
                        const filesArray = Array.from(inputFile.files).filter(f => f !== file);
                        inputFile.files = new FileListItems(filesArray);
                    });

                    previewItem.append(removeButton);
                    $("#preview").append(previewItem);
                };
            }
        }

        function GetVouchers(selector, catId) {
            const type = "";
            $.ajax({
                url: '@Url.Action("GetAllVoucher", "Vouchers")',
                type: 'GET',
                data: { keyword: type },
                success: function (res) {
                    const { data } = res;

                    if (data.length == 0) {
                        $(selector).html(`<option value=""> Không có voucher nào </option>`);
                        return;
                    } else {
                        $(selector).html('');
                        $(selector).append(`<option value=""> Không dùng voucher </option>`);
                        data.map(item => {
                            $(selector).append(`<option value='${item.id}'>${item.code}</option>`);
                        });
                    }

                    if (catId) {
                        $(selector).val(catId).trigger('change');
                    } else {
                        $(selector).val("").trigger('change');
                    }
                }
            });
        }

        // DataTable for list of prizes
        function GetListPrize() {
            table = new DataTable("#list-prize", {
                searching: false,
                sort: false,
                pageLength: 10,
                responsive: true,
                serverSide: true,
                ajax: function (data, callback) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetPage", "GamePrizes")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                stt: data.start + index + 1,
                                name: item.name,
                                value: item.value,
                                description: item.description,
                                prizeType: GetPrizeTypeText(item.type),
                                actions: `<button class="btn btn-info" onclick="GetFormWheelPrize('${item.id}')">
                                                    <i class="ri-edit-line fs-6 mr-0"></i>
                                                </button>
                                                <button class="btn btn-warning" onclick="DeletePrize('${item.id}')">
                                                    <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                </button>`
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "stt", className: 'text-center' },
                    { title: "Tên giải thưởng", data: "name", className: 'text-center' },
                    { title: "Giá trị", data: "value", className: 'text-center' },
                    { title: "Mô tả", data: "description" },
                    { title: "Loại giải thưởng", data: "prizeType", className: 'text-center' },
                    { title: "Thao tác", data: "actions", className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });
        }

        function GetFormWheelPrize(id) {
            const url = id ? '@Url.Action("Detail", "GamePrize")' + `/${id}` : '@Url.Action("Create", "GamePrize")'
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-wheelPrize").modal("toggle");

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function HandleSaveOrUpdate(id) {
            // Validate required fields
            if (!$("#name").val()?.trim()) {
                AlertResponse("Vui lòng nhập tên giải thưởng.", "error");
                return;
            }
            if (!$("#value").val()?.trim()) {
                AlertResponse("Vui lòng nhập giá trị giải thưởng.", "error");
                return;
            }
            if (!$("#description").val()?.trim()) {
                AlertResponse("Vui lòng nhập mô tả giải thưởng.", "error");
                return;
            }

            const formData = new FormData();
            formData.append("Name", $("#name").val()?.trim());
            formData.append("Value", $("#value").val()?.trim());
            formData.append("Description", $("#description").val()?.trim());
            formData.append("Type", parseInt($("#prizeType").val()));
            formData.append("Metadata", $("#metadata").val()?.trim() || "");

            // Handle ReferenceId based on prize type
            const prizeType = parseInt($("#prizeType").val());
            if (prizeType === 1) { // Voucher type
                formData.append("ReferenceId", $("#vouchers").val()?.trim() || "");
            } else {
                formData.append("ReferenceId", "");
            }

            // Handle image upload
            const file = $('#image')[0].files[0];
            if (!id && !file) {
                AlertResponse("Vui lòng tải lên hình ảnh.", "error");
                return;
            }

            if (file) {
                formData.append('Image', file);
            }

            const url = id ? `/api/GamePrizes/${id}` : '/api/GamePrizes';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                        $("#modal-wheelPrize").modal("hide");
                    }
                    else {
                        AlertResponse(response.message, 'error');
                    }
                },
                error: function (xhr) {
                    const errorMessage = xhr.responseJSON ? xhr.responseJSON.message : xhr.responseText || "Có lỗi xảy ra, vui lòng thử lại!";
                    AlertResponse(errorMessage, "error");
                }
            });
        }

        function DeletePrize(id) {
            const url = `/api/GamePrizes/${id}`
            DeleteItem(url);
        }

        function GetListGamePrizes() {
            table = new DataTable("#list-prize", {
                searching: false,
                sort: false,
                pageLength: 10,
                responsive: true,
                serverSide: true,
                ajax: function (data, callback) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '/api/GamePrizes',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: item.name,
                                2: item.value,
                                3: item.description,
                                4: GetPrizeTypeText(item.type),
                                5: `<button class="btn btn-info" onclick="GetGamePrizeForm('${item.id}')">
                                                            <i class="ri-edit-line fs-6 mr-0"></i>
                                                        </button>
                                                        <button class="btn btn-danger" onclick="DeleteGamePrize('${item.id}')">
                                                            <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                        </button>`
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.totalPages * data.length || response.data.length,
                                    recordsFiltered: response.totalPages * data.length || response.data.length,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên giải thưởng", data: 1 },
                    { title: "Giá trị", data: 2 },
                    { title: "Mô tả", data: 3 },
                    { title: "Loại", data: 4 },
                    { title: "Thao tác", data: 5, className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });
        }

        function GetPrizeTypeText(type) {
            const types = {
                0: "Không trúng thưởng",
                1: "Voucher",
                2: "Sản phẩm",
                3: "Tiền mặt",
                4: "Điểm",
                99: "Khác"
            };
            return types[type] || "Không xác định";
        }
    </script>
}
