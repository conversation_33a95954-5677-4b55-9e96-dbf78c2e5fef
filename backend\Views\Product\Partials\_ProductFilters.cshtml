﻿@using MiniAppCore.Enums
@{
    var containsBranch = User.Claims
    .Where(c => c.Type == "ViewPermission")
    .Any(c => c.Value.Contains("Branch"));
}

<div class="container-fluid my-4">
    <!-- Quick Search and Advanced Filters Bar -->
    <div class="row mb-3">
        <div class="col-md-4">
            <div class="input-group shadow-sm">
                <input type="text" class="form-control" placeholder="Tìm kiếm sản phẩm..." id="search">
                <button class="btn btn-primary" type="button" id="btn-search">
                    <i class="ri-search-line"></i>
                </button>
            </div>
        </div>
        <div class="col-md-8 d-flex justify-content-end" style="display: none !important;">
            <button class="btn btn-outline-dark" type="button" data-bs-toggle="offcanvas" data-bs-target="#advancedFiltersOffcanvas">
                <i class="ri-equalizer-line me-1"></i> <PERSON><PERSON> lọc nâng cao
            </button>
        </div>
    </div>

    <!-- Basic Filters - Always Visible -->
    <div class="mb-4">
        <div class="card border-0 shadow">
            <div class="card-header py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0 d-flex align-items-center">
                        <i class="ri-filter-3-line text-primary me-2"></i> Bộ lọc sản phẩm
                    </h5>
                    <button class="btn btn-sm btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#filterBody" aria-expanded="true" aria-controls="filterBody" id="filterCollapseBtn">
                        <i class="ri-arrow-up-s-line fs-4"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="filterBody">
                <div class="card-body p-4">
                    <div class="row g-4">
                        <!-- Category Filter -->
                        <div class="col-md-6 col-lg-3">
                            <div class="filter-group">
                                <label for="filter-category" class="form-label fw-medium">
                                    <i class="ri-price-tag-3-line text-primary me-1"></i> Danh mục
                                </label>
                                <select class="form-select shadow-sm" id="filter-category" multiple>
                                    <!-- Options will be dynamically loaded -->
                                </select>
                            </div>
                        </div>

                        @if (containsBranch)
                        {
                            <!-- Branch Filter -->
                            <div class="col-md-6 col-lg-3">
                                <div class="filter-group">
                                    <label for="filter-branch" class="form-label fw-medium">
                                        <i class="ri-store-2-line text-primary me-1"></i> Chi nhánh
                                    </label>
                                    <select class="form-select shadow-sm" id="filter-branch" multiple>
                                        <!-- Options will be dynamically loaded -->
                                    </select>
                                </div>
                            </div>
                        }

                        <!-- Stock Status Filter -->
                        <div class="col-md-6 col-lg-3">
                            <div class="filter-group">
                                <label for="filter-stock" class="form-label fw-medium">
                                    <i class="ri-stack-line text-primary me-1"></i> Tình trạng
                                </label>
                                <select class="form-select shadow-sm" id="filter-stock">
                                    <option value="0">Tất cả</option>
                                    <option value="1">Còn hàng</option>
                                    <option value="2">Hết hàng</option>
                                    <option value="3">Ngừng kinh doanh</option>
                                </select>
                            </div>
                        </div>

                        <!-- Is Gift Filter -->
                        <div class="col-md-6 col-lg-3">
                            <div class="filter-group">
                                <label for="filter-gift" class="form-label fw-medium">
                                    <i class="ri-gift-line text-primary me-1"></i> Quà tặng
                                </label>
                                <select class="form-select shadow-sm" id="filter-gift">
                                    <option value="all">Tất cả</option>
                                    <option value="true">Là quà tặng</option>
                                    <option value="false">Không phải quà tặng</option>
                                </select>
                            </div>
                        </div>

                        <!-- Price Filter -->
                        <div class="col-md-6 col-lg-3">
                            <div class="filter-group">
                                <label class="form-label fw-medium">
                                    <i class="ri-money-dollar-circle-line text-primary me-1"></i> Khoảng giá
                                </label>
                                <div class="input-group shadow-sm">
                                    <input type="text" class="form-control" id="min-price" placeholder="Tối thiểu" 
                                           oninput="InputValidator.number(this)" value="0">
                                    <span class="input-group-text">-</span>
                                    <input type="text" class="form-control" id="max-price" placeholder="Tối đa" 
                                           oninput="InputValidator.number(this)" value="">
                                </div>
                            </div>
                        </div>

                        <!-- Filter Buttons -->
                        <div class="col-md-6 col-lg-9 d-flex align-items-end">
                            <div class="w-100">
                                <label class="form-label fw-medium d-block">
                                    <i class="ri-tools-line text-primary me-1"></i> Thao tác
                                </label>
                                <div class="w-100">
                                    <button class="btn btn-outline-secondary" id="btn-reset-filters">
                                        <i class="ri-refresh-line"></i> Đặt lại
                                    </button>
                                    <button class="btn btn-primary mx-2" id="btn-apply-filters">
                                        <i class="ri-filter-3-line me-1"></i> Áp dụng
                                    </button>
                                    <button class="btn btn-success" id="btn-export-excel">
                                        <i class="ri-file-excel-line me-1"></i> Xuất Excel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Filters Section -->
    <div id="activeFilters" class="d-none">
        <div class="card border-0 shadow-sm">
            <div class="card-body py-2">
                <div class="d-flex flex-wrap align-items-center">
                    <span class="text-muted me-2">Bộ lọc đang áp dụng:</span>
                    <div id="activeFilterBadges" class="d-flex flex-wrap gap-2"></div>
                    <button class="btn btn-sm btn-link text-danger ms-auto" id="clearAllFilters">
                        <i class="ri-close-circle-line"></i> Xóa tất cả
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Advanced Filters Offcanvas -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="advancedFiltersOffcanvas" aria-labelledby="advancedFiltersLabel">
    <div class="offcanvas-header border-bottom">
        <h5 class="offcanvas-title" id="advancedFiltersLabel">Bộ lọc nâng cao</h5>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
        <!-- Suppliers Section -->
        <div class="mb-4">
            <h6 class="mb-3 border-bottom pb-2">Nhà cung cấp & Thương hiệu</h6>
            <div class="mb-3">
                <label for="filter-supplier" class="form-label">Nhà cung cấp</label>
                <select class="form-select" id="filter-supplier">
                    <option value="">Tất cả nhà cung cấp</option>
                    <option value="1">Nhà cung cấp 1</option>
                    <option value="2">Nhà cung cấp 2</option>
                    <option value="3">Nhà cung cấp 3</option>
                </select>
            </div>
            <div class="mb-3">
                <label for="filter-brand" class="form-label">Thương hiệu</label>
                <select class="form-select" id="filter-brand">
                    <option value="">Tất cả thương hiệu</option>
                    <option value="1">Thương hiệu 1</option>
                    <option value="2">Thương hiệu 2</option>
                    <option value="3">Thương hiệu 3</option>
                </select>
            </div>
        </div>

        <!-- Status & Display Section -->
        <div class="mb-4">
            <h6 class="mb-3 border-bottom pb-2">Trạng thái & Hiển thị</h6>
            <div class="mb-3">
                <label for="filter-status" class="form-label">Trạng thái hiển thị</label>
                <select class="form-select" id="filter-status">
                    <option value="">Tất cả trạng thái</option>
                    <option value="published">Đang hiển thị</option>
                    <option value="draft">Đang ẩn</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label d-block">Đánh giá tối thiểu</label>
                <div class="btn-group w-100" role="group">
                    <input type="radio" class="btn-check" name="rating" id="rate-5" value="5" autocomplete="off">
                    <label class="btn btn-outline-warning" for="rate-5">5 sao</label>

                    <input type="radio" class="btn-check" name="rating" id="rate-4" value="4" autocomplete="off">
                    <label class="btn btn-outline-warning" for="rate-4">4+ sao</label>

                    <input type="radio" class="btn-check" name="rating" id="rate-3" value="3" autocomplete="off">
                    <label class="btn btn-outline-warning" for="rate-3">3+ sao</label>
                </div>
            </div>
        </div>

        <!-- Time & Date Section -->
        <div class="mb-4">
            <h6 class="mb-3 border-bottom pb-2">Thời gian</h6>
            <div class="mb-3">
                <label for="date-from" class="form-label">Từ ngày</label>
                <input type="date" class="form-control" id="date-from">
            </div>
            <div class="mb-3">
                <label for="date-to" class="form-label">Đến ngày</label>
                <input type="date" class="form-control" id="date-to">
            </div>
            <div class="mb-3">
                <label for="filter-sort" class="form-label">Sắp xếp theo</label>
                <select class="form-select" id="filter-sort">
                    <option value="newest">Mới nhất</option>
                    <option value="oldest">Cũ nhất</option>
                    <option value="price_asc">Giá tăng dần</option>
                    <option value="price_desc">Giá giảm dần</option>
                    <option value="name_asc">Tên A-Z</option>
                    <option value="name_desc">Tên Z-A</option>
                </select>
            </div>
        </div>
    </div>
    <div class="offcanvas-footer border-top p-3">
        <div class="d-grid gap-2">
            <button class="btn btn-outline-secondary" id="btn-reset-all-filters">
                <i class="ri-refresh-line"></i> Đặt lại tất cả
            </button>
            <button class="btn btn-primary" id="btn-apply-advanced-filters" data-bs-dismiss="offcanvas">
                <i class="ri-filter-3-line"></i> Áp dụng bộ lọc
            </button>
        </div>
    </div>
</div>
