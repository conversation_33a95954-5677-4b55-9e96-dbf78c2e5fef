﻿using MiniAppCore.Enums;

namespace MiniAppCore.Models.Requests.BookingItems
{
    public class BookingItemRequest
    {
        public string? Name { get; set; }
        public decimal Price { get; set; }
        public string? Description { get; set; }
        public EProduct Status { get; set; }
        public List<IFormFile> Images { get; set; } = new();
        public List<string> RemovedOldImages { get; set; } = new();

        // Statistic
        public int LikeCount { get; set; } = 0;
        public int BoughtCount { get; set; } = 0;
        public int ReviewCount { get; set; } = 0;
        public int DisplayOrder { get; set; } = 0;
        public double ReviewPoint { get; set; } = 0;
    }
}
