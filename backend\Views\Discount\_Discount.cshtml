﻿@using MiniAppCore.Entities.Offers.Discounts
@using MiniAppCore.Entities.Products
@using MiniAppCore.Models.Responses.Products
@using Newtonsoft.Json
@model (Discount discount, List<ProductItemResponse> selectedProducts)
@{
    var lstSelectedProductIds = Model.selectedProducts.Select(x => x.Id).ToList();
}
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.discount.Id" />
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Tên chương trình <span style="color:red">*</span></label>
                            <input id="name" type="text" class="form-control" value="@Model.discount.Name" placeholder="Ví dụ: Sale 5.5, v.v" data-errors="Vui lòng tên sản phẩm." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Loại giảm giá <span style="color:red">*</span></label>
                            <select id="type" class="form-control" data-type="type" oninput="HandleInputDiscountType()">
                                <option value="1" selected="@(Model.discount.Type == 1)">Tỉ lệ phần trăm giảm giá</option>
                                <option value="2" selected="@(Model.discount.Type == 2)">Giảm giá trực tiếp lên giá sản phẩm</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Giá trị giảm <span style="color:red">*</span></label>
                            <input id="discountValue" type="text" oninput="InputValidator.currency(this); HandleDiscountValue(this);" class="form-control" value="@(Model.discount.DiscountValue.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                   placeholder="Giá trị giảm giá... " data-errors="Vui lòng nhập giá trị giảm" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12" id="maxDiscountGroup">
                        <div class="form-group">
                            <label>Giảm giá tối đa <span style="color:red">*</span></label>
                            <input id="maxDiscountAmount" type="text" disabled="@(Model.discount.Type == 2)" oninput="InputValidator.currency(this)"
                                class="form-control" value="@(Model.discount.MaxDiscountAmount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))" min="0" placeholder="Giá tối đa... " data-errors="Vui lòng nhập giá trị" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Ngày bắt đầu <span style="color:red">*</span></label>
                            <input id="startDate" type="datetime-local" data-from="true" class="form-control" value="@Model.discount.StartDate.ToString("yyyy-MM-ddTHH:mm")" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Ngày hết hạn <span style="color:red">*</span></label>
                            <input id="expiryDate" type="datetime-local" data-to="true" class="form-control" value="@Model.discount.ExpiryDate.ToString("yyyy-MM-ddTHH:mm")" required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Trạng thái hoạt động <span style="color:red">*</span></label>
                            <select id="isActive" class="selectpicker form-control" required>
                                <option value="true" selected="@(Model.discount.IsActive)">Đang hoạt động</option>
                                <option value="false" selected="@(!Model.discount.IsActive)">Ngưng hoạt động</option>
                            </select>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="products">Chọn sản phẩm áp dụng <span style="color:red">*</span></label>
                            <select id="products" class="form-control" multiple>
                                @if (ViewBag.Products != null)
                                {
                                    foreach (Product item in ViewBag.Products)
                                    {
                                        if (item.IsGift)
                                        {
                                            continue;
                                        }
                                        @* List<string> images = !string.IsNullOrEmpty(item.Images) ? item.Images.Split(",").ToList() : new List<string>(); *@
                                        @* var image = images.FirstOrDefault() ?? ""; *@
                                        <option value="@item.Id" selected="@(lstSelectedProductIds.Contains(item.Id))">
                                            @item.Name
                                        </option>
                                    }
                                }
                            </select>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="descr">Mô tả chương trình</label>
                            <textarea id="descr" class="form-control" rows="4">@Model.discount.Description</textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (string.IsNullOrEmpty(Model.discount.Id))
        {
            <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
        }
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.discount.Id')">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        const selectedProducts = @Html.Raw(JsonConvert.SerializeObject(Model.selectedProducts));

        function formatProduct(product) {
            if (!product.id) return product.text;

            const image = $(product.element).data("image") || product.image || "/images/product/01.png";
            const price = $(product.element).data("price") || product.originalPrice || 0;
            const formattedPrice = new Intl.NumberFormat("vi-VN", {
                style: "currency",
                currency: "VND"
            }).format(price);

            return $(
                `<div style="display: flex; align-items: center;">
                    <img src="${image}" class="rounded-circle" style="width: 40px; height: 40px; margin-right: 10px;" />
                    <span>${product.text || product.name} - ${formattedPrice}</span>
                </div>`
            );
        }

        $("#products").select2({
            // ajax: {
            //     url: "/api/products",
            //     type: "GET",
            //     dataType: "json",
            //     delay: 300,
            //     data: function (params) {
            //         return {
            //             keyword: params.term,
            //             page: params.page || 1,
            //             pageSize: 10,
            //             stockStatus: 0,
            //         };
            //     },
            //     processResults: function (data, params) {
            //         const results = data.data.map(item => ({
            //             id: item.id,
            //             text: item.name,
            //             image: item.images?.[0] || "/images/product/01.png",
            //             originalPrice: item.originalPrice
            //         }));

            //         return {
            //             results: results,
            //             pagination: { more: data.totalPages > (params.page || 1) }
            //         };
            //     },
            //     cache: true
            // },
            // templateResult: formatProduct,
            // templateSelection: formatProduct,
            // minimumInputLength: 0,
            // width: "100%",
            // allowClear: true
        });

        // selectedProducts.forEach(function (product) {
        //     const option = new Option(product.Name, product.Id, true, true);

        //     console.log(product);

        //     $(option).attr("data-image", product.Images?.[0] || "/images/product/01.png");
        //     $(option).attr("data-price", product.Price);
        //     $("#products").append(option);
        // });

        // $("#products").trigger("change");
        // Gọi lại khi load xong
        HandleInputDiscountType();
    });
    function HandleDiscountValue(input) {
        const type = parseInt($("#type").val());
        if (type === 2) {
            $("#maxDiscountAmount").val($("#discountValue").val());
        }
    }
</script>