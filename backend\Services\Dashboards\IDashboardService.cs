﻿using MiniAppCore.Entities.Dashboards;

namespace MiniAppCore.Services.Dashboards
{
    public interface IDashboardService
    {
        #region Old statistics methods
        Task<int> TotalOrdersInRange(DateTime startDate, DateTime endDate, string branchId = null);
        Task<decimal> TotalRevenueInRange(DateTime startDate, DateTime endDate, string branchId = null);
        Task<List<ChartDataPoint>> ChartRevenueInRange(DateTime startDate, DateTime endDate, string type, string branchId = null);

        // Đơn hàng & doanh thu từ ngày bắt đầu đến ngày kết thúc
        Task<DailyOrdersRevenueDto> GetOrdersAndRevenue(DateTime startDate, DateTime endDate, string branchId = null);

        // Đơn booking từ ngày bắt đầu đến ngày kết thúc
        Task<int> GetBookingsCount(DateTime startDate, DateTime endDate, string branchId = null);

        // Số khách hàng mới từ ngày bắt đầu đến ngày kết thúc
        Task<int> GetNewCustomersCount(DateTime startDate, DateTime endDate, string branchId = null);

        // Lượt book của các dịch vụ từ ngày bắt đầu đến ngày kết thúc
        Task<List<ServiceBookingStatsDto>> GetServiceBookingStats(DateTime startDate, DateTime endDate, string branchId = null);

        // Lượt bán của các sản phẩm từ ngày bắt đầu đến ngày kết thúc
        Task<List<ProductSalesStatsDto>> GetProductSalesStats(DateTime startDate, DateTime endDate, string branchId = null);

        // Biểu đồ doanh thu theo ngày từ ngày bắt đầu đến ngày kết thúc
        Task<List<ChartDataPoint>> GetDailyRevenueChart(DateTime startDate, DateTime endDate, string branchId = null);

        // Biểu đồ đơn hàng theo ngày từ ngày bắt đầu đến ngày kết thúc
        Task<List<ChartDataPoint>> GetDailyOrdersChart(DateTime startDate, DateTime endDate, string branchId = null);

        // Biểu đồ booking theo ngày từ ngày bắt đầu đến ngày kết thúc
        Task<List<ChartDataPoint>> GetDailyBookingsChart(DateTime startDate, DateTime endDate, string branchId = null);

        #endregion

        #region Statistic New Methods 

        // Lấy thống kê đơn booking theo khoảng thời gian
        Task<IEnumerable<StatisticBooking>> GetBookingStatisticsAsync(DateOnly startDate, DateOnly endDate);

        // Lấy thống kê đơn booking theo trạng thái và khoảng thời gian
        Task<IEnumerable<StatisticBooking>> GetBookingStatisticsByStatusAsync(DateOnly startDate, DateOnly endDate, string status);

        // Lấy thống kê membership theo khoảng thời gian
        Task<IEnumerable<StatisticMembership>> GetMembershipStatisticsAsync(DateOnly startDate, DateOnly endDate);

        // Lấy thống kê đơn hàng theo khoảng thời gian
        Task<IEnumerable<StatisticOrder>> GetOrderStatisticsAsync(DateOnly startDate, DateOnly endDate);

        // Lấy thống kê đơn hàng theo trạng thái và khoảng thời gian
        Task<IEnumerable<StatisticOrder>> GetOrderStatisticsByStatusAsync(DateOnly startDate, DateOnly endDate, string status);

        // Lấy thống kê tổng doanh thu theo khoảng thời gian
        Task<decimal> GetTotalRevenueFromStatisticsAsync(DateOnly startDate, DateOnly endDate);

        // Lấy thống kê top sản phẩm bán chạy theo khoảng thời gian
        Task<IEnumerable<StatisticTopProducts>> GetTopProductsStatisticsAsync(DateOnly startDate, DateOnly endDate, int limit = 5);

        // Lấy thống kê top booking item bán chạy theo khoảng thời gian
        Task<IEnumerable<StatisticTopBookingItem>> GetTopBookingItemsStatisticsAsync(DateOnly startDate, DateOnly endDate, int limit = 5);

        // Lấy thống kê voucher theo khoảng thời gian
        Task<IEnumerable<StatisticVoucher>> GetVoucherStatisticsAsync(DateOnly startDate, DateOnly endDate);

        // Lấy thống kê tổng hợp cho dashboard
        Task<DashboardSummaryDto> GetDashboardSummaryAsync(DateOnly startDate, DateOnly endDate);

        #endregion
    }

    public class ChartDataPoint
    {
        public string Label { get; set; }
        public decimal Value { get; set; }
    }

    public class DailyOrdersRevenueDto
    {
        public int OrderCount { get; set; }
        public decimal Revenue { get; set; }
    }

    public class ServiceBookingStatsDto
    {
        public string ServiceId { get; set; }
        public string ServiceName { get; set; }
        public int BookingCount { get; set; }
    }

    public class ProductSalesStatsDto
    {
        public string ProductId { get; set; }
        public string ProductName { get; set; }
        public long Quantity { get; set; }
        public decimal Revenue { get; set; }
    }

    public class DashboardSummaryDto
    {
        public int TotalOrders { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TotalBookings { get; set; }
        public int NewMemberships { get; set; }
        public List<ChartDataPoint> RevenueChart { get; set; } = new List<ChartDataPoint>();
        public List<ChartDataPoint> NewMembershipChart { get; set; } = new List<ChartDataPoint>(); // label là status

        public List<ChartDataPoint> OrdersChart { get; set; } = new List<ChartDataPoint>();
        public List<ChartDataPoint> OrdersCountChart { get; set; } = new List<ChartDataPoint>(); // label là status
        public List<ChartDataPoint> OrdersRevenueChart { get; set; } = new List<ChartDataPoint>(); // label là status

        public List<ChartDataPoint> BookingsChart { get; set; } = new List<ChartDataPoint>();
        public List<ChartDataPoint> BookingsCountChart { get; set; } = new List<ChartDataPoint>(); // label là status
        public List<ChartDataPoint> BookingsRevenueChart { get; set; } = new List<ChartDataPoint>(); // label là status

        public List<StatisticTopProducts> TopProducts { get; set; } = new List<StatisticTopProducts>();
        public List<StatisticTopBookingItem> TopBookingItems { get; set; } = new List<StatisticTopBookingItem>();
    }
}
