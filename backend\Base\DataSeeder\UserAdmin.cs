using Microsoft.AspNetCore.Identity;
using MiniAppCore.Base.Database;
using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Base.DataSeeder
{
    public static class UserAdmin
    {
        public static async Task SeedAsync(IServiceProvider serviceProvider, ApplicationDbContext context)
        {
            using var scope = serviceProvider.CreateScope();
            var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();

            // --- 1. Seed Roles ---
            var roles = new[]
                {
                new IdentityRole { Name = "ADMIN", NormalizedName = "ADMIN" },
                new IdentityRole { Name = "SUPER_ADMIN", NormalizedName = "SUPER_ADMIN" }
            };

            foreach (var role in roles)
            {
                if (!await roleManager.RoleExistsAsync(role.Name))
                {
                    await roleManager.CreateAsync(role);
                }
            }

            // --- 2. Seed Users ---
            var defaultUsers = new[]
                {
                new {
                    UserName = "IncomSaiGon",
                    Email = "<EMAIL>",
                    Password = "Incom@2025",
                    Roles = new[] { "ADMIN", "SUPER_ADMIN" }
                },
                new {
                    UserName = "MiniAppCore",
                    Email = "<EMAIL>",
                    Password = "Incom@2025",
                    Roles = new[] { "ADMIN" }
                }
            };

            foreach (var u in defaultUsers)
            {
                var user = await userManager.FindByNameAsync(u.UserName);
                if (user == null)
                {
                    user = new ApplicationUser
                    {
                        UserName = u.UserName,
                        Email = u.Email,
                        PhoneNumber = "0000000000",
                        EmailConfirmed = true
                    };

                    await userManager.CreateAsync(user, u.Password);
                    await userManager.AddToRolesAsync(user, u.Roles);
                }

                // --- 3. Seed/Update ViewPermission for all users ---
                await SeedOrUpdateViewPermissions(context, user.Id, u.UserName);
            }
        }

        private static List<ViewPermission> GetViewPermissions(string userId)
        {
            return new List<ViewPermission>
            {
                new ViewPermission { ViewId = "Dashboard", SubViewId = "Overview", UserId = userId },
                new ViewPermission { ViewId = "Branch", SubViewId = "BranchList", UserId = userId },
                new ViewPermission { ViewId = "Affiliate", SubViewId = "AffiliateList", UserId = userId },
                new ViewPermission { ViewId = "Article", SubViewId = "ArticleCategory", UserId = userId },
                new ViewPermission { ViewId = "Article", SubViewId = "ArticleList", UserId = userId },
                new ViewPermission { ViewId = "Product", SubViewId = "Category", UserId = userId },
                new ViewPermission { ViewId = "Product", SubViewId = "Brand", UserId = userId },
                new ViewPermission { ViewId = "Product", SubViewId = "ProductProperty", UserId = userId },
                new ViewPermission { ViewId = "Product", SubViewId = "ProductList", UserId = userId },
                new ViewPermission { ViewId = "Booking", SubViewId = "BookingItem", UserId = userId },
                new ViewPermission { ViewId = "Booking", SubViewId = "BookingList", UserId = userId },
                new ViewPermission { ViewId = "Order", SubViewId = "OrderList", UserId = userId },
                new ViewPermission { ViewId = "Order", SubViewId = "InvoiceTemplate", UserId = userId },
                new ViewPermission { ViewId = "Discount", SubViewId = "DiscountList", UserId = userId },
                new ViewPermission { ViewId = "Discount", SubViewId = "Promotion", UserId = userId },
                new ViewPermission { ViewId = "Voucher", SubViewId = "VoucherList", UserId = userId },
                new ViewPermission { ViewId = "Membership", SubViewId = "MembershipList", UserId = userId },
                new ViewPermission { ViewId = "Membership", SubViewId = "Rank", UserId = userId },
                new ViewPermission { ViewId = "Membership", SubViewId = "Tag", UserId = userId },
                new ViewPermission { ViewId = "Survey", SubViewId = "History", UserId = userId },
                new ViewPermission { ViewId = "Survey", SubViewId = "SurveyList", UserId = userId },
                new ViewPermission { ViewId = "LuckyWheel", SubViewId = "GameList", UserId = userId },
                new ViewPermission { ViewId = "LuckyWheel", SubViewId = "History", UserId = userId },
                new ViewPermission { ViewId = "LuckyWheel", SubViewId = "GamePrize", UserId = userId },
                new ViewPermission { ViewId = "OmniTool", SubViewId = "CampaignList", UserId = userId },
                new ViewPermission { ViewId = "OmniTool", SubViewId = "CampaignHistory", UserId = userId },
                new ViewPermission { ViewId = "OmniTool", SubViewId = "EventTemplateList", UserId = userId },
                new ViewPermission { ViewId = "OmniTool", SubViewId = "EventTemplateHistory", UserId = userId },
                new ViewPermission { ViewId = "OmniTool", SubViewId = "TemplateUidList", UserId = userId },
                new ViewPermission { ViewId = "SystemSetting", SubViewId = "GeneralSetting", UserId = userId },
                new ViewPermission { ViewId = "SystemSetting", SubViewId = "EnableFeatures", UserId = userId },
                new ViewPermission { ViewId = "SystemSetting", SubViewId = "MembershipExtendDefaults", UserId = userId },
                new ViewPermission { ViewId = "SystemSetting", SubViewId = "ShippingFeeConfig", UserId = userId },
                new ViewPermission { ViewId = "SystemSetting", SubViewId = "CustomForm", UserId = userId },
                new ViewPermission { ViewId = "Event", SubViewId = "EventList", UserId = userId },
                new ViewPermission { ViewId = "Event", SubViewId = "Sponsor", UserId = userId },
                new ViewPermission { ViewId = "Event", SubViewId = "SponsorshipTier", UserId = userId },
                new ViewPermission { ViewId = "CustomEntity", SubViewId = "ListCustomEntity", UserId = userId },
            };
        }

        private static async Task SeedOrUpdateViewPermissions(ApplicationDbContext context, string userId, string userName)
        {
            var requiredViewPermissions = GetViewPermissions(userId);
            var existingPermissions = context.ViewPermissions.Where(p => p.UserId == userId).ToList();

            // Kiểm tra xem có ViewPermission nào trong database chưa
            bool isFirstTime = !existingPermissions.Any();

            if (isFirstTime)
            {
                // Lần đầu chạy - Seed toàn bộ data
                context.ViewPermissions.AddRange(requiredViewPermissions);
                await context.SaveChangesAsync();
                Console.WriteLine($"[FIRST TIME] Seeded {requiredViewPermissions.Count} view permissions for user {userName} (ID: {userId})");
            }
            else
            {
                // Từ lần 2 trở đi - Chỉ thêm những cái thiếu
                var missingPermissions = requiredViewPermissions
                    .Where(required => !existingPermissions.Any(existing =>
                        existing.ViewId == required.ViewId &&
                        existing.SubViewId == required.SubViewId &&
                        existing.UserId == required.UserId))
                    .ToList();

                if (missingPermissions.Any())
                {
                    context.ViewPermissions.AddRange(missingPermissions);
                    await context.SaveChangesAsync();
                    Console.WriteLine($"[UPDATE] Added {missingPermissions.Count} missing view permissions for user {userName} (ID: {userId}):");

                    // Log chi tiết những permission được thêm
                    foreach (var permission in missingPermissions)
                    {
                        Console.WriteLine($"  - Added: {permission.ViewId} -> {permission.SubViewId}");
                    }
                }
                else
                {
                    Console.WriteLine($"[UPDATE] No missing view permissions found for user {userName} (ID: {userId}). All permissions are up to date.");
                }
            }
        }
    }
}
