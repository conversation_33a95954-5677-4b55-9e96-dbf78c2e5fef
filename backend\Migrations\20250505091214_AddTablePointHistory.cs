﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddTablePointHistory : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RewardPoints");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "10526c72f3fb4e63a966a179144eee08");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "020acf3144e9478fb4d68fc3b3947387");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "09be8118dc1f4087b84eab54f123b92b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "160acbc7632547268fc109e01412a53e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "21e669fdff0f4e4f81372074e9856261");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "27c6b84bb0d24e6992afaabfad232280");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "28a3142ee56844aa9874bcc02aa78db3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2a4964403faf40f1991ecf3dc9a983fd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "35206e626c4742e2970d0b42cfbb8019");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3e1393a16f0f49b29bd5c522590b7bca");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5386ae44aba742c8ab1ae1764db44ea1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5d78cd6539d64b0fa727d37942c96902");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6ad2ccade3b846318a18db47e3f157a5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6ad7b189eb0f4389a07ed849d405d37a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6aef1e3b637344adb2c51198ae51c67b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7561de03066342afa39e3daee9fa13ba");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "76939c19a4ce4e4baf4498716758884c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "76bf383e145f4324be4605e1a23144cb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "846452aa43ea4e7d9e6a257743941ab1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "87ced92c7e8a49c3ac4882bb47e6f026");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "911aa9cb6ce14f858fa18da3e2a665d6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ecd3ee8e34a4ff7b268a6918bf34b38");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a215737b6ac7436c987294e76fb33396");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b92256492b2b491d823f88d3f0162b3b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bd2f0f8c82f2460986f84be386dc8daf");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c7b8e5f42df34f728f8cc4bb629847fc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cb5c2eabae68448b9e67274c6e6910e1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ce082790c573444ba16c6811e9b961a7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "da03c980a317415a8d065daf7ea01879");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e3ac48c79d5c46c8b4251c1dc5c5e6cb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ea09a7fb0d0c4d629e9eeed294abda21");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "eeec3e2ee9974b85b6d1a5b586016b0c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f2000bea390b40a3b8235b4657d4c898");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f9636e302e1341a697ad3e7eb601ba66");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fe765b727b6b4235ad04dd5b64a835d7");

            migrationBuilder.CreateTable(
                name: "PointHistories",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Amount = table.Column<long>(type: "bigint", nullable: false),
                    Type = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    UserZaloId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PointHistories", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOE+NTsEce+W3r6dX1DoPJMuwWDZisOsvDnmq2tcusTYmRs+NahBOC5pxOgHJXNIKQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "5a884f2ab9a3476eae8434a7d1f3139f", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 5, 16, 12, 13, 600, DateTimeKind.Local).AddTicks(2938), "FeaturesButton", new DateTime(2025, 5, 5, 16, 12, 13, 600, DateTimeKind.Local).AddTicks(2941) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "03356be917084ddd921b74965112322f", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3804), true, "ProductProperty", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3805), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "0dd2ecbe15e745529f0141a0e55b68a9", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3814), true, "BookingList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3814), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "11be882c4d65474c9729fc00e8d645a4", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3787), true, "AffiliateList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3788), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "22aca0c646c9492487765a29a7626153", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3915), true, "ShippingFeeConfig", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3915), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "326914d2edeb4db18e39571f774ac696", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3827), true, "MembershipList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3827), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "34c5535e977f40c789804f5401db8f6a", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3830), true, "Rank", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3830), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "3c7b751825884733b430665209df8c8a", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3838), true, "SurveyList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3838), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "47aceb4906dd411d93171fa4c4383d1f", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3840), true, "GameList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3840), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "4849ab59ab86442ab6861ae0d00e685e", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3905), true, "EventTemplateHistory", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3905), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "56f4c87e959b4b62a96e4da9a85a90e3", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3816), true, "OrderList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3817), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "57db3ce3d4d94431929854c37a3b8b0f", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3836), true, "History", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3836), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "698a05b1701649c6b220881e3423f513", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3821), true, "DiscountList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3821), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "6c9a334343504a49aa3277d51857dc6b", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3897), true, "CampaignList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3897), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "705c6ae7106f4e4f9b14ae29b5e298c2", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3913), true, "MembershipExtendDefaults", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3913), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8a3c0336749640fc974c3816d9a9b96c", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3911), true, "EnableFeatures", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3911), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8c67354059d74d049aa09eb8b14c7378", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3800), true, "Category", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3800), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "8d8f056682fe45b3b8035dc587a8361c", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3907), true, "TemplateUidList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3907), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "8e98ed303fe74d10bb2c09e583def6c3", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3825), true, "VoucherList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3825), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "90a5d4b3747b4d5bb5e46299e2265c2b", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3802), true, "Brand", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3802), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "93e97d2505294f71b7895d1bd200d3c8", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3819), true, "InvoiceTemplate", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3819), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "9441f3a7fec24da996f5eeb0000f122f", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3895), true, "GamePrize", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3895), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "9ba71fb0cd8b490d8fff808a1e2ade53", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3792), true, "ArticleList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3792), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "a06f0cae65df48128e6c08fe9fabae49", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3892), true, "History", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3892), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "b0217dcb83274e90a9d2711b09115513", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3806), true, "ProductList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3807), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "b1f2f584e7804e6f9d7ccdf535aed271", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3775), true, "BranchList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3777), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "b3b9033a748d4b19885711d5e6d7d0b6", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3899), true, "CampaignHistory", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3899), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "c699db41fc784fa0ae17a7bc71e61cb8", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3903), true, "EventTemplateList", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3903), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "c9cc66fc9ad249c5a835ad0ea6704b85", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3823), true, "Promotion", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3823), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "cad9981232694d0b8279c3ef655e8879", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3790), true, "ArticleCategory", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3790), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "d22e5042338646ddbd190d372256c82b", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3809), true, "BookingItem", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3810), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "d47c072d9db04a02ab5bf6f5f12145e3", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3918), true, "CustomForm", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3918), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "d54793c063e143a8af713598c2a36a64", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3909), true, "GeneralSetting", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3909), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "fc1654455318420aad7295919e20b71c", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3834), true, "Tag", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3834), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "fc213fddede74fd8b643c1c2e002fafa", new DateTime(2025, 5, 5, 16, 12, 13, 598, DateTimeKind.Local).AddTicks(8697), true, "Overview", new DateTime(2025, 5, 5, 16, 12, 13, 599, DateTimeKind.Local).AddTicks(3063), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_PointHistories_UserZaloId",
                table: "PointHistories",
                column: "UserZaloId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PointHistories");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "5a884f2ab9a3476eae8434a7d1f3139f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "03356be917084ddd921b74965112322f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0dd2ecbe15e745529f0141a0e55b68a9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "11be882c4d65474c9729fc00e8d645a4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "22aca0c646c9492487765a29a7626153");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "326914d2edeb4db18e39571f774ac696");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "34c5535e977f40c789804f5401db8f6a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3c7b751825884733b430665209df8c8a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "47aceb4906dd411d93171fa4c4383d1f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4849ab59ab86442ab6861ae0d00e685e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "56f4c87e959b4b62a96e4da9a85a90e3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "57db3ce3d4d94431929854c37a3b8b0f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "698a05b1701649c6b220881e3423f513");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6c9a334343504a49aa3277d51857dc6b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "705c6ae7106f4e4f9b14ae29b5e298c2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8a3c0336749640fc974c3816d9a9b96c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8c67354059d74d049aa09eb8b14c7378");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8d8f056682fe45b3b8035dc587a8361c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8e98ed303fe74d10bb2c09e583def6c3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "90a5d4b3747b4d5bb5e46299e2265c2b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "93e97d2505294f71b7895d1bd200d3c8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9441f3a7fec24da996f5eeb0000f122f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ba71fb0cd8b490d8fff808a1e2ade53");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a06f0cae65df48128e6c08fe9fabae49");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b0217dcb83274e90a9d2711b09115513");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b1f2f584e7804e6f9d7ccdf535aed271");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b3b9033a748d4b19885711d5e6d7d0b6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c699db41fc784fa0ae17a7bc71e61cb8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c9cc66fc9ad249c5a835ad0ea6704b85");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cad9981232694d0b8279c3ef655e8879");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d22e5042338646ddbd190d372256c82b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d47c072d9db04a02ab5bf6f5f12145e3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d54793c063e143a8af713598c2a36a64");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fc1654455318420aad7295919e20b71c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fc213fddede74fd8b643c1c2e002fafa");

            migrationBuilder.CreateTable(
                name: "RewardPoints",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    CommissionPercentage = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    ConversionRatio = table.Column<decimal>(type: "decimal(18,5)", precision: 18, scale: 5, nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    MaxPointPerTxn = table.Column<int>(type: "int", nullable: false),
                    MemberShipRankId = table.Column<int>(type: "int", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Status = table.Column<bool>(type: "bit", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RewardPoints", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOrJwjglg13bvwdYniHYrjhnPxDRDhaXwXKYEfvQEEfEbWlxlKwPj8rBJHpGgS4RFg==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "10526c72f3fb4e63a966a179144eee08", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 5, 15, 23, 23, 174, DateTimeKind.Local).AddTicks(5388), "FeaturesButton", new DateTime(2025, 5, 5, 15, 23, 23, 174, DateTimeKind.Local).AddTicks(5391) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "020acf3144e9478fb4d68fc3b3947387", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6641), true, "History", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6641), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "09be8118dc1f4087b84eab54f123b92b", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6529), true, "AffiliateList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6530), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "160acbc7632547268fc109e01412a53e", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6655), true, "TemplateUidList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6656), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "21e669fdff0f4e4f81372074e9856261", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6660), true, "EnableFeatures", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6660), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "27c6b84bb0d24e6992afaabfad232280", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6637), true, "SurveyList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6637), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "28a3142ee56844aa9874bcc02aa78db3", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6647), true, "CampaignHistory", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6648), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "2a4964403faf40f1991ecf3dc9a983fd", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6523), true, "BranchList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6526), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "35206e626c4742e2970d0b42cfbb8019", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6572), true, "Promotion", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6573), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "3e1393a16f0f49b29bd5c522590b7bca", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6643), true, "GamePrize", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6643), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "5386ae44aba742c8ab1ae1764db44ea1", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6662), true, "MembershipExtendDefaults", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6662), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "5d78cd6539d64b0fa727d37942c96902", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6664), true, "ShippingFeeConfig", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6664), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "6ad2ccade3b846318a18db47e3f157a5", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6574), true, "VoucherList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6575), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "6ad7b189eb0f4389a07ed849d405d37a", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6559), true, "BookingItem", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6559), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "6aef1e3b637344adb2c51198ae51c67b", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6561), true, "BookingList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6561), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "7561de03066342afa39e3daee9fa13ba", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6551), true, "Brand", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6552), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "76939c19a4ce4e4baf4498716758884c", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6553), true, "ProductProperty", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6554), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "76bf383e145f4324be4605e1a23144cb", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6668), true, "CustomForm", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6668), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "846452aa43ea4e7d9e6a257743941ab1", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6653), true, "EventTemplateHistory", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6653), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "87ced92c7e8a49c3ac4882bb47e6f026", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6539), true, "ArticleCategory", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6539), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "911aa9cb6ce14f858fa18da3e2a665d6", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6555), true, "ProductList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6556), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "9ecd3ee8e34a4ff7b268a6918bf34b38", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6549), true, "Category", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6549), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "a215737b6ac7436c987294e76fb33396", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(609), true, "Overview", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(5815), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "b92256492b2b491d823f88d3f0162b3b", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6567), true, "InvoiceTemplate", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6568), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "bd2f0f8c82f2460986f84be386dc8daf", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6541), true, "ArticleList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6541), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "c7b8e5f42df34f728f8cc4bb629847fc", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6645), true, "CampaignList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6646), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "cb5c2eabae68448b9e67274c6e6910e1", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6565), true, "OrderList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6566), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "ce082790c573444ba16c6811e9b961a7", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6639), true, "GameList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6639), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "da03c980a317415a8d065daf7ea01879", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6634), true, "History", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6635), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "e3ac48c79d5c46c8b4251c1dc5c5e6cb", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6630), true, "Tag", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6630), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "ea09a7fb0d0c4d629e9eeed294abda21", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6658), true, "GeneralSetting", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6658), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "eeec3e2ee9974b85b6d1a5b586016b0c", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6570), true, "DiscountList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6570), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "f2000bea390b40a3b8235b4657d4c898", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6577), true, "MembershipList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6577), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "f9636e302e1341a697ad3e7eb601ba66", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6649), true, "EventTemplateList", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6650), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "fe765b727b6b4235ad04dd5b64a835d7", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6580), true, "Rank", new DateTime(2025, 5, 5, 15, 23, 23, 173, DateTimeKind.Local).AddTicks(6580), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" }
                });
        }
    }
}
