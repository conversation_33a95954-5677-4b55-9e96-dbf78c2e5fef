﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.FormCustoms
{
    public class CustomForm : BaseEntity
    {
        public bool IsActive { get; set; }

        public string? Name { get; set; } // tên form
        public string? Title { get; set; } // tiêu đề  
        public string? CampaignName { get; set; } // tên chiến dịch

        public string? TextColor { get; set; } // màu chữ checkbox
        public string? ButtonText { get; set; } // màu nút đăng kí
        public string? ButtonColor { get; set; } // màu nút đăng kí
        public string? BackgroundColor { get; set; } // màu nền form
    }
}
