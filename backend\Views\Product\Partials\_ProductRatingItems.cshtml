﻿@using Microsoft.AspNetCore.Html
@using MiniAppCore.Models.Responses.Products.Ratings
@model List<ProductReviewDetailResponse>

<style>
    .media-preview {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 6px;
        cursor: pointer;
    }

    .media-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

        .media-wrapper video {
            background: #000;
        }
</style>

@if (Model == null || !Model.Any())
{
    <div class="text-center text-muted my-4">Chưa có đánh giá nào cho sản phẩm này.</div>
}
else
{
    <div class="row">
        @foreach (var review in Model)
        {
            <div class="col-md-12 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-start mb-3">
                            <img src="@review.CustomerAvatar" class="rounded-circle me-3" style="width: 50px; height: 50px; object-fit: cover;" alt="Avatar" />
                            <div>
                                <h6 class="mb-0">@review.CustomerName</h6>
                                <small class="text-muted">@review.RatingDate.ToString("dd/MM/yyyy")</small>
                            </div>
                            <div class="ms-auto star-container" data-star="@review.Star"></div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-3 mb-3">
                            <a onclick="GetFormOrder('@review.OrderId')" class="btn btn-outline-primary btn-sm">
                                <i class="ri-file-text-line me-1"></i> Xem đơn hàng
                            </a>

                            <div class="form-check form-switch">
                                <input class="form-check-input toggle-show" type="checkbox"
                                       data-id="@review.RatingId"
                                       id="<EMAIL>"
                                       @(review.IsShow ? "checked" : "")>
                                <label class="form-check-label" for="<EMAIL>">Hiển thị (Mini App)</label>
                            </div>
                        </div>

                        @if (!string.IsNullOrWhiteSpace(review.Review))
                        {
                            <p class="card-text">@review.Review</p>
                        }

                        @if ((review.Images?.Any() ?? false) || (review.Videos?.Any() ?? false))
                        {
                            <div class="media-wrapper mb-2">
                                @foreach (var img in review.Images ?? new List<string>())
                                {
                                    <img src="@img" class="media-preview" data-type="image" data-src="@img" alt="Ảnh đánh giá" />
                                }

                                @foreach (var video in review.Videos ?? new List<string>())
                                {
                                    <video class="media-preview" data-type="video" data-src="@video" muted>
                                        <source src="@video" type="video/mp4" />
                                    </video>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    </div>
}

<script>
    @if (Model == null || !Model.Any() || ViewBag.HasMore == false)
    {
        @:$("#loadMoreContainer").hide();
    }
</script>