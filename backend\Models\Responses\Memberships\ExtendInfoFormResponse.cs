﻿namespace MiniAppCore.Models.Responses.Memberships
{
    public class ExtendInfoFormResponse
    {
        public string? Id { get; set; }

        public string? Type { get; set; }
        public string? Attribute { get; set; }
        public string? AttributeName { get; set; }
        public string? DefaultValue { get; set; }

        public bool IsActive { get; set; }
        public long? Min { get; set; } // chỉ dành cho type là number
        public long? Max { get; set; } // chỉ dành cho type là number 
        public short? DisplayOrder { get; set; }
        public List<KeyValuePair<string, string>> Options { get; set; } = new(); // tương đương với attribute value trong CustomFormAttribute
    }
}
