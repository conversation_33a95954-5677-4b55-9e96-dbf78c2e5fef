﻿namespace MiniAppCore.Models.Requests.Memberships
{
    public class CustomFormRequest
    {
        public bool IsActive { get; set; }

        public string? Name { get; set; } // tên form
        public string? Title { get; set; } // tiêu đề  
        public string? CampaignName { get; set; } // tên chiến dịch

        public string? TextColor { get; set; } // màu chữ checkbox
        public string? ButtonText { get; set; } // màu nút đăng kí
        public string? ButtonColor { get; set; } // màu nút đăng kí
        public string? BackgroundColor { get; set; } // màu nền form

        public List<CustomFormAttributeRequest>? CustomFormAttributes { get; set; } = new(); // danh sách thuộc tính form
    }

    public class CustomFormAttributeRequest
    {
        public bool IsActive { get; set; }
        public string? Type { get; set; } // kiểu thuộc tính
        public string? Attribute { get; set; } // key thuộc tính
        public string? AttributeLabel { get; set; } // label thuộc tính
        public string? AttributeValue { get; set; } // giá trị thuộc tính
        public string? DefaultValue { get; set; } // giá trị mặc định cho thuộc tính

        public long Min { get; set; }
        public long Max { get; set; }
        public short DislayOrder { get; set; } // thứ tự hiển thị
    }
}
