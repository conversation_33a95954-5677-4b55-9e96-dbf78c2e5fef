﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;
using Newtonsoft.Json;

namespace MiniAppCore.Entities.LuckyWheels
{
    public class GamePrize : BaseEntity
    {
        public string? Name { get; set; } // tên phần thưởng
        public string? Value { get; set; } // giá trị phần thưởng
        public string? Image { get; set; } // ảnh phần thưởng
        public string? Description { get; set; }

        public EPrizeType Type { get; set; } = EPrizeType.None; // loại phần thưởng
        public string? Metadata { get; set; } // thông tin mở rộng thêm của phần thưởng dành cho prize type khác nhau
        public string? ReferenceId { get; set; } // Id tham chiếu tới record trong table nếu như nó là phần thưởng hoặc sản phẩm

        // Helper method để lấy và lưu Metadata
        public T? GetMetadata<T>() where T : class
        {
            if (string.IsNullOrEmpty(Metadata))
                return null;
            return JsonConvert.DeserializeObject<T>(Metadata);
        }

        public void SetMetadata<T>(T data) where T : class
        {
            Metadata = JsonConvert.SerializeObject(data);
        }
    }
}
