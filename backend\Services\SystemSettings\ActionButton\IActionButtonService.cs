﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.ActionButtons;
using MiniAppCore.Models.Responses.ActionButtons;

namespace MiniAppCore.Services.SystemSettings.ActionButtonConfigs
{
    public interface IActionButtonService : IService<ActionButtonConfig>
    {
        Task<int> CreateAsync(ActionButtonRequest dto);
        Task<int> UpdateAsync(string id, ActionButtonRequest dto);

        Task<ActionButtonConfigResponse> GetActionButtonConfigsAsync();
        Task<PagedResult<ActionButtonConfig>> GetPage(RequestQuery query, string? category, string? type);
    }
}
