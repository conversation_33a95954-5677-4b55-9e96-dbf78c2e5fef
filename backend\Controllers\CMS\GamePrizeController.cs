﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.LuckyWheels;
using MiniAppCore.Services.Gamifications.GamePrizes;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class GamePrizeController(IGamePrizeService gamePrizeService) : Controller
    {
        private readonly string prefixViews = "/Views/Gamification/GamePrize";
        public IActionResult Index()
        {
            return View($"{prefixViews}/Index.cshtml");
        }

        [HttpGet("GamePrize/Create")]
        public IActionResult Create()
        {
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới phần thưởng";
            return PartialView($"{prefixViews}/_GamePrize.cshtml", new GamePrize() { Id = string.Empty });
        }

        [HttpGet("GamePrize/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            var gamePrize = await gamePrizeService.GetByIdAsync(id);
            if (gamePrize == null)
            {
                return RedirectToAction("Create");
            }
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật phần thưởng";
            gamePrize.Image = $"{Request.Scheme}://{HttpContext.Request.Host}/uploads/images/gamePrizes/{gamePrize.Image}";
            return PartialView($"{prefixViews}/_GamePrize.cshtml", gamePrize);
        }
    }
}
