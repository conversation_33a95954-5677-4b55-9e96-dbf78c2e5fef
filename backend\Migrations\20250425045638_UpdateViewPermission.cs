﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateViewPermission : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "81a373a311bb43478b33985e89769f44");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0028f9a96cd54eda90bdedbff373aee5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "02d319f8412847d1ae9c9454a2d6c676");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0544a4c5ef4c4564b7a85dbc790b545d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "084a69e40f394d0181e1b4b20da55515");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1625aaae5c784a9c86388a8648a5636b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2ecab6c982024583b5f49d9e725e38b9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3e5ac835f4144678867e04034a6f8698");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3ea6ad07d8c8429ca5009f20aabe614d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4597dc7121a048669425c54f446c97c0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4751924500f84bb8bba5c442bc685289");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6cb0151bfff44710919fbdfed3f8f872");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6fba4aeea1f04ab2a90b8e6d4e2019e8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "790a5c455ac747398bfb19224c8f5c7d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7d59c2351a08488ab34b720d5a633cda");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "861ca78ddbd74b78bad27cacc87350ca");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "95e5543aeec34151837c9209770ab5f1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9a0293dd0d7f4221b4eba5df48bd3dd9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ad42f43ac01f43b395c953884b638c8a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ad62819acb0049e7aa9b05902656d4ed");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b8b84e612f2d49cebc465300779d872b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b942e67f50ee4ae5a252f874689ae94f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b94444fa448244ad8ac5fe5a07b2b3dc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c3d3f16b87e64742bd03905fb5fff8ec");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c931bd9c8f9a4b218df2b0223b4c15cb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dd1cfc19786944d88e24ae7e2317cc6e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e764657be616457db25beac8f10a0e89");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "eafb0caa76004a308fa39442b4ef26ea");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f20c17f859884827ac26f8eb4e759dc3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f2717f81f90e48f8bdd7b511152e110c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f4d6ab54209a44ecbf0ae2dc8445ddbc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f662f57f6eb349b58ca581a0dfff361b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fd69a329ae1b44a680bd09a16c5cee4f");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOyIbIPUQi4ofLCpzPNJnheUu5Krd/V6o5Wu9bMPyzOaa8CvyS7OkDtxTXD0xT3qTQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "cb34d4acb3204360bbc74348a7a7cf8b", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 25, 11, 56, 37, 767, DateTimeKind.Local).AddTicks(5891), "FeaturesButton", new DateTime(2025, 4, 25, 11, 56, 37, 767, DateTimeKind.Local).AddTicks(5894) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "054dce5b09434525827fbe2f9420a7ba", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5300), true, "Rank", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5300), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "09f0951e93154299b476919079f7b22d", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5340), true, "MembershipExtendDefaults", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5341), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "1419ddc1f8054473ae97a9c8c29c7ffe", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5292), true, "Promotion", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5292), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "290254bccc3d4e56b2234841e64b2674", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5294), true, "VoucherList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5294), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "297609da074a45bb8cd3968f300e4322", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5342), true, "ShippingFeeConfig", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5343), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2ce6dcdfe7724536b11e6e817f7b3c50", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5209), true, "Category", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5209), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "345a9db27e8a4e399242682aebf7f813", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5296), true, "MembershipList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5297), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "411e0d12b6414191b88fdfe5f6e22a6d", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5325), true, "EventTemplateHistory", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5325), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "46fc240b4316437d9d41788cfc8d4bab", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5229), true, "InvoiceTemplate", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5230), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "4fef5958c0344a2da2ed90d2fd3f9c14", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5314), true, "History", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5314), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "51cf85cab43b4985ab3005de14f957de", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5327), true, "TemplateUidList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5327), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "526e84e3e8c14efc9a1497821205af77", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5332), true, "EnableFeatures", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5333), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "52acda108c2b4fd99057630f74aff450", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5318), true, "CampaignList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5318), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "5989602aa7824e1a9cbd7fd8b29a0b13", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5304), true, "History", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5304), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "5b4c31399d9a46f283748dd375bd0a1b", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5227), true, "OrderList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5228), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "60a911dbcfcc4fa99eb5f4fbc0cb4368", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5196), true, "AffiliateList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5196), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "7932b0e58e1e4a06ad5fbf1a05bc6ad3", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5302), true, "Tag", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5302), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "825674952682438da2ceae5029bd47e3", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5346), true, "CustomForm", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5346), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "9629fd142d95499897e2564098741eaa", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5199), true, "ArticleCategory", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5199), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "983d41643d054e779487e9980428db9f", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5306), true, "SurveyList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5307), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "abda13dc06aa456d9aedfb03d24e3898", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5316), true, "GamePrize", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5316), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "ad211b915cb14054833dcd1223e77e6e", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(187), true, "Overview", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(4491), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "adda35c5a1c64fc78936e0f61b19eb0b", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5223), true, "BookingItem", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5224), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "af8de9c520524a0482b697f3562db579", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5225), true, "BookingList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5226), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "c1662c365e374a659a41c1e94a1d759a", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5218), true, "ProductProperty", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5218), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c25823b327d0480a9e57a16862c157d9", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5308), true, "GameList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5309), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "c90355425ca042269b2dcad14b8143dc", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5201), true, "ArticleList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5201), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "d074441882ae42d58f34d4e65d0c456b", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5216), true, "Brand", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5216), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "dbfe84e07df74e62a2df43e641482bd0", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5321), true, "CampaignHistory", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5321), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "dd883a71c00846c3ad82a167c5c5872f", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5329), true, "GeneralSetting", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5329), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "df0326c805344153a375f5a76ed2c8aa", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5323), true, "EventTemplateList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5323), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "e5ddaebc2d5d424ba611f2b8b2532c79", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5220), true, "ProductList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5220), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "fce7b7a0e8204b218eef5588d4d34ea4", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5287), true, "DiscountList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5287), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "fd29d91bc5ae4255b317d9fbf968b7e8", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5191), true, "BranchList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5192), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "cb34d4acb3204360bbc74348a7a7cf8b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "054dce5b09434525827fbe2f9420a7ba");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "09f0951e93154299b476919079f7b22d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1419ddc1f8054473ae97a9c8c29c7ffe");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "290254bccc3d4e56b2234841e64b2674");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "297609da074a45bb8cd3968f300e4322");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2ce6dcdfe7724536b11e6e817f7b3c50");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "345a9db27e8a4e399242682aebf7f813");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "411e0d12b6414191b88fdfe5f6e22a6d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "46fc240b4316437d9d41788cfc8d4bab");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4fef5958c0344a2da2ed90d2fd3f9c14");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "51cf85cab43b4985ab3005de14f957de");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "526e84e3e8c14efc9a1497821205af77");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "52acda108c2b4fd99057630f74aff450");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5989602aa7824e1a9cbd7fd8b29a0b13");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5b4c31399d9a46f283748dd375bd0a1b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "60a911dbcfcc4fa99eb5f4fbc0cb4368");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7932b0e58e1e4a06ad5fbf1a05bc6ad3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "825674952682438da2ceae5029bd47e3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9629fd142d95499897e2564098741eaa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "983d41643d054e779487e9980428db9f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "abda13dc06aa456d9aedfb03d24e3898");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ad211b915cb14054833dcd1223e77e6e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "adda35c5a1c64fc78936e0f61b19eb0b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "af8de9c520524a0482b697f3562db579");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c1662c365e374a659a41c1e94a1d759a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c25823b327d0480a9e57a16862c157d9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c90355425ca042269b2dcad14b8143dc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d074441882ae42d58f34d4e65d0c456b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dbfe84e07df74e62a2df43e641482bd0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dd883a71c00846c3ad82a167c5c5872f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "df0326c805344153a375f5a76ed2c8aa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e5ddaebc2d5d424ba611f2b8b2532c79");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fce7b7a0e8204b218eef5588d4d34ea4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fd29d91bc5ae4255b317d9fbf968b7e8");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEM14RtKG2Ryd/InsqsoYhez7l8hq4QzDxUYc4pN3FNS4iaDX7VmqKToYWAfbiMYwHg==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "81a373a311bb43478b33985e89769f44", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 25, 10, 4, 49, 716, DateTimeKind.Local).AddTicks(1860), "FeaturesButton", new DateTime(2025, 4, 25, 10, 4, 49, 716, DateTimeKind.Local).AddTicks(1874) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "0028f9a96cd54eda90bdedbff373aee5", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6880), true, "AffiliateList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6881), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "02d319f8412847d1ae9c9454a2d6c676", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6960), true, "CustomForm", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6961), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "0544a4c5ef4c4564b7a85dbc790b545d", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6929), true, "Rank", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6930), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "084a69e40f394d0181e1b4b20da55515", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6958), true, "ShippingFeeConfig", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6959), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "1625aaae5c784a9c86388a8648a5636b", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6956), true, "MembershipExtendDefaults", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6957), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2ecab6c982024583b5f49d9e725e38b9", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6883), true, "ArticleCategory", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6883), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "3e5ac835f4144678867e04034a6f8698", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6931), true, "Tag", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6932), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "3ea6ad07d8c8429ca5009f20aabe614d", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6913), true, "OrderList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6914), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "4597dc7121a048669425c54f446c97c0", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6918), true, "DiscountList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6918), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "4751924500f84bb8bba5c442bc685289", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6906), true, "ProductList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6906), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "6cb0151bfff44710919fbdfed3f8f872", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6916), true, "InvoiceTemplate", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6916), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "6fba4aeea1f04ab2a90b8e6d4e2019e8", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6897), true, "Brand", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6897), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "790a5c455ac747398bfb19224c8f5c7d", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6875), true, "BranchList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6877), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "7d59c2351a08488ab34b720d5a633cda", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6909), true, "BookingItem", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6910), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "861ca78ddbd74b78bad27cacc87350ca", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6936), true, "History", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6936), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "95e5543aeec34151837c9209770ab5f1", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6911), true, "BookingList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6912), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "9a0293dd0d7f4221b4eba5df48bd3dd9", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6938), true, "GamePrize", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6938), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "ad42f43ac01f43b395c953884b638c8a", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6948), true, "EventTemplateHistory", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6948), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "ad62819acb0049e7aa9b05902656d4ed", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6942), true, "CampaignHistory", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6942), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "b8b84e612f2d49cebc465300779d872b", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6954), true, "EnableFeatures", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6955), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "b942e67f50ee4ae5a252f874689ae94f", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6920), true, "Promotion", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6920), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "b94444fa448244ad8ac5fe5a07b2b3dc", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(1564), true, "Overview", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6162), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "c3d3f16b87e64742bd03905fb5fff8ec", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6895), true, "Category", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6895), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c931bd9c8f9a4b218df2b0223b4c15cb", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6950), true, "TemplateUidList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6950), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "dd1cfc19786944d88e24ae7e2317cc6e", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6952), true, "GeneralSetting", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6952), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "e764657be616457db25beac8f10a0e89", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6926), true, "MembershipList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6927), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "eafb0caa76004a308fa39442b4ef26ea", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6946), true, "EventTemplateList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6946), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "f20c17f859884827ac26f8eb4e759dc3", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6940), true, "CampaignList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6940), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "f2717f81f90e48f8bdd7b511152e110c", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6899), true, "ProductProperty", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6899), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "f4d6ab54209a44ecbf0ae2dc8445ddbc", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6934), true, "GameList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6934), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "f662f57f6eb349b58ca581a0dfff361b", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6922), true, "VoucherList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6922), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "fd69a329ae1b44a680bd09a16c5cee4f", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6885), true, "ArticleList", new DateTime(2025, 4, 25, 10, 4, 49, 625, DateTimeKind.Local).AddTicks(6885), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" }
                });
        }
    }
}
