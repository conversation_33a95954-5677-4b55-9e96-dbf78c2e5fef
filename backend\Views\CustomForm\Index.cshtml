﻿@using MiniAppCore.Entities.Categories
@model (List<string> selectedCategories, IEnumerable<Category> allCategories)
@{
    var qrBaseUrl = ViewBag.QrCodeBaseUrl;
}
<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Danh sách form tùy chỉnh</h4>
                <p class="mb-0">
                    Quản lý các form tùy chỉnh cho website của bạn. <br>
                    Bạn có thể thêm, sửa, xóa form tùy chỉnh tại đây.
                </p>
            </div>
            <button onclick="GetFormCustom('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-customForm">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--<PERSON><PERSON> lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0; z-index: -1;"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-customForm" class="data-table table mb-0 tbl-server-info"> </table>
        </div>
    </div>
</div>

<div id="modal-customForm" class="modal fade" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        const QrCodeBaseUrl = "@qrBaseUrl";
        $(document).ready(function () {
            GetListCustomForm();
            $('#search').on('input', search);

            // Initialize select2 for the category home
            $('#categoryHome').select2({
                placeholder: 'Chọn danh mục hiển thị home',
                width: '100%'
            });
        });

        function GetListCustomForm() {
            table = new DataTable("#list-customForm", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetPage", "CustomForms")',
                        type: 'GET',
                        data: {
                            page: page,
                            pageSize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword,
                            type: "sanpham"
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: `<div class="d-flex align-items-center"><div>${item.name}</div></div>`,
                                2: item.title || '',
                                3: item.campaignName || '',
                                4: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                <a href="${QrCodeBaseUrl.replace('{formId}', item.id)}" target="_blank" class="badge bg-success" data-toggle="tooltip" data-placement="top" title="Mã QR">
                                                    <i class="ri-qr-code-line fs-6 mr-0"></i>
                                                </a>
                                                <a onclick="GetFormCustom('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                    <i class="ri-edit-line fs-6 mr-0"></i>
                                                </a>
                                                <a onclick="DeleteCustomForm('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                    <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                </a>
                                            </div>`,
                                id: item.id,
                                isActive: item.isActive ? `<div class="m-auto circle-active"><div>` : `<div class="m-auto circle-inactive"></div>`,
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên form", data: 1 },
                    { title: "Tiêu đề", data: 2 },
                    { title: "Tên chiến dịch", data: 3, className: 'text-center' },
                    { title: "Trạng thái", data: "isActive", className: 'text-center' },
                    { title: "Thao tác", data: 4, className: 'text-center' },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('light light-data');
        }

        function GetFormCustom(id) {
            const url = id ? `@Url.Action("Detail", "CustomForm")/${id}` : "@Url.Action("Create", "CustomForm")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-customForm").modal("toggle");
                }
            });
        }

        function DeleteCustomForm(id) {
            const url = `/api/CustomForms/${id}`
            DeleteItem(url);
        }
    </script>
}
