﻿using MiniAppCore.Base.Helpers;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Models.Requests.Orders
{
    public class AdminCreateOrderRequset
    {
        [Required(ErrorMessage = "Tên khách hàng không được để trống")]
        public string MembershipName { get; set; } = null!;

        private string _phoneNumber = null!;

        [Required(ErrorMessage = "Số điện thoại không được để trống")]
        [RegularExpression(@"^(0\d{9}|84\d{9})$", ErrorMessage = "Số điện thoại không hợp lệ")]
        public string PhoneNumber
        {
            get => _phoneNumber;
            set => _phoneNumber = PhoneNumberHandler.FixFormatPhoneNumber(value);
        }

        [Required(ErrorMessage = "Địa chỉ giao hàng không được để trống")]
        public string Address { get; set; } = null!;

        [Required(ErrorMessage = "Phương thức thanh toán không được để trống")]
        public string PaymentMethod { get; set; } = null!;

        public string? Note { get; set; }

        public string? BranchId { get; set; }
        public string? VoucherCode { get; set; } // Keep for backward compatibility

        public List<string> VoucherCodes { get; set; } = new List<string>(); // Add support for multiple vouchers

        [Required(ErrorMessage = "Đơn hàng cần có ít nhất một sản phẩm")]
        [MinLength(1, ErrorMessage = "Đơn hàng cần có ít nhất một sản phẩm")]
        public List<OrderDetailRequest> Products { get; set; } = new List<OrderDetailRequest>();
    }

    public class OrderItemRequest
    {
        [Required(ErrorMessage = "Mã sản phẩm không được để trống")]
        public string ProductId { get; set; } = null!;

        [Required(ErrorMessage = "Số lượng sản phẩm không được để trống")]
        [Range(1, int.MaxValue, ErrorMessage = "Số lượng sản phẩm phải lớn hơn 0")]
        public int Quantity { get; set; }
        public string? VariantId { get; set; }
        public string? Note { get; set; }
    }

    public class ApplyVoucherRequest
    {
        [Required(ErrorMessage = "Mã voucher không được để trống")]
        public string VoucherCode { get; set; } = null!;

        [Required(ErrorMessage = "Đơn hàng cần có ít nhất một sản phẩm")]
        public List<OrderItemRequest> Products { get; set; } = new List<OrderItemRequest>();
    }
}
