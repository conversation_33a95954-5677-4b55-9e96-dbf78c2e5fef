﻿namespace MiniAppCore.Models.DTOs.Promotions
{
    public class PromotionDTO
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public decimal TotalAmount { get; set; }
        public string? ExtraGift { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime StartDate { get; set; }
        public DateTime ExpiryDate { get; set; }

        public List<PromotionItemDTO> Gifts { get; set; } = new List<PromotionItemDTO>();
        public List<PromotionItemDTO> Products { get; set; } = new List<PromotionItemDTO>();
    }
}
