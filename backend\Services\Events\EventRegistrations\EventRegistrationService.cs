﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Events;
using MiniAppCore.Entities.Events.Sponsors;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events;
using MiniAppCore.Models.Responses.Events;
using MiniAppCore.Services.Events.Sponsors;
using MiniAppCore.Services.Memberships;

namespace MiniAppCore.Services.Events.EventRegistrations
{
    public class EventRegistrationService(IUnitOfWork unitOfWork, IMembershipService membershipService) : Service<EventRegistration>(unitOfWork), IEventRegistrationService
    {
        private readonly IRepository<Event> _eventRepo = unitOfWork.GetRepository<Event>();

        public async Task<PagedResult<EventRegistration>> GetPaged(RequestQuery query, string eventId)
        {
            var events = _repository.AsQueryable();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                var keyword = query.Keyword.ToLower();

                events = events.Where(x =>
                    (!string.IsNullOrEmpty(x.Name) && x.Name.ToLower().Contains(keyword)) ||
                    (!string.IsNullOrEmpty(x.Phone) && x.Phone.ToLower().Contains(keyword)) ||
                    (!string.IsNullOrEmpty(x.Email) && x.Email.ToLower().Contains(keyword))
                );
            }

            if (!string.IsNullOrEmpty(eventId))
            {
                events = events.Where(x => x.EventId == eventId);
            }

            var totalItems = await events.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);

            var listEvents = await events
                .OrderByDescending(t => t.CreatedDate)
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            return new PagedResult<EventRegistration>
            {
                Data = listEvents,
                TotalPages = totalPages
            };
        }

        public async Task<int> RegisterEvent(string eventId, string userZaloId, EventRegistrationDTO registration)
        {
            var ev = await _eventRepo.FindByIdAsync(eventId);
            if (ev == null) throw new CustomException(1, "Không tìm thấy sự kiện");

            var member = await membershipService.GetByUserZaloId(userZaloId);
            if (member == null) throw new CustomException(1, "Không tìm thấy người dùng");

            var existed = await _repository.AsQueryable()
                .FirstOrDefaultAsync(r => r.EventId == eventId && r.UserZaloId == userZaloId);

            if (existed != null)
            {
                if (existed.IsCheckIn == ECheckInStatus.CheckIn)
                    throw new CustomException(1, "Bạn đã check-in và không thể thay đổi đăng ký.");

                existed.IsCheckIn = ECheckInStatus.NotCheckIn;
                existed.Name = registration.Name ?? member.UserZaloName;
                existed.Phone = registration.Phone ?? member.PhoneNumber;
                existed.Email = registration.Email ?? "";
                existed.UpdatedDate = DateTime.Now;

                return await base.UpdateAsync(existed);
            }

            int attempts = 0; // Số lần thử trong vòng lặp while, tăng độ an toàn
            string uniqueCode;
            do
            {
                if (attempts++ > 100)
                    throw new CustomException(500, "Không thể tạo mã check-in duy nhất. Vui lòng thử lại.");

                uniqueCode = Tools.GenerateCode(8);
            }
            while (await _repository.AsQueryable().AnyAsync(r => r.CheckInCode == uniqueCode));

            return await base.CreateAsync(new EventRegistration
            {
                UserZaloId = userZaloId,
                EventId = eventId,
                Name = registration.Name ?? member.UserZaloName,
                Phone = registration.Phone ?? member.PhoneNumber,
                Email = registration.Email ?? "",
                IsCheckIn = ECheckInStatus.NotCheckIn,
                CheckInCode = uniqueCode
            });
        }

        public async Task<EventRegistrationResponse> CheckInAsync(string checkInCode, string eventId)
        {
            var registration = await _repository.AsQueryable()
                .FirstOrDefaultAsync(r => r.CheckInCode == checkInCode && r.EventId == eventId);

            if (registration == null)
                throw new CustomException(1, "Không tìm thấy đăng ký với mã check-in này.");

            if (registration.IsCheckIn == ECheckInStatus.CheckIn)
                throw new CustomException(1, "Người dùng đã check-in trước đó.");

            var member = await membershipService.GetByUserZaloId(registration.UserZaloId);
            if (member == null)
                throw new CustomException(1, "Người dùng chưa đăng ký thành viên hoặc tài khoản không còn khả dụng.");

            registration.IsCheckIn = ECheckInStatus.CheckIn;
            registration.UpdatedDate = DateTime.Now;

            await base.UpdateAsync(registration);

            return new EventRegistrationResponse
            {
                Name = member.DisplayName,
                Phone = member.PhoneNumber,
                Email = registration.Email ?? "",
                Avatar = member.Avatar,
                ZaloName = member.UserZaloName,
            };
        }

        public async Task<List<EventRegistrationResponse>> CheckInMultipleAsync(List<string> codes, string eventId)
        {
            if (codes == null || codes.Count == 0)
                throw new CustomException(1, "Danh sách mã check-in không hợp lệ.");

            // Lấy danh sách đăng ký hợp lệ theo eventId và checkInCode
            var registrations = await _repository.AsQueryable()
                .Where(r => r.EventId == eventId && codes.Contains(r.CheckInCode))
                .ToListAsync();

            if (!registrations.Any())
                throw new CustomException(1, "Không tìm thấy dữ liệu check-in hợp lệ.");

            // Lọc các UserZaloId hợp lệ
            var userIds = registrations.Select(r => r.UserZaloId).Distinct().ToList();
            var members = await membershipService.GetByUserZaloIds(userIds);

            var memberDict = members.ToDictionary(m => m.UserZaloId);

            var responses = new List<EventRegistrationResponse>();

            foreach (var reg in registrations)
            {
                // Bỏ qua nếu đã check-in hoặc bị hủy
                if (reg.IsCheckIn == ECheckInStatus.CheckIn || reg.IsCheckIn == ECheckInStatus.Cancel)
                    continue;

                // Kiểm tra thành viên còn tồn tại
                if (!memberDict.TryGetValue(reg.UserZaloId, out var member))
                    continue;

                reg.IsCheckIn = ECheckInStatus.CheckIn;
                reg.UpdatedDate = DateTime.Now;

                responses.Add(new EventRegistrationResponse
                {
                    Name = member.DisplayName,
                    Phone = member.PhoneNumber,
                    Email = reg.Email ?? "",
                    Avatar = member.Avatar,
                    ZaloName = member.UserZaloName,
                });
            }

            // Cập nhật tất cả 1 lần (tối ưu hơn so với update từng cái)
            await base.UpdateRangeAsync(registrations);

            return responses;
        }

        public async Task<int> Cancel(string eventId, string userZaloId)
        {
            var registration = await _repository.AsQueryable()
                .FirstOrDefaultAsync(r => r.EventId == eventId && r.UserZaloId == userZaloId);

            if (registration == null)
                throw new CustomException(1, "Bạn chưa đăng ký.");

            if (registration.IsCheckIn == ECheckInStatus.CheckIn)
                throw new CustomException(1, "Người dùng đã check-in trước đó.");

            registration.IsCheckIn = ECheckInStatus.Cancel;

            return await base.UpdateAsync(registration);
        }

        public async Task<int> CancelByCode(string eventId, string code)
        {
            var registration = await _repository.AsQueryable()
                .FirstOrDefaultAsync(r => r.EventId == eventId && r.CheckInCode == code);

            if (registration == null)
                throw new CustomException(1, "Người dùng chưa đăng ký.");

            if (registration.IsCheckIn == ECheckInStatus.CheckIn)
                throw new CustomException(1, "Người dùng đã check-in trước đó.");

            registration.IsCheckIn = ECheckInStatus.Cancel;

            return await base.UpdateAsync(registration);
        }

        public async Task<byte[]> ExportToExcelAsync(string eventId)
        {
            var eventInfo = await _eventRepo.FindByIdAsync(eventId)
                ?? throw new CustomException(1, "Không tìm thấy sự kiện.");

            var participants = await _repository.AsQueryable()
                .Where(x => x.EventId == eventId)
                .OrderByDescending(x => x.CreatedDate)
                .ToListAsync();

            var data = new Dictionary<string, List<string>>
            {
                { "STT", new List<string>() },
                { "Họ và Tên", new List<string>() },
                { "Số điện thoại", new List<string>() },
                { "Email", new List<string>() },
                { "Mã tham dự", new List<string>() },
                { "Trạng thái Check-in", new List<string>() },
                { "Ngày đăng ký", new List<string>() },
                { "Ngày Check-in", new List<string>() }
            };

            for (int i = 0; i < participants.Count; i++)
            {
                var p = participants[i];

                data["STT"].Add((i + 1).ToString());
                data["Họ và Tên"].Add(p.Name ?? "-");
                data["Số điện thoại"].Add(p.Phone ?? "-");
                data["Email"].Add(p.Email ?? "-");
                data["Mã tham dự"].Add(p.CheckInCode ?? "-");
                data["Trạng thái Check-in"].Add(p.IsCheckIn switch
                {
                    ECheckInStatus.CheckIn => "Đã check-in",
                    ECheckInStatus.Cancel => "Hủy tham dự",
                    ECheckInStatus.NotCheckIn => "Chưa check-in",
                    _ => "-"
                });
                data["Ngày đăng ký"].Add(p.CreatedDate.ToString("dd/MM/yyyy HH:mm"));
                data["Ngày Check-in"].Add(
                    p.IsCheckIn == ECheckInStatus.CheckIn
                        ? (p.UpdatedDate.ToString("dd/MM/yyyy HH:mm") ?? "")
                        : ""
                );
            }

            var sheetTitle = $"event_{DateTime.Now:yyyyMMdd_HHmm}";
            return await ExportHandler.ExportData(sheetTitle, data);
        }
    }
}
