﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Events.Sponsors;
using MiniAppCore.Entities.Events.SponsorshipTiers;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events.Sponsors;
using MiniAppCore.Services.Events.SponsorshipTiers;

namespace MiniAppCore.Services.Events.Sponsors
{
    public class SponsorService(IUnitOfWork unitOfWork, IWebHostEnvironment env, IHttpContextAccessor httpContextAccessor, IMapper mapper) : Service<Sponsor>(unitOfWork), ISponsorService
    {
        private readonly string _uploadPath = "uploads/images/sponsors";
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public async Task<PagedResult<Sponsor>> GetPaged(RequestQuery query, short activeStatus)
        {
            var sponsors = _repository.AsQueryable();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                sponsors = sponsors.Where(x => x.SponsorName!.Contains(query.Keyword) || x.Introduction!.Contains(query.Keyword));
            }

            if (!IsAdmin)
            {
                sponsors = sponsors.Where(x => x.IsActive);
            }
            else
            {
                if (activeStatus == 1) sponsors = sponsors.Where(x => x.IsActive);
                else if (activeStatus == 2) sponsors = sponsors.Where(x => !x.IsActive);
            }

            var totalItems = await sponsors.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);

            var listSponsors = await sponsors
                .OrderByDescending(t => t.CreatedDate)
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            foreach (var tier in listSponsors)
            {
                if (!string.IsNullOrEmpty(tier.Image) && !tier.Image.StartsWith("http"))
                {
                    tier.Image = $"{hostUrl}/{_uploadPath}/{tier.Image}";
                }
            }

            return new PagedResult<Sponsor>
            {
                Data = listSponsors,
                TotalPages = totalPages
            };
        }

        public async Task<Sponsor?> GetById(string id)
        {
            var sponsor = await _repository.AsQueryable()
                .Where(x => x.Id == id)
                .FirstOrDefaultAsync();
            if (sponsor != null && !string.IsNullOrEmpty(sponsor.Image) && !sponsor.Image.StartsWith("http"))
            {
                sponsor.Image = $"{hostUrl}/{_uploadPath}/{sponsor.Image}";
            }
            return sponsor;
        }

        public async Task<int> CreateSponsorAsync(SponsorDTO dto)
        {
            var sponsor = mapper.Map<Sponsor>(dto);
            sponsor.Image = await HandleImageUploadAsync(dto.Image);
            return await base.CreateAsync(sponsor);
        }

        public async Task<int> UpdateSponsorAsync(string id, SponsorDTO dto)
        {
            var sponsor = await _repository.FindByIdAsync(id);
            if (sponsor == null) throw new CustomException(1, "Nhà tài trợ không khả dụng!");

            mapper.Map(dto, sponsor);
            sponsor.Image = await HandleImageUploadAsync(dto.Image, sponsor.Image);
            return await base.UpdateAsync(sponsor);
        }

        public async Task<List<Sponsor>> GetListByIdsAsync(IEnumerable<string> ids)
        {
            return await _repository.AsQueryable()
                .Where(s => ids.Contains(s.Id))
                .ToListAsync();
        }

        public async Task<int> DeleteSponsorAsync(string id)
        {
            var sponsor = await _repository.FindByIdAsync(id);
            if (sponsor == null) throw new CustomException(1, "Nhà tài trợ không tồn tại!");

            if (!string.IsNullOrEmpty(sponsor.Image))
                RemoveOldImage(sponsor.Image);

            return await base.DeleteAsync(sponsor);
        }

        #region Data Process

        private async Task<string> ProcessUpload(List<IFormFile> files)
        {
            if (files == null || files.Count == 0)
                return string.Empty;

            var savePath = Path.Combine(env.WebRootPath, _uploadPath);
            var fileResult = await Helpers.FileHandler.SaveFiles(files, savePath);
            return string.Join(",", fileResult);
        }

        private void RemoveOldImage(string listImage)
        {
            var images = listImage.Split(",")
                .Select(x => Path.Combine(env.WebRootPath, _uploadPath, x))
                .ToList();

            Helpers.FileHandler.RemoveFiles(images);
        }

        private async Task<string?> HandleImageUploadAsync(IFormFile? imageFile, string? oldImage = null)
        {
            if (imageFile == null) return oldImage;

            if (!string.IsNullOrEmpty(oldImage))
                RemoveOldImage(oldImage);

            return await ProcessUpload(new List<IFormFile> { imageFile });
        }

        #endregion
    }

}
