﻿using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Articles;
using MiniAppCore.Exceptions;

namespace MiniAppCore.Services.Articles.ArticleCategories
{
    public class ArticleCategoryService(IUnitOfWork unitOfWork) : Service<ArticleCategory>(unitOfWork), IArticleCategoryService
    {
        public async Task<int> UpdateAsync(string id, ArticleCategory model)
        {
            var existing = await GetByIdAsync(id);
            if (existing == null)
            {
                throw new CustomException(200, "Cannot found article catetory");
            }

            existing.Name = model.Name;
            return await base.UpdateAsync(existing);
        }
    }
}
