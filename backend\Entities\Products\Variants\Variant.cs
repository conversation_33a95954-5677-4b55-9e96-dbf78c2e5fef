﻿using MiniAppCore.Enums;

namespace MiniAppCore.Entities.Products.Variants
{
    public class Variant
    {
        public string Id { get; set; } = Guid.NewGuid().ToString("N");
        public decimal Price { get; set; }
        public required string ProductId { get; set; }

        //public bool IsActive { get; set; } = true;
        public long Quantity { get; set; }
        public EProduct Status { get; set; }

        public string? DiscountId { get; set; }
        public string? PromotionId { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
