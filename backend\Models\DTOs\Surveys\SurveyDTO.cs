﻿namespace MiniAppCore.Models.DTOs.Surveys
{
    public class SurveyDTO
    {
        public string? Id { get; set; }
        public string? Title { get; set; }

        public short Status { get; set; }
        public short DisplayOrder { get; set; }

        public bool IsDisplay { get; set; }

        public DateTime EndDate { get; set; }
        public DateTime StartedDate { get; set; }

        public List<SurveySectionDTO> Sections { get; set; } = new List<SurveySectionDTO>();
    }
}
