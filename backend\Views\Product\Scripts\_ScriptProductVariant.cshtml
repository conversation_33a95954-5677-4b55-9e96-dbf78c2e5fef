﻿<script>
    function GetProperties(selector, queryselector, selectedProperties, options) {
        $(selector).select2({
            placeholder: "<PERSON>ọn thuộc tính",
            allowClear: true,
            // dropdownParent: options?.dropdownParent || null,
            ajax: {
                url: '@Url.Action("GetPageProperty", "Products")',
                type: 'GET',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        keyword: params.term,
                        page: params.page || 1,
                        pageSize: 30
                    };
                },
                processResults: function (res, params) {
                    let data = res.data || [];

                    if (!params.page || params.page === 1) {
                        // data.unshift({ id: "all", name: "Tất cả danh mục"});
                    }

                    return {
                        results: data.map(item => ({ id: item.id, text: item.name })),
                        pagination: { more: data.length >= 30 }
                    };
                },
                cache: true
            }
        });

        console.log(selectedProperties);

        // Set giá trị mặc định nếu có danh mục đã chọn
        if (selectedProperties && selectedProperties.length > 0) {
            let selectedValues = selectedProperties.map(item => item.propertyId); // Lấy danh sách ID
            let selectedData = selectedProperties.map(item => ({
                id: item.propertyId,
                text: item.optionName
            }));

            // Thêm các tùy chọn đã chọn vào Select2 nếu chúng chưa có trong danh sách
            selectedData.forEach(item => {
                let newOption = new Option(item.text, item.id, true, true);
                $(selector).append(newOption);
            });

            // Gán giá trị đã chọn
            $(selector).val(selectedValues).trigger("change");
        }

        // Lắng nghe sự kiện thay đổi
        $(selector).on("change", async function (e) {
            let selected = $(this).val(); // Danh sách ID đang chọn
            let currentCards = $("#selectedPropertiesContainer").children().map(function () {
                return $(this).data("id");
            }).get();

            // Thêm thẻ mới nếu có thuộc tính mới được chọn
            for (let id of selected) {
                if (!currentCards.includes(id)) {
                    await RenderPropertyCard(id);
                }
            }

            // Xoá thẻ nếu thuộc tính bị bỏ chọn
            for (let id of currentCards) {
                if (!selected.includes(id)) {
                    $(`#property-card-${id}`).remove();
                }
            }

            GenerateVariants();
        });
    }

    function RenderPropertyCard(propertyId, selectedValueIds = []) {
        const url = `/api/products/property/${propertyId}`;

        $.ajax({
            url: url,
            method: 'GET',
            success: function (res) {
                const property = res.data;

                const optionsHtml = property.options.map(opt => {
                    const selected = selectedValueIds.includes(opt.propertyValueId) ? 'selected' : '';
                    return `<option value="${opt.propertyValueId}" ${selected}>${opt.value}</option>`;
                }).join("");

                const cardHtml = `
                <div class="col-md-6 mb-3" id="property-card-${property.id}" data-id="${property.id}">
                    <div class="card">
                        <div class="card-header font-weight-bold">${property.name}</div>
                        <div class="card-body">
                            <select class="form-control property-option-select" multiple id="property-options-${property.id}" data-name="${property.name}">
                                ${optionsHtml}
                            </select>
                        </div>
                    </div>
                </div>`;

                $("#selectedPropertiesContainer").append(cardHtml);

                $(`#property-options-${property.id}`).select2({
                    placeholder: `Chọn phân loại áp dụng`
                });

                $(`#property-options-${property.id}`).on("change", function () {
                    GenerateVariants();
                });

                $(`#property-options-${property.id}`).val(selectedValueIds).trigger("change");
            },
            error: function (xhr, status, error) {
                console.error("Lỗi khi gọi GetProperty:", error);
            }
        });
    }

    function GenerateVariants() {
        $(".variant-card").each(function () {
            const key = $(this).data("variant-key");
            oldVariantData[key] = {
                variantId: $(this).data("variant-id") || null,
                price: $(this).find(".variant-price").val(),
                stock: $(this).find(".variant-stock").val()
            };
        });

        let propertyOptions = [];

        $(".property-option-select").each(function () {
            const propertyName = $(this).data("name");
            const options = $(this).val();

            if (options && options.length > 0) {
                const labels = $(this).find("option:selected").map(function () {
                    return $(this).text();
                }).get();

                propertyOptions.push({
                    propertyName,
                    optionIds: options,
                    optionLabels: labels
                });
            }
        });

        if (propertyOptions.length === 0) {
            $("#variantContainer").html(`<p>Không có biến thể cho sản phẩm này.</p>`);
            return;
        }

        let generatedVariants = [];

        if (propertyOptions.length === 1) {
            generatedVariants = propertyOptions[0].optionIds.map((id, idx) => ([{
                propertyName: propertyOptions[0].propertyName,
                value: propertyOptions[0].optionLabels[idx],
                valueId: id
            }]));
        } else {
            const mapped = propertyOptions.map(p =>
                p.optionIds.map((id, idx) => ({
                    propertyName: p.propertyName,
                    value: p.optionLabels[idx],
                    valueId: id
                }))
            );
            generatedVariants = CartesianProduct(mapped);
        }

        RenderVariantCards(generatedVariants);
    }

    function RenderVariantCards(variants) {
        if (variants.length === 0) {
            $("#variantContainer").html("<p>Không có biến thể cho sản phẩm này.</p>");
            return;
        }

        let html = variants.map((variant, index) => {
            const title = variant.map(v => `${v.propertyName}: ${v.value}`).join(", ");
            const variantKey = GenerateVariantKey(variant.map(v => v.valueId));

            const saved = oldVariantData[variantKey] || { price: "", stock: "", variantId: "", status: "" };
            return `<div class="card mb-2 variant-card" data-variant-key="${variantKey}" data-variant-id="${saved.variantId || ''}">
            <div class="card-body row align-items-center">
                <div class="col-md-6 font-weight-bold">${title}</div>
                <div class="col-md-3">
                    <input type="text" class="form-control variant-price" oninput="InputValidator.currency(this)" placeholder="Giá" min="0" value="${saved.price}" />
                </div>
                <div class="col-md-3">
                    <select class="form-control variant-stock">
                        <option value="1" ${saved.status == 1 ? 'selected' : ''}>Còn hàng</option>
                        <option value="2" ${saved.status == 2 ? 'selected' : ''}>Hết hàng</option>
                        <option value="3" ${saved.status == 3 ? 'selected' : ''}>Ngưng kinh doanh</option>
                    </select>
                </div>
            </div>
        </div>`;
        }).join("");

        $("#variantContainer").html(html);
    }

    function FillVariantData(variants) {
        console.log(variants);
        variants.forEach(v => {
            const key = GenerateVariantKey(v.propertyValueIds);
            const card = $(`.variant-card[data-variant-key="${key}"]`);

            card.find(".variant-price").val(Number(v.price).toLocaleString('vi-VN'));
            card.find(".variant-price").val(Number(v.originalPrice).toLocaleString('vi-VN'));
            card.find(".variant-stock").val(v.status);
            card.attr("data-variant-id", v.variantId);
        });
    }

    function GenerateVariantKey(propertyValueIds) {
        return propertyValueIds.slice().sort().join("-");
    }

    function CartesianProduct(arr) {
        return arr.reduce((a, b) =>
            a.flatMap(d => b.map(e => [].concat(d, e)))
        );
    }
</script>