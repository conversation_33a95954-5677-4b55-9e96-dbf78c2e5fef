﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Memberships
{
    public class MembershipExtendDefault : BaseEntity
    {
        public string? Content { get; set; } // chứa giá trị mặc định cho cấu hình
        public string? DefaultContent { get; set; } // chứa giá trị mặc định cho cấu hình

        public string? Type { get; set; }
        public string? Attribute { get; set; } // trường thuộc tính
        public string? AttributeName { get; set; } // Tên của thuộc tính

        public long Min { get; set; }
        public long Max { get; set; }

        public bool IsActive { get; set; }
    }
}
