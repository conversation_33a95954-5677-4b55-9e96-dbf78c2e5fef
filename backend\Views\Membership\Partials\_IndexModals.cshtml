<!-- Loading Spinner <PERSON>dal (Backdrop) -->
<div class="modal" id="loadingModal" tabindex="-1" role="dialog" aria-labelledby="loadingModalLabel" aria-hidden="true"
    style="z-index: 1060; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5);">
    <div id="modal-loading-content" class="modal-dialog modal-xl" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
        <div class="d-flex justify-content-center align-items-center">
            <div class="spinner-border text-primary" role="status" style="width: 5rem; height: 5rem;">
            </div>
        </div>
    </div>
</div>

<!--Model membership-->
<div id="modal-membership" class="modal fade" tabindex="-1" data-bs-backdrop="static" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<!-- Mo<PERSON> Nhập Thành Viên -->
<div id="modal-import-membership" class="modal fade" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-modal="true" role="dialog">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Nhập danh sách khách hàng từ Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p><strong>Hướng dẫn tải lên:</strong></p>
                <p>Vui lòng chọn tệp Excel có các cột sau:</p>
                <ul>
                    <li><strong>STT</strong>: Số thứ tự.</li>
                    <li><strong>Tên</strong>: Họ và tên khách hàng.</li>
                    <li><strong>Số Điện Thoại</strong>: Số điện thoại của khách hàng (định dạng số, không chứa ký tự đặc biệt).</li>
                    <li><strong>Sinh Nhật</strong>: Ngày sinh của khách hàng (có thể nhập <code>dd/MM/yyyy</code> hoặc <code>d/M/yyyy</code>).</li>
                    <li><strong>Nghề Nghiệp</strong>: Công việc hiện tại của khách hàng.</li>
                    <li><strong>Hạng</strong>: Hạng khách hàng (VD: Silver, Gold, Platinum).</li>
                    <li><strong>Điểm</strong>: Tổng điểm tích lũy của khách hàng.</li>
                    <li><strong>Địa Chỉ</strong>: Địa chỉ nơi ở của khách hàng.</li>
                    <li><strong>Ghi Chú</strong>: Thông tin bổ sung (tùy chọn, có thể để trống).</li>
                </ul>

                <p><strong>Lưu ý:</strong></p>
                <ul>
                    <li><strong>Hạng</strong>: Hệ thống sẽ tự động chuyển đổi giá trị này sang ID nội bộ.</li>
                    <li><strong>Sinh Nhật</strong>: Có thể nhập theo định dạng <code>dd/MM/yyyy</code> hoặc <code>d/M/yyyy</code>.</li>
                    <li><strong>Số Điện Thoại</strong>: Phải là số hợp lệ, không chứa ký tự đặc biệt.</li>
                    <li>Nếu cột nào không có dữ liệu, hệ thống sẽ tự động điền giá trị mặc định.</li>
                </ul>

                <p><strong>Ví dụ về định dạng tệp Excel:</strong></p>
                <pre>
                    STT  | Tên         | Số Điện Thoại | Sinh Nhật  | Nghề Nghiệp | Hạng   | Điểm  | Địa Chỉ | Ghi Chú
                     1   | Nguyễn An   | 0987654321    | 1/1/1990   | Kế toán     | Gold   |  500  | Hà Nội  | Khách VIP
                     2   | Lê Bình     | 0912345678    | 15/5/1985  | Bác sĩ      | Silver |  300  | TP HCM  | Mới đăng ký
                </pre>
                <form id="import-membership-form" method="post" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="import-membership-input">Chọn tệp Excel</label>
                        <input type="file" class="form-control" id="import-membership-input" name="file" accept=".xls,.xlsx" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="ImportMemberships()">Tải lên</button>
            </div>
        </div>
    </div>
</div>

<!--Model cskh-->
<div id="modal-noti" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

<!--Model referral-->
<div id="modal-ref" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-dialog-centered">
        <div class="modal-content modal-xl">
            <div class="modal-header">
                <h5 class="modal-title">Người giới thiệu</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <ul id="tree-container" class="container-fluid"></ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!--Model Error-->
<div id="error-modal" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="errorModalLabel">Lỗi khi tải lên</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Thông báo lỗi sẽ được thêm vào đây -->
                <div id="error-messages">
                    <!-- Ví dụ thông báo lỗi: -->
                    <!-- <ul><li>Lỗi ở dòng 5: Số điện thoại không hợp lệ</li><li>Lỗi ở dòng 8: Số điện thoại đã tồn tại</li></ul> -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Export Excel Modal -->
<div class="modal fade" id="modal-excel-export" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xuất danh sách khách hàng <small>(Theo thời gian đăng ký)</small></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Ngày bắt đầu</label>
                    <input id="startExcel" class="form-control" type="date" />
                </div>
                <div class="form-group">
                    <label>Ngày kết thúc</label>
                    <input id="endExcel" class="form-control" type="date" />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="HandleExportData()">Xuất</button>
            </div>
        </div>
    </div>
</div>
