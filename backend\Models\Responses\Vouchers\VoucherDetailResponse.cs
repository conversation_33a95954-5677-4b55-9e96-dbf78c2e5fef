﻿namespace MiniAppCore.Models.Responses.Vouchers
{
    public class VoucherDetailResponse : VoucherResponse
    {
        public bool IsActive { get; set; }
        public bool IsExchange { get; set; }
        public bool IsAllProducts { get; set; }

        public long Quantity { get; set; }
        public long RankingPoint { get; set; }
        public long ExchangeTimes { get; set; }

        public short ApplyType { get; set; }
        public short VoucherType { get; set; }
        public short DiscountType { get; set; }
        public List<string> Products { get; set; } = new List<string>();
    }
}
