﻿@model MiniAppCore.Models.DTOs.Surveys.SurveyQuestionDTO

@{
    // Đ<PERSON>i với Likert, chúng ta cần phân tích câu trả lời để tạo các câu hỏi con
    var subQuestions = Model.LikertQuestions;
}

<div class="sub-questions-container">
    @if (!subQuestions.Any())
    {
        <div class="alert alert-info"><PERSON><PERSON> lòng thêm câu hỏi con cho thang đo Likert</div>
    }
    else
    {
        foreach (var subQuestion in subQuestions)
        {
            <div class="likert-question-item" data-sub-question-id="@subQuestion.QuestionLikertId">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">@subQuestion.QuestionLikertTitle</h6>
                    <button class="btn btn-sm btn-outline-danger" onclick="SurveyModule.removeSubQuestion('@Model.Id', '@subQuestion.QuestionLikertId')">
                        <i class="ri-delete-bin-line fs-6"></i>
                    </button>
                </div>
                <div class="likert-scale-container">
                    <div class="likert-scale d-flex justify-content-between align-items-center">
                        @* @for (int i = 1; i <= 5; i++) *@
                        @* { *@
                        @*     <div class="likert-option text-center"> *@
                        @*         <div class="form-check"> *@
                        @*             <input class="form-check-input" type="radio" disabled> *@
                        @*             <label class="form-check-label">@i</label> *@
                        @*         </div> *@
                        @*     </div> *@
                        @* } *@

                        @foreach(var answer in Model.Answers)
                        {
                            <div class="likert-option text-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" disabled data-likertAnswer-id="@answer.Id" data-likertAnswer-key="@answer.Key">
                                    <label class="form-check-label">@answer.Key</label>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        }
    }
</div>
<div class="form-group mt-3">
    <label class="form-label">Thêm câu hỏi con mới</label>
    <div class="input-group">
        <input type="text" class="form-control new-sub-question" placeholder="Nhập câu hỏi con">
        <button class="btn btn-primary" onclick="SurveyModule.addSubQuestion('@Model.Id')">
            <i class="ri-add-line me-1"></i> Thêm
        </button>
    </div>
</div>