﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Entities.Products;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Models.Responses.Products;

namespace MiniAppCore.Services.Offers.Discounts
{
    public interface IDiscountService : IService<Discount>
    {
        Task<int> CreateAsync(DiscountRequest request);
        Task<int> UpdateAsync(string id, DiscountRequest request);

        Task<Discount?> GetDiscountByProductIdAsync(string productId);
        Task<IEnumerable<ProductItemResponse>> GetDiscountItems(string id);
        Task<(IEnumerable<Discount>, IEnumerable<DiscountItem>)> GetDiscountsByProductIdsAsync(IEnumerable<string> productIds);

        //decimal CalculateDiscountedPrice(Product product, IEnumerable<DiscountItem> discountItems, IEnumerable<Discount> discounts);

        // (discountType, discountValue)
        (short, decimal) CalculateDiscountedPrice(Product product, IEnumerable<DiscountItem> discountItems, IEnumerable<Discount> discounts);

        (short, decimal) CalculateDiscountedPrice(string productId, decimal originalPrice, IEnumerable<DiscountItem> discountItems, IEnumerable<Discount> discounts);
    }
}