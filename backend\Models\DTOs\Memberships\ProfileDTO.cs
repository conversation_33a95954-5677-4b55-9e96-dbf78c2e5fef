﻿namespace MiniAppCore.Models.DTOs
{
    public class ProfileDTO
    {
        //public required string DisplayName {  get; set; }
        //public required string PhoneNumber { get; set; }    
        //public string? Email { get; set; }
        //public string? Address { get; set; }
        //public int Point { get; set; }
        //public byte Rank { get; set; }  
        //public int Level { get;set; }

        public byte Gender { get; set; } = 0;
        public DateTime? Dob { get; set; }
        public string? Job { get; set; }
        public string? Address { get; set; }

    }
}
