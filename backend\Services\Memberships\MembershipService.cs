﻿using AutoMapper;
using Hangfire;
using MediatR;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.FormCustoms;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Settings;
using MiniAppCore.Exceptions;
using MiniAppCore.Features.EventTrigger;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Memberships;
using MiniAppCore.Models.Responses.Memberships;
using OfficeOpenXml;
using System.Globalization;
using System.Reflection;

namespace MiniAppCore.Services.Memberships
{
    public class MembershipService(IUnitOfWork unitOfWork, IMapper mapper, IMediator mediator, IConfiguration configuration, IHttpContextAccessor httpContextAccessor) : Service<Membership>(unitOfWork), IMembershipService
    {
        private readonly IRepository<Rank> _rankRepo = unitOfWork.GetRepository<Rank>();
        private readonly IRepository<MembershipTag> _membershipTagRep = unitOfWork.GetRepository<MembershipTag>();

        private readonly IRepository<CustomFormAttribute> _customFormAttributeRep = unitOfWork.GetRepository<CustomFormAttribute>();

        private readonly IRepository<MembershipExtend> _membershipExRep = unitOfWork.GetRepository<MembershipExtend>();
        private readonly IRepository<MembershipExtendDefault> _membershipExDfRep = unitOfWork.GetRepository<MembershipExtendDefault>();

        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        #region "Get Memberships

        public async Task<PagedResult<Membership>> GetPage(UserQueryParameters queryParams)
        {
            var memberships = _repository.AsQueryable();
            if (!string.IsNullOrEmpty(queryParams.Keyword))
            {
                string keyword = queryParams.Keyword;
                // Check if the keyword is a valid phone number (you can adjust the regex for your phone number format)
                if (PhoneNumberHandler.IsValidPhoneNumber(keyword))
                {
                    // If it's a valid phone number, format it
                    keyword = PhoneNumberHandler.FixFormatPhoneNumber(keyword);
                }
                memberships = memberships.Where(m => m.UserZaloName.Contains(keyword) || m.PhoneNumber.Contains(keyword));
            }

            if (queryParams.StartDate.HasValue)
            {
                memberships = memberships.Where(p => p.CreatedDate >= queryParams.StartDate);
            }

            if (queryParams.EndDate.HasValue)
            {
                memberships = memberships.Where(p => p.CreatedDate <= queryParams.EndDate);
            }

            var totalItems = await memberships.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)queryParams.PageSize);
            var items = await memberships
                .OrderBy(x => x.CreatedDate)
                .Skip(queryParams.Skip)
                .Take(queryParams.PageSize)
                .ToListAsync();

            return new PagedResult<Membership>()
            {
                Data = items,
                TotalPages = totalPages,
            };
        }

        public async Task<Membership?> GetByUserZaloId(string userZaloId)
        {
            return await _repository.AsQueryable().AsNoTracking().Where(m => m.UserZaloId == userZaloId).FirstOrDefaultAsync();
        }

        public async Task<Membership?> GetMember(string phoneNumber, string userZaloId, bool isFromLogin = false)
        {
            var membership = await _repository.AsQueryable()
                                              .AsNoTracking()
                                              .FirstOrDefaultAsync(m => m.PhoneNumber == phoneNumber || m.UserZaloId == userZaloId);
            return membership;
        }

        public async Task<Membership?> GetMember(string phoneNumber, string userZaloId, string? userZaloIdByOa)
        {
            return await _repository.AsQueryable().AsNoTracking().FirstOrDefaultAsync(m => m.PhoneNumber == phoneNumber || m.UserZaloId == userZaloId);
        }

        public async Task<Membership?> GetByPhoneNumber(string phoneNumber)
        {
            return await _repository.AsQueryable().FirstOrDefaultAsync(m => m.PhoneNumber == phoneNumber);
        }

        public async Task<Membership?> GetByReferralCode(string referralCode)
        {
            return await _repository.AsQueryable().FirstOrDefaultAsync(m => m.ReferralCode == referralCode);
        }

        public async Task<MembershipResponse?> GetMembershipProfile(string userZaloId)
        {
            var membership = await GetByUserZaloId(userZaloId);
            if (membership == null) return null;

            var rank = await _rankRepo.AsQueryable().AsNoTracking().FirstOrDefaultAsync(x => x.Id == membership.RankingId);
            if (rank != null && !string.IsNullOrEmpty(rank.Image))
            {
                rank.Image = $"{hostUrl}/uploads/images/ranks/{rank.Image}";
            }

            // lấy thông tin mặc định cấu hình
            var membershipExtendDefault = await _membershipExDfRep.AsQueryable().Where(x => x.IsActive).ToListAsync();

            // lấy thông tin nhập vào
            var membershipExtend = await _membershipExRep.AsQueryable()
                .Where(x => x.UserZaloId == userZaloId)
                .ToListAsync();

            var membershipAttribute = membershipExtend.Select(x => x.Attribute);

            var membershipResponse = new MembershipResponse()
            {
                UserZaloId = membership.UserZaloId,
                UserZaloName = membership.UserZaloName,
                DisplayName = membership.UserZaloName,
                PhoneNumber = membership.PhoneNumber,
                DateOfBirth = membership.DateOfBirth ?? DateTime.Now,
                SpinPoint = membership.SpinPoint,
                UsingPoint = membership.UsingPoint,
                RankingPoint = membership.RankingPoint,
                RankingId = membership.RankingId,
                TotalCommission = 0,
                Rank = rank,
                InputFields = membershipExtendDefault.Select(defaultConfig =>
                {
                    // Xác định Min, Max, Type, AttributeName từ nguồn phù hợp
                    var min = defaultConfig?.Min ?? 0;
                    var max = defaultConfig?.Max ?? 0;
                    var type = defaultConfig?.Type ?? string.Empty;
                    var attributeName = defaultConfig?.AttributeName ?? string.Empty;

                    // Xử lý Options nếu là kiểu "option"
                    var options = (defaultConfig != null && !string.IsNullOrEmpty(defaultConfig.Content) &&
                                   !string.IsNullOrEmpty(type) &&
                                   type.Equals("option", StringComparison.OrdinalIgnoreCase))
                        ? defaultConfig.Content.Split(',')
                                   .Select(opt => new KeyValuePair<string, string>(opt.Trim(), opt.Trim()))
                                   .ToList()
                        : new List<KeyValuePair<string, string>>();

                    return new ExtendInfoFormResponseWithValue
                    {
                        Id = defaultConfig?.Id,
                        Attribute = defaultConfig?.Attribute,
                        Type = type,
                        AttributeName = attributeName,
                        Min = min,
                        Max = max,
                        Options = options,
                        Value = membershipExtend.FirstOrDefault(me => me.Attribute == defaultConfig?.Attribute)?.Content ?? string.Empty // hoặc chọn từ defaultConfig/ customConfig nếu cần default
                    };
                }).ToList()
            };
            return membershipResponse;
        }

        #endregion

        public async Task<int> RegisterMembership(RegisterMember info, string? source, string? referralCode)
        {
            var member = await GetMember(info.PhoneNumber, info.UserZaloId);
            var isNewMembeship = member == null;
            if (member == null)
            {
                member = CreateNewMember(info, source);
                member.UserZaloId = info.UserZaloId;
                member.UserZaloIdByOA = info.UserZaloIdByOA;

                // add referral
                if (!string.IsNullOrEmpty(referralCode))
                {
                    var referrer = await GetByReferralCode(referralCode);
                    member.ReferrerId = referrer?.UserZaloId;
                }

                // add rank default
                var defaultRank = await _rankRepo.AsQueryable().FirstOrDefaultAsync(x => x.IsDefault);
                if (defaultRank != null)
                {
                    member.RankingId = defaultRank.Id;
                }
                _repository.Add(member);
            }
            else
            {
                UpdateExistingMember(member, info);
                _repository.Update(member);
            }

            if (info.ExtendInfo.Any())
            {
                var extendIds = info.ExtendInfo.Select(x => x.Id).ToList();

                // xử lý khi có custom form
                if (!string.IsNullOrEmpty(info.FormId))
                {
                    // Lấy danh sách cấu hình mở rộng
                    var extendDefaults = await _customFormAttributeRep.AsQueryable()
                        .Where(x => extendIds.Contains(x.Id))
                        .ToListAsync();

                    // Lấy danh sách Extend đã có của user
                    var existingExtends = await _membershipExRep.AsQueryable()
                        .Where(x => x.UserZaloId == info.UserZaloId)
                        .ToListAsync();

                    foreach (var extend in info.ExtendInfo)
                    {
                        var config = extendDefaults.FirstOrDefault(x => x.Id == extend.Id);
                        if (config != null)
                        {
                            var existing = existingExtends.FirstOrDefault(x => x.Attribute == config.Attribute);
                            if (existing != null)
                            {
                                // Nếu đã tồn tại, cập nhật nội dung
                                existing.Content = extend.Content;
                                _membershipExRep.Update(existing);
                            }
                            else
                            {
                                // Nếu chưa tồn tại, thêm mới
                                var extendEntity = new MembershipExtend
                                {
                                    Content = extend.Content,
                                    CustomFormId = info.FormId,
                                    Attribute = config.Attribute,
                                    UserZaloId = info.UserZaloId
                                };

                                _membershipExRep.Add(extendEntity);
                            }
                        }
                    }
                }
                else
                {
                    // Lấy danh sách cấu hình mở rộng
                    var extendDefaults = await _membershipExDfRep.AsQueryable()
                        .Where(x => extendIds.Contains(x.Id))
                        .ToListAsync();

                    // Lấy danh sách Extend đã có của user
                    var existingExtends = await _membershipExRep.AsQueryable()
                        .Where(x => x.UserZaloId == info.UserZaloId)
                        .ToListAsync();

                    foreach (var extend in info.ExtendInfo)
                    {
                        var config = extendDefaults.FirstOrDefault(x => x.Id == extend.Id);
                        if (config != null)
                        {
                            var existing = existingExtends.FirstOrDefault(x => x.Attribute == config.Attribute);
                            if (existing != null)
                            {
                                // Nếu đã tồn tại, cập nhật nội dung
                                existing.Content = extend.Content;
                                _membershipExRep.Update(existing);
                            }
                            else
                            {
                                // Nếu chưa tồn tại, thêm mới
                                var extendEntity = new MembershipExtend
                                {
                                    Content = extend.Content,
                                    Attribute = config.Attribute,
                                    UserZaloId = info.UserZaloId
                                };

                                _membershipExRep.Add(extendEntity);
                            }
                        }
                    }
                }
            }

            var result = await unitOfWork.SaveChangesAsync();

            if (isNewMembeship && result > 0)
            {
                // thực thi job cho gửi tin UID
                var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 1;
                var emitEventArgs = new EmitEventArgs
                {
                    EventName = "Membership.Register",
                    TriggerPhoneNumber = info.PhoneNumber,
                    TriggerZaloIdByOA = info.UserZaloIdByOA,
                    Payload = new
                    {
                        info.PhoneNumber,
                        info.UserZaloId,
                        info.UserZaloName,
                        info.FormId,
                        info.ExtendInfo,
                        member.ReferralCode,
                        member.ReferrerId,
                        member.RankingId,
                    }
                };
                BackgroundJob.Schedule<IMediator>(x => x.Send(emitEventArgs, CancellationToken.None), TimeSpan.FromSeconds(delayInSeconds));
            }

            return result;
        }

        private Membership CreateNewMember(RegisterMember info, string? source)
        {
            var newMember = mapper.Map<Membership>(info);

            newMember.Source = source;
            newMember.DisplayName = info.UserZaloName;
            newMember.ReferralCode = Tools.GenerateCode(8);
            newMember.CreatedDate = DateTime.Now;
            newMember.UpdatedDate = DateTime.Now;

            return newMember;
        }

        private void UpdateExistingMember(Membership member, RegisterMember info)
        {
            mapper.Map(info, member);
            member.UpdatedDate = DateTime.Now;
        }

        public async Task<int> UpdateAsync(string id, MembershipDTO dto)
        {
            var membership = await GetByIdAsync(id);
            if (membership == null)
            {
                return 0;
            }
            var isChangeRankingPoint = membership.RankingPoint != dto.RankingPoint;
            mapper.Map(dto, membership);
            await base.UpdateAsync(membership);

            if (!string.IsNullOrEmpty(dto.RankId))
            {
                membership.RankingId = dto.RankId;
            }
            membership.UpdatedDate = DateTime.Now;

            if (dto.ListMembershipExtend?.Count > 0)
            {
                foreach (var item in dto.ListMembershipExtend)
                {
                    var mbex = await _membershipExRep.FindByIdAsync(item.Id);
                    if (mbex != null)
                    {
                        if (mbex.Content != item.Content && mbex.Attribute == item.Attribute)
                        {
                            mbex.Content = item.Content;
                            mbex.Attribute = item.Attribute;
                            mbex.UpdatedDate = DateTime.Now;
                            _membershipExRep.Update(mbex);
                        }
                    }
                    else
                    {
                        var mbexNew = new MembershipExtend()
                        {
                            Content = item.Content,
                            Attribute = item.Attribute,
                            UpdatedDate = DateTime.Now,
                            CreatedDate = DateTime.Now,
                            UserZaloId = membership.UserZaloId,
                        };
                        _membershipExRep.Add(mbexNew);
                    }
                }
                //Xóa các phần tử bị loại bỏ
                var listMbExId = dto.ListMembershipExtend.Select(x => x.Id).ToList();
                var listMbExRemove = _membershipExRep.AsQueryable().Where(x => !listMbExId.Contains(x.Id) && x.UserZaloId == membership.UserZaloId).ToList();
                _membershipExRep.DeleteRange(listMbExRemove);
            }

            // Xóa các Tag cũ trực tiếp trên database
            await _membershipTagRep.AsQueryable()
                .Where(x => x.UserZaloId == membership.UserZaloId)
                .ExecuteDeleteAsync();

            if (dto.TagIds.Any())
            {
                // thêm cái mới
                var tags = dto.TagIds.Select(x => new MembershipTag
                {
                    TagId = x,
                    UserZaloId = membership.UserZaloId,
                }).ToList();
                _membershipTagRep.AddRange(tags);
            }

            // cập nhật hạng khi điểm thay đổi
            if (isChangeRankingPoint)
            {
                var newRank = await _rankRepo.AsQueryable()
                    .Where(r => r.RankingPoint <= dto.RankingPoint)
                    .OrderByDescending(r => r.RankingPoint)
                    .FirstOrDefaultAsync();
                if (newRank != null)
                {
                    membership.RankingId = newRank.Id;
                }
            }

            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> UpdateProfileAsync(string userZaloId, UpdateProfileRequest dto)
        {
            var membership = await GetByUserZaloId(userZaloId);
            if (membership == null)
            {
                return 0;
            }

            membership.DisplayName = dto.DisplayName;
            membership.DateOfBirth = dto.DateOfBirth;
            membership.UpdatedDate = DateTime.Now;

            await base.UpdateAsync(membership);
            // cập nhật thông tin membership extend
            var attributeKeys = dto.UpdateFields.Keys;
            var attributeValues = await _membershipExRep.AsQueryable()
                                            .Where(x => x.UserZaloId == userZaloId &&
                                                        !string.IsNullOrEmpty(x.Attribute) &&
                                                        attributeKeys.Contains(x.Attribute)).ToListAsync();

            foreach (var item in dto.UpdateFields)
            {
                var existAttribute = attributeValues.FirstOrDefault(x => x.Attribute == item.Key);
                if (existAttribute != null)
                {
                    existAttribute.Content = item.Value;
                    existAttribute.UpdatedDate = DateTime.Now;
                    _membershipExRep.Update(existAttribute);
                }
                else
                {
                    _membershipExRep.Add(new MembershipExtend()
                    {
                        UserZaloId = userZaloId,
                        Attribute = item.Key,
                        Content = item.Value,
                    });
                }
            }
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<List<string>> GetTagsMembership(string userZaloId)
        {
            return await _membershipTagRep.AsQueryable().Where(x => x.UserZaloId == userZaloId).Select(x => x.TagId!).ToListAsync();
        }

        public async Task<IEnumerable<Membership>> GetByUserZaloIds(List<string> userZaloIds)
        {
            return await _repository.AsQueryable().Where(m => userZaloIds.Contains(m.UserZaloId) || userZaloIds.Contains(m.Id)).ToListAsync();
        }

        public async Task<List<MembershipExtendDefault>> GetListMembershipExtendDefault()
        {
            return await _membershipExDfRep.AsQueryable().ToListAsync();
        }

        public async Task<List<MembershipExtend>> GetListMembershipExtendByUserZaloId(string userZaloId)
        {
            return await _membershipExRep.AsQueryable().Where(x => x.UserZaloId == userZaloId).ToListAsync();
        }

        public async Task<List<ExtendInfoFormResponse>> GetFormExtendInfo()
        {
            var memberhsipExtendDefault = await _membershipExDfRep.AsQueryable().Where(x => x.IsActive).ToListAsync();
            var response = memberhsipExtendDefault.Select(x => new ExtendInfoFormResponse()
            {
                Id = x.Id,
                Attribute = x.Attribute,
                Type = x.Type,
                AttributeName = x.AttributeName,
                Min = x.Min,
                Max = x.Max,
                Options = !string.IsNullOrEmpty(x.Content) && !string.IsNullOrEmpty(x.Type) && x.Type.Equals("option", StringComparison.OrdinalIgnoreCase)
                ? x.Content.Split(",")
                   .Select(opt => new KeyValuePair<string, string>(opt.Trim(), opt.Trim()))
                   .ToList()
                : new List<KeyValuePair<string, string>>()
            }).ToList();
            return response;
        }

        public async Task<PagedResult<PointHistoryResponse>> GetPointHistoriesByUserZaloIdAsync(string userZaloId, RequestQuery query)
        {
            var messageTemplate = configuration["MiniAppSettings:PointHistoryMessageTemplate"] ?? string.Empty;

            var pointHistoryQuery = unitOfWork.GetRepository<PointHistory>()
                                        .AsQueryable()
                                        .Where(x => x.UserZaloId == userZaloId && !string.IsNullOrEmpty(x.Type) && x.Type.Contains("PLUS"));

            var totalItems = await pointHistoryQuery.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);

            var pointHistories = await pointHistoryQuery
                .OrderByDescending(x => x.CreatedDate)
                .Skip(query.Skip)
                .Take(query.PageSize)
                .ToListAsync();

            var pointHistoryResponses = pointHistories.Select(ph => new PointHistoryResponse
            {
                Amount = ph.Amount,
                Message = messageTemplate
                            .Replace("{referenceId}", ph.ReferenceId ?? string.Empty)
                            .Replace("{amount}", ph.Amount.ToString()),
                CreatedDate = ph.CreatedDate
            }).ToList();

            return new PagedResult<PointHistoryResponse>()
            {
                Data = pointHistoryResponses,
                TotalPages = totalPages
            };
        }

        #region Import & Export membership 

        public async Task<byte[]> ExportMemberships(DateTime? startDate, DateTime? endDate)
        {
            // Dictionary ánh xạ tiếng Anh sang tiếng Việt
            var mappingDictHeaders = new Dictionary<string, string>
            {
                { "Avatar", "Ảnh đại diện" },
                { "MiniAppId", "Mã ứng dụng nhỏ" },
                { "UserZaloId", "ID Zalo khách hàng" },
                { "PhoneNumber", "Số điện thoại" },
                { "UserZaloName", "Tên Zalo khách hàng" },
                { "Point", "Điểm" },
                { "SpinPoint", "Điểm quay" },
                { "UsingPoint", "Điểm sử dụng" },
                { "RankingId", "ID xếp hạng" },
                { "RankingPoint", "Điểm xếp hạng" },
                { "Job", "Nghề nghiệp" },
                { "Source", "Nguồn" },
                { "Address", "Địa chỉ" },
                { "ReferralCode", "Mã giới thiệu" },
                { "UserZaloIdByOA", "ID Zalo theo OA" },
                { "DateOfBirth", "Ngày sinh" }
            };

            // Tạo một dictionary để lưu trữ dữ liệu theo cột
            var data = new Dictionary<string, List<string>>()
            {
                { "Avatar", new List<string>() },
                { "MiniAppId", new List<string>() },
                { "UserZaloId", new List<string>() },
                { "PhoneNumber", new List<string>() },
                { "UserZaloName", new List<string>() },
                { "Point", new List<string>() },
                { "SpinPoint", new List<string>() },
                { "UsingPoint", new List<string>() },
                { "RankingId", new List<string>() },
                { "RankingPoint", new List<string>() },
                { "Job", new List<string>() },
                { "Source", new List<string>() },
                { "Address", new List<string>() },
                { "ReferralCode", new List<string>() },
                { "UserZaloIdByOA", new List<string>() },
                { "DateOfBirth", new List<string>() }
            };

            // Duyệt qua các đối tượng Membership và điền dữ liệu vào dictionary
            var memberships = await _repository.AsQueryable().Where(x =>
                (startDate == null || x.CreatedDate >= startDate) &&
                (endDate == null || x.CreatedDate <= endDate)
            ).ToListAsync();

            // Sử dụng Reflection để lấy các thuộc tính của class Membership
            var membershipProperties = typeof(Membership).GetProperties(BindingFlags.Public | BindingFlags.Instance);
            foreach (var property in membershipProperties)
            {
                // Nếu property có giá trị ánh xạ trong mappingDictHeaders
                if (mappingDictHeaders.ContainsKey(property.Name))
                {
                    // Tạo danh sách cho mỗi property
                    data[property.Name] = new List<string>();

                    // Duyệt qua danh sách memberships và lấy giá trị của property
                    foreach (var membership in memberships)
                    {
                        var value = property.GetValue(membership);

                        // Xử lý null hoặc kiểu dữ liệu cần format (vd: DateTime, long)
                        if (value == null)
                        {
                            data[property.Name].Add(string.Empty);
                        }
                        else if (value is DateTime dateTimeValue)
                        {
                            data[property.Name].Add(dateTimeValue.ToString("yyyy-MM-dd"));
                        }
                        else if (value is long || value is int)
                        {
                            data[property.Name].Add(value.ToString());
                        }
                        else
                        {
                            data[property.Name].Add(value.ToString());
                        }
                    }
                }
            }

            // Ánh xạ tiêu đề cột từ tiếng Anh sang tiếng Việt
            var mappedData = new Dictionary<string, List<string>>();
            foreach (var key in data.Keys)
            {
                if (mappingDictHeaders.ContainsKey(key))
                {
                    var mappedHeader = mappingDictHeaders[key];
                    mappedData[mappedHeader] = data[key];
                }
            }

            return await ExportHandler.ExportData("Thành viên", mappedData);
        }

        public async Task<List<string>> ImportMembershipList(IFormFile file)
        {
            var errorMessages = new List<string>();

            if (file == null || file.Length == 0)
            {
                throw new CustomException(200, "Tệp không hợp lệ.");
            }

            var memberships = new List<Membership>();
            var existingPhoneNumbers = await GetExistingPhoneNumbersAsync();

            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);
            stream.Position = 0; // Reset stream position to beginning

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using var package = new ExcelPackage(stream);
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            if (worksheet == null)
            {
                throw new CustomException(200, "Tệp Excel không có dữ liệu.");
            }

            // Kiểm tra định dạng cột
            ValidateExcelColumns(worksheet);

            int rowCount = worksheet.Dimension.Rows;

            for (int row = 2; row <= rowCount; row++) // Bỏ qua header
            {
                try
                {
                    var membership = CreateMembershipFromRow(worksheet, row);

                    // Xử lý số điện thoại
                    string originalPhoneNumber = membership.PhoneNumber;

                    // Xử lý đặc biệt cho số điện thoại bắt đầu bằng 84 trước khi gọi FixFormatPhoneNumber
                    if (membership.PhoneNumber.StartsWith("84") && membership.PhoneNumber.Length >= 10)
                    {
                        membership.PhoneNumber = "0" + membership.PhoneNumber.Substring(2);
                    }
                    else
                    {
                        membership.PhoneNumber = PhoneNumberHandler.FixFormatPhoneNumber(membership.PhoneNumber);
                    }

                    // Kiểm tra số điện thoại hợp lệ
                    if (!IsValidPhoneNumber(membership.PhoneNumber))
                    {
                        errorMessages.Add($"Dòng {row}: Số điện thoại '{originalPhoneNumber}' không hợp lệ.");
                        continue;
                    }

                    // Kiểm tra trùng lặp số điện thoại
                    if (existingPhoneNumbers.Contains(membership.PhoneNumber) ||
                        memberships.Any(x => x.PhoneNumber == membership.PhoneNumber))
                    {
                        errorMessages.Add($"Dòng {row}: Số điện thoại '{membership.PhoneNumber}' đã tồn tại trong hệ thống.");
                        continue;
                    }

                    // Tạo mã giới thiệu và các dữ liệu mặc định
                    membership.ReferralCode = Tools.GenerateCode(8);
                    membership.CreatedDate = DateTime.Now;
                    membership.UpdatedDate = DateTime.Now;

                    memberships.Add(membership);
                }
                catch (Exception ex)
                {
                    errorMessages.Add($"Dòng {row}: Lỗi xử lý - {ex.Message}");
                }
            }

            // Lưu dữ liệu vào database nếu có
            if (memberships.Any())
            {
                _repository.AddRange(memberships);
                await unitOfWork.SaveChangesAsync();
            }

            return errorMessages;
        }

        // ✅ Validate thứ tự cột
        private void ValidateExcelColumns(ExcelWorksheet worksheet)
        {
            var expectedColumns = new List<string>
            {
                // "STT",
                "Tên", "Số Điện Thoại", "Sinh Nhật",
                "Nghề Nghiệp", "Hạng", "Điểm", "Địa Chỉ", "Ghi Chú"
            };

            // Kiểm tra số lượng cột
            if (worksheet.Dimension.Columns < expectedColumns.Count)
            {
                throw new CustomException(400, $"File Excel cần có ít nhất {expectedColumns.Count} cột. Vui lòng kiểm tra lại.");
            }

            // Kiểm tra tiêu đề cột
            for (int col = 1; col <= expectedColumns.Count; col++)
            {
                string actualColumn = worksheet.Cells[1, col].Text?.Trim() ?? string.Empty;
                if (!string.Equals(actualColumn, expectedColumns[col - 1], StringComparison.OrdinalIgnoreCase))
                {
                    throw new CustomException(400,
                        $"Cột {col} phải là '{expectedColumns[col - 1]}', nhưng nhận được '{actualColumn}'. " +
                        $"Vui lòng kiểm tra lại định dạng file Excel.");
                }
            }
        }

        private Membership CreateMembershipFromRow(ExcelWorksheet worksheet, int row)
        {
            var membership = new Membership
            {
                Id = Guid.NewGuid().ToString(),
                Avatar = string.Empty,
                MiniAppId = string.Empty,
                UserZaloId = string.Empty,
                UserZaloName = worksheet.Cells[row, 2].Text?.Trim() ?? string.Empty,
                PhoneNumber = worksheet.Cells[row, 3].Text?.Trim() ?? string.Empty,
                DateOfBirth = ParseDate(worksheet.Cells[row, 4].Text?.Trim()), // Xử lý ngày sinh
                Job = worksheet.Cells[row, 5].Text?.Trim() ?? string.Empty,
                Address = worksheet.Cells[row, 8].Text?.Trim() ?? string.Empty,
                Source = "Import Excel",
                ReferralCode = Tools.GenerateCode(8),
                CreatedDate = DateTime.Now,
                UpdatedDate = DateTime.Now
            };

            // Xử lý điểm
            if (long.TryParse(worksheet.Cells[row, 7].Text, out var points))
            {
                membership.RankingPoint = points;
            }

            // Xử lý hạng thành viên (cần cải thiện với mapping từ tên hạng sang ID)
            string rankName = worksheet.Cells[row, 6].Text?.Trim() ?? string.Empty;
            if (!string.IsNullOrEmpty(rankName))
            {
                // Xử lý rank ở đây - có thể query từ database hoặc cache
            }

            return membership;
        }

        // ✅ Kiểm tra số điện thoại hợp lệ
        private bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber)) return false;

            // Regex kiểm tra số điện thoại Việt Nam (bắt đầu bằng 0, tiếp theo là 9 số)
            return PhoneNumberHandler.GetIdTelco(phoneNumber) != 0;
            //var regex = new Regex(@"^0[0-9]{9}$");
            //return regex.IsMatch(phoneNumber) && phoneNumber.Length == 10;
        }

        // ✅ Lấy tất cả số điện thoại đã tồn tại trong hệ thống
        private async Task<HashSet<string>> GetExistingPhoneNumbersAsync()
        {
            // Lấy tất cả số điện thoại trong hệ thống để kiểm tra
            var existingMembers = await _repository.AsQueryable()
                                                    .Select(m => m.PhoneNumber)
                                                    .ToListAsync();
            // Trả về HashSet chứa tất cả số điện thoại
            return new HashSet<string>(existingMembers);
        }

        // ✅ Xử lý ngày tháng
        private DateTime? ParseDate(string? dateStr)
        {
            if (string.IsNullOrWhiteSpace(dateStr))
                return null;

            // Hỗ trợ nhiều định dạng ngày tháng phổ biến
            string[] formats = {
                "d/M/yyyy", "dd/MM/yyyy",
                "d-M-yyyy", "dd-MM-yyyy",
                "yyyy/MM/dd", "yyyy-MM-dd"
            };

            if (DateTime.TryParseExact(dateStr, formats, CultureInfo.InvariantCulture,
                                      DateTimeStyles.None, out var date))
            {
                return date;
            }

            // Thử parse bằng cách thông thường
            if (DateTime.TryParse(dateStr, out date))
            {
                return date;
            }

            return null;
        }

        #endregion
    }
}
