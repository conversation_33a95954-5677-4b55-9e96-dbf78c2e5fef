﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Products.Variants;
using MiniAppCore.Models.DTOs.Properties;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Models.Responses.Products;

namespace MiniAppCore.Services.Products.Variants
{
    public interface IProductPropertyService : IService<Property>
    {
        Task<int> CreateAsync(PropertyDTO model);
        Task<int> CreateProductVariants(string productId, List<ProductVariantRequest> variants);

        Task<PropertyDTO> GetDetailProperty(string propertyId);

        Task<Dictionary<string, decimal>> GetPriceByVariantIds(IEnumerable<string> variantIds);
        Task<Dictionary<string, List<string>>> GetPropertyValuesByVariantIds(IEnumerable<string> variantIds);
        Task<(List<ProductVariantResponse>, List<ProductVariantDetailResponse>)> GetProductVariants(string productId);

        Task<int> UpdateAsync(string id, PropertyDTO model);
        Task<int> UpdateProductVariants(string productId, List<ProductVariantRequest> variants);

        Task<int> DeleteProductVariants(string productId);
    }
}