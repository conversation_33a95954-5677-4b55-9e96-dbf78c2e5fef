﻿@model MiniAppCore.Entities.Commons.Tag
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="modal-wrapper d-flex">
            <div class="col-md-12">
                <div class="form-group">
                    <label>Tên <span style="color:red">*</span></label>
                    <input id="name" value="@Model.Name" class="form-control" />
                </div>
                <div class="form-group">
                    <label>Mô tả</label>
                    <input id="description" value="@Model.Description" class="form-control" />
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
    </div>
</div>