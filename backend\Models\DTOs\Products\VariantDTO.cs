﻿using MiniAppCore.Enums;
using MiniAppCore.Models.DTOs.Properties;

namespace MiniAppCore.Models.DTOs.Products
{
    public class VariantDTO
    {
        public string? VariantId { get; set; }
        //public bool IsActive { get; set; } = true;
        public short Stock { get; set; } = (short)StockStatus.IN_STOCK;
        public decimal Price { get; set; }
        public List<PropertyDTO>? Properties { get; set; }
    }
}
