﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Branches;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Branches;

namespace MiniAppCore.Services.Branches
{
    public interface IBranchService : IService<Branch>
    {
        Task<int> CreateAsync(BranchRequest dto);
        Task<int> UpdateAsync(string id, BranchRequest dto);
        Task<PagedResult<Branch>> GetPage(BranchQueryParameters query);

        Task<List<Branch>> GetBranchesByIds(List<string?> branchIds);
        Task<List<Branch>> GetBranchesByProductId(string productId);
        Task<int> AssignProductToBranches(string productId, List<string> branchIds);
        Task<int> RemoveAllBranchesFromProduct(string productId); // xóa tất cả sản phẩm thuộc 
        Task<int> RemoveProductFromBranches(string productId, List<string> branchIds);
    }
}