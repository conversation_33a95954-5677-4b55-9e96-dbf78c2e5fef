﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Articles;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.ActionButtons;
using MiniAppCore.Models.Responses.ActionButtons;
using MiniAppCore.Services.SystemSettings.ActionButtonConfigs;

namespace MiniAppCore.Services.SystemSettings.ActionButton
{
    public class ActionButtonService(IUnitOfWork unitOfWork, IWebHostEnvironment env, IHttpContextAccessor httpContextAccessor)
        : OrderedEntityService<ActionButtonConfig>(unitOfWork), IActionButtonService
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";
        protected override string OrderColumnName => "SortOrder";
        public async Task<ActionButtonConfigResponse> GetActionButtonConfigsAsync()
        {
            // Lấy tất cả button và nhóm theo Category (shortcut, button, floatingBubble)
            var allButtons = await _repository.AsQueryable()
                .Where(x => x.IsActive)
                .OrderBy(b => b.SortOrder)
                .ToListAsync();

            // Nhóm theo category để tạo response phù hợp
            var response = new ActionButtonConfigResponse
            {
                ListShortcut = allButtons
                    .Where(b => b.Category?.ToLower() == "shortcut")
                    .Select(b => new ButtonItemResponse
                    {
                        Title = b.Title ?? "",
                        Image = !string.IsNullOrEmpty(b.Image) && !b.Image.StartsWith("http")
                            ? $"{hostUrl}/uploads/images/actionButtons/{b.Image}"
                            : b.Image ?? "",
                        Type = b.Type ?? "",
                        Desc = b.Description ?? ""
                    }).ToList(),

                ListButton = allButtons
                    .Where(b => b.Category?.ToLower() == "button")
                    .Select(b => new ButtonItemResponse
                    {
                        Title = b.Title ?? "",
                        Image = !string.IsNullOrEmpty(b.Image) && !b.Image.StartsWith("http")
                            ? $"{hostUrl}/uploads/images/actionButtons/{b.Image}"
                            : b.Image ?? "",
                        Type = b.Type ?? "",
                        Desc = b.Description ?? ""
                    }).ToList(),

                ListFloatingBubble = allButtons
                    .Where(b => b.Category?.ToLower() == "floatingbubble")
                    .Select(b => new ButtonItemResponse
                    {
                        Title = b.Title ?? "",
                        Image = !string.IsNullOrEmpty(b.Image) && !b.Image.StartsWith("http")
                            ? $"{hostUrl}/uploads/images/actionButtons/{b.Image}"
                            : b.Image ?? "",
                        Type = b.Type ?? "",
                        Desc = b.Description ?? ""
                    }).ToList()
            };

            return response;
        }

        public async Task<PagedResult<ActionButtonConfig>> GetPage(RequestQuery query, string? category, string? type)
        {
            var buttons = _repository.AsQueryable();

            // Áp dụng filter nếu có keyword
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                buttons = buttons.Where(x =>
                    x.Title!.Contains(query.Keyword) ||
                    x.Description!.Contains(query.Keyword));
            }

            // Áp dụng filter nếu có category
            if (!string.IsNullOrEmpty(category))
            {
                buttons = buttons.Where(x => x.Category == category);
            }

            // Áp dụng filter nếu có type
            if (!string.IsNullOrEmpty(type))
            {
                buttons = buttons.Where(x => x.Type == type);
            }

            var totalItems = await buttons.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);

            // Sắp xếp và phân trang
            var listButtons = await buttons
                .OrderBy(x => x.SortOrder)
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            // Xử lý đường dẫn hình ảnh
            foreach (var button in listButtons)
            {
                if (!string.IsNullOrEmpty(button.Image) && !button.Image.StartsWith("http"))
                {
                    button.Image = $"{hostUrl}/uploads/images/actionButtons/{button.Image}";
                }
            }

            return new PagedResult<ActionButtonConfig>
            {
                Data = listButtons,
                TotalPages = totalPages
            };
        }

        public async Task<int> CreateAsync(ActionButtonRequest dto)
        {
            await unitOfWork.BeginTransactionAsync();
            try
            {
                var button = new ActionButtonConfig
                {
                    Type = dto.Type,
                    Title = dto.Title,
                    Category = dto.Category,
                    Description = dto.Description,
                    SortOrder = dto.SortOrder,
                    IsActive = dto.IsActive,
                };

                // Xử lý upload hình ảnh
                if (dto.Images?.Count > 0)
                {
                    button.Image = await ProcessUpload(dto.Images);
                }

                //await base.CreateAsync(button);

                await base.PrepareInsertFirstAsync(button);

                _repository.Add(button);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public async Task<int> UpdateAsync(string id, ActionButtonRequest dto)
        {
            await unitOfWork.BeginTransactionAsync();
            try
            {
                var button = await GetByIdAsync(id);
                if (button == null)
                {
                    return 0;
                }

                int currentOrder = button.SortOrder;

                button.Title = dto.Title;
                button.Type = dto.Type;
                button.Category = dto.Category;
                button.Description = dto.Description;
                button.SortOrder = dto.SortOrder;
                button.IsActive = dto.IsActive;

                await base.ReorderAsync(button, currentOrder, button.SortOrder);

                // Xử lý xóa hình ảnh cũ nếu có
                if (dto.Images.Count > 0 && !string.IsNullOrEmpty(button.Image))
                {
                    RemoveOldImage(button.Image);
                    button.Image = string.Empty;
                }

                // Xử lý upload hình ảnh mới
                if (dto.Images?.Count > 0)
                {
                    var newImage = await ProcessUpload(dto.Images);
                    if (!string.IsNullOrEmpty(newImage))
                    {
                        button.Image = newImage;
                    }
                }

                //return await base.UpdateAsync(button);

                _repository.Update(button);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var button = await GetByIdAsync(id);
            if (button == null)
            {
                return 0;
            }

            await unitOfWork.BeginTransactionAsync();

            try
            {
                if (!string.IsNullOrEmpty(button.Image))
                {
                    RemoveOldImage(button.Image);
                }

                await ReorderAfterDeleteAsync(button.SortOrder);

                _repository.Delete(button);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        #region Image handlers

        private async Task<string> ProcessUpload(List<IFormFile> files)
        {
            var stringFiles = string.Empty;
            if (files != null && files.Any())
            {
                var savePath = Path.Combine(env.WebRootPath, "uploads/images/actionButtons");
                var fileResult = await FileHandler.SaveFiles(files, savePath);
                stringFiles = string.Join(",", fileResult);
            }
            return stringFiles;
        }

        private void RemoveOldImage(string listImage)
        {
            var images = listImage.Split(",").Select(x => Path.Combine(env.WebRootPath, "uploads/images/actionButtons", x)).ToList();
            FileHandler.RemoveFiles(images);
        }

        #endregion
    }
}
