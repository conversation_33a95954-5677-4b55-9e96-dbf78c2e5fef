﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Services.Authencation;
using System.Security.Claims;
using System.Text;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class HomeController(IConfiguration configuration, IAuthencationService authencationService) : Controller
    {
        [AllowAnonymous]
        [HttpGet]
        [Route("Login")]
        public IActionResult Login()
        {
            ViewBag.Error = TempData["Error"] ?? "Vui lòng đăng nhập để tiếp tục.";
            return View("Views/Login/Login.cshtml");
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("Login")]
        [ActionName("Login")]
        public async Task<IActionResult> Login(string username, string password)
        {
            try
            {
                var claims = await authencationService.GetClaimsCmsAsync(username, password);
                var accessToken = JwtGenerator.GenerateJwtToken(configuration, claims);
                var encodedToken = Convert.ToBase64String(Encoding.UTF8.GetBytes(accessToken));

                var cookieOptions = new CookieOptions()
                {
                    HttpOnly = true,
                    Expires = DateTime.Now.AddHours(12)
                };

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var claimsPrinciple = new ClaimsPrincipal(claimsIdentity);

                TempData["access_token"] = encodedToken;

                await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, claimsPrinciple);

                return Redirect("/Product");
            }
            catch (Exception)
            {
                TempData["Error"] = "Sai tên đăng nhập hoặc mật khẩu";
                return Redirect("/Login");
            }
        }

        [HttpGet]
        [Route("Logout")]
        [ActionName("Logout")]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return Redirect("/");
        }

        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Affiliate()
        {
            return View();
        }

        public IActionResult Setting()
        {
            return View();
        }

        [AllowAnonymous]
        public IActionResult Privacy()
        {
            return View();
        }

        [AllowAnonymous]
        public IActionResult Error()
        {
            return View();
        }
    }
}
