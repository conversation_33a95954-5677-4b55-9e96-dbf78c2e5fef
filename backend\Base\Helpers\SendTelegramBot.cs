﻿using System.Text;

namespace MiniAppCore.Base.Helpers
{
    public class SendTelegramBot
    {
        public static async Task SendAlertTelegramBot(IConfiguration configuration, string ipAddress)
        {
            var botToken = configuration["NotificationSetting:Telegram:BotToken"];
            var chatId = configuration["NotificationSetting:Telegram:ChatId"];
            var prefixMessage = configuration["NotificationSetting:Telegram:PrefixMessage"];

            if (string.IsNullOrEmpty(botToken) || string.IsNullOrEmpty(chatId) || string.IsNullOrEmpty(prefixMessage))
            {
                Console.WriteLine("❌ Lỗi: Cấu hình Telegram chưa được thiết lập đúng.");
                return;
            }

            var content = @$"{prefixMessage}: {ipAddress}";
            var url = $"https://api.telegram.org/bot{botToken}/sendMessage?chat_id={chatId}&text={content}&parse_mode=Markdown";
            var stringContent = new StringContent("", Encoding.UTF8, "application/xml"); // Replace "" with actual XML if needed
            using var client = new HttpClient();
            var response = await client.PostAsync(url, stringContent);
            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Failed to send notification: {response.StatusCode}");
            }
        }

        public static async Task SendAlertTelegramBot(string botToken, string chatId, string content)
        {
            if (string.IsNullOrEmpty(botToken) || string.IsNullOrEmpty(chatId))
            {
                Console.WriteLine("❌ Lỗi: Cấu hình Telegram chưa được thiết lập đúng.");
                return;
            }

            var url = $"https://api.telegram.org/bot{botToken}/sendMessage?chat_id={chatId}&text={content}&parse_mode=Markdown";
            var stringContent = new StringContent("", Encoding.UTF8, "application/xml"); // Replace "" with actual XML if needed
            using var client = new HttpClient();
            var response = await client.PostAsync(url, stringContent);
            if (!response.IsSuccessStatusCode)
            {
                Console.WriteLine($"Failed to send notification: {response.StatusCode}");
            }
        }

        public static async Task SendMessageTelegramBot(IConfiguration configuration, string content)
        {
            try
            {
                var botToken = configuration["NotificationSetting:Telegram:BotToken"];
                var chatId = configuration["NotificationSetting:Telegram:ChatId"];
                if (string.IsNullOrEmpty(botToken) || string.IsNullOrEmpty(chatId))
                {
                    Console.WriteLine("❌ Lỗi: Cấu hình Telegram chưa được thiết lập đúng.");
                    return;
                }
                var url = $"https://api.telegram.org/bot{botToken}/sendMessage?chat_id={chatId}&text={content}&parse_mode=Markdown";
                var stringContent = new StringContent("", Encoding.UTF8, "application/xml"); // Replace "" with actual XML if needed
                using var client = new HttpClient();
                var response = await client.PostAsync(url, stringContent);
                if (!response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"Failed to send notification: {response.StatusCode}");
                }
            }
            catch (Exception)
            {

            }
        }
    }
}
