@import url('https://fonts.googleapis.com/css?family=Muli');

* {
    box-sizing: border-box;
    outline: none;
    line-height: 1.25em;
}

html,
body {
    margin: 0 auto;
}

html {
    font-family: 'Muli', sans-serif;
    font-size: 100%;
    line-height: 1.5em;
    background-color: #fefefe;
}

pre {
    overflow: auto;
}
pre code {
    font-size: 0.85em;
}

h1, h2, h3, h4, h5, h6 {
    color: #1a1a1b;
    letter-spacing: 1px;
}
h1 {
    font-size: 3.65em;
    color: #f44336;
}
h2 {
    font-size: 1.65em;
    color: #f44336;
}
p {
    font-size: 1.05em;
    color: #68696b;
}

ul, ol {
    color: #68696b;
}

a {
    text-decoration: none;
}
a:link, a {
    color: #1E7BDE;
}
a:visited {
    color: #1E7BDE;
}
a:active {
    color: #DE651E;
}
a:hover {
    text-decoration: underline;
}

select {
    color: inherit;
}

.content {
    max-width: 640px;
    margin: 0 auto;
    padding: 0em 1.65em 1.65em 1.65em;
}


/* PRESENTATION:
 * ------------------------------------------------------------------------- */

#presentation {
    padding: 0;
    position: relative;
}

#presentation .content {
    min-height: 100vh;
}

/* title page: */
#presentation h1 {
    font-size: 4.5em;
    margin: 0 auto;
    padding: 1em 0 0 0;
}
#presentation h1 b {
    display: inline-block;
    position: relative;
}
#presentation h1 b:before {
    content: attr(data-version);
    font-size: 12px;
    position: absolute;
      top: -22px;
    right: -14px;
}
#presentation p.main-description {
    font-size: 1.25em;
    margin: 3.5em auto;
}

#presentation p .btn {
    margin: 5px auto;
    min-width: 120px;
}

#presentation .transition-select select {
    border: 2px solid #AFAFAF;
    font-size: inherit;
    padding: 10px 20px;
    width: 92%;
    border-radius: 3px;
}


/**
 * Separator:
 */
.separator {
    display: inline-block;
    margin: 0 1em;
}


/**
 * Text alignment:
 */
.text-center {
    text-align: center;
}


/* CSS BUTTONS LIBRARY:
 * ------------------------------------------------------------------------- */

.btn {
    border: 1px solid transparent;
    display: inline-block;
    padding: 0.70em 1em;
    font-size: inherit;
    margin-bottom: 0;
    line-height: 1em;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    transition: all 0.2s ease 0s;
    border-radius: 3px;    
}
.btn:active {
    outline: 0;
    box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

/* saf3+, chrome1+ */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    .btn {
        padding: 0.65em 1em;
        line-height: 1.1em;
    }
}

.btn,
[role="button"] {
    cursor: pointer;
    text-decoration: none;
}
.btn--primary,
.btn--success,
.btn--warning,
.btn--caution,
.btn--download {
    color: #FEFEFE !important;
    text-decoration: none !important;
}

.btn--default {
    color: #455A64 !important;
    text-decoration: none !important;
}

.btn--primary {
    background-color: #337AB7;
    border-color: #2E6DA4;
}
.btn--primary.hover,
.btn--primary:hover {
    background-color: #286090;
    border-color: #204D74;
}
.btn--primary.focus,
.btn--primary:focus {
    background-color: #286090;
    border-color: #122B40;
}

.btn--success {
    background-color: #5CB85C;
    border-color: #4CAE4C;
}
.btn--success.hover,
.btn--success:hover {
    background-color: #449D44;
    border-color: #398439;
}
.btn--success.focus,
.btn--success:focus {
    background-color: #449D44;
    border-color: #255625;
}

.btn--warning {
    background-color: #F0AD4E;
    border-color: #EEA236;
}
.btn--warning.hover,
.btn--warning:hover {
    background-color: #EC971F;
    border-color: #D58512;
}
.btn--warning.focus,
.btn--warning:focus {
    background-color: #EC971F;
    border-color: #985F0D;
}

.btn--caution {
    background-color: #D9534F;
    border-color: #D43F3A;
}
.btn--caution.hover,
.btn--caution:hover {
    background-color: #C9302C;
    border-color: #AC2925;
}
.btn--caution.focus,
.btn--caution:focus {
    background-color: #C9302C;
    border-color: #761C19;
}

.btn--default {
    color: #312F2F;
    background-color: #E6E6E6;
    border-color: #D0D0D0;
}
.btn--default.hover,
.btn--default:hover {
    color: #312F2F;
    background-color: #E0DCDC;
    border-color: #ADADAD;
}
.btn--default.focus,
.btn--default:focus {
    color: #312F2F;
    background-color: #E0DCDC;
    border-color: #8C8C8C;
}

.btn--download {
    background-color: #F44336;
    border-color: #D23A2F;
}
.btn--download.hover,
.btn--download:hover {
    background-color: #E03C30;
    border-color: #B13027;
}
.btn--download.focus,
.btn--download:focus {
    background-color: #E03C30;
    border-color: #B13027;
}

.btn--link {
    color: #1E7BDE;
}
.btn--link,
.btn--link:active,
.btn--link:focus,
.btn--link:hover {
    border-color: transparent;
    box-shadow: none;
}

.test, .test * {
    outline: 1px solid red;
    outline-offset: -1px;
}