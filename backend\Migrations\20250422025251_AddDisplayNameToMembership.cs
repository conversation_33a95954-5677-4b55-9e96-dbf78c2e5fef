﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddDisplayNameToMembership : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "76008688ecd84c85b20422bcabe1c814");

            migrationBuilder.AddColumn<string>(
                name: "DisplayName",
                table: "Memberships",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEBT6zqIVCbYzLDxz7RIz9UwR45heptUPhLnMNk3GQGS4oe+yx7q+kBGjnrp5QVY1/w==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "ea43256c838845e994173d3d03e9d3d0", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 22, 9, 52, 49, 269, DateTimeKind.Local).AddTicks(8544), "FeaturesButton", new DateTime(2025, 4, 22, 9, 52, 49, 270, DateTimeKind.Local).AddTicks(3225) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "ea43256c838845e994173d3d03e9d3d0");

            migrationBuilder.DropColumn(
                name: "DisplayName",
                table: "Memberships");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEPMaXGeTnL8z2fpuCEITgIPNMBXj9juESN8MVWdu44lFbwj+QEClpreuw0hVJ9+Ekg==");

            //migrationBuilder.InsertData(
            //    table: "Commons",
            //    columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
            //    values: new object[] { "76008688ecd84c85b20422bcabe1c814", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 21, 11, 37, 39, 684, DateTimeKind.Local).AddTicks(3383), "FeaturesButton", new DateTime(2025, 4, 21, 11, 37, 39, 684, DateTimeKind.Local).AddTicks(9528) });
        }
    }
}
