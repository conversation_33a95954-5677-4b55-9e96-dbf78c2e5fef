﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Settings
{
    public class Rank : BaseEntity
    {
        public string? Name { get; set; }
        public string? Image { get; set; }
        public string? Description { get; set; }
        public long RankingPoint { get; set; }
        public float ConvertRate { get; set; }

        public bool IsDefault { get; set; }
        public bool IsActive { get; set; }
    }
}
