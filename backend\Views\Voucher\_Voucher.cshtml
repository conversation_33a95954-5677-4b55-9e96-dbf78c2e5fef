﻿@using MiniAppCore.Entities.Products
@using MiniAppCore.Enums
@model MiniAppCore.Models.Responses.Vouchers.VoucherDetailResponse;
@{
    var allProducts = ((List<Product>)ViewBag.Products).Where(x => !x.IsGift).ToList();
}
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.Id" />
                <!-- Thông tin cơ bản -->
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Thông tin cơ bản</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Tên voucher <span class="text-danger">*</span></label>
                                    <input id="name" type="text" class="form-control" value="@Model.Name" maxlength="120" required>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Mã voucher <span class="text-danger">*</span></label>
                                    <input id="code" type="text" onkeypress="preventInvalidChars(event)" class="form-control" value="@Model.Code" required>
                                    <small class="form-text text-muted">Viết liền không dấu, không ký tự đặc biệt</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Trạng thái <span class="text-danger">*</span></label>
                                    <select id="isActive" class="selectpicker form-control" required>
                                        <option value="true" selected="@Model.IsActive">Đang hoạt động</option>
                                        <option value="false" selected="@(!Model.IsActive)">Ngưng hoạt động</option>
                                    </select>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Mô tả<span class="text-danger">*</span></label>
                                    <textarea id="descr" class="form-control" rows="2" required>@Model.Description</textarea>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Thời gian áp dụng -->
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="far fa-calendar-alt me-2"></i>Thời gian áp dụng</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Ngày bắt đầu <span class="text-danger">*</span></label>
                                    <input id="startDate" type="datetime-local" class="form-control" value="@Model.StartDate.ToString("yyyy-MM-ddTHH:mm")" required>
                                    <small class="form-text text-muted">Là ngày bắt đầu đổi voucher</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Ngày kết thúc <span class="text-danger">*</span></label>
                                    <input id="endDate" type="datetime-local" class="form-control" value="@Model.EndDate.ToString("yyyy-MM-ddTHH:mm")" required>
                                    <small class="form-text text-muted">Là ngày kết thúc đổi voucher</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Ngày hết hạn <span class="text-danger">*</span></label>
                                    <input id="expiryDate" type="datetime-local" class="form-control" value="@Model.ExpiryDate.ToString("yyyy-MM-ddTHH:mm")" required>
                                    <small class="form-text text-muted">Thời gian hết hạn sử dụng voucher</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Điều kiện giảm giá -->
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-percentage me-2"></i>Điều kiện giảm giá</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Loại áp dụng <span class="text-danger">*</span></label>
                                    <select id="applyType" class="selectpicker form-control" required>
                                        <option value="@((short)EVoucherType.Order)" selected="@(Model.VoucherType == (short)EVoucherType.Order)">Tổng đơn hàng</option>
                                        <option value="@((short)EVoucherType.Product)" selected="@(Model.VoucherType == (short)EVoucherType.Product)">Sản phẩm</option>
                                        <option value="@((short)EVoucherType.Shipping)" selected="@(Model.VoucherType == (short)EVoucherType.Shipping)">Vận chuyển</option>
                                    </select>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Loại giảm <span class="text-danger">*</span></label>
                                    <select id="discountType" class="selectpicker form-control" required onchange="toggleDiscountType(this)">
                                        <option value="0" selected="@(Model.DiscountType == (short)EDiscountType.Percentage)">Phần trăm</option>
                                        <option value="1" selected="@(Model.DiscountType == (short)EDiscountType.FixedAmount)">Tiền</option>
                                    </select>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Giá trị giảm <span class="text-danger">*</span></label>
                                    <input id="discountValue" oninput="handleDiscountInput(this)" type="text" min="0" max="100" class="form-control" value="@(Model.DiscountValue.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                        required>
                                    <small class="form-text text-muted">Tối đa 100% nếu giảm theo phần trăm</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Giảm giá tối đa <span class="text-danger">*</span></label>
                                    <input id="maxDiscount"
                                           
                                           type="text" min="0"
                                        class="form-control"
                                           oninput="InputValidator.currency(this,{min:0})"
                                           value="@(Model.MaxDiscountAmount.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                           required>
                                    <small class="form-text text-muted">Nhập 0 nếu không giới hạn giảm giá tối đa khi loại giảm là phần trăm</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Đơn hàng tối thiểu <span class="text-danger">*</span></label>
                                    <input id="minimumOrderValue" 
                                        type="text" 
                                        min="0" 
                                        class="form-control"
                                        oninput="InputValidator.currency(this,{min:0})"
                                        value="@(Model.MinimumOrderValue.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                        required>
                                    <small class="form-text text-muted">Số tiền tối thiểu để sử dụng voucher</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Số lượng <span class="text-danger">*</span></label>
                                    <input id="quantity" type="number" min="-1" 
                                    max="@long.MaxValue" class="form-control" value="@Model.Quantity" required
                                        oninput="if (parseFloat(this.value) < -1) this.value = -1; if (parseFloat(this.value) > 9223372036854775807) this.value = 9223372036854775807;">
                                    <small class="form-text text-muted">Nhập -1 cho số lượng không giới hạn</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Điều kiện đổi điểm -->
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>Điều kiện đổi điểm</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Có thể đổi bằng điểm <span class="text-danger">*</span></label>
                                    <select id="isExchange" class="selectpicker form-control" required onchange="toggleIsExchange(this)">
                                        <option value="true" selected="@Model.IsExchange">Có</option>
                                        <option value="false" selected="@(!Model.IsExchange)">Không</option>
                                    </select>
                                    <small class="form-text text-muted">Khách hàng sử dụng mã để đổi</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Số lần được đổi <span class="text-danger">*</span></label>
                                    <input id="exchangeTimes"
                                           oninput="InputValidator.currency(this, {min:0})"
                                           type="text"
                                           min="0"
                                           class="form-control"
                                           value="@(Model.ExchangeTimes.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                           required>
                                    <small class="form-text text-muted">Số lần tối đa một khách hàng có thể đổi</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Số điểm quy đổi <span class="text-danger">*</span></label>
                                    <input id="pointRequired"
                                           type="text"
                                           min="0"
                                           oninput="InputValidator.currency(this, {min:0})"
                                           class="form-control"
                                           value="@(Model.PointRequired.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                           required
                                           disabled="@(!Model.IsExchange)">
                                    <small class="form-text text-muted">Số điểm khách hàng cần đổi lấy voucher</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Điểm hạng thành viên <span class="text-danger">*</span></label>
                                    <input id="rankingPoint"
                                           oninput="InputValidator.currency(this, {min:0})"
                                           type="text"
                                           min="0"
                                           class="form-control"
                                           value="@(Model.RankingPoint.ToString("#,0", System.Globalization.CultureInfo.GetCultureInfo("vi-VN")))"
                                           required>
                                    <small class="form-text text-muted">Điểm tối thiểu của khách hàng để đổi voucher</small>
                                    <div class="help-block with-errors"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sản phẩm áp dụng -->
                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-box-open me-2"></i>Sản phẩm áp dụng</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Chọn loại áp dụng sản phẩm</label>
                                    <select id="applyAll" class="form-control" onchange="toggleProductSelection(this.value)">
                                        <option value="true" selected="@Model.IsAllProducts">Tất cả sản phẩm</option>
                                        <option value="false" selected="@(!Model.IsAllProducts)">Từng sản phẩm</option>
                                    </select>
                                </div>
                            </div>

                            <div id="productSelection" class="col-md-12 mt-3" style="display: @(!Model.IsAllProducts ? "block" : "none")">
                                <div class="form-group">
                                    <label>Sản phẩm áp dụng voucher này</label>
                                    <select id="products" class="form-control" multiple>
                                        @if (ViewBag.Products != null)
                                        {
                                            @foreach (var item in allProducts)
                                            {
                                                <option value="@item.Id" selected="@(Model.Products.Contains(item.Id))">@item.Name</option>
                                            }
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (User.IsInRole("ADMIN"))
        {
            @if (string.IsNullOrEmpty(Model.Id))
            {
                <button type="button" class="btn btn-outline-secondary" onclick="ClearForm()">Làm mới</button>
            }
            <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
        }
    </div>
</div>

<script>
    $(document).ready(function () {
        // Khởi tạo multiselect với tìm kiếm
        $('#products').select2({
            placeholder: 'Chọn sản phẩm...',
        });

        // Kiểm tra và cập nhật trạng thái ban đầu của maxDiscount dựa trên discountType
        updateMaxDiscountState();

        // Kiểm tra khi thay đổi loại giảm giá
        $("#discountType").on('change', function () {
            updateMaxDiscountState();
        });

        // Kiểm tra khi nhập giá trị
        // $("#discountValue").on('input', function () {
        //     handleDiscountValueChange(this);
        // });

        // Hiển thị/ẩn phần chọn sản phẩm
        toggleProductSelection($("#applyAll").val());
    });

    function cleanNumber(value) {
        return value.replace(/[^0-9]/g, '') || '0';
    }

    // Hàm cập nhật trạng thái của maxDiscount dựa trên discountType
    function updateMaxDiscountState() {
        const isPercent = parseInt($("#discountType").val()) === 0;
        const discountValue = $("#discountValue").val();

        // Nếu là phần trăm (discountType = 0)
        if (isPercent) {
            // Giới hạn giá trị không quá 100
            if (parseInt(discountValue) > 100) {
                $("#discountValue").val(100);
            }
            // Mở khóa trường maxDiscount
            $("#maxDiscount").prop('disabled', false);
        }
        // Nếu là giá tiền cố định (discountType = 1)
        else {
            // Vô hiệu hóa trường maxDiscount
            $("#maxDiscount").prop('disabled', true);
            // Đặt giá trị maxDiscount = discountValue
            $("#maxDiscount").val(discountValue);
        }
    }

    function handleDiscountInput(input) {
        // Xử lý logic sau đó format
        handleDiscountValueChange(input);
        InputValidator.currency(input, {min:0});
    }

    // Hàm xử lý khi giá trị discountValue thay đổi
    function handleDiscountValueChange(input) {
        input.value = cleanNumber(input.value);
        const discountType = parseInt($("#discountType").val());
        let val = parseInt(input.value) || 0;

        // Nếu là phần trăm, giới hạn giá trị tối đa là 100
        if (discountType === 0 && val > 100) {
            input.value = 100;
        } else {
            input.value = val;

            // Nếu là giá tiền cố định, cập nhật maxDiscount
            if (discountType === 1) {
                $("#maxDiscount").val(val.toLocaleString('vi-VN'));
            }
        }
    }

    // Hàm xử lý khi thay đổi loại discountType
    function toggleDiscountType(input) {
        const isPercent = parseInt(input.value) === 0;

        // Nếu là phần trăm
        if (isPercent) {
            // Giới hạn giá trị của discountValue không quá 100
            let discountVal = parseInt($("#discountValue").val()) || 0;
            if (discountVal > 100) {
                $("#discountValue").val(100);
            }
            // Mở khóa maxDiscount
            $("#maxDiscount").prop('disabled', false);
        }
        // Nếu là giá tiền cố định
        else {
            // Vô hiệu hóa maxDiscount và gán giá trị = discountValue
            $("#maxDiscount").prop('disabled', true);
            $("#maxDiscount").val($("#discountValue").val());
        }
    }

    function toggleIsExchange(input) {
        const value = input.value;
        if (value === "true") {
            $("#pointRequired").val(0);
            $("#pointRequired").prop("disabled", false);
        } else {
            $("#pointRequired").prop("disabled", true);
        }
    }

    // Hàm hiển thị hoặc ẩn phần chọn sản phẩm
    function toggleProductSelection(value) {
        if (value === "true") {
            $("#productSelection").slideUp();
        } else {
            $("#productSelection").slideDown();
        }
    }
</script>