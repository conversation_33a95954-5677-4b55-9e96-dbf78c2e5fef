<script>
    // Custom Field Form Scripts
    $(document).ready(function () {
        initializeCustomFieldForm();
    });

    function initializeCustomFieldForm() {
        const form = $('#customFieldForm');

        // Handle form submission with standard browser validation
        form.on('submit', function (e) {
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                e.preventDefault();
                submitCustomFieldForm(form);
            }
            $(this).addClass('was-validated');
        });

        // Preview field name formatting
        $('input[name="FieldName"]').on('input', function () {
            const value = $(this).val();
            // Remove special characters and spaces, ensure PascalCase-like format
            let formatted = value.replace(/[^a-zA-Z0-9]/g, '');
            if (formatted.length > 0) {
                formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1);
            }

            if (formatted !== value) {
                $(this).val(formatted);
            }
        });

        // Auto-generate display name from field name if display name is empty
        $('input[name="FieldName"]').on('input', function () {
            const fieldName = $(this).val();
            const displayNameInput = $('input[name="FieldNameDisplay"]');
            
            // Only auto-generate if display name is empty
            if (!displayNameInput.val() && fieldName) {
                // Convert camelCase/PascalCase to readable text
                let displayName = fieldName
                    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
                    .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
                    .trim();
                displayNameInput.val(displayName);
            }
        });

        // Data type selection change
        $('select[name="DataType"]').on('change', function () {
            const selectedType = $(this).val();
            showDataTypeInfo(selectedType);
        });

        // Show initial data type info if a type is already selected
        const initialDataType = $('select[name="DataType"]').val();
        if (initialDataType) {
            showDataTypeInfo(initialDataType);
        }
    }

    function showDataTypeInfo(dataType) {
        const infoText = getDataTypeInfo(dataType);
        let infoElement = $('#dataTypeInfo');

        if (infoElement.length === 0) {
            infoElement = $('<small id="dataTypeInfo" class="form-text text-info mt-1"></small>');
            $('select[name="DataType"]').closest('.form-group').append(infoElement);
        }

        infoElement.text(infoText);
    }

    function getDataTypeInfo(dataType) {
        const dataTypeInfoMap = {
            'string': 'Văn bản thông thường, tối đa 255 ký tự.',
            'int': 'Số nguyên (ví dụ: 1, 2, 100).',
            'decimal': 'Số thập phân (ví dụ: 1.5, 99.99).',
            'bool': 'Giá trị đúng/sai (true/false).',
            'datetime': 'Ngày và giờ (ví dụ: 2025-12-25 10:30:00).',
            'date': 'Chỉ ngày (ví dụ: 2025-12-25).',
            'email': 'Địa chỉ email hợp lệ.',
            'phone': 'Số điện thoại.',
            'url': 'Đường dẫn URL hợp lệ.',
            'textarea': 'Văn bản dài, nhiều dòng.',
            'select': 'Danh sách lựa chọn duy nhất.',
            'multiselect': 'Cho phép chọn nhiều giá trị.',
            'file': 'Tệp đính kèm.',
            'image': 'Tệp hình ảnh.'
        };

        return dataTypeInfoMap[dataType] || 'Chọn một kiểu dữ liệu để xem mô tả.';
    }

    // This function is now called from the form's submit event
    function submitCustomFieldForm(form) {
        const submitBtn = form.find('button[type="submit"]');
        const originalBtnHtml = submitBtn.html();

        // Show loading state
        submitBtn.prop('disabled', true).html(`
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            Đang xử lý...
        `);

        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            success: function (response) {
                if (response.success) {
                    $('#customFieldModal').modal('hide');
                    // Assuming you have a global alert function and table refresh logic
                    if (typeof AlertResponse === 'function') AlertResponse(response.message, "success");
                    if (typeof table !== 'undefined') table.ajax.reload(null, false);
                    // Also, reload the overview for the currently selected entity
                    const entityName = $('#entitySelector').val();
                    if (entityName) loadEntityData(entityName);
                } else {
                    if (typeof AlertResponse === 'function') AlertResponse(response.message, "error");
                }
            },
            error: function (xhr) {
                const message = xhr.responseJSON?.message || 'Đã xảy ra lỗi không mong muốn. Vui lòng thử lại.';
                if (typeof AlertResponse === 'function') AlertResponse(message, "error");
            },
            complete: function () {
                // Restore button state
                submitBtn.prop('disabled', false).html(originalBtnHtml);
            }
        });
    }
</script>
