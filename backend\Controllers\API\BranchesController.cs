﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Branches;
using MiniAppCore.Services.Branches;
using Newtonsoft.Json;

namespace MiniAppCore.Controllers.API
{
    [Route("api/[controller]")]
    [ApiController]
    public class BranchesController(ILogger<BranchesController> logger, IBranchService branchService) : ControllerBase
    {
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> GetPage([FromQuery] BranchQueryParameters queryParams)
        {
            try
            {
                var branches = await branchService.GetPage(queryParams);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    branches.Data,
                    branches.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("NearByMe")]
        public async Task<IActionResult> GetAllBranchNearByMe([FromQuery] BranchQueryParameters queryParams, [FromBody] DataForm user_access_token)
        {
            try
            {
                var userLocation = await GetLocation(user_access_token);
                if (userLocation == null)
                {
                    throw new Exception();
                }
                var userLatitude = Convert.ToDouble(userLocation.data["latitude"]);
                var userLongitude = Convert.ToDouble(userLocation.data["longitude"]);

                var branches = await branchService.GetAllAsync();

                if (queryParams.IsActive.HasValue)
                {
                    branches = branches.Where(branch => branch.Status == queryParams.IsActive).ToList();
                }

                string? Keyword = user_access_token.Keyword ?? queryParams.Keyword;
                string? City = user_access_token.City ?? queryParams.City;
                string? District = user_access_token.District ?? queryParams.Keyword;
                string? Ward = user_access_token.Ward ?? queryParams.Ward;

                if (!string.IsNullOrEmpty(Keyword))
                {
                    branches = branches.Where(branch => new string[] {
                                        branch.Name,
                                        branch.Description,
                                        branch.StreetLine }.Any(value => value != null &&
                                                                   value.Contains(Keyword, StringComparison.OrdinalIgnoreCase))
                             ).ToList();
                }

                if (!string.IsNullOrEmpty(City))
                {
                    branches = branches.Where(branch => !string.IsNullOrEmpty(branch.ProvinceId) && branch.ProvinceId.ToLower().Equals(City.ToLower())).ToList();
                }

                if (!string.IsNullOrEmpty(District))
                {
                    branches = branches.Where(branch => !string.IsNullOrEmpty(branch.DistrictId) && branch.DistrictId.ToLower().Equals(District.ToLower())).ToList();
                }

                if (!string.IsNullOrEmpty(Ward))
                {
                    branches = branches.Where(branch => !string.IsNullOrEmpty(branch.WardId) && branch.WardId.ToLower().Equals(Ward.ToLower())).ToList();
                }

                var nearbyBranches = branches.Select(item =>
                {
                    var destLongitude = Convert.ToDouble(item.Longitude);
                    var destLatitude = Convert.ToDouble(item.Latitude);
                    double distance = Tools.CalculateDistance(userLatitude, userLongitude, destLatitude, destLongitude);
                    return new
                    {
                        item.Id,
                        item.Name,
                        item.Description,
                        item.PhoneNumber,
                        item.ProvinceId,
                        item.ProvinceName,
                        item.DistrictId,
                        item.DistrictName,
                        item.WardId,
                        item.WardName,
                        item.StreetLine,
                        item.FullAddress,
                        item.GoogleMapURL,
                        item.Image,
                        OpenTime = item.OpeningTime.ToString("HH:mm"),
                        CloseTime = item.ClosingTime.ToString("HH:mm"),
                        Distance = distance
                    };
                }).OrderBy(p => p.Distance).ToList();

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = nearbyBranches
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Không tìm thấy vị trí người dùng"
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetDetailBranch(string id)
        {
            try
            {
                var item = await branchService.GetByIdAsync(id);
                if (item == null)
                {
                    return Ok(new
                    {
                        Code = 1,
                        Message = "Không tìm thấy chi nhánh!"
                    });
                }

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = item
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateBranch([FromForm] BranchRequest dto)
        {
            try
            {
                await branchService.CreateAsync(dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateBranch(string id, [FromForm] BranchRequest dto)
        {

            try
            {
                await branchService.UpdateAsync(id, dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật chi nhánh thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteBranch(string id)
        {
            try
            {
                await branchService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa chi nhánh thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        private async Task<ResponseZaloDTO?> GetLocation(DataForm dataPost)
        {

            try
            {
                const string endpoint = "https://graph.zalo.me/v2.0/me/info";

                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("access_token", dataPost.accessToken);
                    client.DefaultRequestHeaders.Add("code", dataPost.tokenNumber);
                    client.DefaultRequestHeaders.Add("secret_key", dataPost.secretKey);

                    HttpResponseMessage response = await client.GetAsync(endpoint);

                    string responseBody = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<ResponseZaloDTO>(responseBody);
                }
            }
            catch (Exception ex)
            {
                logger.LogDebug(ex.Message);
                return null;
            }
        }
    }
}
