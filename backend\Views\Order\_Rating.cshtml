﻿@using MiniAppCore.Models.Responses.Products.Ratings
@model CustomerProductRatingResponse

<style>
    .rating-modal {
        background-color: #fff;
        border-radius: 8px;
        padding: 1.5rem;
        max-width: 500px;
        margin: 0 auto;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }

    .customer-info {
        margin-bottom: 1.5rem;
    }

    .product-rating {
        border-top: 1px solid #eee;
        padding-top: 1rem;
    }

    .rating-stars {
        font-size: 1.25rem;
    }

    .rating-stars i {
        margin-right: 2px;
    }

    .rating-images {
        display: flex;
        flex-wrap: nowrap;
        /* Prevent wrapping to next line */
        gap: 8px;
        overflow-x: auto;
        /* Enable horizontal scrolling */
        padding-bottom: 10px;
        /* Space for scrollbar */
        max-width: 100%;
        scrollbar-width: thin;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
        /* Smooth scrolling on iOS */
    }

    /* Style the scrollbar for Chrome/Edge/Safari */
    .rating-images::-webkit-scrollbar {
        height: 5px;
    }

    .rating-images::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .rating-images::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 10px;
    }

    .rating-images::-webkit-scrollbar-thumb:hover {
        background: #555;
    }

    .rating-image,
    .rating-video-thumbnail {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 4px;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .rating-image:hover,
    .rating-video-thumbnail:hover {
        transform: scale(1.05);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }

    .media-container {
        position: relative;
        width: 80px;
        height: 80px;
    }

    .video-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(0, 0, 0, 0.6);
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        pointer-events: none;
        z-index: 2;
    }

    .rating-comment {
        font-size: 0.9rem;
    }

    .no-rating-message {
        text-align: center;
        padding: 30px 0;
    }

    .no-rating-icon {
        font-size: 3rem;
        color: #ccc;
        margin-bottom: 15px;
    }

    /* Media preview modal styles */
    .media-preview-modal .modal-content {
        background-color: #000000;
        border: none;
        border-radius: 8px;
    }

    .media-preview-modal .modal-body {
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 300px;
    }

    .media-preview-modal img {
        max-height: 80vh;
        max-width: 100%;
        border-radius: 4px;
    }

    .media-preview-modal video {
        max-height: 80vh;
        max-width: 100%;
        border-radius: 4px;
    }

    .media-preview-modal .modal-header {
        border: none;
        padding: 15px;
        background-color: rgba(0, 0, 0, 0.8);
        justify-content: flex-end;
        position: absolute;
        top: 0;
        right: 0;
        z-index: 10;
        width: 100%;
    }

    .media-preview-close {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .media-preview-close:hover {
        background-color: rgba(255, 255, 255, 0.4);
    }

    /* Fixed size for the media preview modal */
    .media-preview-modal .modal-dialog {
        width: 800px;
        height: 600px;
        max-width: 90%;
        max-height: 90vh;
        margin: 1.75rem auto;
    }

    .media-preview-modal .modal-content {
        background-color: #000000;
        border: none;
        border-radius: 8px;
        height: 100%;
    }

    .media-preview-modal .modal-body {
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
        overflow: hidden;
    }

    .media-preview-modal img {
        max-height: 100%;
        max-width: 100%;
        object-fit: contain;
    }

    .media-preview-modal video {
        max-height: 100%;
        max-width: 100%;
        object-fit: contain;
    }

    /* Add these styles */
    .rating-video-thumbnail {
        width: 80px;
        height: 80px;
        background-color: #1a1a1a;
        border-radius: 4px;
        cursor: pointer;
        transition: transform 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .rating-video-thumbnail::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="%23ffffff" opacity="0.3"><path d="M4,4H20a2,2,0,0,1,2,2V18a2,2,0,0,1-2,2H4a2,2,0,0,1-2-2V6A2,2,0,0,1,4,4Zm7,5.5v5l4.4-2.5Z"/></svg>');
        background-repeat: no-repeat;
        background-position: center;
        background-size: 40px;
        background-color: rgba(0, 0, 0, 0.3);
    }
</style>
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">Đánh giá</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        @if (Model != null)
        {
            <div class="customer-info mb-4">
                <div>@(Model.CustomerName ?? "Nguyễn Văn A")</div>
                <div>Mã đơn hàng: @Model.OrderId</div>
                <div>Ngày đánh giá: @Model.RatingDate.ToString("dd/MM/yyyy")</div>
            </div>

            var ratedProducts = Model.Products?.Where(p => p.IsRating).ToList() ?? new List<ProductRatingItemResponse>();

            if (ratedProducts.Any())
            {
                @foreach (var item in ratedProducts)
                {
                    <div class="product-rating mb-3">
                        <div class="product-name mb-2">@(item.Product?.ProductName ?? "Sản phẩm")</div>
                        <div class="rating-stars">
                            @for (int i = 0; i < 5; i++)
                            {
                                <i class="@(i < item.Star ? "ri-star-fill" : "ri-star-line") text-warning"></i>
                            }
                        </div>
                        @if ((item.Images != null && item.Images.Any()) || (item.Videos != null && item.Videos.Any()))
                        {
                            <div class="mt-2 mb-1">Media</div>
                            <div class="rating-images">
                                @if (item.Images != null)
                                {
                                    @foreach (var image in item.Images)
                                    {
                                        <img src="@image" alt="Product image" class="rating-image" onclick="openMediaPreview('@image', 'image')" />
                                    }
                                }

                                @if (item.Videos != null)
                                {
                                    @foreach (var video in item.Videos)
                                    {
                                        <div class="media-container">
                                            <div class="rating-video-thumbnail" data-video-src="@video" onclick="openMediaPreview('@video', 'video')">
                                                <div class="video-indicator">
                                                    <i class="ri-play-fill"></i>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                }
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(item.Review))
                        {
                            <div class="rating-comment mt-2">
                                @item.Review
                            </div>
                        }
                    </div>
                }
            }
            else
            {
                <div class="no-rating-message">
                    <div class="no-rating-icon">
                        <i class="ri-star-smile-line"></i>
                    </div>
                    <p>Đơn hàng này chưa có đánh giá nào</p>
                </div>
            }
        }
        else
        {
            <div class="no-rating-message">
                <div class="no-rating-icon">
                    <i class="ri-error-warning-line"></i>
                </div>
                <p>Không thể tải thông tin đánh giá</p>
            </div>
        }
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
    </div>
</div>

<!-- Media Preview Modal -->
<div class="modal fade media-preview-modal" id="mediaPreviewModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="media-preview-close" data-bs-dismiss="modal" aria-label="Close">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            <div class="modal-body" id="mediaPreviewContent">
                <!-- Content will be inserted by JavaScript -->
            </div>
        </div>
    </div>
</div>

<script>
    // Add this function to your existing JavaScript
    document.addEventListener("DOMContentLoaded", function () {
        // Generate video thumbnails dynamically
        const videoThumbnails = document.querySelectorAll('.rating-video-thumbnail[data-video-src]');

        videoThumbnails.forEach(thumbnail => {
            const videoSrc = thumbnail.getAttribute('data-video-src');
            generateVideoThumbnail(videoSrc, thumbnail);
        });
    });

    function generateVideoThumbnail(videoUrl, containerElement) {
        const video = document.createElement('video');
        video.src = videoUrl;
        video.crossOrigin = 'anonymous';
        video.muted = true;
        video.preload = 'metadata';

        video.onloadeddata = function () {
            // Jump to 25% of the video
            video.currentTime = Math.floor(video.duration * 0.25);
        };

        video.onseeked = function () {
            // Create canvas and draw video frame
            const canvas = document.createElement('canvas');
            canvas.width = 80;
            canvas.height = 80;

            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

            try {
                // Use canvas as background
                const thumbnailUrl = canvas.toDataURL('image/jpeg');
                containerElement.style.backgroundImage = `url(${thumbnailUrl})`;
                containerElement.style.backgroundSize = 'cover';
                containerElement.style.backgroundPosition = 'center';
            } catch (e) {
                console.log('Error generating thumbnail:', e);
            }

            // Clean up
            video.remove();
        };

        video.onerror = function () {
            // Keep default styling on error
            console.log('Error loading video for thumbnail');
        };
    }

    function openMediaPreview(source, type) {
        const container = document.getElementById('mediaPreviewContent');
        container.innerHTML = '';

        if (type === 'image') {
            const img = document.createElement('img');
            img.src = source;
            img.alt = 'Product image';
            container.appendChild(img);
        } else if (type === 'video') {
            const video = document.createElement('video');
            video.src = source;
            video.controls = true;
            video.autoplay = true;
            container.appendChild(video);
        }

        $('#mediaPreviewModal').modal('show');
    }
</script>