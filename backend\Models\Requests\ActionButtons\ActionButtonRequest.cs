namespace MiniAppCore.Models.Requests.ActionButtons
{
    public class ActionButtonRequest
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int SortOrder { get; set; }
        public bool IsActive { get; set; }

        // For image uploads
        public List<IFormFile> Images { get; set; } = new();
        public List<string> RemovedOldImages { get; set; } = new();
    }
}