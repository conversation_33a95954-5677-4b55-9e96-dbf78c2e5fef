﻿﻿<style>
    .spinner {
        z-index: 0 !important;
    }
</style>

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Mua X tặng Y</h4>
                <p class="mb-0">
                    Tạ<PERSON> các chương trình mua kèm để tăng đơn hàng, ví dụ: mua sản phẩm X tặng kèm sản phẩm Y, v.v
                </p>
            </div>
            <button onclick="GetFormPromotion('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-promotion">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>
    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0; z-index: -1;"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-promotions" class="data-table table mb-0 tbl-server-info"> </table>
        </div>
    </div>
</div>

<div id="modal-promotion" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListPromotion();

            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#search').on('input', search);
        });

        function GetListPromotion() {
            const status = {
                0: `Tỉ lệ phần trăm`,
                1: `Giảm giá trực tiếp`,
            };

            table = new DataTable("#list-promotions", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    // const type = $("#filter-type").val();
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetAll", "promotions")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword,
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: `<div class="d-flex align-items-center">
                                        <div>
                                            ${item.name}
                                            <p class="mb-0"><small>${truncateText(item.description, 100)}</small></p>
                                        </div>
                                    </div>`,
                                2: new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(item.totalAmount),
                                3: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                                                <a onclick="GetFormPromotion('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                                                    <i class="ri-edit-line fs-6 mr-0"></i>
                                                                                </a>
                                                                                <a onclick="DeletePromotion('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                                                    <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                                                </a>
                                                                            </div>`,
                                4: FormatDateTime(item.startDate),
                                5: FormatDateTime(item.expiryDate),
                                6: item.isActive ? `<div class="m-auto circle-active"><div>` : `<div class="m-auto circle-inactive"></div>`,
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên", data: 1 },
                    { title: "Tổng giá trị", data: 2 },
                    { title: "Trạng thái", data: 6 },
                    { title: "Ngày bắt đầu", data: 4 },
                    { title: "Ngày hết hạn", data: 5 },
                    { title: "Thao tác", data: 3 },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormPromotion(id) {
            const url = id ? `@Url.Action("Detail", "Promotion")/${id}` : "@Url.Action("Create", "Promotion")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-promotion").modal("toggle");

                    $("#type").select2();

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function InitialEditor() {
            window.editor = new Quill('#extraGift', {
                theme: 'snow',
                modules: {
                    imageResize: {
                        displaySize: true,
                    },
                    toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered' }, { 'list': 'bullet' }],  // Thêm chỉnh list
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'align': [] }], // Căn lề (trái, phải, giữa, đều)
                        ['blockquote', 'code-block'], // Thêm blockquote và code block
                        ['image'],
                        ['link',], // Thêm liên kết
                        ['clean'] // Xóa định dạng
                    ]
                }
            });
        }

        function DeletePromotion(id) {
            const url = `/api/promotions/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const selectedProducts = $("#products").val() || [];
            const selectedGifts = $("#gifts").val() || [];

            // if(selectedProducts.length == 0 ){
            //     AlertResponse("Vui lòng chọn ít nhất một sản phẩm!", 'warning');
            //     return;
            // }

            if(!$("#name").val()?.trim()){
                AlertResponse("Vui lòng chọn nhập tên chương trình khuyến mãi", 'warning');
                return;
            }

            if (selectedProducts.length == 0) {
                AlertResponse("Vui lòng chọn ít nhất 1 sản phẩm mua", 'warning');
                return;
            }

            if (selectedGifts.length == 0) {
                AlertResponse("Vui lòng chọn ít nhất 1 sản phẩm tặng", 'warning');
                return;
            }

            const gifts = selectedGifts.map(productId => {
                return {
                    ProductId: productId,
                    Quantity: parseInt($(`.gift-quantity[data-id="${productId}"]`).val()) || 1
                };
            });

            const products = selectedProducts.map(productId => {
                return {
                    ProductId: productId,
                    Quantity: parseInt($(`.gift-quantity[data-id="${productId}"]`).val()) || 1
                };
            });

            const price = parseCurrency($("#totalAmount").val()?.trim() || "0");

            const data = {
                id: id !== "" ? id : null,
                name: $("#name").val()?.trim(),
                description: $("#descr").val()?.trim(),
                totalAmount: price,
                // extraGift: window.editor.root.innerHTML,
                startDate: moment($("#startDate").val()).format(),
                expiryDate: moment($("#expiryDate").val()).format(),
                isActive: $("#isActive").val() === "true",
                gifts: gifts,
                products: products,
            };

            const url = id !== "" ? `/api/promotions/${id}` : '/api/promotions';
            const method = id !== "" ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'error')
                    }
                    $("#modal-promotion").modal("toggle");
                },
                error: function (err) {
                    AlertResponse('Thao tác thất bại!', 'error');
                }
            });
        }
    </script>
}
