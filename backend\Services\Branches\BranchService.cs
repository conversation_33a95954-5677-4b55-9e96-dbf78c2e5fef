﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Branches;
using MiniAppCore.Entities.Products;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Branches;

namespace MiniAppCore.Services.Branches
{
    public class BranchService(IUnitOfWork unitOfWork, IWebHostEnvironment env, IHttpContextAccessor httpContextAccessor, IMapper mapper) : Service<Branch>(unitOfWork), IBranchService
    {
        private readonly string _uploadPath = "uploads/images/branches";
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public async Task<PagedResult<Branch>> GetPage(BranchQueryParameters query)
        {
            var branchQuery = _repository.AsQueryable().AsNoTracking();

            // Lọc theo City hoặc ProvinceId
            if (!string.IsNullOrWhiteSpace(query.City))
            {
                branchQuery = branchQuery.Where(b =>
                    !string.IsNullOrEmpty(b.ProvinceId) &&
                    b.ProvinceId.ToLower().Contains(query.City.ToLower()));
            }
            else if (!string.IsNullOrEmpty(query.ProvinceId))
            {
                branchQuery = branchQuery.Where(b => b.ProvinceId == query.ProvinceId);
            }

            // Lọc theo Ward hoặc WardId
            if (!string.IsNullOrWhiteSpace(query.Ward))
            {
                branchQuery = branchQuery.Where(b =>
                    !string.IsNullOrEmpty(b.WardId) &&
                    b.WardId.ToLower().Contains(query.Ward.ToLower()));
            }
            else if (!string.IsNullOrEmpty(query.WardId))
            {
                branchQuery = branchQuery.Where(b => b.WardId == query.WardId);
            }

            // Lọc theo District hoặc DistrictId
            if (!string.IsNullOrWhiteSpace(query.District))
            {
                branchQuery = branchQuery.Where(b =>
                    !string.IsNullOrEmpty(b.DistrictId) &&
                    b.DistrictId.ToLower().Contains(query.District.ToLower()));
            }
            else if (!string.IsNullOrEmpty(query.DistrictId))
            {
                branchQuery = branchQuery.Where(b => b.DistrictId == query.DistrictId);
            }

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                var keyword = query.Keyword;
                branchQuery = branchQuery.Where(x =>
                    !string.IsNullOrEmpty(x.PhoneNumber) && x.PhoneNumber.Contains(keyword) ||
                    !string.IsNullOrEmpty(x.FullAddress) && x.FullAddress.Contains(keyword) ||
                    x.Name.Contains(keyword)
                );
            }

            // Trạng thái hoạt động
            if (query.IsActive.HasValue)
            {
                branchQuery = branchQuery.Where(b => b.Status == query.IsActive.Value);
            }

            var totalItems = await branchQuery.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)query.PageSize);

            // Lấy dữ liệu từ database trước
            var branches = await branchQuery
                .OrderBy(x => x.CreatedDate)
                .Skip(query.Skip)
                .Take(query.PageSize)
                .ToListAsync(); // ✅ Chuyển dữ liệu vào bộ nhớ trước khi xử lý

            branches.ForEach(x =>
            {
                x.Image = $"{hostUrl}/{_uploadPath}/{x.Image}";
            });

            return new PagedResult<Branch>()
            {
                Data = branches,
                TotalPages = totalPages,
            };
        }

        public override async Task<Branch?> GetByIdAsync(string id)
        {
            var branch = await base.GetByIdAsync(id);
            if (branch != null)
            {
                branch.Image = string.IsNullOrEmpty(branch.Image) ? string.Empty : $"{hostUrl}/{_uploadPath}/{branch.Image}";
            }
            return branch;
        }

        public override async Task<IEnumerable<Branch>> GetAllAsync()
        {
            var allBranches = await _repository.AsQueryable().AsNoTracking().ToListAsync();
            allBranches.ToList().ForEach(x =>
            {
                x.Image = $"{hostUrl}/{_uploadPath}/{x.Image}";
            });
            return allBranches;
        }

        public async Task<int> CreateAsync(BranchRequest dto)
        {
            var branch = mapper.Map<Branch>(dto);

            // Xử lý upload ảnh
            if (dto.files != null && dto.files.Any())
            {
                branch.Image = await ProcessUpload(dto.files);
            }

            // Xử lý chi nhánh mặc định
            if (dto.IsDefault)
            {
                await UpdateDefaultBranches();
            }
            await MappingBranchLocation(branch, dto);
            return await base.CreateAsync(branch);
        }

        public async Task<int> UpdateAsync(string id, BranchRequest dto)
        {
            var branch = await _repository.FindByIdAsync(id);

            if (branch == null)
            {
                throw new Exception("Không tìm thấy chi nhánh!");
            }

            // Cập nhật thông tin từ DTO
            mapper.Map(dto, branch);

            // Xử lý upload ảnh mới nếu có
            if (dto.files != null && dto.files.Any())
            {
                // Xóa ảnh cũ trước khi lưu ảnh mới
                if (!string.IsNullOrEmpty(branch.Image))
                {
                    RemoveOldImages(branch.Image);
                }

                branch.Image = await ProcessUpload(dto.files);
            }

            // Xử lý chi nhánh mặc định
            if (dto.IsDefault)
            {
                await UpdateDefaultBranches(id);
            }
            await MappingBranchLocation(branch, dto);
            return await base.UpdateAsync(branch);
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var branch = await _repository.FindByIdAsync(id);

            if (branch == null)
            {
                return 0;
            }

            // Xóa ảnh trước khi xóa chi nhánh
            if (!string.IsNullOrEmpty(branch.Image))
            {
                RemoveOldImages(branch.Image);
            }
            return await base.DeleteByIdAsync(id);
        }

        public async Task<int> AssignProductToBranches(string productId, List<string> branchIds)
        {
            // Get the repository for ProductBranch
            var productBranchRepo = unitOfWork.GetRepository<ProductStock>();

            // Get existing product-branch relationships for this product
            var existingBranches = await productBranchRepo.AsQueryable()
                .Where(pb => pb.ProductId == productId)
                .ToListAsync();

            // Identify branches to remove (exists in DB but not in the new list)
            var branchesToRemove = existingBranches
                .Where(pb => !branchIds.Contains(pb.BranchId))
                .ToList();

            // Identify branches to add (exists in the new list but not in DB)
            var existingBranchIds = existingBranches.Select(pb => pb.BranchId).ToList();
            var branchesToAdd = branchIds
                .Where(branchId => !existingBranchIds.Contains(branchId))
                .Select(branchId => new ProductStock
                {
                    ProductId = productId,
                    BranchId = branchId
                })
                .ToList();

            // Remove branches that are no longer associated
            if (branchesToRemove.Any())
            {
                productBranchRepo.DeleteRange(branchesToRemove);
            }

            // Add new branch associations
            if (branchesToAdd.Any())
            {
                productBranchRepo.AddRange(branchesToAdd);
            }

            // Save changes and return the total number of affected records
            //await unitOfWork.SaveChangesAsync(); // Commit sau
            return branchesToRemove.Count + branchesToAdd.Count;
        }

        public async Task<List<Branch>> GetBranchesByProductId(string productId)
        {
            var productBranchRepo = unitOfWork.GetRepository<ProductStock>();

            // Get all branch IDs associated with this product
            var branchIds = await productBranchRepo.AsQueryable()
                .Where(pb => pb.ProductId == productId)
                .Select(pb => pb.BranchId)
                .ToListAsync();

            if (!branchIds.Any())
            {
                return new List<Branch>();
            }

            // Get the actual branch objects
            var branches = await _repository.AsQueryable()
                .Where(b => branchIds.Contains(b.Id))
                .ToListAsync();

            // Format images with correct URL
            branches.ForEach(branch =>
            {
                branch.Image = string.IsNullOrEmpty(branch.Image)
                    ? string.Empty
                    : $"{hostUrl}/{_uploadPath}/{branch.Image}";
            });

            return branches;
        }

        public async Task<List<Branch>> GetBranchesByIds(List<string?> branchIds)
        {
            var branches = await _repository.AsQueryable().AsNoTracking()
                .Where(b => branchIds.Contains(b.Id))
                .ToListAsync();

            // Format images with correct URL
            branches.ForEach(branch =>
            {
                branch.Image = string.IsNullOrEmpty(branch.Image)
                    ? string.Empty
                    : $"{hostUrl}/{_uploadPath}/{branch.Image}";
            });

            return branches;
        }

        public async Task<int> RemoveAllBranchesFromProduct(string productId)
        {
            var productBranchRepo = unitOfWork.GetRepository<ProductStock>();

            // Get all branch associations for this product
            var productBranches = await productBranchRepo.AsQueryable()
                .Where(pb => pb.ProductId == productId)
                .ToListAsync();

            if (!productBranches.Any())
            {
                return 0;
            }

            // Remove all associations
            productBranchRepo.DeleteRange(productBranches);
            //await unitOfWork.SaveChangesAsync(); // Commit sau

            return productBranches.Count;
        }

        public async Task<int> RemoveProductFromBranches(string productId, List<string> branchIds)
        {
            if (branchIds == null || !branchIds.Any())
            {
                return 0;
            }

            var productBranchRepo = unitOfWork.GetRepository<ProductStock>();

            // Get specific branch associations to remove
            var productBranchesToRemove = await productBranchRepo.AsQueryable()
                .Where(pb => pb.ProductId == productId && branchIds.Contains(pb.BranchId))
                .ToListAsync();

            if (!productBranchesToRemove.Any())
            {
                return 0;
            }

            // Remove the specified associations
            productBranchRepo.DeleteRange(productBranchesToRemove);
            await unitOfWork.SaveChangesAsync();

            return productBranchesToRemove.Count;
        }

        #region branch mapping location

        private async Task MappingBranchLocation(Branch branch, BranchRequest request)
        {
            branch.ProvinceName = (await Locator.GetCityByLocationId(request.ProvinceId))?.Name;
            branch.DistrictName = (await Locator.GetDistrictByLocationId(request.ProvinceId, request.DistrictId))?.Name;
            branch.WardName = (await Locator.GetWardByLocationId(request.DistrictId, request.WardId))?.Name;
            branch.FullAddress = Locator.FormatFullAddress(request.StreetLine, branch.WardName, branch.DistrictName, branch.ProvinceName);
        }

        private async Task UpdateDefaultBranches(string? excludeId = null)
        {
            // Lấy tất cả chi nhánh đang đặt là mặc định (trừ chi nhánh hiện tại nếu có)
            var query = _repository.AsQueryable().Where(x => x.IsDefault);

            if (!string.IsNullOrEmpty(excludeId))
            {
                query = query.Where(x => x.Id != excludeId);
            }

            var defaultBranches = await query.ToListAsync();

            // Cập nhật tất cả các chi nhánh khác không còn là mặc định
            foreach (var branch in defaultBranches)
            {
                branch.IsDefault = false;
            }

            if (defaultBranches.Any())
            {
                _repository.UpdateRange(defaultBranches);
                await unitOfWork.SaveChangesAsync();
            }
        }

        #endregion

        #region Xử lý ảnh

        private async Task<string> ProcessUpload(List<IFormFile> files)
        {
            if (files == null || !files.Any())
            {
                return string.Empty;
            }

            var savePath = Path.Combine(env.WebRootPath, _uploadPath);

            // Đảm bảo thư mục tồn tại
            if (!Directory.Exists(savePath))
            {
                Directory.CreateDirectory(savePath);
            }

            var fileResult = await Helpers.FileHandler.SaveFiles(files, savePath);
            return string.Join(",", fileResult);
        }

        private void RemoveOldImages(string imageList)
        {
            if (string.IsNullOrEmpty(imageList))
            {
                return;
            }

            var images = imageList.Split(",", StringSplitOptions.RemoveEmptyEntries)
                .Select(x => Path.Combine(env.WebRootPath, _uploadPath, x))
                .ToList();

            Helpers.FileHandler.RemoveFiles(images);
        }

        #endregion
    }
}
