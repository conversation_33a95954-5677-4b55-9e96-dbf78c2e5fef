﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Events;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events;
using MiniAppCore.Models.Responses.Events;

namespace MiniAppCore.Services.Events.EventRegistrations
{
    public interface IEventRegistrationService : IService<EventRegistration>
    {
        Task<PagedResult<EventRegistration>> GetPaged(RequestQuery query, string eventId);

        Task<int> RegisterEvent(string eventId, string userZaloId, EventRegistrationDTO registration);
        Task<EventRegistrationResponse> CheckInAsync(string checkInCode, string eventId);
        Task<List<EventRegistrationResponse>> CheckInMultipleAsync(List<string> codes, string eventId);
        Task<int> Cancel(string eventId, string userZaloId);
        Task<int> CancelByCode(string eventId, string code);
        Task<byte[]> ExportToExcelAsync(string eventId);
    }

}
