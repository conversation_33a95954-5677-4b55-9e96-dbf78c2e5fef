﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.LuckyWheels;
using MiniAppCore.Models.DTOs.LuckyWheels;
using MiniAppCore.Models.Requests.GamePrizes;
using MiniAppCore.Models.Responses.Gamification.GamePrizes;

namespace MiniAppCore.Services.Gamifications.GamePrizes
{
    public interface IGamePrizeService : IService<GamePrize>
    {
        // GamePrize CRUD
        Task<int> CreateAsync(GamePrizeRequest gamePrizeRequest);
        Task<int> UpdateAsync(string id, GamePrizeRequest gamePrizeRequest);
        Task<List<GamePrizeResponse>> GetGamePrizesByGameId(string gameId); // dành cho response vòng quay trả về
        Task<List<GamePrizeDetailResponse>> GetGamePrizesDetailByGameId(string gameId); // danh cho response danh sách giải thưởng
        Task<List<GamePrizeConfigDTO>> GetGameConfiguredPrizesByGameId(string gameId); // danh cho admin cấu hình
        Task<List<ConfiguredGamePrizeDTO>> GetGamePrizesConfiguredByGameId(string gameId); // dành cho logic xử lý vòng quay

        // GamePrizeConfig CRUD
        Task<int> DeleteGamePrizeConfigsByGameId(string gameId);
        Task<int> CreateListGamePrizeConfigAsync(string gameId, List<GamePrizeConfigDTO> gamePrize);
    }
}
