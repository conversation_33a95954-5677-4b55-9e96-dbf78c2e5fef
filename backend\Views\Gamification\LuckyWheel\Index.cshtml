﻿﻿@{
    ViewData["Title"] = "Quản lý Vòng Quay May Mắn";
}

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON>h sách vòng quay</h4>
                <p class="mb-0">
                    Danh sách vòng quay sẽ hiển thị các thông tin về các vòng quay hiện có và các chi tiết kèm theo.
                </p>
            </div>
            <button type="button" class="btn btn-primary mt-2" onclick="GetFormLuckyWheel()">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>
    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3">
                    <i class="ri-filter-3-line mr-2"></i>Bộ lọc
                </h6>
                <div class="row align-items-end pb-4">
                    <!-- Tìm kiếm -->
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm theo tên</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Nhập tên vòng quay..." />
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <!-- Trạng thái -->
                    <div class="col-md-3">
                        <label for="filterStatus" class="form-label">Trạng thái</label>
                        <select id="filterStatus" class="form-select">
                            <option value="">Tất cả</option>
                            <option value="true">Đang hoạt động</option>
                            <option value="false">Ngừng hoạt động</option>
                        </select>
                    </div>

                    <!-- Thời gian -->
                    <div class="col-md-3">
                        <label for="filterTime" class="form-label">Thời gian</label>
                        <select id="filterTime" class="form-select">
                            <option value="">Tất cả</option>
                            <option value="active">Đang diễn ra</option>
                            <option value="upcoming">Sắp diễn ra</option>
                            <option value="expired">Đã kết thúc</option>
                        </select>
                    </div>

                    <!-- Nút hành động -->
                    <div class="col-md-3 text-end">
                        <button type="button" id="btnSearch" class="btn btn-primary me-2">
                            <i class="ri-search-line me-1"></i> Tìm kiếm
                        </button>
                        <button type="button" id="btnReset" class="btn btn-outline-secondary">
                            <i class="ri-refresh-line me-1"></i> Đặt lại
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="wheel-table" class="table-responsive rounded mb-3">
            <table id="list-wheel" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-wheel" data-bs-backdrop="static" data-bs-keyboard="true" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Khởi tạo bảng và các sự kiện
            GetListWheel();
            $('#search').on('input', search);

            // Sự kiện cho nút tìm kiếm và reset
            $('#btnSearch').on('click', search);
            $('#btnReset').on('click', resetFilters);

            // Thêm sự kiện change cho các select filter
            $('#filterStatus, #filterTime, #filterPoints').on('change', function () {
                // Auto tìm kiếm sau khi thay đổi lựa chọn
                table.ajax.reload();
            });

            // Thêm sự kiện change cho date filters
            $('#filterStartDate, #filterEndDate').on('change', function () {
                validateDateRange();
                table.ajax.reload();
            });
        });

        function resetFilters() {
            $('#search').val('');
            $('#filterStatus').val('');
            $('#filterTime').val('');
            $('#filterPoints').val('');
            $('#filterStartDate').val('');
            $('#filterEndDate').val('');
            search();
        }

        function validateDateRange() {
            const startDate = $('#filterStartDate').val();
            const endDate = $('#filterEndDate').val();

            if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
                AlertResponse('Ngày bắt đầu không thể sau ngày kết thúc', 'warning');
                $('#filterEndDate').val('');
            }
        }

        function InitialEditor() {
            window.quill = new Quill('#editor', {
                theme: 'snow',
                modules: {
                    imageResize: {
                        displaySize: true,
                    },
                    toolbar: [
                        [{ 'header': [1, 2, 3, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'align': [] }],
                        ['blockquote', 'code-block'],
                        ['link'],
                        ['clean']
                    ]
                }
            });
        }

        function ShowPreview(input) {
            $("#preview").html("");
            if (!event) return;
            const files = input.files;
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = (e) => {
                    const previewItem = $(` <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height: 170px;"> `);
                    if (file.type.startsWith('image/')) {
                        const image = $(` <img src="${e.target.result}" class="card-img-top h-100" alt="Hình ảnh" style="object-fit:contain" /> `);
                        previewItem.append(image);
                    } else if (file.type.startsWith('video/')) {
                        const video = $(` <video src="${e.target.result}" class="card-img-top h-100" style="object-fit:contain" controls></video> `);
                        previewItem.append(video);
                    }
                    const removeButton = $('<span class="btn-preview-remove">x</span>');
                    removeButton.click(function () {
                        $(this).parent().remove();
                        const currentFiles = Array.from(event.target.files);
                        const newFiles = currentFiles.filter(f => f !== file);
                    });
                    previewItem.append(removeButton);
                    $("#preview").append(previewItem);
                };
            }
        }

        function GetListWheel() {
            table = new DataTable("#list-wheel", {
                searching: false,
                sort: false,
                pageLength: 10,
                responsive: true,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const status = $("#filterStatus").val();
                    const timeFilter = $("#filterTime").val();
                    const pointsFilter = $("#filterPoints").val();
                    const startDate = $("#filterStartDate").val();
                    const endDate = $("#filterEndDate").val();

                    $.ajax({
                        url: '/api/LuckyWheels',
                        type: 'GET',
                        data: {
                            page: page,
                            pageSize: data.length,
                            keyword: keyword,
                            status: status,
                            timeFilter: timeFilter,
                            pointsFilter: pointsFilter,
                            startDate: startDate,
                            endDate: endDate
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => {
                                const status = item.isActive
                                    ? '<span class="badge bg-success">Hoạt động</span>'
                                    : '<span class="badge bg-danger">Ngừng hoạt động</span>';

                                const actions = `
                                                                <div class="flex align-items-center list-user-action">
                                                                    <button class="btn btn-sm btn-primary" onclick="GetFormLuckyWheel('${item.id}')" data-toggle="tooltip" title="Chỉnh sửa">
                                                                        <i class="ri-edit-line fs-6 mr-0"></i>
                                                                    </button>
                                                                    <button class="btn btn-sm btn-warning ms-2" onclick="DeleteLuckyWheel('${item.id}')" data-toggle="tooltip" title="Xóa">
                                                                        <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                                    </button>
                                                                </div>
                                                            `;

                                return {
                                    index: data.start + index + 1,
                                    name: item.name,
                                    endDate: FormatDateTime(item.expiryDate),
                                    startDate: FormatDateTime(item.startDate),
                                    requiredPoints: item.requiredPoints || 0,
                                    status: status,
                                    remainingSpins: item.remainingSpins || 0,
                                    actions: actions
                                };
                            });

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.totalPages * data.length || 0,
                                    recordsFiltered: response.totalPages * data.length || 0,
                                    data: formattedData
                                });
                            }, 200);
                        },
                        error: function (error) {
                            console.error("Lỗi khi tải dữ liệu vòng quay:", error);
                            callback({
                                draw: data.draw,
                                recordsTotal: 0,
                                recordsFiltered: 0,
                                data: []
                            });
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "index", width: "5%" },
                    { title: "Tên vòng quay", data: "name", width: "20%" },
                    { title: "Ngày bắt đầu", data: "startDate", className: "text-center", width: "12%" },
                    { title: "Ngày hết hạn", data: "endDate", className: "text-center", width: "12%" },
                    // { title: "Lượt còn lại", data: "remainingSpins", className: "text-center", width: "10%" },
                    { title: "Điểm yêu cầu", data: "requiredPoints", className: "text-center", width: "10%" },
                    { title: "Trạng thái", data: "status", className: "text-center", width: "10%" },
                    { title: "Thao tác", data: "actions", className: "text-center", width: "15%" }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                },
                drawCallback: function () {
                    // Re-enable tooltips after redraw
                    $('[data-toggle="tooltip"]').tooltip();
                }
            });
        }

        function GetFormLuckyWheel(id) {
            const url = id ? `/LuckyWheel/Detail/${id}` : "/LuckyWheel/Create";
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#spinner").html('');
                    $("#modal-content").html(data);
                    $("#modal-wheel").modal("show");

                    // Initialize Select2 on any select elements if needed
                    $('.select2-prizes').select2({
                        theme: 'bootstrap-5',
                        dropdownParent: $('#modal-wheel')
                    });
                },
                error: function (error) {
                    $("#spinner").html('');
                    console.error("Lỗi khi tải form vòng quay:", error);
                }
            });
        }

        function HandleSaveOrUpdate(id) {
            if (!validateForm()) {
                return false;
            }

            const formData = new FormData();

            const startDateStr = $("#startDate").val();
            const expiryDateStr = $("#expiryDate").val();

            if (startDateStr && expiryDateStr) {
                const startDate = new Date(startDateStr);
                const expiryDate = new Date(expiryDateStr);

                if (startDate >= expiryDate) {
                    AlertResponse("Ngày bắt đầu phải nhỏ hơn ngày hết hạn!", "warning");
                    return false;
                }
            } else {
                AlertResponse("Vui lòng chọn ngày bắt đầu và ngày hết hạng!", "warning");
                return false;
            }

            // Gán từng field form vào FormData
            formData.append("id", id || "");
            formData.append("name", $("#name").val());
            formData.append("description", window.quill.root.innerHTML);
            formData.append("requiredPoints", $("#requiredPoints").val());
            formData.append("isActive", $("#status").val() === "true");
            formData.append("startDate", startDateStr);
            formData.append("expiryDate", expiryDateStr);

            // Auto add spin points configuration
            formData.append("enableAutoSpinPoints", $("#enableAutoSpinPoints").val() === "true");
            formData.append("spinPointsToAdd", $("#spinPointsToAdd").val() || "0");
            // Auto add spin points configuration
            formData.append("enableAutoSpinPoints", $("#enableAutoSpinPoints").val() === "true");
            formData.append("spinPointsToAdd", $("#spinPointsToAdd").val() || "0");

            // Thu thập giải thưởng và thêm vào FormData
            const prizes = CollectGamePrizes();
            formData.append("gamePrizesString", JSON.stringify(prizes));

            // Hình ảnh nếu có
            const imageFile = $("#pics")[0].files[0];
            if (imageFile) {
                formData.append("image", imageFile);
            }

            // URL và method
            const url = id ? `/api/LuckyWheels/${id}` : "/api/LuckyWheels";
            const method = id ? "PUT" : "POST";

            // Gửi request
            $.ajax({
                url: url,
                type: method,
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    AlertResponse(response.message, `${response.code === 0 ? 'success' : "error"}`);
                    table.ajax.reload();
                    $("#modal-wheel").modal("toggle");
                },
                error: function (error) {
                    AlertResponse(`Đã xảy ra lỗi khi ${id ? "cập nhật" : "thêm mới"} vòng quay`, 'error');
                }
            });
        }

        function DeleteLuckyWheel(id) {
            const url = `/api/Luckywheels/${id}`
            DeleteItem(url);
        }
    </script>
}
