﻿using MiniAppCore.Models.Responses.Affiliates;

namespace MiniAppCore.Services.Affiliates.Referrals
{
    public interface IReferralService
    {
        /// <summary>
        /// Lấy cây đa cấp của một thành viên
        /// </summary>
        /// <param name="userZaloId">ID Zalo của thành viên gốc</param>
        /// <param name="maxDepth">Độ sâu tối đa của cây (mặc định 5)</param>
        /// <returns>Cây đa cấp</returns>
        Task<ReferralTreeResponse?> GetReferralTreeAsync(string userZaloId, int maxDepth = 5);

        /// <summary>
        /// Lấy thống kê đa cấp của một thành viên
        /// </summary>
        /// <param name="userZaloId">ID Zalo của thành viên</param>
        /// <returns>Thống kê đa cấp</returns>
        Task<ReferralStatisticsResponse?> GetReferralStatisticsAsync(string userZaloId);

        /// <summary>
        /// Lấy danh sách thành viên được giới thiệu trực tiếp
        /// </summary>
        /// <param name="userZaloId">ID Zalo của thành viên giới thiệu</param>
        /// <returns>Danh sách thành viên được giới thiệu trực tiếp</returns>
        Task<List<ReferralTreeResponse>> GetDirectReferralsAsync(string userZaloId);

        /// <summary>
        /// Lấy đường dẫn từ gốc đến một thành viên trong cây đa cấp
        /// </summary>
        /// <param name="userZaloId">ID Zalo của thành viên</param>
        /// <returns>Đường dẫn từ gốc đến thành viên</returns>
        Task<List<ReferralTreeResponse>> GetReferralPathAsync(string userZaloId);
    }
}
