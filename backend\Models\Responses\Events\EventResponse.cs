﻿using MiniAppCore.Enums;

namespace MiniAppCore.Models.Responses.Events
{
    public class EventResponse
    {
        public string Id { get; set; } = default!;
        public string Title { get; set; } = default!;
        public string Content { get; set; } = default!;
        public string? Banner { get; set; }
        public List<string> Images { get; set; } = new();

        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }

        public EEvent Type { get; set; }
        public string? MeetingLink { get; set; }
        public string? GoogleMapURL { get; set; }

        public string? Address { get; set; }

        public bool IsActive { get; set; }
        public EEventStatus Status { get; set; }

        public bool IsCheckIn { get; set; }
        public bool IsRegister { get; set; } = false;

        public string? CheckInCode { get; set; }
    }

    public class EventDetailResponse : EventResponse
    {
        public List<EventGiftResponse> Gifts { get; set; } = new();

        public List<EventSponsorResponse> Sponsors { get; set; } = new();

        public List<EventTierResponse> Tiers { get; set; } = new();
    }

    public class EventGiftResponse
    {
        public string ProductId { get; set; } = default!;
        public string Name { get; set; } = default!;
        public List<string>? Images { get; set; }
        public decimal Price { get; set; }
        public int Quantity { get; set; }
    }

    public class EventSponsorResponse
    {
        public string SponsorId { get; set; } = default!;
        public string SponsorName { get; set; } = default!;
        public string? Introduction { get; set; }

        public string? Image { get; set; }
        public string? WebsiteURL { get; set; }

        public string? TierId { get; set; }
        public string? TierName { get; set; }
        public string? TierImage { get; set; }
    }

    public class EventTierResponse
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Image { get; set; }

        public List<EventSponsorByTierResponse> Sponsors { get; set; } = new();
    }

    public class EventSponsorByTierResponse
    {
        public string Id { get; set; } = default!;
        public string Name { get; set; } = default!;
        public string? Introduction { get; set; }

        public string? Image { get; set; }
        public string? WebsiteURL { get; set; }
    }
}
