﻿namespace MiniAppCore.Models.DTOs.InvoiceTemplates
{
    public class InvoiceTemplateVariables
    {
        public string? Notes { get; set; }
        public string? OrderId { get; set; }
        public string? PhoneNumber { get; set; }
        public string? MembershipName { get; set; }
        public string? DeliveryAddress { get; set; }
        public decimal Total { get; set; }
        public decimal ShippingFee { get; set; }
        public decimal DiscountAmount { get; set; }
        public DateTime CreatedDate { get; set; }
        public string? ReceiverPhoneNumber { get; set; }
        public string? ReceiverName { get; set; }
        public List<InvoiceProductItem> ProductList { get; set; } = new();
    }

    public class InvoiceProductItem
    {
        public long Quantity { get; set; }
        public string? ProductName { get; set; }
        public decimal Price { get; set; }
        public decimal SubTotal => Price * Quantity;
    }
}
