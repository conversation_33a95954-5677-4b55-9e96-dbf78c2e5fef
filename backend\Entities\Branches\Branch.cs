﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Branches
{
    public class Branch : BaseEntity
    {
        public required string Name { get; set; }

        public string? ProvinceId { get; set; }
        public string? ProvinceName { get; set; }

        public string? DistrictId { get; set; }
        public string? DistrictName { get; set; }

        public string? WardId { get; set; }
        public string? WardName { get; set; }

        public string? StreetLine { get; set; }
        public string? FullAddress { get; set; }

        public string? Latitude { get; set; }
        public string? Longitude { get; set; }
        public string? PhoneNumber { get; set; }

        public string? Image { get; set; }
        public string? Description { get; set; }
        public string? GoogleMapURL { get; set; }

        public bool Status { get; set; } = false;
        public bool IsDefault { get; set; } = true;
        public bool IsOpenHoliday { get; set; } = true;

        public DateTime OpeningTime { get; set; }
        public DateTime ClosingTime { get; set; }
    }
}
