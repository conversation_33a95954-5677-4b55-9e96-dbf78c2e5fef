﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Orders
{
    public class OrderDetail : BaseEntity
    {
        public long Quantity { get; set; }
        public required string OrderId { get; set; }
        public required string ProductId { get; set; }
        public string? Note { get; set; }
        public string? RefCode { get; set; }
        public string? VariantId { get; set; }
        public bool IsUseForCommission { get; set; } = false;

        public decimal OriginalPrice { get; set; } // giá gốc mua
        public decimal DiscountPrice { get; set; } // giá giảm mua
        public decimal TotalPrice => OriginalPrice * Quantity; // Tổng
    }
}
