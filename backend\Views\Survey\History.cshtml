﻿@{
    ViewData["Title"] = "Lịch sử phản hồi kh<PERSON> sát";
}

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Lịch sử phản hồi kh<PERSON>o sát</h4>
            </div>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">T<PERSON><PERSON> kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="T<PERSON><PERSON> theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-3">
                        <label for="filter-survey">Khảo sát</label>
                        <select id="filter-survey" class="form-control select2-ajax">
                            <option value="">Tất cả</option>
                        </select>
                    </div>

                    <div class="col-6">
                        <div class="input-daterange input-group" id="date-range">
                            <input type="date" class="form-control" id="date-from" placeholder="Từ ngày" />
                            <span class="input-group-text">đến</span>
                            <input type="date" class="form-control" id="date-to" placeholder="Đến ngày" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="history-table" class="table-responsive rounded mb-3">
            <table id="list-history" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div class="modal fade" id="surveyResultModal" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Lấy ngày hôm nay
            var today = moment();
            var startOfMonth = today.clone().startOf('month').format('YYYY-MM-DD');
            var endOfMonth = today.clone().endOf('month').format('YYYY-MM-DD');

            // Đặt giá trị cho các input
            $('#date-from').val(startOfMonth);
            $('#date-to').val(endOfMonth);

            // Khởi tạo Select2 cho dropdown survey
            initializeSelect2();

            // Khởi tạo datatable
            GetResponseHistory();

            // Sự kiện xử lý
            $('#search').on('input', function () {
                table.ajax.reload();
            });

            $('#filter-survey').on('change', function () {
                table.ajax.reload();
            });

            $('#date-from, #date-to').on('change', function () {
                table.ajax.reload();
            });
        });

        function initializeSelect2() {
            $('#filter-survey').select2({
                placeholder: 'Chọn khảo sát',
                allowClear: false,
                width: '100%',
                ajax: {
                    url: '/api/Surveys',
                    dataType: 'json',
                    delay: 250,
                    data: function (params) {
                        return {
                            keyword: params.term || '',
                            page: params.page || 1,
                            pagesize: 10,
                            status: 1 // Lấy các survey đã active
                        };
                    },
                    processResults: function (response, params) {
                        params.page = params.page || 1;

                        if (response.code === 0) {
                            return {
                                results: response.data.map(function (survey) {
                                    return {
                                        id: survey.id,
                                        text: survey.title
                                    };
                                }),
                                pagination: {
                                    more: params.page < response.totalPages
                                }
                            };
                        } else {
                            return {
                                results: []
                            };
                        }
                    },
                    cache: true
                },
                minimumInputLength: 0
            });
        }

        function GetResponseHistory() {
            table = new DataTable("#list-history", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const surveyId = $("#filter-survey").val();
                    const dateFrom = $("#date-from").val();
                    const dateTo = $("#date-to").val();

                    $.ajax({
                        url: '/api/Surveys/History',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            surveyId: surveyId,
                            fromDate: dateFrom,
                            toDate: dateTo
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                rowIndex: data.start + index + 1,
                                surveyTitle: item.surveyTitle || 'Untitled Survey',
                                userName: item.userZaloName || item.displayName || 'Anonymous',
                                submissionDate: FormatDateTime(item.submissionDate),
                                actions: `<div class="d-flex align-items-center justify-content-center list-action">
                                                        <a onclick="GetDetail('${item.id}')" class="badge bg-info mx-1" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                            <i class="ri-eye-line fs-6 mr-0"></i>
                                                        </a>
                                                    </div>`
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 500);
                        },
                        error: function () {
                            callback({
                                draw: data.draw,
                                recordsTotal: 0,
                                recordsFiltered: 0,
                                data: []
                            });
                            $("#spinner").hide();
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "rowIndex", className: 'text-center' },
                    { title: "Khảo sát", data: "surveyTitle" },
                    { title: "Người trả lời", data: "userName" },
                    { title: "Thời gian", data: "submissionDate" },
                    { title: "Thao tác", data: "actions", className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetDetail(id) {
            const url = `@Url.Action("SubmissionDetail", "Survey")/${id}`
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#surveyResultModal").modal("toggle");
                }
            })
        }

        // function viewResponseDetails(responseId) {
        //     $('#response-details').html('<div class="text-center"><div class="spinner-border" role="status"></div></div>');
        //     $('#modal-response').modal('show');

        //     $.ajax({
        //         url: `/api/SurveyResponses/${responseId}`,
        //         type: 'GET',
        //         success: function (response) {
        //             if (response.code === 0) {
        //                 renderResponseDetails(response.data);
        //             } else {
        //                 $('#response-details').html('<div class="alert alert-danger">Không thể tải chi tiết phản hồi.</div>');
        //             }
        //         },
        //         error: function () {
        //             $('#response-details').html('<div class="alert alert-danger text-center">Đã xảy ra lỗi khi tải chi tiết phản hồi.</div>');
        //         }
        //     });
        // }

        // function renderResponseDetails(data) {
        //     let html = `
        //                                 <div class="mb-3">
        //                                     <h5 class="text-primary">${data.surveyTitle || 'Untitled Survey'}</h5>
        //                                     <div class="text-muted">
        //                                         <small>Người trả lời: ${data.userZaloName || data.displayName || 'Anonymous'}</small>
        //                                         <small class="mx-2">|</small>
        //                                         <small>Thời gian: ${FormatDate(data.submissionDate)}</small>
        //                                     </div>
        //                                 </div>`;

        //     if (data.phoneNumber) {
        //         html += `<div class="mb-3">Số điện thoại: ${data.phoneNumber}</div>`;
        //     }

        //     html += `<div class="response-items">`;

        //     // Lặp qua các câu hỏi và câu trả lời
        //     if (data.answers && data.answers.length > 0) {
        //         data.answers.forEach(item => {
        //             html += `
        //                                         <div class="card mb-3">
        //                                             <div class="card-body">
        //                                                 <h6 class="card-title">${item.questionText}</h6>
        //                                                 <p class="card-text">${formatAnswer(item)}</p>
        //                                             </div>
        //                                         </div>
        //                                     `;
        //         });
        //     } else {
        //         html += `<div class="alert alert-info">Không có dữ liệu chi tiết phản hồi.</div>`;
        //     }

        //     html += `</div>`;
        //     $('#response-details').html(html);
        // }

        function formatAnswer(answer) {
            // Format dựa vào loại câu hỏi
            switch (answer.questionType) {
                case 'MultipleChoice':
                case 'Dropdown':
                    return answer.selectedText || 'Không có câu trả lời';
                case 'CheckboxList':
                    if (answer.selectedTexts && answer.selectedTexts.length > 0) {
                        return answer.selectedTexts.map(t => `<span class="badge bg-primary me-1">${t}</span>`).join(' ');
                    }
                    return 'Không có câu trả lời';
                case 'TextBox':
                case 'TextArea':
                    return answer.textValue || 'Không có câu trả lời';
                case 'Rating':
                    return `<div class="rating-display">${'★'.repeat(answer.ratingValue)} ${'☆'.repeat(5 - answer.ratingValue)}</div>`;
                default:
                    return 'Không có câu trả lời';
            }
        }
    </script>
}
