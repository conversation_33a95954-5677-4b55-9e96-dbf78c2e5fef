﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Memberships;

namespace MiniAppCore.Services.Memberships.Addresses
{
    public class MembershipAddressService(IUnitOfWork unitOfWork, IMapper mapper) : Service<MembershipAddress>(unitOfWork), IMembershipAddressService
    {
        public async Task<PagedResult<MembershipAddress>> GetAddressesByUserZaloId(string userUserZaloId, RequestQuery query)
        {
            var membershipAddresses = _repository.AsQueryable().Where(x => x.UserZaloId == userUserZaloId);
            if (!string.IsNullOrEmpty(query.Keyword))
            {

            }
            var totalItems = await membershipAddresses.CountAsync();
            var totalPage = (int)Math.Ceiling((double)totalItems / query.PageSize);
            var items = await membershipAddresses.OrderBy(x => x.CreatedDate).Skip(query.Skip).Take(query.PageSize).ToListAsync();
            return new PagedResult<MembershipAddress>()
            {
                Data = items,
                TotalPages = totalPage
            };
        }

        public async Task<MembershipAddress?> GetDefaultAddress(string userZaloId)
        {
            // Tìm địa chỉ mặc định
            var defaultAddress = await _repository.AsQueryable()
                                                  .AsNoTracking()
                                                  .FirstOrDefaultAsync(x => x.UserZaloId == userZaloId && x.IsDefault);

            if (defaultAddress != null)
            {
                return defaultAddress;
            }

            // Nếu không có địa chỉ mặc định, tìm địa chỉ đầu tiên và đánh dấu là mặc định
            var firstAddress = await _repository.AsQueryable()
                                                .AsNoTracking()
                                                .OrderBy(x => x.CreatedDate)
                                                .FirstOrDefaultAsync(x => x.UserZaloId == userZaloId);
            if (firstAddress != null)
            {
                firstAddress.IsDefault = true;
            }

            return firstAddress;
        }

        public async Task<int> CreateAsync(string userZaloId, MembershipAddressRequest entity)
        {
            ValidateAddress(entity);
            var (wardName, districtName, provinceName) = await GetLocationNamesAsync(entity);

            var address = mapper.Map<MembershipAddress>(entity);
            address.UserZaloId = userZaloId;
            address.FullAddress = FormatFullAddress(address.StreetLine, wardName, districtName, provinceName);

            if (entity.IsDefault)
            {
                await EnsureSingleDefaultAddressAsync(userZaloId, address.Id);
            }

            if (!(await _repository.AsQueryable().AnyAsync(x => x.UserZaloId == userZaloId)))
            {
                entity.IsDefault = true;
            }

            return await base.CreateAsync(address);
        }

        public async Task<int> UpdateAsync(string id, MembershipAddressRequest entity)
        {
            ValidateAddress(entity);

            var exist = await GetByIdAsync(id) ?? throw new NotFoundException(404, "Không tìm thấy địa chỉ này!");

            var (wardName, districtName, provinceName) = await GetLocationNamesAsync(entity);
            mapper.Map(entity, exist);
            exist.FullAddress = FormatFullAddress(exist.StreetLine, wardName, districtName, provinceName);

            if (entity.IsDefault)
            {
                await EnsureSingleDefaultAddressAsync(exist.UserZaloId, exist.Id);
            }

            return await base.UpdateAsync(exist);
        }

        private void ValidateAddress(MembershipAddressRequest entity)
        {
            if (string.IsNullOrEmpty(entity.City) || string.IsNullOrEmpty(entity.Ward) || string.IsNullOrEmpty(entity.District))
            {
                throw new CustomException(400, "Địa chỉ không hợp lệ! Vui lòng kiểm tra lại!");
            }
        }

        private async Task<(string wardName, string districtName, string provinceName)> GetLocationNamesAsync(MembershipAddressRequest entity)
        {
            var city = await Locator.GetCityByLocationId(entity.City);
            var district = await Locator.GetDistrictByLocationId(entity.City, entity.District);
            var ward = await Locator.GetWardByLocationId(entity.District, entity.Ward);

            return (ward?.Name ?? "", district?.Name ?? "", city?.Name ?? "");
        }

        private string FormatFullAddress(string street, string ward, string district, string province)
        {
            return $"{street}, {ward}, {district}, {province}".Trim(',', ' ');
        }

        private async Task EnsureSingleDefaultAddressAsync(string userZaloId, string? excludedId = null)
        {
            var otherAddresses = _repository.AsQueryable()
                                      .Where(x => x.UserZaloId == userZaloId && (excludedId == null || x.Id != excludedId))
                                      .ToList();

            if (otherAddresses.Any(x => x.IsDefault))
            {
                otherAddresses.ForEach(x => x.IsDefault = false);
                _repository.UpdateRange(otherAddresses);
            }
            await unitOfWork.SaveChangesAsync();
        }
    }
}
