﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Services.OmniTool.TokenManager;
using System.Security.Claims;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "ADMIN, SUPER_ADMIN")]
    public class HelpersController(ILogger<HelpersController> logger,
                                   IWebHostEnvironment env,
                                   UserManager<ApplicationUser> userManager,
                                   ITokenManagerService tokenManagerService) : ControllerBase
    {
        [HttpGet("RefreshToken")]
        public async Task<IActionResult> RefreshToken()
        {
            try
            {
                var check = await tokenManagerService.RefreshToken();
                var access_token = await tokenManagerService.GetAccessToken();
                var refresh_token = await tokenManagerService.GetRefreshToken();
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = new
                    {
                        AccessToken = access_token,
                        RefreshToken = refresh_token
                    }
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex.Message);
                return Ok(new { ex.Message });
            }
        }


        [HttpDelete("DeleteImage")]
        public IActionResult RemoveImage([FromBody] ImageDTO model)
        {
            try
            {
                var savePath = Path.Combine(env.WebRootPath, "uploads/images/editor");
                FileHandler.DeleteFile($"{savePath}/{model.FileName}");
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa ảnh thành công",
                });
            }
            catch (Exception ex)
            {
                return Ok(new
                {
                    Code = 1,
                    ex.Message
                });
            }
        }

        [HttpPost("UploadImage")]
        public async Task<IActionResult> UploadImage([FromForm] ImageUpload model)
        {
            try
            {
                var savePath = Path.Combine(env.WebRootPath, "uploads/images/editor");
                var file = await FileHandler.SaveFile(model.Image, savePath);
                return Ok(new
                {
                    Code = 0,
                    Data = new ImageDTO()
                    {
                        FileName = file,
                        FileUrl = $"{Request.Scheme}://{Request.Host.Value}/uploads/images/editor/{file}"
                    }
                });
            }
            catch (Exception ex)
            {
                return Ok(new
                {
                    Code = 1,
                    ex.Message
                });
            }
        }

        [HttpPost("ChangePassword")]
        public async Task<IActionResult> ChangePassword([FromForm] string currentPassword, [FromForm] string newPassword)
        {
            try
            {
                var email = User.Claims.FirstOrDefault(x => x.Type == ClaimTypes.Email)?.Value;
                if (string.IsNullOrEmpty(email)) return Ok(new
                {
                    Message = "Không tìm thấy tài khoản!"
                });
                var user = await userManager.FindByEmailAsync(email);
                if (user == null) return Ok(new
                {
                    Message = "Không tìm thấy tài khoản!"
                });
                var result = await userManager.ChangePasswordAsync(user, currentPassword, newPassword);
                return Ok(new
                {
                    result.Succeeded
                });
            }
            catch (Exception)
            {
                return Ok(new
                {
                    Message = "Có lỗi xảy ra!",
                });
            }
        }
    }

    public class ImageDTO
    {
        public string? FileName { get; set; }
        public string? FileUrl { get; set; }
    }

    public class ImageUpload
    {
        public required IFormFile Image { get; set; }
    }
}
