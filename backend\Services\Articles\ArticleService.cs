﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Articles;
using MiniAppCore.Entities.Products;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Articles;
using OfficeOpenXml.Packaging.Ionic.Zip;

namespace MiniAppCore.Services.Articles
{
    public class ArticleService(IUnitOfWork unitOfWork, IMapper mapper, IWebHostEnvironment env) : OrderedEntityService<Article>(unitOfWork), IArticleService
    {
        protected override string OrderColumnName => "OrderPriority";

        public async Task<PagedResult<Article>> GetPage(ArticleQueryParams query)
        {
            var articles = _repository.AsQueryable();
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                var keyword = query.Keyword.ToLower().Trim();
                articles = articles.Where(p => p.Title.ToLower().Contains(keyword) || p.Content.ToLower().Contains(keyword));
            }

            if (query.Status.HasValue)
            {
                articles = articles.Where(p => p.Status == query.Status.Value);
            }

            if (!string.IsNullOrEmpty(query.CategoryId))
            {
                articles = articles.Where(p => p.CategoryId == query.CategoryId);
            }

            if (query.StartDate != null)
            {
                articles = articles.Where(p => p.CreatedDate >= query.StartDate);
            }

            if (query.EndDate != null)
            {
                articles = articles.Where(p => p.CreatedDate <= query.EndDate);
            }

            var totalItems = await articles.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);
            var items = await articles
                                .OrderBy(x => x.OrderPriority)
                                .ThenByDescending(x => x.CreatedDate)
                                .Skip(query.Skip)
                                .Take(query.PageSize)
                                .ToListAsync();
            return new PagedResult<Article>()
            {
                Data = items,
                TotalPages = totalPages,
            };
        }

        public async Task<int> CreateAsync(ArticleRequest model)
        {
            Article article = mapper.Map<Article>(model);

            await unitOfWork.BeginTransactionAsync();

            try
            {
                ValidateImages(null, null, model.Images);

                if (string.IsNullOrEmpty(model.Author))
                {
                    article.Author = string.Empty;
                }
                article.CategoryId = model.CategoryId;
                article.Images = await ProcessUploadImages(model.Images);
                article.BannerImage = await ProcessUploadBanner(model.BannerImage);

                await base.PrepareInsertFirstAsync(article);

                //await base.CreateAsync(article);
                _repository.Add(article);

                return await unitOfWork.CommitAsync();
            } 
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public async Task<int> UpdateAsync(string id, ArticleRequest model)
        {
            var article = await GetByIdAsync(id);
            if (article == null)
            {
                throw new NotFoundException(200, "Không tìm thấy tin tức này!");
            }

            await unitOfWork.BeginTransactionAsync();

            try
            {

                ValidateImages(article.Images, model.RemovedOldImages, model.Images);

                int currentOrder = article.OrderPriority;

                mapper.Map(model, article);
                article.CategoryId = model.CategoryId;

                await base.ReorderAsync(article, currentOrder, article.OrderPriority);

                if (string.IsNullOrEmpty(model.Author))
                {
                    article.Author = string.Empty;
                }

                if (model.Images != null)
                {
                    var newImages = await ProcessUploadImages(model.Images);
                    article.Images = string.Join(',', article.Images, newImages).Trim(',');
                }

                if (model.RemovedOldImages.Any())
                {
                    foreach (var imageUrl in model.RemovedOldImages)
                    {
                        if (imageUrl != null)
                        {
                            RemoveOldImage(imageUrl, "/uploads/images/articles");

                            var imageList = article.Images?.Split(',')
                                    .Where(img => !string.IsNullOrEmpty(img) && img != imageUrl)
                                    .ToArray();

                            article.Images = imageList != null ? string.Join(",", imageList) : null;
                        }
                    }
                }

                if (!string.IsNullOrEmpty(model.RemovedOldBanner))
                {
                    RemoveOldImage(model.RemovedOldBanner ?? "", "uploads/images/articles");
                    article.BannerImage = "";
                }
                else
                {
                    if (model.BannerImage != null)
                    {
                        RemoveOldImage(article.BannerImage ?? "", "uploads/images/articles");
                        article.BannerImage = await ProcessUploadBanner(model.BannerImage);
                    }
                }

                article.BannerImage = ValidateBanner(article.Images ?? "", article.BannerImage);

                //await base.UpdateAsync(article);
                _repository.Update(article);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var article = await GetByIdAsync(id);
            if (article == null)
            {
                throw new CustomException(200, "Không tìm thấy tin tức này!");
            }
            RemoveOldImage(article.Images ?? "", "uploads/images/articles");
            RemoveOldImage(article.BannerImage ?? "", "uploads/images/articles");

            await base.ReorderAfterDeleteAsync(article.OrderPriority);

            return await base.DeleteByIdAsync(id);
        }

        #region "File Handler"

        private async Task<string> ProcessUploadImages(List<IFormFile>? images)
        {
            var stringFiles = string.Empty;
            var savePath = Path.Combine(env.WebRootPath, "uploads/images/articles");

            if (images != null)
            {
                var fileResult = await FileHandler.SaveFiles(images, savePath);
                stringFiles = string.Join(",", fileResult);
            }

            return stringFiles;
        }

        private async Task<string> ProcessUploadBanner(IFormFile? bannerImage)
        {
            var stringFiles = string.Empty;
            var savePath = Path.Combine(env.WebRootPath, "uploads/images/articles");

            if (bannerImage != null)
            {
                var fileResult = await FileHandler.SaveFile(bannerImage, savePath);
                stringFiles = string.Join(",", fileResult);
            }

            return stringFiles;
        }

        private void RemoveOldImage(string listImage, string rootFolder)
        {
            var images = listImage.Split(",")
                                  .Select(x => Path.Combine(env.WebRootPath, rootFolder, x))
                                  .ToList();
            FileHandler.RemoveFiles(images);
        }

        private void ValidateImages(string? currentImages, List<string>? removedOldImages, List<IFormFile>? newImages)
        {
            // Tách danh sách ảnh hiện tại
            var existingImages = (currentImages ?? "")
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .ToList();

            // Loại bỏ những ảnh bị remove
            if (removedOldImages != null && removedOldImages.Any())
            {
                existingImages = existingImages
                    .Where(img => !removedOldImages.Contains(img))
                    .ToList();
            }

            // 3Đếm ảnh mới upload
            var newImagesCount = newImages?.Count ?? 0;

            // Tính tổng
            var finalImagesCount = existingImages.Count + newImagesCount;

            // ít nhất 1 ảnh
            if (finalImagesCount == 0)
                throw new CustomException("Bài viết phải có ít nhất một ảnh. Vui lòng thêm ảnh trước khi lưu.");

            // tối đa 5 ảnh
            if (finalImagesCount > 5)
                throw new CustomException("Tối đa 5 ảnh cho một tin tức.");
        }

        private string ValidateBanner(string images, string banner)
        {
            if (string.IsNullOrEmpty(banner) && !string.IsNullOrEmpty(images))
            {
                var imageList = images.Split(',')
                                              .Where(img => !string.IsNullOrWhiteSpace(img))
                                              .ToArray();
                if (imageList.Length > 0)
                {
                    return imageList[0];
                }
            }

            return banner;
        }

        #endregion

    }
}
