﻿﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3">Cài đặt thông tin khách hàng</h4>
                <p class="mb-0">
                    <PERSON><PERSON> sách các thông tin mặc định khách hàng sẽ có<br />
                </p>
            </div>
            <button type="button" class="btn btn-primary mt-2" onclick="GetFormMemberShipExtendDefault()">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i><PERSON><PERSON> lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search_member" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search_member" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="tag-table" class="table-responsive rounded mb-3">
            <table id="list-tag" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-tag" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-dialog-centered modal-lg"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListMemberShipExtendDefault();
            $('#search_member').on('keypress', function (e) {
                if (e.which === 13) {
                    table.ajax.reload();
                }
            });
        });

        function GetListMemberShipExtendDefault() {
            table = new DataTable("#list-tag", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search_member").val();
                    $.ajax({
                        url: '@Url.Action("GetPageMemberShipExtendDefault", "SystemSetting")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                        },
                        success: function (response) {
                            console.log(response)
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: item.attribute,
                                2: item.content,
                                5: FormatDate(item.createdDate),
                                6: `<div class="d-flex align-items-center justify-content-center list-action">
                                                                <a onclick="GetFormMemberShipExtendDefault('${item.id}')" class="badge badge-info mx-1" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                                    <i class="ri-edit-line fs-6 mr-0"></i>
                                                                </a>

                                                                <a onclick="DeleteMemberShipExtendDefault('${item.id}')" class="badge bg-warning mx-1" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                                    <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                                </a>
                                                            </div>`,
                                status: item.isActive ? "Đã kích hoạt" : "Chưa kích hoạt"
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên trường", data: 1 },
                    { title: "Giá trị của trường", data: 2 },
                    { title: "Trạng thái", data: "status" },
                    { title: "Thao tác", data: 6, className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormMemberShipExtendDefault(id) {
            const url = id ? `@Url.Action("MembershipExtendDefault", "SystemSetting")/${id}` : "@Url.Action("CreateMembershipExtendDefault", "SystemSetting")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-tag").modal("toggle");
                }
            })
        }

        function DeleteMemberShipExtendDefault(id) {
            if (id === '') return;

            swal({
                title: "Cảnh báo",
                text: "Dữ liệu sẽ không được phục hồi với thao tác này!",
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Hủy",
                        value: null,
                        visible: true,
                        className: "",
                        closeModal: true,
                    },
                    confirm: {
                        text: "Xác nhận",
                        value: true,
                        visible: true,
                        className: "",
                        closeModal: true,
                    },
                },
            }).then((result) => {
                if (result) {
                    $.ajax({
                        url: `@Url.Action("DeleteMemberShipExtendDefault", "SystemSetting")/${id}`,
                        type: 'GET',
                        dataType: "json",
                        success: function (res) {
                            if (res.code == 0) {
                                $("#modal-tag").modal("hide");
                                table.ajax.reload();
                                AlertResponse(res.message, 'success');
                            } else {
                                AlertResponse(res.message, 'error');
                            }

                        },
                        error: function (error) {
                            AlertResponse("Đã có lỗi xảy ra vui lòng liên hệ kĩ thuật", 'error');
                        }
                    })
                }
            });
        }

        async function HandleSaveOrUpdate(id) {
            // Kiểm tra các trường bắt buộc
            if (!$("#attribute_member").val().trim()) {
                AlertResponse("Bạn vui lòng nhập tên trường!", 'warning');
                return;
            }

            if (!$("#attribute_name").val().trim()) {
                AlertResponse("Bạn vui lòng nhập tên trường đầy đủ!", 'warning');
                return;
            }

            const type = $("#type_member").val();
            let min = null, max = null, content = null;

            // Validate theo loại trường
            min = $("#attribute_min").val().trim();
            max = $("#attribute_max").val().trim();
            switch (type) {
                case "text":
                    if (!min) {
                        AlertResponse("Bạn vui lòng nhập kí tự tối thiểu!", 'warning');
                        return;
                    }
                    if (!max) {
                        AlertResponse("Bạn vui lòng nhập kí tự tối đa!", 'warning');
                        return;
                    }
                    break;
                case "number":
                    if (isNaN(Number(min)) || isNaN(Number(max))) {
                        AlertResponse("Giá trị nhỏ nhất và lớn nhất phải là số!", 'warning');
                        return;
                    }
                    if (Number(min) > Number(max)) {
                        AlertResponse("Giá trị nhỏ nhất không được lớn hơn giá trị lớn nhất!", 'warning');
                        return;
                    }
                    break;
                case "option":
                    content = $("#content_member").val().trim();
                    if (!content) {
                        AlertResponse("Bạn vui lòng nhập ít nhất 1 giá trị tùy chọn!", 'warning');
                        return;
                    }
                    break;
            }

            // Lấy trạng thái kích hoạt
            const isActive = $("#is_active").is(":checked");

            // Chuẩn bị dữ liệu gửi đi
            var dataPost = {
                id: $("#id_member").val(),
                attribute: $("#attribute_member").val().trim(),
                attributeName: $("#attribute_name").val().trim(),
                type: type,
                min: min,
                max: max,
                content: content,
                isActive: isActive
            }

            try {
                // Hiển thị loading nếu cần
                // $("#loading").show();

                const path = $("#id_member").val() === "" ? "CreateNewMembershipExtendDefault" : "UpdateMembershipExtendDefault";
                const url = `/SystemSetting/${path}`;

                const response = await $.ajax({
                    url: url,
                    type: 'POST',
                    dataType: "json",
                    data: dataPost
                });

                if (response.code == 0) {
                    $("#modal-tag").modal("hide");
                    AlertResponse(response.message, 'success');
                    table.ajax.reload();
                } else {
                    AlertResponse(response.message || "Có lỗi xảy ra, vui lòng thử lại!", 'error');
                }
            } catch (error) {
                console.error("Error:", error);
                AlertResponse("Đã có lỗi xảy ra vui lòng liên hệ kĩ thuật", 'error');
            } finally {
                // Ẩn loading nếu cần
                // $("#loading").hide();
            }
        }
    </script>
}
