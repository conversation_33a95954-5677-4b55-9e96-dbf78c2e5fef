﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Services.OmniTool.TokenManager;
using MiniAppCore.Services.Orders.ShippingFees;
using MiniAppCore.Services.SystemSettings;
using MiniAppCore.Services.SystemSettings.ActionButtonConfigs;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class SystemSettingController(ISystemSettingService systemSettingService,
                                         ITokenManagerService tokenManagerService,
                                         IActionButtonService actionButtonService,
                                         IShippingFeeService shippingFeeService) : Controller
    {
        public async Task<IActionResult> Index()
        {
            var omniAccount = await systemSettingService.GetOmniAccountAsync();

            var linkPaymentGuide = await systemSettingService.GetLinkPaymentGuidAsync();

            var enterpriseInfomation = await systemSettingService.GetEnterpriseInformationAsync();

            var oaInfo = await tokenManagerService.GetAllInfo();

            return View((linkPaymentGuide, omniAccount, enterpriseInfomation, oaInfo));
        }

        #region MembershipExtenddefault

        public IActionResult MembershipExtendDefaults()
        {
            return View();
        }

        [HttpGet]
        public async Task<ActionResult> GetPageMemberShipExtendDefault([FromQuery] RequestQuery query)
        {
            try
            {
                var result = await systemSettingService.GetPageMemberShipExtendDefault(query);
                return Ok(new
                {
                    Code = 200,
                    Message = "Thành công",
                    result.Data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpGet("SystemSetting/CreateMembershipExtendDefault")]
        public async Task<IActionResult> CreateMembershipExtendDefault()
        {
            var member = new MembershipExtendDefault()
            {
                Id = string.Empty,
                Attribute = string.Empty,
                Content = string.Empty
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới trường mặc định";
            return PartialView("_MemberShipExtendDefaultDetail", member);
        }

        [HttpGet("SystemSetting/MembershipExtendDefault/{id}")]
        public async Task<IActionResult> MembershipExtendDefault(string id)
        {
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật trường mặc định";
            var result = await systemSettingService.GetMembershipExtendDefaultById(id);

            if (result == null)
            {
                return RedirectToAction("CreateMembershipExtendDefault");
            }
            return PartialView("_MemberShipExtendDefaultDetail", result);
        }

        [HttpGet("SystemSetting/DeleteMemberShipExtendDefault/{id}")]
        public async Task<IActionResult> DeleteMemberShipExtendDefault(string id)
        {
            var rs = await systemSettingService.DeleteMembershipExtendDefault(id);
            if (rs > 0)
            {
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa thẻ thành công!",
                });
            }
            return Ok(new
            {
                Code = -1,
                Message = "Xóa thẻ thất bại!",
            });

        }

        [HttpPost("SystemSetting/CreateNewMembershipExtendDefault")]
        public async Task<IActionResult> CreateNewMembershipExtendDefault(MembershipExtendDefault data)
        {
            var rs = await systemSettingService.CreateMembershipExtendDefault(data);
            if (rs > 0)
            {
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo dữ liệu thành công!",
                });
            }
            return Ok(new
            {
                Code = -1,
                Message = "Tạo dữ liệu thất bại",
            });

        }

        [HttpPost("SystemSetting/UpdateMembershipExtendDefault")]
        public async Task<IActionResult> UpdateMembershipExtendDefault(MembershipExtendDefault data)
        {
            var rs = await systemSettingService.UpdateMembershipExtendDefault(data);
            if (rs > 0)
            {
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật dữ liệu thành công!",
                });
            }
            return Ok(new
            {
                Code = -1,
                Message = "Cập nhật dữ liệu thất bại",
            });

        }

        #endregion

        #region Shipping fee config

        public IActionResult ShippingFeeConfig()
        {
            return View();
        }

        [HttpGet("SystemSetting/CreateShippingFeeConfig")]
        public IActionResult CreateShippingFeeConfig()
        {
            var article = new ShippingFeeConfig()
            {
                Id = string.Empty,
                MinOrderValue = 0,
                MaxOrderValue = 0,
                ShippingFee = 0,
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới trường dữ liệu";
            return PartialView("_ShippingFeeConfig", article);
        }

        [HttpGet("SystemSetting/UpdateShippingFeeConfig/{id}")]
        public async Task<IActionResult> UpdateShippingFeeConfig(string id)
        {
            var shippingFeeConfig = await shippingFeeService.GetByIdAsync(id);
            if (shippingFeeConfig == null)
            {
                return RedirectToAction("CreateShippingFeeConfig");
            }
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới trường dữ liệu";
            return PartialView("_ShippingFeeConfig", shippingFeeConfig);
        }

        #endregion

        #region Action Button Config

        public IActionResult ActionButtonConfig()
        {
            return View("ActionButtonConfig/Index");
        }

        [HttpGet("SystemSetting/ActionButtonConfig/Create")]
        public IActionResult CreateActionButtonConfig()
        {
            var buttonConfig = new ActionButtonConfig()
            {
                Id = string.Empty,
                IsActive = true,
                SortOrder = 1
            };
            ViewBag.Title = "Thêm mới nút hành động";
            ViewBag.Button = "Lưu";
            return PartialView("ActionButtonConfig/_ActionButtonConfig", buttonConfig);
        }

        [HttpGet("SystemSetting/ActionButtonConfig/{id}")]
        public async Task<IActionResult> UpdateActionButtonConfig(string id)
        {
            var buttonConfig = await actionButtonService.GetByIdAsync(id);
            if (buttonConfig == null)
            {
                return RedirectToAction("CreateActionButtonConfig");
            }
            ViewBag.Title = "Cập nhật nút hành động";
            ViewBag.Button = "Cập nhật";
            return PartialView("ActionButtonConfig/_ActionButtonConfig", buttonConfig);
        }

        #endregion
    }
}
