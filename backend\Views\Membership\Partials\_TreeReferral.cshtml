﻿@using MiniAppCore.Models.Responses.Affiliates
@model ReferralTreeResponse

@await Html.PartialAsync("Styles/_TreeReferralStyles")

<!-- Fullscreen Toggle Button for Modal -->
<button type="button" class="modal-fullscreen-btn" onclick="toggleFullscreen()" title="Toggle Fullscreen">
    <i class="fas fa-expand" id="fullscreen-icon"></i>
</button>

<div class="tree-container">
    <!-- Statistics Section -->
    <div class="statistics-section">
        @if (ViewBag.Statistics != null)
        {
            var stats = ViewBag.Statistics as ReferralStatisticsResponse;
            if (stats != null)
            {
                <div class="statistics-summary">
                    <h5><i class="fas fa-chart-network"></i> Thống kê cây đa cấp</h5>
                    <div class="mt-2 mb-3">
                        <small class="d-block text-truncate" title="@stats.UserZaloName">@stats.UserZaloName</small>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-value">@stats.TotalDirectReferrals</span>
                            <span class="stat-label">Trực tiếp</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">@stats.TotalIndirectReferrals</span>
                            <span class="stat-label">Gián tiếp</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">@stats.TotalReferrals</span>
                            <span class="stat-label">Tổng cộng</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">@stats.MaxDepth</span>
                            <span class="stat-label">Độ sâu tối đa</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">@stats.TotalCommissionEarned.ToString("N0")</span>
                            <span class="stat-label">Hoa hồng (VNĐ)</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">@stats.PendingCommission.ToString("N0")</span>
                            <span class="stat-label">Chờ thanh toán (VNĐ)</span>
                        </div>
                    </div>
                </div>
            }
        }
    </div>

    <!-- Tree Section -->
    <div class="tree-section">
        <div class="tree-scroll-wrapper">
            @if (Model != null)
            {
                @await Html.PartialAsync("Partials/_ReferralNode", Model)
            }
            else
            {
                <div class="no-referrals">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>Không có dữ liệu cây đa cấp</p>
                </div>
            }
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        // Xóa tất cả event handlers cũ để tránh duplicate
        $(document).off('click.treeCollapse', '.expand-toggle');
        $(document).off('click.treeHighlight', '.tree-node');

        // Khởi tạo collapse behavior
        initializeTreeCollapse();

        // Expand/collapse functionality với namespace
        $(document).on('click.treeCollapse', '.expand-toggle', function (e) {
            e.preventDefault();
            e.stopPropagation();

            const $this = $(this);
            const $node = $this.closest('.tree-node');
            const $children = $node.find('> .tree-children');
            const childCount = $children.find('> .tree-node').length;
            const isExpanded = $this.attr('data-expanded') === 'true';

            if (isExpanded) {
                // Collapse - Không dùng animation
                $children.hide();
                $this.html(`<i class="fas fa-plus-circle"></i> Mở rộng (${childCount})`);
                $this.attr('data-expanded', 'false');
            } else {
                // Expand - Không dùng animation
                $children.show();
                $this.html('<i class="fas fa-minus-circle"></i> Thu gọn');
                $this.attr('data-expanded', 'true');
            }
        });

        // Smooth scroll to clicked node
        $(document).on('click.treeHighlight', '.tree-node', function (e) {
            if ($(e.target).hasClass('expand-toggle') || $(e.target).closest('.expand-toggle').length) {
                return;
            }

            $(this).addClass('highlight');
            setTimeout(() => {
                $(this).removeClass('highlight');
            }, 1000);
        });

        // Initialize modal resize functionality
        initModalResize();

        // Listen for modal events to reapply styles
        $(document).on('shown.bs.modal', '.modal', function () {
            const modal = $(this);
            if (modal.find('.tree-container').length > 0) {
                initModalResize();
            }
        });
    });

    // Toggle fullscreen modal
    function toggleFullscreen() {
        const modal = $('.tree-container').closest('.modal');
        const icon = $('#fullscreen-icon');

        if (modal.hasClass('fullscreen')) {
            modal.removeClass('fullscreen');
            icon.removeClass('fa-compress').addClass('fa-expand');
        } else {
            modal.addClass('fullscreen');
            icon.removeClass('fa-expand').addClass('fa-compress');
        }
    }

    // Initialize modal resize functionality
    function initModalResize() {
        const modal = $('.tree-container').closest('.modal');

        if (modal.length > 0) {
            // Force apply modal styles
            const modalDialog = modal.find('.modal-dialog');
            const modalContent = modal.find('.modal-content');
            const modalBody = modal.find('.modal-body');

            // Apply styles directly via JavaScript to override Bootstrap
            modalDialog.css({
                'max-width': 'none',
                'width': '90vw',
                'height': '90vh',
                'margin': '2vh auto'
            });

            modalContent.css({
                'height': '100%',
                'resize': 'both',
                'overflow': 'auto',
                'min-width': '800px',
                'min-height': '600px',
                'max-width': 'none',
                'max-height': 'none'
            });

            modalBody.css({
                'height': 'calc(100% - 120px)',
                'overflow': 'hidden',
                'padding': '0',
                'max-height': 'none'
            });

            // Check if jQuery UI resizable is available
            if ($.fn.resizable) {
                // Make modal resizable with jQuery UI
                modal.find('.modal-content').resizable({
                    minWidth: 800,
                    minHeight: 600,
                    handles: 'n, e, s, w, ne, se, sw, nw',
                    start: function () {
                        $(this).css('max-width', 'none');
                    },
                    resize: function () {
                        // Adjust tree container height on resize
                        const modalHeight = $(this).height();
                        const headerHeight = $(this).find('.modal-header').outerHeight() || 60;
                        const treeHeight = modalHeight - headerHeight - 40;

                        $(this).find('.tree-container').css('max-height', treeHeight + 'px');
                        $(this).find('.tree-scroll-wrapper').css('max-height', (treeHeight - 100) + 'px');
                    }
                });
            } else {
                // Fallback: Enable CSS resize
                modal.find('.modal-content').css({
                    'resize': 'both',
                    'overflow': 'auto',
                    'min-width': '800px',
                    'min-height': '600px'
                });
            }

            // Show fullscreen button only in modal
            $('.modal-fullscreen-btn').show();

            // Hide fullscreen button when modal is closed
            modal.on('hidden.bs.modal', function () {
                $(this).removeClass('fullscreen');
                $('#fullscreen-icon').removeClass('fa-compress').addClass('fa-expand');
            });
        } else {
            // Hide fullscreen button if not in modal
            $('.modal-fullscreen-btn').hide();
        }
    }

    // Khởi tạo collapse behavior cho cây đa cấp
    function initializeTreeCollapse() {
// Clear any existing timers or animations
        $('.tree-children').stop(true, true);
        
        // Auto-collapse các node từ level 2 trở lên
        $('.tree-node').each(function () {
            const level = parseInt($(this).attr('class').match(/level-(\d+)/)?.[1] || 0);
            if (level >= 2) {
                const $node = $(this);
                const $children = $node.find('> .tree-children');
                const $toggle = $node.find('.expand-toggle').first();
                const childCount = $children.find('> .tree-node').length;

                if ($children.length > 0 && childCount > 0) {
                    // Tắt ngay lập tức, không animation
                    $children.hide();
                    $toggle.html(`<i class="fas fa-plus-circle"></i> Mở rộng (${childCount})`);
                    $toggle.attr('data-expanded', 'false');
                }
            }
        });

        // Tooltip for truncated text
        $('.member-name, .member-details').each(function () {
            if (this.scrollWidth > this.clientWidth) {
                $(this).attr('title', $(this).text());
            }
        });
    }
</script>