﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Events.SponsorshipTiers;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Services.Events.SponsorshipTiers;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class SponsorshipTierController(IHttpContextAccessor httpContextAccessor, ISponsorshipTierService tierService) : Controller
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Create()
        {
            var tier = new SponsorshipTier()
            {
                Id = string.Empty,
                TierName = string.Empty,
                Description = string.Empty,
            };

            ViewBag.Title = "Thêm mới hạng tài trợ";
            ViewBag.Button = "Lưu";

            return PartialView("_SponsorshipTier", tier);
        }

        public async Task<IActionResult> Detail(string id)
        {
            var result = await tierService.GetById(id);
            if (result == null) throw new CustomException(1, "Hạng tài trợ không khả dụng");

            ViewBag.Image = Tools.GetImage(hostUrl, result.Image, "uploads/images/sponsorshiptiers");

            ViewBag.Title = "Cập nhật hạng tài trợ";
            ViewBag.Button = "Cập nhật";

            return PartialView("_SponsorshipTier", result);
        }
    }
}
