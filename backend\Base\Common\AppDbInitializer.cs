﻿using MiniAppCore.Base.Database;
using MiniAppCore.Base.DataSeeder;

namespace MiniAppCore.Base.Common
{
    public static class AppDbInitializer
    {
        public static async Task InitializeAsync(this IHost app)
        {
            using var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var dbContext = services.GetRequiredService<ApplicationDbContext>();

            // 1. Seed base settings
            await BaseSettings.SeedAsync(dbContext);

            // 2. Seed user/role
            await UserAdmin.SeedAsync(services, dbContext);
        }
    }
}
