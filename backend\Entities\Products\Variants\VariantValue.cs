﻿namespace MiniAppCore.Entities.Products.Variants
{
    public class VariantValue
    {
        public string Id { get; set; } = Guid.NewGuid().ToString("N");
        public required string VariantId { get; set; }
        public required string PropertyId { get; set; }
        public required string PropertyValueId { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
