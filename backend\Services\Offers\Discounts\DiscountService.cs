﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Entities.Products;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Requests.Offers;
using MiniAppCore.Models.Responses.Products;

namespace MiniAppCore.Services.Offers.Discounts
{
    public class DiscountService(IUnitOfWork unitOfWork, IMapper mapper, IHttpContextAccessor httpContextAccessor) : Service<Discount>(unitOfWork), IDiscountService
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";
        private readonly IRepository<Product> _productRepo = unitOfWork.GetRepository<Product>();
        private readonly IRepository<DiscountItem> _discountItemRepo = unitOfWork.GetRepository<DiscountItem>();
        public async Task<int> CreateAsync(DiscountRequest request)
        {
            var discount = mapper.Map<Discount>(request);
            if (request.Products.Any())
            {
                var products = request.Products.Select(x => new DiscountItem()
                {
                    ProductId = x,
                    DiscountId = discount.Id
                });
                _discountItemRepo.AddRange(products);
            }
            return await base.CreateAsync(discount);
        }

        public async Task<int> UpdateAsync(string id, DiscountRequest request)
        {
            var existingDiscount = await GetByIdAsync(id);
            if (existingDiscount == null)
            {
                throw new CustomException(200, "Discount not found");
            }

            mapper.Map(request, existingDiscount);
            // Lấy danh sách DiscountItem cũ
            var existingDiscountItems = await _discountItemRepo.AsQueryable()
                .Where(di => di.DiscountId == id)
                .ToListAsync();

            // Danh sách ProductId mới từ request
            var newProductIds = request.Products ?? new List<string>();

            // Lọc ra các DiscountItem cần xóa (có trong DB nhưng không có trong request)
            var discountItemsToRemove = existingDiscountItems
                .Where(di => !newProductIds.Contains(di.ProductId))
                .ToList();

            // Lọc ra các ProductId cần thêm mới (có trong request nhưng không có trong DB)
            var discountItemsToAdd = newProductIds
                .Where(pid => !existingDiscountItems.Any(di => di.ProductId == pid))
                .Select(pid => new DiscountItem { ProductId = pid, DiscountId = id })
                .ToList();

            // Xóa DiscountItem không còn trong request
            if (discountItemsToRemove.Any())
            {
                _discountItemRepo.DeleteRange(discountItemsToRemove);
            }

            // Thêm mới DiscountItem từ request
            if (discountItemsToAdd.Any())
            {
                _discountItemRepo.AddRange(discountItemsToAdd);
            }
            return await base.UpdateAsync(existingDiscount);
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var existingDiscount = await GetByIdAsync(id);
            if (existingDiscount == null)
            {
                throw new Exception("Discount not found");
            }

            // Lấy danh sách DiscountItem liên quan đến Discount này
            var discountItems = await _discountItemRepo.AsQueryable()
                .Where(di => di.DiscountId == id)
                .ToListAsync();

            // Xóa các DiscountItem trước khi xóa Discount
            if (discountItems.Any())
            {
                _discountItemRepo.DeleteRange(discountItems);
            }

            // Xóa Discount
            return await base.DeleteAsync(existingDiscount);
        }

        public async Task<(IEnumerable<Discount>, IEnumerable<DiscountItem>)> GetDiscountsByProductIdsAsync(IEnumerable<string> productIds)
        {

            var discountItems = await _discountItemRepo.AsQueryable()
                                .Where(di => productIds.Contains(di.ProductId))
                                .ToListAsync();

            var discountIds = discountItems.Select(di => di.DiscountId).Distinct().ToList();

            var discounts = await _repository.AsQueryable()
                .Where(d => discountIds.Contains(d.Id)
                    && d.IsActive
                    && d.StartDate <= DateTime.Now
                    && d.ExpiryDate >= DateTime.Now)
                .ToListAsync();

            return (discounts, discountItems);
        }

        public async Task<Discount?> GetDiscountByProductIdAsync(string productId)
        {

            var discountItems = await _discountItemRepo.AsQueryable()
                                .Where(di => productId == di.ProductId)
                                .ToListAsync();

            var discountIds = discountItems.Select(di => di.DiscountId).Distinct().ToList();

            var discount = await _repository.AsQueryable()
                .FirstOrDefaultAsync(d => discountIds.Contains(d.Id)
                    && d.IsActive
                    && d.StartDate <= DateTime.Now
                    && d.ExpiryDate >= DateTime.Now);

            return discount;
        }

        public (short, decimal) CalculateDiscountedPrice(Product product, IEnumerable<DiscountItem> discountItems, IEnumerable<Discount> discounts)
        {
            short noDiscount = 0;
            // Lấy danh sách Discount áp dụng cho sản phẩm này
            var applicableDiscounts = discounts
                .Where(d => discountItems.Any(di => di.ProductId == product.Id && di.DiscountId == d.Id))
                .ToList();

            if (!applicableDiscounts.Any()) return (noDiscount, product.Price); // Không có discount

            // Tìm discount có lợi nhất cho khách hàng
            var bestDiscount = applicableDiscounts.OrderByDescending(d => d.DiscountValue).FirstOrDefault();
            if (bestDiscount == null) return (noDiscount, product.Price); // Không có discount hợp lệ
            if (bestDiscount.Type == 1)
            {
                decimal discountAmount = product.Price * (bestDiscount.DiscountValue / 100m);
                if (bestDiscount.MaxDiscountAmount > 0)
                {
                    discountAmount = Math.Min(discountAmount, bestDiscount.MaxDiscountAmount);
                }
                return (bestDiscount.Type, product.Price - discountAmount);
            }
            else if (bestDiscount.Type == 2)
            {
                return (bestDiscount.Type, Math.Max(0, product.Price - bestDiscount.DiscountValue));
            }

            return (noDiscount, product.Price);
        }

        public (short, decimal) CalculateDiscountedPrice(string productId, decimal originalPrice, IEnumerable<DiscountItem> discountItems, IEnumerable<Discount> discounts)
        {
            short noDiscount = 0;
            // Lấy danh sách Discount áp dụng cho sản phẩm này
            var applicableDiscounts = discounts
                .Where(d => discountItems.Any(di => di.ProductId == productId && di.DiscountId == d.Id))
                .ToList();

            if (!applicableDiscounts.Any()) return (noDiscount, originalPrice); // Không có discount

            // Tìm discount có lợi nhất cho khách hàng
            var bestDiscount = applicableDiscounts.OrderByDescending(d => d.DiscountValue).FirstOrDefault();
            if (bestDiscount == null) return (noDiscount, originalPrice); // Không có discount hợp lệ
            if (bestDiscount.Type == 1)
            {
                decimal discountAmount = originalPrice * (bestDiscount.DiscountValue / 100m);
                if (bestDiscount.MaxDiscountAmount > 0)
                {
                    discountAmount = Math.Min(discountAmount, bestDiscount.MaxDiscountAmount);
                }
                return (bestDiscount.Type, originalPrice - discountAmount);
            }
            else if (bestDiscount.Type == 2)
            {
                return (bestDiscount.Type, Math.Max(0, originalPrice - bestDiscount.DiscountValue));
            }

            return (noDiscount, originalPrice);
        }

        public async Task<IEnumerable<ProductItemResponse>> GetDiscountItems(string discountId)
        {
            var discountItems = await _discountItemRepo.AsQueryable()
                .Where(x => x.DiscountId == discountId)
                .ToListAsync();

            var productIds = discountItems
                .Where(x => !string.IsNullOrEmpty(x.ProductId))
                .Select(x => x.ProductId!)
                .Distinct()
                .ToList();

            if (!productIds.Any()) return new List<ProductItemResponse>();

            var products = await _productRepo.AsQueryable()
                .Where(p => productIds.Contains(p.Id))
                .ToListAsync();

            var result = products.Select(p => new ProductItemResponse
            {
                Id = p.Id,
                Name = p.Name,
                Price = p.Price,
                Images = string.IsNullOrEmpty(p.Images)
                    ? new List<string>()
                    : p.Images.Split(',').Select(x =>
                    {
                        var trimmed = x.Trim();
                        if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                            return trimmed;
                        else
                            return $"{hostUrl}/uploads/images/products/{trimmed}";
                    }).ToList()
            });

            return result;
        }
    }
}
