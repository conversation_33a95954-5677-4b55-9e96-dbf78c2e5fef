﻿namespace MiniAppCore.Models.Requests.Memberships
{
    public class MembershipVATRequest
    {
        public string? StreetLine { get; set; }
        public string? City { get; set; } // city Id
        public string? District { get; set; } // district Id
        public string? Ward { get; set; } // ward Id
        public string OwnerName { get; set; }
        public string? Email { get; set; }
        public string Type { get; set; } = "Personal";
        public string TaxCode { get; set; }
        public bool IsDefault { get; set; } = true;
    }
}
