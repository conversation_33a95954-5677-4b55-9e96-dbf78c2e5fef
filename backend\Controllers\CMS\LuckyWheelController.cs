﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.DTOs.LuckyWheels;
using MiniAppCore.Services.Gamifications.LuckyWheels;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class LuckyWheelController(ILuckyWheelService luckyWheelService) : Controller
    {
        private readonly string prefixViews = "/Views/Gamification/LuckyWheel";
        public IActionResult Index()
        {
            return View($"{prefixViews}/Index.cshtml");
        }

        public IActionResult History()
        {
            return View($"{prefixViews}/History.cshtml");
        }

        [HttpGet("LuckyWheel/HistoryDetail/{historyId}")]
        public async Task<IActionResult> HistoryDetail(string historyId)
        {
            try
            {
                var historyDetail = await luckyWheelService.GetClaimStatusReward(historyId);
                return PartialView($"{prefixViews}/_HistoryDetail.cshtml", historyDetail);
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Có lỗi xảy ra trong quá trình xử lý yêu cầu",
                });
            }
        }


        [HttpGet("LuckyWheel/Create")]
        public IActionResult Create()
        {
            ViewBag.Title = "Thêm mới vòng quay";
            ViewBag.Button = "Lưu";
            var luckyWheel = new LuckyWheelDTO()
            {
                Id = string.Empty,
                StartDate = DateTime.Now,
                ExpiryDate = DateTime.Now.AddMonths(1),
                IsActive = true,
            };
            return PartialView($"{prefixViews}/_LuckyWheel.cshtml", luckyWheel);
        }

        [HttpGet("LuckyWheel/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            var luckyWheel = await luckyWheelService.GetLuckyWheelDetailById(id);
            if (luckyWheel == null)
            {
                return RedirectToAction("Create");
            }

            if (!string.IsNullOrEmpty(luckyWheel.ImageUrl))
            {
                luckyWheel.ImageUrl = $"{Request.Scheme}://{Request.Host}/uploads/images/luckyWheels/{luckyWheel.ImageUrl}";
            }

            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật vòng quay";
            return PartialView($"{prefixViews}/_LuckyWheel.cshtml", luckyWheel);
        }
    }
}
