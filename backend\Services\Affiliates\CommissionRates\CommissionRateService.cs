﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Affiliates;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Exceptions;
using Newtonsoft.Json;

namespace MiniAppCore.Services.Affiliates.CommissionRates
{
    public class CommissionRateService(IUnitOfWork unitOfWork) : Service<AffiliateCommissionRate>(unitOfWork), ICommissionRateService
    {
        private readonly IRepository<Common> _commonRepository = unitOfWork.GetRepository<Common>();
        private readonly IRepository<Membership> _membershipRepository = unitOfWork.GetRepository<Membership>();

        public override async Task<int> CreateAsync(AffiliateCommissionRate entity)
        {
            var existingRates = await GetAllAsync();
            if (entity.IsActive && existingRates
                                    .Any(r => r.IsActive &&
                                              r.Level == entity.Level &&
                                              r.EffectiveDate.Date == entity.EffectiveDate.Date))
            {
                throw new CustomException("Mức hoa hồng đang hoạt động cho cấp này đã có sẵn.");
            }
            return await base.CreateAsync(entity);
        }

        public override async Task<int> UpdateAsync(string id, AffiliateCommissionRate entity)
        {
            var existingRate = await GetByIdAsync(id);
            if (existingRate == null)
            {
                throw new CustomException("Không tìm thấy cài đặt hoa hồng này.");
            }

            // Check for duplicate active level
            var allRates = await GetAllAsync();
            if (entity.IsActive && allRates
                                    .Any(r => r.IsActive &&
                                              r.Id != id &&
                                              r.Level == entity.Level &&
                                              r.EffectiveDate.Date == entity.EffectiveDate.Date))
            {
                throw new CustomException("Mức hoa hồng đang hoạt động cho cấp này đã có sẵn.");
            }

            // Update properties
            existingRate.Level = entity.Level;
            existingRate.Rate = entity.Rate;
            existingRate.IsActive = entity.IsActive;
            existingRate.EffectiveDate = entity.EffectiveDate;

            return await base.UpdateAsync(existingRate);
        }

        public async Task<List<AffiliateCommissionRate>> GetActiveRatesAsync()
        {
            var now = DateTime.Now;

            // Lấy tất cả bản ghi active với EffectiveDate <= now
            var activeRates = await _repository.AsQueryable()
                .Where(r => r.IsActive && r.EffectiveDate <= now)
                .ToListAsync();

            // Lấy bản ghi EffectiveDate lớn nhất từng cấp
            var latestRates = activeRates
                .GroupBy(r => r.Level)
                .Select(g => g.OrderByDescending(r => r.EffectiveDate).First())
                .OrderBy(r => r.Level)
                .ToList();

            return latestRates;
        }

        public async Task<List<AffiliateNode>> GetUserAffiliateLevelAsync(string sourceUserZaloId, int maxLevel = 10) // sourceUserId: có thể là userZaloId hoặc Id
        {
            var result = new List<AffiliateNode>();
            string? currentUserZaloId = sourceUserZaloId;
            int level = 0;

            while (level <= maxLevel && currentUserZaloId != null)
            {
                // Chỉ thêm vào danh sách khi không phải cấp 0
                if (level > 0)
                {
                    result.Add(new AffiliateNode
                    {
                        UserZaloId = currentUserZaloId,
                        Level = level
                    });
                }

                var user = await _membershipRepository.AsQueryable()
                    .Where(u => u.UserZaloId == currentUserZaloId)
                    .Select(u => new { u.ReferrerId })
                    .FirstOrDefaultAsync();

                if (user?.ReferrerId == null)
                    break;

                currentUserZaloId = user.ReferrerId;
                level++;
            }

            return result;
        }

        #region Old Commission Rate Config

        public async Task<decimal> GetCommissionConfigAsync()
        {
            var commissionConfig = await _commonRepository
                .AsQueryable()
                .FirstOrDefaultAsync(x => x.Name == "CommissionConfig");

            if (commissionConfig == null || string.IsNullOrEmpty(commissionConfig.Content))
                return 0m;

            try
            {
                return JsonConvert.DeserializeObject<decimal>(commissionConfig.Content);
            }
            catch
            {
                return 0m;
            }
        }

        public async Task<int> AddOrUpdateCommissionRateAsync(decimal percentage)
        {
            var commissionConfig = await _commonRepository
             .AsQueryable()
             .FirstOrDefaultAsync(x => x.Name == "CommissionConfig");
            if (commissionConfig == null)
            {
                commissionConfig = new Common
                {
                    Name = "CommissionConfig",
                    Content = JsonConvert.SerializeObject(percentage)
                };
                _commonRepository.Add(commissionConfig);
            }
            else
            {
                commissionConfig.Content = JsonConvert.SerializeObject(percentage);
                _commonRepository.Update(commissionConfig);
            }

            return await unitOfWork.SaveChangesAsync();
        }

        #endregion
    }

    public class AffiliateNode
    {
        public int Level { get; set; }
        public required string UserZaloId { get; set; }
    }
}
