﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace MiniAppCore.Controllers.API
{
    [Route("api/[controller]")]
    [ApiController]
    public class ZaloHelperApiController : ControllerBase
    {
        private readonly ILogger<ZaloHelperApiController> _logger;
        private string apiLocationUrl = "https://open.oapi.vn/location";
        private readonly HttpClient _httpClient;

        public ZaloHelperApiController(ILogger<ZaloHelperApiController> logger)
        {
            _logger = logger;
            _httpClient = new HttpClient();
        }

        [HttpPost("GetPhoneNumber")]
        public async Task<IActionResult> GetPhoneNumber(DataForm dataPost)
        {
            try
            {
                const string endpoint = "https://graph.zalo.me/v2.0/me/info";

                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("access_token", dataPost.accessToken);
                    client.DefaultRequestHeaders.Add("code", dataPost.tokenNumber);
                    client.DefaultRequestHeaders.Add("secret_key", dataPost.secretKey);

                    HttpResponseMessage response = await client.GetAsync(endpoint);

                    if (response.IsSuccessStatusCode)
                    {
                        string responseBody = await response.Content.ReadAsStringAsync();
                        return Ok(JsonConvert.DeserializeObject<ResponseZaloDTO>(responseBody));
                    }
                    else
                    {
                        return StatusCode((int)response.StatusCode);
                    }
                }
            }
            catch (Exception)
            {
                return StatusCode(500, "Lỗi server!");
            }
        }

        [HttpGet("GetCities")]
        public async Task<IActionResult> GetCity([FromQuery] string? keyword, [FromQuery] int page = 0, [FromQuery] int size = 63)
        {

            try
            {
                string endpoint = $"{apiLocationUrl}/provinces?page={page}&size={size}&query={keyword}";
                var response = await _httpClient.GetAsync(endpoint);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return Content(content, "application/json");
                }

                return StatusCode((int)response.StatusCode, await response.Content.ReadAsStringAsync());
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("GetDistricts")]
        public async Task<IActionResult> GetDistrict([FromQuery] string cityId, [FromQuery] string? keyword, [FromQuery] int page = 0, [FromQuery] int size = 30)
        {
            try
            {
                string endpoint = $"{apiLocationUrl}/districts/{cityId}?page={page}&size={size}&query={keyword}";

                var response = await _httpClient.GetAsync(endpoint);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return Content(content, "application/json");
                }

                return StatusCode((int)response.StatusCode, await response.Content.ReadAsStringAsync());
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("GetWards")]
        public async Task<IActionResult> GetWard([FromQuery] string districtId, [FromQuery] string? keyword, [FromQuery] int page = 0, [FromQuery] int size = 30)
        {
            try
            {
                string endpoint = $"{apiLocationUrl}/wards/{districtId}?page={page}&size={size}&query={keyword}";
                var response = await _httpClient.GetAsync(endpoint);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return Content(content, "application/json");
                }

                return StatusCode((int)response.StatusCode, await response.Content.ReadAsStringAsync());
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }
    }

    public class DataForm
    {
        public required string accessToken { get; set; }

        public required string tokenNumber { get; set; }

        public required string secretKey { get; set; }

        public string? City { get; set; }
        public string? District { get; set; }
        public string? Ward { get; set; }
        public string? Keyword { get; set; }
    }

    public class ResponseZaloDTO
    {
        public Dictionary<string, dynamic>? data { get; set; }
        public int error { get; set; }
        public string? message { get; set; }
    }

}
