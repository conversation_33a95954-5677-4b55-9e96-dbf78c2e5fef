﻿@model MiniAppCore.Models.Responses.Surveys.QuestionResponse

<div class="question-card card">
    <div class="card-body">
        <div class="question-title d-flex justify-content-between align-items-start">
            <div class="me-2 flex-grow-1">@Html.Raw(Model.QuestionTitle)</div>
            @await Html.PartialAsync("SurveySubmission/_QuestionTypeBadgePartial", Model.Type)
            @if (Model.IsRequied)
            {
                <span class="badge bg-danger question-badge" title="Bắt buộc">
                    <i class="ri-asterisk me-1"></i> Bắt buộc
                </span>
            }
        </div>

        @if (Model.UserResponses?.Any() == true)
        {
            @if (Model.Type?.ToLower() == "multichoice")
            {
                <ul class="list-group list-group-flush">
                    @foreach (var response in Model.UserResponses)
                    {
                        var selectedOption = Model.ListOption.FirstOrDefault(o => o.Value == response.AnswerId || o.AnswerId == response.AnswerId);
                        if (selectedOption != null)
                        {
                            <li class="list-group-item response-text mb-2">
                                @selectedOption.Key
                                @if (selectedOption.IsInput && !string.IsNullOrEmpty(response.InputValue))
                                {
                                    <span>(@response.InputValue)</span>
                                }
                            </li>
                        }
                    }
                </ul>
            }
            else
            {
                @foreach (var response in Model.UserResponses)
                {
                    var selectedOption = Model.ListOption.FirstOrDefault(o => o.Value == response.AnswerId || o.AnswerId == response.AnswerId);
                    if (selectedOption != null)
                    {
                        <div class="response-text mb-2">@selectedOption.Key</div>
                    }
                }
            }
        }
    </div>
</div>
