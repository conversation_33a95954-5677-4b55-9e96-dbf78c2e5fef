using MediatR;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Database;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Exceptions;

namespace MiniAppCore.Features.CustomEntity.Handlers
{
    // DTOs
    public class CustomFieldRequest
    {
        public string? Id { get; set; }
        public string? EntityName { get; set; }
        public string? FieldName { get; set; }
        public string? FieldNameDisplay { get; set; }
        public string? DataType { get; set; }
        public bool IsRequired { get; set; }
    }

    // New DTO for entity configuration
    public class EntityConfigurationRequest
    {
        public string? EntityName { get; set; }
        public List<CustomFieldRequest> Fields { get; set; } = new();
    }

    public class CustomFieldValueRequest
    {
        public string? Id { get; set; }
        public string? CustomFieldID { get; set; }
        public string? EntityID { get; set; }
        public string? Value { get; set; }
    }

    // Commands
    public class CreateCustomFieldCommand : IRequest<CustomField>
    {
        public required CustomFieldRequest CustomField { get; set; }
    }

    public class UpdateCustomFieldCommand : IRequest<CustomField>
    {
        public required string Id { get; set; }
        public required CustomFieldRequest CustomField { get; set; }
    }

    public class DeleteCustomFieldCommand : IRequest<bool>
    {
        public required string Id { get; set; }
    }

    public class CreateCustomFieldValueCommand : IRequest<CustomFieldValue>
    {
        public required CustomFieldValueRequest CustomFieldValue { get; set; }
    }

    public class UpdateCustomFieldValueCommand : IRequest<CustomFieldValue>
    {
        public required string Id { get; set; }
        public required CustomFieldValueRequest CustomFieldValue { get; set; }
    }

    // New Commands/Queries to replace Service methods
    public class SaveCustomFieldValuesCommand : IRequest<bool>
    {
        public required string EntityId { get; set; }
        public required Dictionary<string, string> Values { get; set; }
    }

    public class ValidateCustomFieldsQuery : IRequest<(bool IsValid, List<string> Errors)>
    {
        public required string EntityName { get; set; }
        public required Dictionary<string, string> Values { get; set; }
    }

    public class GetCustomFieldValuesAsMapQuery : IRequest<Dictionary<string, string>>
    {
        public required string EntityId { get; set; }
    }

    public class GetAvailableEntitiesQuery : IRequest<List<string>>
    {
    }

    // Commands/Queries for entity configuration
    public class SaveEntityConfigurationCommand : IRequest<bool>
    {
        public required EntityConfigurationRequest Configuration { get; set; }
    }

    public class GetEntityConfigurationQuery : IRequest<EntityConfigurationRequest>
    {
        public required string EntityName { get; set; }
    }

    // Queries
    public class GetAllCustomFieldsQuery : IRequest<List<CustomField>>
    {
        public string? EntityName { get; set; }
    }

    public class GetCustomFieldByIdQuery : IRequest<CustomField>
    {
        public required string Id { get; set; }
    }

    public class GetCustomFieldValuesByEntityQuery : IRequest<List<CustomFieldValue>>
    {
        public required string EntityId { get; set; }
    }

    public class GetCustomFieldValuesQuery : IRequest<List<CustomFieldValue>>
    {
        public string? CustomFieldId { get; set; }
        public string? EntityId { get; set; }
    }

    // Handlers
    public class CustomFieldHandler(ApplicationDbContext context) :
        IRequestHandler<CreateCustomFieldCommand, CustomField>,
        IRequestHandler<UpdateCustomFieldCommand, CustomField>,
        IRequestHandler<DeleteCustomFieldCommand, bool>,
        IRequestHandler<GetAllCustomFieldsQuery, List<CustomField>>,
        IRequestHandler<GetCustomFieldByIdQuery, CustomField>,
        IRequestHandler<CreateCustomFieldValueCommand, CustomFieldValue>,
        IRequestHandler<UpdateCustomFieldValueCommand, CustomFieldValue>,
        IRequestHandler<GetCustomFieldValuesByEntityQuery, List<CustomFieldValue>>,
        IRequestHandler<GetCustomFieldValuesQuery, List<CustomFieldValue>>,
        IRequestHandler<SaveCustomFieldValuesCommand, bool>,
        IRequestHandler<ValidateCustomFieldsQuery, (bool, List<string>)>,
        IRequestHandler<GetCustomFieldValuesAsMapQuery, Dictionary<string, string>>,
        IRequestHandler<GetAvailableEntitiesQuery, List<string>>,
        IRequestHandler<SaveEntityConfigurationCommand, bool>,
        IRequestHandler<GetEntityConfigurationQuery, EntityConfigurationRequest>
    {
        // Constants
        private static readonly HashSet<string> AvailableEntities = new()
        {
            "Product", "Category", "Article", "Event",
            "Booking", "Order", "Membership", "Branch"
        };

        private static readonly Dictionary<string, Func<string, bool>> ValidationPatterns = new()
        {
            ["int"] = value => int.TryParse(value, out _),
            ["decimal"] = value => decimal.TryParse(value, out _),
            ["bool"] = value => bool.TryParse(value, out _),
            ["datetime"] = value => DateTime.TryParse(value, out _),
            ["date"] = value => DateTime.TryParseExact(value, "yyyy-MM-dd", null, System.Globalization.DateTimeStyles.None, out _),
            ["email"] = value => value.Contains("@") && value.Contains(".") && value.IndexOf("@") < value.LastIndexOf("."),
            ["url"] = value => Uri.TryCreate(value, UriKind.Absolute, out _),
            ["phone"] = value => System.Text.RegularExpressions.Regex.IsMatch(value, @"^[\+]?[0-9\-\(\)\s]+$")
        };

        // CustomField Handlers
        public async Task<CustomField> Handle(CreateCustomFieldCommand request, CancellationToken cancellationToken)
        {
            var customField = new CustomField
            {
                EntityName = request.CustomField.EntityName,
                FieldName = request.CustomField.FieldName,
                FieldNameDisplay = request.CustomField.FieldNameDisplay,
                DataType = request.CustomField.DataType,
                IsRequired = request.CustomField.IsRequired
            };

            context.CustomFields.Add(customField);
            await context.SaveChangesAsync(cancellationToken);
            return customField;
        }

        public async Task<CustomField> Handle(UpdateCustomFieldCommand request, CancellationToken cancellationToken)
        {
            var customField = await context.CustomFields.FindAsync(request.Id);
            if (customField == null)
                throw new NotFoundException($"Custom field with ID {request.Id} not found");

            customField.EntityName = request.CustomField.EntityName;
            customField.FieldName = request.CustomField.FieldName;
            customField.FieldNameDisplay = request.CustomField.FieldNameDisplay;
            customField.DataType = request.CustomField.DataType;
            customField.IsRequired = request.CustomField.IsRequired;
            customField.UpdatedDate = DateTime.Now;

            await context.SaveChangesAsync(cancellationToken);
            return customField;
        }

        public async Task<bool> Handle(DeleteCustomFieldCommand request, CancellationToken cancellationToken)
        {
            var customField = await context.CustomFields.FindAsync(request.Id);
            if (customField == null)
                throw new NotFoundException($"Custom field with ID {request.Id} not found");

            // Xóa tất cả custom field values liên quan
            var relatedValues = await context.CustomFieldValues
                .Where(v => v.CustomFieldID == request.Id)
                .ToListAsync(cancellationToken);

            context.CustomFieldValues.RemoveRange(relatedValues);
            context.CustomFields.Remove(customField);

            await context.SaveChangesAsync(cancellationToken);
            return true;
        }

        public async Task<List<CustomField>> Handle(GetAllCustomFieldsQuery request, CancellationToken cancellationToken)
        {
            var query = context.CustomFields.AsQueryable();

            if (!string.IsNullOrEmpty(request.EntityName))
            {
                query = query.Where(f => f.EntityName == request.EntityName);
            }

            return await query.OrderBy(f => f.EntityName).ThenBy(f => f.FieldName).ToListAsync(cancellationToken);
        }

        public async Task<CustomField> Handle(GetCustomFieldByIdQuery request, CancellationToken cancellationToken)
        {
            var customField = await context.CustomFields.FindAsync(request.Id);
            if (customField == null)
                throw new NotFoundException($"Custom field with ID {request.Id} not found");

            return customField;
        }

        // CustomFieldValue Handlers
        public async Task<CustomFieldValue> Handle(CreateCustomFieldValueCommand request, CancellationToken cancellationToken)
        {
            var customFieldValue = new CustomFieldValue
            {
                CustomFieldID = request.CustomFieldValue.CustomFieldID,
                EntityID = request.CustomFieldValue.EntityID,
                Value = request.CustomFieldValue.Value
            };

            context.CustomFieldValues.Add(customFieldValue);
            await context.SaveChangesAsync(cancellationToken);
            return customFieldValue;
        }

        public async Task<CustomFieldValue> Handle(UpdateCustomFieldValueCommand request, CancellationToken cancellationToken)
        {
            var customFieldValue = await context.CustomFieldValues.FindAsync(request.Id);
            if (customFieldValue == null)
                throw new NotFoundException($"Custom field value with ID {request.Id} not found");

            customFieldValue.CustomFieldID = request.CustomFieldValue.CustomFieldID;
            customFieldValue.EntityID = request.CustomFieldValue.EntityID;
            customFieldValue.Value = request.CustomFieldValue.Value;
            customFieldValue.UpdatedDate = DateTime.Now;

            await context.SaveChangesAsync(cancellationToken);
            return customFieldValue;
        }

        public async Task<List<CustomFieldValue>> Handle(GetCustomFieldValuesByEntityQuery request, CancellationToken cancellationToken)
        {
            return await context.CustomFieldValues
                .Where(v => v.EntityID == request.EntityId)
                .Include(v => v.CustomField)
                .ToListAsync(cancellationToken);
        }

        public async Task<List<CustomFieldValue>> Handle(GetCustomFieldValuesQuery request, CancellationToken cancellationToken)
        {
            var query = context.CustomFieldValues.AsQueryable();

            if (!string.IsNullOrEmpty(request.CustomFieldId))
            {
                query = query.Where(v => v.CustomFieldID == request.CustomFieldId);
            }

            if (!string.IsNullOrEmpty(request.EntityId))
            {
                query = query.Where(v => v.EntityID == request.EntityId);
            }

            return await query.ToListAsync(cancellationToken);
        }

        // New Handler Implementations
        public async Task<bool> Handle(SaveCustomFieldValuesCommand request, CancellationToken cancellationToken)
        {
            using var transaction = await context.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                // Delete existing values
                await context.CustomFieldValues
                    .Where(v => v.EntityID == request.EntityId)
                    .ExecuteDeleteAsync(cancellationToken);

                // Batch insert new values
                var newValues = request.Values
                    .Where(v => !string.IsNullOrEmpty(v.Value))
                    .Select(kvp => new CustomFieldValue
                    {
                        CustomFieldID = kvp.Key,
                        EntityID = request.EntityId,
                        Value = kvp.Value,
                        CreatedDate = DateTime.UtcNow
                    })
                    .ToList();

                if (newValues.Any())
                {
                    await context.CustomFieldValues.AddRangeAsync(newValues, cancellationToken);
                    await context.SaveChangesAsync(cancellationToken);
                }

                await transaction.CommitAsync(cancellationToken);
                return true;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }

        public async Task<(bool, List<string>)> Handle(ValidateCustomFieldsQuery request, CancellationToken cancellationToken)
        {
            var errors = new List<string>();
            var customFields = await context.CustomFields
                .Where(f => f.EntityName == request.EntityName)
                .ToListAsync(cancellationToken);

            var requiredFields = customFields.Where(f => f.IsRequired).ToList();

            foreach (var field in requiredFields)
            {
                if (!request.Values.TryGetValue(field.Id, out var value) || string.IsNullOrWhiteSpace(value))
                {
                    errors.Add($"Field '{field.FieldName}' is required.");
                    continue;
                }

                if (!ValidateDataType(field.DataType, value))
                {
                    errors.Add($"Field '{field.FieldName}' has invalid format for type '{field.DataType}'.");
                }
            }

            return (errors.Count == 0, errors);
        }

        public async Task<Dictionary<string, string>> Handle(GetCustomFieldValuesAsMapQuery request, CancellationToken cancellationToken)
        {
            var values = await context.CustomFieldValues
                .Where(v => v.EntityID == request.EntityId && v.CustomFieldID != null)
                .ToListAsync(cancellationToken);

            return values.ToDictionary(v => v.CustomFieldID!, v => v.Value ?? string.Empty);
        }

        public Task<List<string>> Handle(GetAvailableEntitiesQuery request, CancellationToken cancellationToken)
        {
            return Task.FromResult(AvailableEntities.ToList());
        }

        // Entity Configuration Handlers
        public async Task<bool> Handle(SaveEntityConfigurationCommand request, CancellationToken cancellationToken)
        {
            using var transaction = await context.Database.BeginTransactionAsync(cancellationToken);
            try
            {
                // Delete existing custom fields for the entity
                var existingFields = await context.CustomFields
                    .Where(f => f.EntityName == request.Configuration.EntityName)
                    .ToListAsync(cancellationToken);

                context.CustomFields.RemoveRange(existingFields);

                // Add new custom fields
                var newFields = request.Configuration.Fields
                    .Select(field => new CustomField
                    {
                        EntityName = field.EntityName,
                        FieldName = field.FieldName,
                        FieldNameDisplay = field.FieldNameDisplay,
                        DataType = field.DataType,
                        IsRequired = field.IsRequired
                    })
                    .ToList();

                if (newFields.Any())
                {
                    await context.CustomFields.AddRangeAsync(newFields, cancellationToken);
                }

                await context.SaveChangesAsync(cancellationToken);
                await transaction.CommitAsync(cancellationToken);
                return true;
            }
            catch
            {
                await transaction.RollbackAsync(cancellationToken);
                throw;
            }
        }

        public async Task<EntityConfigurationRequest> Handle(GetEntityConfigurationQuery request, CancellationToken cancellationToken)
        {
            var entityName = request.EntityName;
            var fields = await context.CustomFields
                .Where(f => f.EntityName == entityName)
                .ToListAsync(cancellationToken);

            return new EntityConfigurationRequest
            {
                EntityName = entityName,
                Fields = fields.Select(f => new CustomFieldRequest
                {
                    Id = f.Id,
                    EntityName = f.EntityName,
                    FieldName = f.FieldName,
                    FieldNameDisplay = f.FieldNameDisplay,
                    DataType = f.DataType,
                    IsRequired = f.IsRequired
                }).ToList()
            };
        }

        private bool ValidateDataType(string? dataType, string value)
        {
            if (string.IsNullOrEmpty(dataType) || string.IsNullOrEmpty(value))
                return true;

            var normalizedType = dataType.ToLowerInvariant();

            return ValidationPatterns.TryGetValue(normalizedType, out var validator)
                ? validator(value)
                : true;
        }
    }
}
