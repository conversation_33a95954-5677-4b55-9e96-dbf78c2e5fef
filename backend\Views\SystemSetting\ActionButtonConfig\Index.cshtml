﻿@{
    ViewData["Title"] = "Cấu hình nút hành động";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> sách nút điều hướng</h4>
                <p class="mb-0">
                    Quản lý các nút hành động hiển thị trong ứng dụng: Shortcut, Button và Floating Bubble.
                </p>
            </div>
            <button onclick="GetActionButtonForm('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--<PERSON><PERSON> lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label for="filter-category" class="form-label">Nhóm nút</label>
                        <select id="filter-category" class="form-select" onchange="table.ajax.reload()">
                            <option value="">Tất cả nhóm</option>
                            <option value="shortcut">Shortcut (Phím tắt)</option>
                            <option value="button">Button (Nút chính)</option>
                            <option value="floatingBubble">Floating Bubble (Nút nổi)</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="filter-type" class="form-label">Loại nút</label>
                        <select id="filter-type" class="form-select" onchange="table.ajax.reload()">
                            <option value="">Tất cả loại</option>
                            <option value="phone">Phone (Gọi điện)</option>
                            <option value="webview">Webview (Mở trang web)</option>
                            <option value="email">Email</option>
                            <option value="direct">Direct (Liên kết trực tiếp)</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0; z-index: -1;"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-action-buttons" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-action-button" class="modal fade" tabindex="-1" aria-modal="true" role="dialog" data-bs-backdrop="static">
    <div id="modal-content" class="modal-dialog modal-lg"></div>
</div>

@section Scripts {
    <script>
        let removedOldImages = [];
        let newImages = [];
        let currentImages = 0;
        const catgoryDatadefault = {};

        // Mapping functions for category and type
        function getCategoryName(category) {
            const categoryMap = {
                'shortcut': 'Shortcut (Phím tắt)',
                'button': 'Button (Nút chính)',
                'floatingBubble': 'Floating Bubble (Nút nổi)'
            };
            return categoryMap[category] || category;
        }

        function getTypeName(type) {
            const typeMap = {
                'phone': 'Phone (Gọi điện)',
                'webview': 'Webview (Mở trang web)',
                'email': 'Email',
                'direct': 'Direct (Liên kết trực tiếp)'
            };
            return typeMap[type] || type;
        }

        $(document).ready(function () {
            GetActionButtonsList();
            $('#search').on('input', search);
        });

        $(document).on("click", ".btn-preview-remove", function () {
            const fullImageUrl = $(this).data("url");
            const imageUrl = fullImageUrl.split("/").pop();

            $(this).parent().remove();

            if (!removedOldImages.includes(imageUrl)) {
                removedOldImages.push(imageUrl);
            }
        });

        function GetActionButtonsList() {
            table = new DataTable("#list-action-buttons", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const category = $("#filter-category").val();
                    const type = $("#filter-type").val();

                    $.ajax({
                        url: '/api/SystemSettings/ActionButtons',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            category: category,
                            type: type
                        },
                        success: function (response) {
                            if (response.code === 0) {
                                const formattedData = response.data.map((item, index) => ({
                                    index: data.start + index + 1,
                                    title: item.title,
                                    category: getCategoryName(item.category),
                                    type: getTypeName(item.type),
                                    image: `<div class="d-flex align-items-center">
                                                  <img src="${item.image || '/images/no-image-1.png'}" style="background-color: #f0f0f0; padding: 2px; border-radius: 8px;" class="img-fluid rounded avatar-50 mr-3" alt="image" />
                                                </div>`,
                                    description: item.description,
                                    sortOrder: item.sortOrder,
                                    actions: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                   <a onclick="GetActionButtonForm('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                     <i class="ri-edit-line fs-6 mr-0"></i>
                                                   </a>
                                                   <a onclick="DeleteActionButton('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                      <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                   </a>
                                                </div>`,
                                    id: item.id,
                                    status: item.isActive ? `<div class="m-auto circle-active"><div>` : `<div class="m-auto circle-inactive"></div>`
                                }));

                                setTimeout(() => {
                                    callback({
                                        draw: data.draw,
                                        recordsTotal: response.data.length,
                                        recordsFiltered: response.totalPages * data.length || 0,
                                        data: formattedData
                                    })
                                }, 300);
                            }
                        },
                        error: function (error) {
                            console.error("Error loading data:", error);
                            callback({
                                draw: data.draw,
                                recordsTotal: 0,
                                recordsFiltered: 0,
                                data: []
                            });
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "index", className: 'text-center', width: "5%" },
                    { title: "Tiêu đề", data: "title", width: "15%" },
                    { title: "Nhóm", data: "category", width: "10%" },
                    { title: "Loại", data: "type", width: "10%" },
                    { title: "Hình ảnh", data: "image", width: "15%" },
                    { title: "Mô tả/URL/SĐT", data: "description", width: "30%" },
                    { title: "Thứ tự", data: "sortOrder", className: 'text-center', width: "5%" },
                    { title: "Trạng thái hoạt động", data: "status", className: 'text-center', width: "5%" },
                    { title: "Thao tác", data: "actions", className: 'text-center', width: "10%" },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetActionButtonForm(id) {
            const url = id ? `/SystemSetting/ActionButtonConfig/${id}` : '/SystemSetting/ActionButtonConfig/Create';
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-action-button").modal("toggle");
                },
                error: function () {
                    AlertResponse("Không thể tải form", "error");
                }
            });
        }

        function DeleteActionButton(id) {
            const url = `/api/SystemSettings/ActionButtons/${id}`
            DeleteItem(url);
        }

        // Save action button
        function HandleSaveOrUpdate(id) {
            // Validate form
            const title = $("#title").val()?.trim();
            const description = $("#description").val()?.trim();

            if (!title) {
                AlertResponse("Vui lòng nhập tiêu đề", "warning");
                return;
            }

            if (!description) {
                AlertResponse("Vui lòng nhập mô tả/URL/Số điện thoại", "warning");
                return;
            }

            // Check if image is provided
            const hasNewImage = $("#buttonImage")[0].files.length > 0;
            if (!hasNewImage && !id) {
                AlertResponse("Vui lòng chọn hình ảnh cho nút", "warning");
                return;
            }

            // Create form data object
            const formData = new FormData();
            formData.append('title', title);
            formData.append('type', $("#type").val());
            formData.append('isActive', $("#isActive").val() === 'true');
            formData.append('category', $("#category").val());
            formData.append('description', description);
            formData.append('sortOrder', $("#sortOrder").val() || 0);

            // handle image
            {
                const files = $('#buttonImage')[0].files;
                if (removedOldImages.length === currentImages) {
                    if (files.length === 0) {
                        // AlertResponse("Bạn cần ít nhất một hình ảnh!", "warning");
                        // return;
                    }
                }

                if (id) {
                    if (files.length == 0) {
                        // AlertResponse('Bạn vui lòng tải hình ảnh lên!', 'error');
                        // return;
                    }
                }

                for (let i = 0; i < files.length; i++) {
                    formData.append('images', files[i]);
                }

                if (removedOldImages.length > 0) {
                    for (const removedImage of removedOldImages) {
                        formData.append('removedOldImages', removedImage);
                    }
                }
            }

            // Determine endpoint and method
            const isNew = !id || id === '';
            const url = isNew ? '/api/SystemSettings/ActionButtons' : `/api/SystemSettings/ActionButtons/${id}`;
            const method = isNew ? 'POST' : 'PUT';

            // Submit form
            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                        $("#modal-action-button").modal("toggle");
                    } else {
                        AlertResponse(response.message || 'Lưu nút không thành công', 'error');
                    }
                },
                error: function (xhr) {
                    AlertResponse('Lưu nút thất bại: ' + (xhr.responseJSON?.message || 'Lỗi không xác định'), 'error');
                }
            });
        }
    </script>
}
