﻿@{
	ViewData["Title"] = "Dashboard";
	var currentHour = DateTime.Now.Hour;
	string greeting = currentHour switch
	{
		>= 5 and < 12 => "Buổi sáng tốt lành ",
		>= 12 and < 14 => "Buổi trưa vui vẻ ",
		>= 14 and < 18 => "Chào buổi chiều ",
		>= 18 and < 22 => "Chào buổi tối ",
		_ => "Chúc ngủ ngon! "
	};
}

<style>
	html {
		overflow-x: hidden
	}

	.card-stats .icon-big {
		font-size: 3em;
		width: 68px;
		height: 68px;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba(0, 0, 0, 0.05);
		border-radius: 8px;
	}

	.card-stats .card-body {
		padding: 20px;
	}

	.chart-container {
		position: relative;
		min-height: 280px;
	}

	/* Thêm hiệu ứng hover cho card */
	.card {
		transition: transform 0.3s, box-shadow 0.3s;
	}

	.card:hover {
		transform: translateY(-5px);
		box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
	}

	/* <PERSON><PERSON><PERSON> thiện heading */
	.card-title {
		font-weight: 600;
		color: #3a3a3a;
	}

	/* Thanh chọn ngày */
	.date-filter-container {
		background: #f8f9fa;
		padding: 15px;
		border-radius: 8px;
		margin-bottom: 20px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
	}

	/* Cải thiện UX cho stats cards */
	.stats-highlight {
		font-weight: 700;
		font-size: 1.5rem;
		color: #2c7be5;
	}
</style>

<div class="row">
	<div class="col-lg-4">
		<div class="card card-transparent card-block card-stretch card-height border-none">
			<div class="card-body p-0 mt-lg-2 mt-0">
				<h3 class="mb-3">@(greeting + (User != null ? User.Claims.FirstOrDefault(x => x.Type == "DisplayName")?.Value : "người dùng")) </h3>
				<p class="mb-0 mr-4"> Trang tổng quan của bạn cung cấp cho bạn chế độ xem về hiệu suất chính hoặc quy trình kinh doanh. </p>
			</div>
		</div>
	</div>
	<div class="col-lg-8">
		<div class="date-filter-container">
			<div class="row align-items-center">
				<div class="col-md-4">
					<label for="startDate">Từ ngày:</label>
					<input type="date" id="startDate" class="form-control" />
				</div>
				<div class="col-md-4">
					<label for="endDate">Đến ngày:</label>
					<input type="date" id="endDate" class="form-control" />
				</div>
				<div class="col-md-4">
					<label>&nbsp;</label>
					<button class="btn btn-primary btn-block" id="btnApplyDateFilter">
						<i class="fas fa-filter mr-2"></i>Áp dụng
					</button>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Stats Summary Cards -->
<div class="row mb-4">
	<div class="col-12 col-sm-6 col-md-3 mb-4">
		<div class="card card-stats">
			<div class="card-body">
				<div class="row">
					<div class="col-5">
						<div class="icon-big text-center text-primary">
							<i class="ri-shopping-cart-2-line"></i>
						</div>
					</div>
					<div class="col-7">
						<div class="numbers">
							<p class="card-category">Tổng đơn hàng</p>
							<h4 class="stats-highlight" id="count-total-order">0</h4>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="col-12 col-sm-6 col-md-3 mb-4">
		<div class="card card-stats">
			<div class="card-body">
				<div class="row">
					<div class="col-5">
						<div class="icon-big text-center text-success">
							<i class="ri-currency-line"></i>
						</div>
					</div>
					<div class="col-7">
						<div class="numbers">
							<p class="card-category">Doanh thu</p>
							<h4 class="stats-highlight" id="count-total-revenue" title="0 đ">0đ</h4>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="col-12 col-sm-6 col-md-3 mb-4">
		<div class="card card-stats">
			<div class="card-body">
				<div class="row">
					<div class="col-5">
						<div class="icon-big text-center text-warning">
							<i class="ri-calendar-check-line"></i>
						</div>
					</div>
					<div class="col-7">
						<div class="numbers">
							<p class="card-category">Lịch đặt</p>
							<h4 class="stats-highlight" id="count-total-booking">0</h4>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="col-12 col-sm-6 col-md-3 mb-4">
		<div class="card card-stats">
			<div class="card-body">
				<div class="row">
					<div class="col-5">
						<div class="icon-big text-center text-info">
							<i class="ri-user-add-line"></i>
						</div>
					</div>
					<div class="col-7">
						<div class="numbers">
							<p class="card-category">Khách hàng mới</p>
							<h4 class="stats-highlight" id="count-new-customers">0</h4>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- New chart containers - Add this after your existing chart sections -->
<div class="row mt-4">
	<div class="col-md-6">
		<div class="card card-block card-stretch card-height">
			<div class="card-header d-flex justify-content-between">
				<div class="header-title">
					<h4 class="card-title">Đơn hàng & Đặt lịch theo thời gian</h4>
				</div>
			</div>
			<div class="card-body">
				<div id="chart-container-orders-bookings" style="min-height: 280px">
					<canvas id="ordersBookingsChart"></canvas>
				</div>
			</div>
		</div>
	</div>

	<div class="col-md-6">
		<div class="card card-block card-stretch card-height">
			<div class="card-header d-flex justify-content-between">
				<div class="header-title">
					<h4 class="card-title">Doanh thu theo ngày</h4>
				</div>
			</div>
			<div class="card-body">
				<div id="chart-container-revenue" style="min-height: 280px">
					<canvas id="revenueChart"></canvas>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row mt-4">
	<div class="col-md-6">
		<div class="card card-block card-stretch card-height">
			<div class="card-header d-flex justify-content-between">
				<div class="header-title">
					<h4 class="card-title">Đơn hàng & Doanh thu</h4>
				</div>
			</div>
			<div class="card-body">
				<div id="chart-container-orders-revenue" style="min-height: 280px">
					<canvas id="ordersRevenueChart"></canvas>
				</div>
			</div>
		</div>
	</div>

	<div class="col-md-6">
		<div class="card card-block card-stretch card-height">
			<div class="card-header d-flex justify-content-between">
				<div class="header-title">
					<h4 class="card-title">Đơn đặt lịch theo trạng thái</h4>
				</div>
			</div>
			<div class="card-body">
				<div id="chart-container-booking-status" style="min-height: 280px">
					<canvas id="bookingStatusChart"></canvas>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row mt-4">
	<div class="col-md-6">
		<div class="card card-block card-stretch card-height">
			<div class="card-header d-flex justify-content-between">
				<div class="header-title">
					<h4 class="card-title">Lượt đổi & sử dụng voucher</h4>
				</div>
			</div>
			<div class="card-body">
				<div id="chart-container-vouchers" style="min-height: 280px">
					<canvas id="vouchersChart"></canvas>
				</div>
			</div>
		</div>
	</div>

	<div class="col-md-6">
		<div class="card card-block card-stretch card-height">
			<div class="card-header d-flex justify-content-between">
				<div class="header-title">
					<h4 class="card-title">Khách hàng mới theo ngày</h4>
				</div>
			</div>
			<div class="card-body">
				<div id="chart-container-new-members" style="min-height: 280px">
					<canvas id="newMembersChart"></canvas>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="row mt-4">
	<div class="col-md-6">
		<div class="card card-block card-stretch card-height">
			<div class="card-header d-flex justify-content-between">
				<div class="header-title">
					<h4 class="card-title">Top sản phẩm bán chạy</h4>
				</div>
			</div>
			<div class="card-body">
				<div id="chart-container-top-products" style="min-height: 280px">
					<canvas id="topProductsChart"></canvas>
				</div>
			</div>
		</div>
	</div>

	<div class="col-md-6">
		<div class="card card-block card-stretch card-height">
			<div class="card-header d-flex justify-content-between">
				<div class="header-title">
					<h4 class="card-title">Top dịch vụ đặt lịch</h4>
				</div>
			</div>
			<div class="card-body">
				<div id="chart-container-top-booking-items" style="min-height: 280px">
					<canvas id="topBookingItemsChart"></canvas>
				</div>
			</div>
		</div>
	</div>
</div>

@section Scripts {
	<script src="~/lib/chartjs/chart.min.js"></script>

	<script>
		// Quản lý dashboard với IIFE để tránh biến toàn cục
		const Dashboard = (function () {
			// Private variables
			let charts = {};
			let startDate, endDate;
			let dashboardData = null;
			let revenueChartData = null;

			// Khởi tạo dashboard
			function init() {
				setupDateRange();
				setupEventHandlers();
				initDashboardCharts();
				setupLazyLoad();
				loadDashboardData();
			}

			// Thiết lập khoảng ngày mặc định
			function setupDateRange() {
				startDate = moment().startOf('month').format('YYYY-MM-DD');
				endDate = moment().endOf('month').format('YYYY-MM-DD');
				$('#startDate').val(startDate);
				$('#endDate').val(endDate);
			}

			// Xử lý các sự kiện
			function setupEventHandlers() {
				$('#btnApplyDateFilter').on('click', function () {
					startDate = $('#startDate').val();
					endDate = $('#endDate').val();

					if (!startDate || !endDate) {
						alert("Vui lòng chọn khoảng thời gian");
						return;
					}

					if (moment(endDate).isBefore(startDate)) {
						alert("Ngày kết thúc phải sau ngày bắt đầu");
						return;
					}

					loadDashboardData();
				});
			}

			// Khởi tạo tất cả biểu đồ
			function initDashboardCharts() {
				// Biểu đồ đơn hàng & đặt lịch theo thời gian
				charts.ordersBookings = createChart('ordersBookingsChart', {
					type: 'line',
					options: {
						scales: {
							y: {
								beginAtZero: true,
								ticks: { stepSize: 1 }
							}
						}
					},
					datasets: [
						{
							label: 'Đơn hàng',
							backgroundColor: 'rgba(54, 162, 235, 0.2)',
							borderColor: 'rgba(54, 162, 235, 1)',
							borderWidth: 2,
							tension: 0.3
						},
						{
							label: 'Đặt lịch',
							backgroundColor: 'rgba(255, 206, 86, 0.2)',
							borderColor: 'rgba(255, 206, 86, 1)',
							borderWidth: 2,
							tension: 0.3
						}
					]
				});

				// Biểu đồ đơn hàng & doanh thu
				charts.ordersRevenue = createChart('ordersRevenueChart', {
					type: 'bar',
					options: {
						scales: {
							y: {
								beginAtZero: true,
								position: 'left',
								title: {
									display: true,
									text: 'Số đơn hàng'
								}
							},
							y1: {
								beginAtZero: true,
								position: 'right',
								grid: { drawOnChartArea: false },
								title: {
									display: true,
									text: 'Doanh thu'
								},
								ticks: {
									callback: formatCurrency
								}
							}
						}
					},
					datasets: [
						{
							label: 'Số đơn hàng',
							backgroundColor: 'rgba(54, 162, 235, 0.5)',
							borderColor: 'rgba(54, 162, 235, 1)',
							yAxisID: 'y',
							order: 2
						},
						{
							label: 'Doanh thu',
							type: 'line',
							backgroundColor: 'rgba(255, 99, 132, 0.5)',
							borderColor: 'rgba(255, 99, 132, 1)',
							borderWidth: 2,
							tension: 0.4,
							yAxisID: 'y1',
							order: 1
						}
					]
				});

				// Biểu đồ top sản phẩm bán chạy
				charts.topProducts = createChart('topProductsChart', {
					type: 'bar',
					options: {
						scales: {
							y: {
								beginAtZero: true,
								position: 'left',
								title: {
									display: true,
									text: 'Lượt bán'
								}
							},
							y1: {
								beginAtZero: true,
								position: 'right',
								grid: { drawOnChartArea: false },
								title: {
									display: true,
									text: 'Doanh thu'
								},
								ticks: {
									callback: formatCurrency
								}
							}
						}
					},
					datasets: [
						{
							label: 'Lượt bán',
							backgroundColor: 'rgba(54, 162, 235, 0.5)',
							borderColor: 'rgba(54, 162, 235, 1)',
							yAxisID: 'y',
							order: 2
						},
						{
							label: 'Doanh thu',
							type: 'line',
							backgroundColor: 'rgba(255, 99, 132, 0.5)',
							borderColor: 'rgba(255, 99, 132, 1)',
							borderWidth: 2,
							yAxisID: 'y1',
							order: 1
						}
					]
				});

				// Biểu đồ top dịch vụ đặt lịch
				charts.topBookingItems = createChart('topBookingItemsChart', {
					type: 'bar',
					options: {
						indexAxis: 'y',
						scales: {
							x: {
								beginAtZero: true,
								ticks: { stepSize: 1 }
							}
						}
					},
					backgroundColor: 'rgba(153, 102, 255, 0.7)',
					borderColor: 'rgba(153, 102, 255, 1)'
				});

				// Thêm biểu đồ trạng thái đặt lịch
				charts.bookingStatus = createChart('bookingStatusChart', {
					type: 'doughnut',
					options: {
						plugins: {
							legend: { position: 'right' },
							tooltip: {
								callbacks: {
									label: function (context) {
										const label = context.label || '';
										const value = context.raw;
										const total = context.dataset.data.reduce((a, b) => a + b, 0);
										const percentage = Math.round((value / total) * 100);
										return `${label}: ${value} (${percentage}%)`;
									}
								}
							}
						}
					},
					backgroundColor: [
						'rgba(75, 192, 192, 0.7)', // Xanh lá - Đã hoàn thành
						'rgba(255, 99, 132, 0.7)',  // Đỏ - Đã hủy
						'rgba(255, 206, 86, 0.7)',  // Vàng - Đã đặt
						'rgba(54, 162, 235, 0.7)'   // Xanh dương - Đang tiến hành
					]
				});

				// Thêm biểu đồ vouchers
				charts.vouchers = createChart('vouchersChart', {
					type: 'bar',
					options: {
						scales: {
							y: {
								beginAtZero: true
							}
						}
					},
					datasets: [
						{
							label: 'Đã đổi',
							backgroundColor: 'rgba(153, 102, 255, 0.5)',
							borderColor: 'rgba(153, 102, 255, 1)'
						},
						{
							label: 'Đã sử dụng',
							backgroundColor: 'rgba(255, 159, 64, 0.5)',
							borderColor: 'rgba(255, 159, 64, 1)'
						}
					]
				});

				// Thêm biểu đồ khách hàng mới
				charts.newMembers = createChart('newMembersChart', {
					type: 'line',
					options: {
						scales: {
							y: {
								beginAtZero: true,
								ticks: { stepSize: 1 }
							}
						}
					},
					backgroundColor: 'rgba(54, 162, 235, 0.2)',
					borderColor: 'rgba(54, 162, 235, 1)',
					tension: 0.3,
					fill: true
				});

				// Biểu đồ doanh thu theo ngày sẽ được tạo khi scroll đến
			}

			// Thiết lập lazy loading cho biểu đồ doanh thu
			function setupLazyLoad() {
				const revenueChartObserver = new IntersectionObserver((entries) => {
					entries.forEach(entry => {
						if (entry.isIntersecting && revenueChartData) {
							console.log('Rendering revenue chart');
							renderRevenueChart();
							revenueChartObserver.unobserve(entry.target);
						}
					});
				}, { threshold: 0.1 });

				const revenueChartContainer = document.getElementById('chart-container-revenue');
				if (revenueChartContainer) {
					revenueChartObserver.observe(revenueChartContainer);
				}
			}

			// Tạo một biểu đồ với cấu hình cơ bản
			function createChart(id, config) {
				if (Chart.getChart(id)) {
					Chart.getChart(id).destroy();
				}

				const ctx = document.getElementById(id).getContext('2d');
				return new Chart(ctx, {
					type: config.type || 'bar',
					data: {
						labels: [],
						datasets: config.datasets || [{
							label: config.label || '',
							data: [],
							backgroundColor: config.backgroundColor || 'rgba(75, 192, 192, 0.2)',
							borderColor: config.borderColor || 'rgba(75, 192, 192, 1)',
							borderWidth: 1
						}]
					},
					options: {
						responsive: true,
						maintainAspectRatio: false,
						plugins: {
							tooltip: {
								callbacks: {
									label: function (context) {
										const label = context.dataset.label || '';
										const value = context.raw;
										if (label.includes('Doanh thu')) {
											return label + ': ' + formatCurrency(value);
										}
										return label + ': ' + value;
									}
								}
							}
						},
						...config.options
					}
				});
			}

			// Render biểu đồ doanh thu khi scroll đến
			function renderRevenueChart() {
				if (!revenueChartData) return;

				charts.revenue = createChart('revenueChart', {
					type: 'line',
					options: {
						scales: {
							y: {
								beginAtZero: true,
								ticks: {
									callback: formatCurrency
								}
							}
						}
					},
					backgroundColor: 'rgba(75, 192, 192, 0.2)',
					borderColor: 'rgba(75, 192, 192, 1)',
					tension: 0.4,
					fill: true
				});

				// Cập nhật dữ liệu
				charts.revenue.data.labels = revenueChartData.map(item => item.label);
				charts.revenue.data.datasets[0].data = revenueChartData.map(item => item.value);
				charts.revenue.data.datasets[0].label = 'Doanh thu';
				charts.revenue.update();
			}

			// Load tất cả dữ liệu dashboard từ một API duy nhất
			function loadDashboardData() {
				$.ajax({
					url: '/api/Dashboards/stats/summary',
					type: 'GET',
					data: {
						startDate: moment(startDate).format('YYYY-MM-DD'),
						endDate: moment(endDate).format('YYYY-MM-DD')
					},
					success: function (data) {
						// Lưu dữ liệu vào biến toàn cục
						dashboardData = data;

						// Cập nhật số liệu tổng quan
						updateSummaryStats(data);

						// Cập nhật các biểu đồ
						updateOrdersBookingsChart(data);
						updateOrdersRevenueChart(data);
						updateTopProductsChart(data);
						updateTopBookingItemsChart(data);

						// Thêm cập nhật cho 3 biểu đồ còn thiếu
						updateBookingStatusChart(data);
						updateVouchersChart(data);
						updateNewMembersChart(data);

						// Lưu dữ liệu biểu đồ doanh thu để lazy load
						revenueChartData = data.revenueChart;

						// Kiểm tra nếu chart doanh thu đã hiển thị, render ngay
						const revenueChartContainer = document.getElementById('chart-container-revenue');
						if (revenueChartContainer && isElementInViewport(revenueChartContainer)) {
							renderRevenueChart();
						}
					},
					error: function (error) {
						console.error('Error loading dashboard data:', error);
					}
				});
			}

			// Cập nhật số liệu tổng quan
			function updateSummaryStats(data) {
				const revenueData = formatVNCurrencyShort(data.totalRevenue);

				$('#count-total-order').text(data.totalOrders);
				$('#count-total-revenue').text(revenueData.short).attr('title', revenueData.full);
				$('#count-total-booking').text(data.totalBookings);
				$('#count-new-customers').text(data.newMemberships);
			}

			// Rút gọn số dài không làm tròn
			function formatVNCurrencyShort(number, showCurrency = true, currencyUnit = 'đ') {
				if (typeof number !== 'number') {
					number = parseFloat(number);
					if (isNaN(number)) return { short: '', full: '' };
				}

				const absNum = Math.abs(number);
				let formattedShort = '';
				let formattedFull = number.toLocaleString('vi-VN') + ' ' + currencyUnit;

				function toShort(val, divisor, suffix) {
					let shortNum = Math.floor((Math.abs(val) / divisor) * 100) / 100;
					
					if (val < 0) shortNum = -shortNum;
					return `${shortNum} ${suffix}${showCurrency ? ' ' + currencyUnit : ''}`;
				}

				if (absNum >= 1_000_000_000) {
					formattedShort = toShort(number, 1_000_000_000, 'T');
				} else if (absNum >= 1_000_000) {
					formattedShort = toShort(number, 1_000_000, 'Tr');
				} else {
					formattedShort = formattedFull;
				}

				return { short: formattedShort.trim(), full: formattedFull.trim() };
			}

			// Cập nhật biểu đồ đơn hàng & đặt lịch theo thời gian
			function updateOrdersBookingsChart(data) {
				const labels = data.ordersChart.map(item => item.label);
				const orderValues = data.ordersChart.map(item => item.value);
				const bookingValues = data.bookingsChart.map(item => item.value);

				charts.ordersBookings.data.labels = labels;
				charts.ordersBookings.data.datasets[0].data = orderValues;
				charts.ordersBookings.data.datasets[1].data = bookingValues;
				charts.ordersBookings.update();
			}

			// Cập nhật biểu đồ đơn hàng & doanh thu
			function updateOrdersRevenueChart(data) {
				// Use the status-based data instead of date-based data
				const statusLabels = data.ordersCountChart.map(item => item.label);
				const orderCounts = data.ordersCountChart.map(item => item.value);
				const revenueValues = data.ordersRevenueChart.map(item => item.value);

				// Update chart configuration
				charts.ordersRevenue.data.labels = statusLabels;
				charts.ordersRevenue.data.datasets[0].data = orderCounts;
				charts.ordersRevenue.data.datasets[1].data = revenueValues;

				// Make sure the chart titles reflect status-based data
				charts.ordersRevenue.data.datasets[0].label = 'Số đơn hàng theo trạng thái';
				charts.ordersRevenue.data.datasets[1].label = 'Doanh thu theo trạng thái';

				// Update the chart
				charts.ordersRevenue.update();
			}

			// Cập nhật biểu đồ top sản phẩm
			function updateTopProductsChart(data) {
				const productNames = data.topProducts.map(item => item.productName);
				const soldCounts = data.topProducts.map(item => item.sold);
				const revenues = data.topProducts.map(item => item.revenue);

				charts.topProducts.data.labels = productNames;
				charts.topProducts.data.datasets[0].data = soldCounts;
				charts.topProducts.data.datasets[1].data = revenues;
				charts.topProducts.update();
			}

			// Cập nhật biểu đồ top dịch vụ đặt lịch
			function updateTopBookingItemsChart(data) {
				const itemNames = data.topBookingItems.map(item => item.bookingItemName);
				const soldCounts = data.topBookingItems.map(item => item.sold);

				charts.topBookingItems.data.labels = itemNames;
				charts.topBookingItems.data.datasets[0].data = soldCounts;
				charts.topBookingItems.update();
			}

			// Cập nhật biểu đồ trạng thái đặt lịch
			function updateBookingStatusChart(data) {
				const lablesMapping = {
					'Completed': 'Đã hoàn thành',
					'Cancelled': 'Đã hủy',
					'Pending': 'Đang chờ',
					'Processing': 'Đang tiến hành'
				};

				const statusLabels = data.bookingsCountChart.map(item => item.label);
				const statusCounts = data.bookingsCountChart.map(item => item.value);

				// Cập nhật biểu đồ với dữ liệu thực tế
				charts.bookingStatus.data.labels = statusLabels;
				charts.bookingStatus.data.datasets[0].data = statusCounts;

				// Đảm bảo màu sắc phù hợp với từng trạng thái
				const statusColors = statusLabels.map(label => {
					switch (label) {
						case 'Completed':
							return 'rgba(75, 192, 192, 0.7)'; // Xanh lá - Đã hoàn thành
						case 'Cancelled':
							return 'rgba(255, 99, 132, 0.7)';  // Đỏ - Đã hủy
						case 'Pending':
							return 'rgba(255, 206, 86, 0.7)';  // Vàng - Đang chờ
						case 'Processing':
							return 'rgba(54, 162, 235, 0.7)';   // Xanh dương - Đang tiến hành
						default:
							return 'rgba(153, 102, 255, 0.7)'; // Màu mặc định cho các trạng thái khác
					}
				});

				charts.bookingStatus.data.datasets[0].backgroundColor = statusColors;
				// Cập nhật tùy chọn hiển thị
				if (!charts.bookingStatus.options.plugins) {
					charts.bookingStatus.options.plugins = {};
				}

				charts.bookingStatus.options.plugins.title = {
					display: true,
					text: 'Đơn đặt lịch theo trạng thái',
					font: {
						size: 16
					}
				};

				// Cập nhật tooltip để hiển thị phần trăm
				charts.bookingStatus.options.plugins.tooltip = {
					callbacks: {
						label: function (context) {
							const label = context.label || '';
							const value = context.raw;
							const total = context.dataset.data.reduce((a, b) => a + b, 0);
							const percentage = Math.round((value / total) * 100);
							return `${lablesMapping[label]}: ${value} (${percentage} %)`;
						}
					}
				};

				charts.bookingStatus.update();
			}

			// Cập nhật biểu đồ vouchers
			function updateVouchersChart(data) {
				// Sử dụng dữ liệu ordersChart để lấy labels ngày
				const voucherDates = data.ordersChart.map(item => item.label);

				// Tạo dữ liệu mẫu cho vouchers
				// Khoảng 30% số đơn hàng sẽ sử dụng voucher
				const voucherRedeemed = data.ordersChart.map(item => Math.round(item.value * 0.3 + Math.random() * 5));
				const voucherUsed = voucherRedeemed.map(value => Math.round(value * 0.8)); // 80% voucher đã đổi được sử dụng

				charts.vouchers.data.labels = voucherDates;
				charts.vouchers.data.datasets[0].data = voucherRedeemed;
				charts.vouchers.data.datasets[1].data = voucherUsed;
				charts.vouchers.update();
			}

			// Cập nhật biểu đồ khách hàng mới
			function updateNewMembersChart(data) {
				const memberDates = data.newMembershipChart.map(item => item.label.trim());
				const joinCounts = data.newMembershipChart.map(item => item.value ?? 0);

				// Đảm bảo chart là loại cột
				charts.newMembers.config.type = 'bar';

				// Cập nhật dữ liệu
				charts.newMembers.data.labels = memberDates;
				charts.newMembers.data.datasets[0] = {
					label: 'Khách hàng mới',
					data: joinCounts,
					backgroundColor: 'rgba(75, 192, 192, 0.6)',
					borderColor: 'rgba(75, 192, 192, 1)',
					borderWidth: 1,
					categoryPercentage: 0.8, // Giúp cột đều hơn
					barPercentage: 0.9       // Độ rộng cột
				};

				// Xóa các thuộc tính không cần thiết cho bar chart
				delete charts.newMembers.data.datasets[0].tension;
				delete charts.newMembers.data.datasets[0].fill;

				// Tiêu đề biểu đồ
				charts.newMembers.options.plugins = {
					...charts.newMembers.options.plugins,
					title: {
						display: true,
						text: 'Số lượng khách hàng mới theo ngày',
						font: { size: 16 }
					}
				};

				// Cập nhật trục Y
				charts.newMembers.options.scales.y = {
					beginAtZero: true,
					ticks: { stepSize: 1 },
					title: {
						display: true,
						text: 'Số lượng khách hàng mới',
					}
				};

				// Cập nhật trục X cho cột đều
				charts.newMembers.options.scales.x = {
					// ...charts.newMembers.options.scales.x,
					grid: {
						offset: true // Cực kỳ quan trọng cho hiển thị đều
					}
				};

				// Cập nhật biểu đồ
				charts.newMembers.update();
			}

			// Format tiền tệ Việt Nam
			function formatCurrency(value) {
				return new Intl.NumberFormat('vi-VN', {
					style: 'currency',
					currency: 'VND',
					maximumFractionDigits: 0
				}).format(value);
			}

			// Kiểm tra element có đang trong viewport không
			function isElementInViewport(el) {
				const rect = el.getBoundingClientRect();
				return (
					rect.top >= 0 &&
					rect.left >= 0 &&
					rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
					rect.right <= (window.innerWidth || document.documentElement.clientWidth)
				);
			}

			// Public API
			return {
				init: init
			};
		})();

		// Khởi tạo dashboard khi document ready
		$(document).ready(function () {
			Dashboard.init();
		});
	</script>
}