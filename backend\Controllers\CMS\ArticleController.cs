﻿using AutoMapper;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Articles;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Responses.Articles;
using MiniAppCore.Services.Articles;
using MiniAppCore.Services.Articles.ArticleCategories;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class ArticleController(IMapper mapper, IArticleService articleService, IArticleCategoryService articleCategoryService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("Article/Create")]
        public async Task<IActionResult> Create()
        {
            var article = new ArticleResponse()
            {
                Id = string.Empty,
                BannerImage = string.Empty,
                Title = string.Empty,
                Content = string.Empty,
                Status = 0,
                LastModifiedDate = DateTime.Now,
                CreatedDate = DateTime.Now,
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới tin tức";
            ViewBag.ArticleCategories = await articleCategoryService.GetAllAsync();
            return PartialView("_Article", article);
        }

        [HttpGet("Article/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật tin tức";
            var result = await articleService.GetByIdAsync(id);
            if (result == null)
            {
                return RedirectToAction("Create");
            }
            ViewBag.ArticleCategories = await articleCategoryService.GetAllAsync();
            return PartialView("_Article", ConvertArticle(result));
        }

        private ArticleResponse ConvertArticle(Article item, bool summarize = true)
        {
            ArticleResponse article = mapper.Map<ArticleResponse>(item);

            var baseUrl = $"{Request.Scheme}://{Request.Host.Value}";

            // Xử lý Images
            article.Images = !string.IsNullOrEmpty(item.Images)
                ? item.Images.Split(',').Select(x => $"{baseUrl}/uploads/images/articles/{x}").ToList()
                : new List<string>();

            // Xử lý BannerImage
            article.BannerImage = !string.IsNullOrEmpty(item.BannerImage)
                ? $"{baseUrl}/uploads/images/articles/{item.BannerImage.Split(',').FirstOrDefault()}"
                : $"{baseUrl}/images/no-image-2.jpg";

            article.SummarizeContent = Tools.SummarizeHtmlContent(article.Content, 200);
            return article;
        }
    }
}
