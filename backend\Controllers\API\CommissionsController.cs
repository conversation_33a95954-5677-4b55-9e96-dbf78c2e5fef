﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Responses.Affiliates;
using MiniAppCore.Services.Affiliates.Commissions;
using MiniAppCore.Services.Memberships;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class CommissionsController(ILogger<CommissionsController> logger, ICommissionService commissionService, IMembershipService membershipService) : ControllerBase
    {
        [HttpGet("my-transactions")]
        public async Task<IActionResult> GetUserTransactions()
        {
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var details = await commissionService.GetUserCommissionTransactions(userZaloId);
                var totalCommission = details.Sum(c => c.TotalCommission);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = details,
                    TotalCommissions = totalCommission
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in GetReferralDetail: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpGet("Invited")]
        public async Task<IActionResult> GetUsersInvited()
        {
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var userCommssions = await commissionService.GetUserCommissionTransactions(userZaloId);
                var groupedCommissions = userCommssions
                   .GroupBy(t => t.UserZaloId)
                   .Select(g => new GroupedCommission
                   {
                       UserZaloId = g.Key,
                       UserZaloName = g.First().UserZaloName,
                       PhoneNumber = g.First().PhoneNumber,
                       ReferralCode = g.First().ReferralCode,
                       Avatar = g.First().Avatar,
                       TotalCommission = g.Sum(t => t.TotalCommission)
                   })
                   .ToList();
                var totalCommission = groupedCommissions.Sum(c => c.TotalCommission);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = groupedCommissions,
                    TotalCommission = totalCommission
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in GetReferralDetail: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpGet("{referredUserZaloId}/History")]
        public async Task<IActionResult> GetUserDetailCommissions(string referredUserZaloId)
        {
            try
            {
                var userZaloId = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "UserZaloId")?.Value ?? string.Empty;
                if (string.IsNullOrEmpty(userZaloId))
                {
                    return Unauthorized();
                }
                var details = await commissionService.GetReferredHistoryCommissions(userZaloId, referredUserZaloId);

                var commissionHistory = details
                    .GroupBy(d => new { d.TotalCommission, d.CreatedDate })
                    .Select(g => new
                    {
                        Commission = g.Key.TotalCommission,
                        CreatedDate = g.Key.CreatedDate
                    })
                    .ToList();

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = new
                    {
                        UserZaloId = userZaloId,
                        UserZaloName = details.FirstOrDefault()?.UserZaloName ?? string.Empty,
                        CommissionHistory = commissionHistory
                    }
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in GetReferralDetail: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpGet("{userZaloId}/TreeReferral")]
        public async Task<IActionResult> GetTreeReferral(string userZaloId)
        {
            await Task.CompletedTask;
            return Ok();
        }

        [HttpGet("recipients")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> GetCommissionRecipients(
            [FromQuery] int page = 1,
            [FromQuery] int pagesize = 10,
            [FromQuery] string? keyword = null,
            [FromQuery] string? status = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var (data, count) = await commissionService.GetCommissionRecipients(
                    page, pagesize, keyword, status, startDate, endDate);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = data,
                    Count = count
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in GetCommissionRecipients: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpGet("transactions")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> GetTransactions(
            [FromQuery] int page = 1,
            [FromQuery] int pagesize = 10,
            [FromQuery] string referrerZaloId = "",
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            try
            {
                var (data, count) = await commissionService.GetTransactionsByReferrerId(
                    page, pagesize, referrerZaloId, startDate, endDate);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = data,
                    Count = count
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in GetTransactions: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> UpdateTransactionStatus(string id, [FromBody] UpdateStatusRequest request)
        {
            try
            {
                var success = await commissionService.UpdateTransactionStatus(id, request.Status);
                return Ok(new
                {
                    Code = success ? 0 : 1,
                    Message = success ? "Cập nhật trạng thái thành công!" : "Không thể cập nhật trạng thái!"
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in UpdateTransactionStatus: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpPost("pay-all/{referrerZaloId}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> PayAllCommissions(string referrerZaloId)
        {
            try
            {
                var success = await commissionService.PayAllCommissions(referrerZaloId);

                return Ok(new
                {
                    Code = success ? 0 : 1,
                    Message = success ? "Thanh toán tất cả hoa hồng thành công!" : "Không có hoa hồng nào cần thanh toán!"
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in PayAllCommissions: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpPost("export-commission")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> ExportCommissions([FromForm] DateTime startDate, [FromForm] DateTime endDate)
        {
            try
            {
                var excelData = await commissionService.ExportCommissionsToExcel(startDate, endDate);

                return File(
                    excelData,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "nguoi_nhan_hoa_hong.xlsx");
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in ExportCommissions: {ex.Message}");
                return BadRequest(new
                {
                    Code = 1,
                    Message = "Không thể xuất dữ liệu!"
                });
            }
        }

        [HttpPost]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> CalculateCommissions([FromForm] DateTime? startDate, [FromForm] DateTime? endDate)
        {
            try
            {
                var success = await commissionService.CalculateCommissions(startDate, endDate);

                return Ok(new
                {
                    Code = success ? 0 : 1,
                    Message = success ? "Tính hoa hồng thành công!" : "Không thể tính hoa hồng!"
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in CalculateCommissions: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpGet("config")]
        public async Task<IActionResult> GetCommissionConfig()
        {
            try
            {
                var rate = await commissionService.GetCommissionConfigAsync();

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = rate
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in GetCommissionConfig: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpPut("config")]
        [Authorize(Roles = "ADMIN")]
        public async Task<IActionResult> UpdateCommissionConfig([FromBody] CommissionConfigRequest request)
        {
            try
            {
                var success = await commissionService.AddOrUpdateCommissionRateAsync(request.Rate);
                return Ok(new
                {
                    Code = success > 0 ? 0 : 1,
                    Message = success > 0 ? "Cập nhật tỷ lệ hoa hồng thành công!" : "Không thể cập nhật tỷ lệ hoa hồng!"
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(200, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in UpdateCommissionConfig: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = "Đã có lỗi xảy ra!",
                });
            }
        }

        [HttpPost("test-commission/{orderId}")]
        public async Task<IActionResult> TestCommissionCalculation(string orderId)
        {
            try
            {
                // Test tính toán commission cho order
                await commissionService.CalculateAndSaveCommissionForOrder(orderId);

                return Ok(new
                {
                    Code = 0,
                    Message = "Tính toán hoa hồng thành công!",
                    Data = new { OrderId = orderId }
                });
            }
            catch (Exception ex)
            {
                logger.LogError($"Error in TestCommissionCalculation: {ex.Message}");
                return Ok(new
                {
                    Code = 1,
                    Message = $"Đã có lỗi xảy ra: {ex.Message}",
                });
            }
        }
    }

    public class UpdateStatusRequest
    {
        public bool Status { get; set; }
    }

    public class CommissionConfigRequest
    {
        public decimal Rate { get; set; }
    }
}
