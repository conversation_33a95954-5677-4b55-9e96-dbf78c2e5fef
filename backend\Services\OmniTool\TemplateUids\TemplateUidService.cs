﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.ETM;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;

namespace MiniAppCore.Services.OmniTool.TemplateUids
{
    public class TemplateUidService(IUnitOfWork unitOfWork) : Service<ZaloTemplateUid>(unitOfWork), ITemplateUidService
    {
        public override async Task<PagedResult<ZaloTemplateUid>> GetPage(IRequestQuery query)
        {
            var invoicteTemplateQuery = _repository.AsQueryable();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                string keyword = query.Keyword;
                invoicteTemplateQuery = invoicteTemplateQuery.Where(b => b.Name.Contains(keyword));
            }

            var totalItems = await invoicteTemplateQuery.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)query.PageSize);
            var items = await invoicteTemplateQuery.OrderByDescending(x => x.CreatedDate)
                                                   .Skip(query.Skip)
                                                   .Take(query.PageSize)
                                                   .ToListAsync();
            return new PagedResult<ZaloTemplateUid>()
            {
                Data = items,
                TotalPages = totalPages,
            };
        }

        public override async Task<int> UpdateAsync(string id, ZaloTemplateUid dto)
        {
            var template = await GetByIdAsync(id);
            if (template == null)
            {
                throw new CustomException(200, "Không tìm thấy mẫu hóa đơn!");
            }
            template.Name = dto.Name;
            template.Message = dto.Message;
            template.ListParams = dto.ListParams;
            return await base.UpdateAsync(template);
        }
    }
}
