﻿using System.Text.Json;

namespace MiniAppCore.Base.Helpers
{
    public class Locator
    {
        private static string apiLocationUrl = "https://open.oapi.vn/location";
        private static HttpClient httpClient = new HttpClient();

        public static async Task<Location?> GetCityByLocationId(string cityId)
        {
            string endpoint = $"{apiLocationUrl}/provinces?page=0&size=63";
            var response = await httpClient.GetAsync(endpoint);

            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse>(jsonString, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Data != null)
                {
                    // Tìm city theo id
                    var city = apiResponse.Data.FirstOrDefault(x => x.Id == cityId);
                    return city; // Nếu null thì tự null về
                }
            }

            return null;
        }

        public static async Task<Location?> GetDistrictByLocationId(string cityId, string districtId)
        {
            string endpoint = $"{apiLocationUrl}/districts/{cityId}?page=0&size=100";
            var response = await httpClient.GetAsync(endpoint);
            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse>(jsonString, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Data != null)
                {
                    // Tìm city theo id
                    var city = apiResponse.Data.FirstOrDefault(x => x.Id == districtId);
                    return city; // Nếu null thì tự null về
                }
            }

            return null;
        }

        public static async Task<Location?> GetWardByLocationId(string districtId, string wardId)
        {
            string endpoint = $"{apiLocationUrl}/wards/{districtId}?page=0&size=100";
            var response = await httpClient.GetAsync(endpoint);

            if (response.IsSuccessStatusCode)
            {
                var jsonString = await response.Content.ReadAsStringAsync();
                var apiResponse = JsonSerializer.Deserialize<ApiResponse>(jsonString, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (apiResponse?.Data != null)
                {
                    // Tìm city theo id
                    var city = apiResponse.Data.FirstOrDefault(x => x.Id == wardId);
                    return city; // Nếu null thì tự null về
                }
            }

            return null;
        }

        public static async Task<(string wardName, string districtName, string provinceName)> GetLocationNamesAsync(string cityId, string districtId, string wardId)
        {
            var city = await GetCityByLocationId(cityId);
            var district = await GetDistrictByLocationId(cityId, districtId);
            var ward = await GetWardByLocationId(districtId, wardId);

            return (ward?.Name ?? "", district?.Name ?? "", city?.Name ?? "");
        }

        public static string FormatFullAddress(string? street, string? ward, string? district, string? province)
        {
            return $"{street}, {ward}, {district}, {province}".Trim().Trim(',', ' ');
        }
    }

    public class ApiResponse
    {
        public int Total { get; set; }
        public List<Location>? Data { get; set; }
        public string? Code { get; set; }
        public string? Message { get; set; }
    }

    public class Location
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public int Type { get; set; } // Vì trong response là int
        public string? TypeText { get; set; }
        public string? Slug { get; set; }
    }
}
