﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Offers
{
    public class Promotion : BaseEntity
    {
        public required string Name { get; set; }
        public string? Description { get; set; }
        public decimal TotalAmount { get; set; }
        public bool IsActive { get; set; } = true;

        public DateTime StartDate { get; set; } = DateTime.Now;
        public DateTime ExpiryDate { get; set; } = DateTime.Now;
    }
}
