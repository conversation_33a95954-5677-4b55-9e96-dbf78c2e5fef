﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateFieldNameEntityForSurvey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "c0f03acf6c6a4532b0521833de5611fb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "050ce0ac4bfa428a899673a188cf9564");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0704b3e405bc4a3a80a91d1e2096c385");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0a617a21ad454ce5984b504aa2ad093e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0d0aac6d1c994a2999e0c1978f0f2b37");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0d4ae8118cb44188bee06e84dd414d4b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "121272c7d60d49539d8006539a2d34a6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "12d329bb8fa146cbb838c01dcb96ccb0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "14dc21725cd94d6b85936bd79b8f5f1c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2cc506faa3084a27bef9fe6d7733b53b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2f654980583a4971b75192d99e4ab7d1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "511d5b240f1d4a6f9623cf48346cd9d8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "518fd7d216bc4e32b18d623fc15062d4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5596c9c7a60b44679746527f8126df1b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "664cfe60885745ab98cc035a1b9db861");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "667b611ea0c94932b92ec834f7cc07d6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "75d9c11f1e214dd6a6e38339ba8f1032");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7922926f9495478bba727124868001a3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "83729961fb254b2c85a4b6e09edce637");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8756d7fded514827b34e6ce3199f1993");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "93c87391b27b438ebb43329a111d149d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9ad17a1668c8448ba452b88d3366aef8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9fe71f506f7f4121bb7414e9d3869c9c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b14ba72c9cdc473f937063e02c5712fd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b1740b50787043a79768fa47e7530224");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b1ae743ca8fd4cd6824bb3eda80a638d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bb662e0088fd42249bc952abfe388662");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bc1e1dcd981e44c28b77c0d07944239a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c5b6742f3d454e41bc73a9a44d2c6130");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c835bf7cae3b40bdadc2ec483f2d0e19");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cbe4e961b93344d18c432faaaf299479");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d29c5d96d2b24e8aa9ccacb148ba3643");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "db3527865c8f40939e408cc83cf13f3f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e35e41342eb7434dac2068350b9a6493");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f5ee9443dd11469fb43f71c16665413f");

            migrationBuilder.DropColumn(
                name: "AnswerId",
                table: "SurveySubmissionDetails");

            migrationBuilder.RenameColumn(
                name: "SubmissionId",
                table: "SurveySubmissionDetails",
                newName: "SurveySubmissionId");

            migrationBuilder.RenameColumn(
                name: "QuestionId",
                table: "SurveySubmissionDetails",
                newName: "SurveyQuestionId");

            migrationBuilder.RenameColumn(
                name: "LikertQuestionId",
                table: "SurveySubmissionDetails",
                newName: "SurveyLikertQuestionId");

            migrationBuilder.RenameColumn(
                name: "LikertId",
                table: "SurveySubmissionDetails",
                newName: "SurveyAnswerId");

            migrationBuilder.RenameColumn(
                name: "SectionId",
                table: "SurveyQuestions",
                newName: "SurveySectionId");

            migrationBuilder.RenameColumn(
                name: "QuestionId",
                table: "SurveyLikertQuestions",
                newName: "SurveyQuestionId");

            migrationBuilder.RenameColumn(
                name: "QuestionId",
                table: "SurveyAnswers",
                newName: "SurveyQuestionId");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEP2FDbahLwlsorVdiQvjEjvvB0M8jofChs7rBBklQXC6EWlIDtOeleNDk0p4eQoaDA==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "5cda10eb8a3b414a8f8d13fc242fbb90", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 22, 11, 42, 5, 887, DateTimeKind.Local).AddTicks(2670), "FeaturesButton", new DateTime(2025, 5, 22, 11, 42, 5, 887, DateTimeKind.Local).AddTicks(2674) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "048f25d4b83342cbae3593616d2def50", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4124), true, "DiscountList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4125), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "11e9adfefdd843bc9eafe8245f22e63c", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4142), true, "SurveyList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4142), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "137846f0f22344f69086bcb67a7c9838", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4146), true, "History", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4147), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "13e7d53296874a4995c62680d7fc4ffb", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4162), true, "GeneralSetting", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4163), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "1845b3432ced4461a18d98b3ceb412ff", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4223), true, "ShippingFeeConfig", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4224), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "1a6a47a882454b428e952d4498fb4b67", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4228), true, "CustomForm", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4228), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "20489cc9d1b5429e846344aa2b5fb75e", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4075), true, "BranchList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4077), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "20617f181e6b40b5b66955b447fad7bd", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4118), true, "BookingList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4118), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "233b2a9ac10d48459f8adc22a322ba05", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4160), true, "TemplateUidList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4161), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "40d87bcc8b83491a81a539da4430a68a", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4144), true, "GameList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4145), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "432281fd7da84d029f08e926983232a3", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4093), true, "ArticleList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4093), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "4e9773b349d24170a33fcd2c5a4f5b86", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4148), true, "GamePrize", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4149), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "515e34a4a3874cadbb4e1a5db9501ba9", new DateTime(2025, 5, 22, 11, 42, 5, 885, DateTimeKind.Local).AddTicks(7690), true, "Overview", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(3293), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "52398028e57544b0936c866ffc8b827d", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4122), true, "InvoiceTemplate", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4123), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "528a875690de42b5829a78aef50c215b", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4140), true, "History", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4140), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "593ff3958f64491186a0324f53efe34c", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4120), true, "OrderList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4120), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "61f3e0049d9a4b678bbd726066fb16b8", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4113), true, "BookingItem", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4113), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "67d308c4e9bd49d0827e0d3e28e9ddac", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4164), true, "EnableFeatures", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4165), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "6fdbdcde35a9404cb012b55ea80c2757", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4090), true, "ArticleCategory", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4091), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "73426e57c6224996b67c370e369d03ff", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4105), true, "Brand", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4106), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "73e2350fa27c4a58a0e2d3c4501073c4", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4087), true, "AffiliateList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4087), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "7e583a5756414fe5867307b34cefb4f8", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4137), true, "Tag", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4138), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "8307bccda9d04d1486cc310ab64611a9", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4110), true, "ProductList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4110), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "aaf9f5f4c1bd43a5b024371ff40e0588", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4131), true, "MembershipList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4131), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "b73779b0dce5464785e7e183cda0740f", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4129), true, "VoucherList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4129), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "be0fa9beb6bb46cdb44678e0447a30ca", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4158), true, "EventTemplateHistory", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4159), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "c10c548374f3458399c39d87254dec6d", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4151), true, "CampaignList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4151), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "c4054a3114ff4f0cb864a3fb9059a76f", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4167), true, "MembershipExtendDefaults", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4167), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "cac5c4a4dd5a4a42b59d1a9ecbd99697", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4108), true, "ProductProperty", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4108), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "d619c649570d46e096a6cc24aca123d7", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4134), true, "Rank", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4134), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "da962c5bb54a47a68b163975687ee853", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4153), true, "CampaignHistory", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4153), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "de52f5b57cef4ac7b0ce695596fc6c9f", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4127), true, "Promotion", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4127), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "eb018cdfc8e84e419bdde5abc6867b07", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4156), true, "EventTemplateList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4157), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "fd9b6d538b684a45b79ddb318e3139e7", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4103), true, "Category", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4104), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "5cda10eb8a3b414a8f8d13fc242fbb90");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "048f25d4b83342cbae3593616d2def50");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "11e9adfefdd843bc9eafe8245f22e63c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "137846f0f22344f69086bcb67a7c9838");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "13e7d53296874a4995c62680d7fc4ffb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1845b3432ced4461a18d98b3ceb412ff");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1a6a47a882454b428e952d4498fb4b67");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "20489cc9d1b5429e846344aa2b5fb75e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "20617f181e6b40b5b66955b447fad7bd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "233b2a9ac10d48459f8adc22a322ba05");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "40d87bcc8b83491a81a539da4430a68a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "432281fd7da84d029f08e926983232a3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4e9773b349d24170a33fcd2c5a4f5b86");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "515e34a4a3874cadbb4e1a5db9501ba9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "52398028e57544b0936c866ffc8b827d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "528a875690de42b5829a78aef50c215b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "593ff3958f64491186a0324f53efe34c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "61f3e0049d9a4b678bbd726066fb16b8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "67d308c4e9bd49d0827e0d3e28e9ddac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6fdbdcde35a9404cb012b55ea80c2757");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "73426e57c6224996b67c370e369d03ff");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "73e2350fa27c4a58a0e2d3c4501073c4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7e583a5756414fe5867307b34cefb4f8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8307bccda9d04d1486cc310ab64611a9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "aaf9f5f4c1bd43a5b024371ff40e0588");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b73779b0dce5464785e7e183cda0740f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "be0fa9beb6bb46cdb44678e0447a30ca");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c10c548374f3458399c39d87254dec6d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c4054a3114ff4f0cb864a3fb9059a76f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cac5c4a4dd5a4a42b59d1a9ecbd99697");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d619c649570d46e096a6cc24aca123d7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "da962c5bb54a47a68b163975687ee853");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "de52f5b57cef4ac7b0ce695596fc6c9f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "eb018cdfc8e84e419bdde5abc6867b07");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fd9b6d538b684a45b79ddb318e3139e7");

            migrationBuilder.RenameColumn(
                name: "SurveySubmissionId",
                table: "SurveySubmissionDetails",
                newName: "SubmissionId");

            migrationBuilder.RenameColumn(
                name: "SurveyQuestionId",
                table: "SurveySubmissionDetails",
                newName: "QuestionId");

            migrationBuilder.RenameColumn(
                name: "SurveyLikertQuestionId",
                table: "SurveySubmissionDetails",
                newName: "LikertQuestionId");

            migrationBuilder.RenameColumn(
                name: "SurveyAnswerId",
                table: "SurveySubmissionDetails",
                newName: "LikertId");

            migrationBuilder.RenameColumn(
                name: "SurveySectionId",
                table: "SurveyQuestions",
                newName: "SectionId");

            migrationBuilder.RenameColumn(
                name: "SurveyQuestionId",
                table: "SurveyLikertQuestions",
                newName: "QuestionId");

            migrationBuilder.RenameColumn(
                name: "SurveyQuestionId",
                table: "SurveyAnswers",
                newName: "QuestionId");

            migrationBuilder.AddColumn<string>(
                name: "AnswerId",
                table: "SurveySubmissionDetails",
                type: "nvarchar(36)",
                maxLength: 36,
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEKPJlENgen/pqI7bldPBbL144Qz5wvf/KQ3KlzZIX02S9otl3aRF7nS0IXvcCQtFmw==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "c0f03acf6c6a4532b0521833de5611fb", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(7684), "FeaturesButton", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(7688) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "050ce0ac4bfa428a899673a188cf9564", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(616), true, "GameList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(616), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "0704b3e405bc4a3a80a91d1e2096c385", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(542), true, "Promotion", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(543), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "0a617a21ad454ce5984b504aa2ad093e", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(546), true, "VoucherList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(546), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "0d0aac6d1c994a2999e0c1978f0f2b37", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(624), true, "GamePrize", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(624), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "0d4ae8118cb44188bee06e84dd414d4b", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(491), true, "ArticleList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(492), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "121272c7d60d49539d8006539a2d34a6", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(555), true, "Rank", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(555), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "12d329bb8fa146cbb838c01dcb96ccb0", new DateTime(2025, 5, 14, 14, 38, 46, 686, DateTimeKind.Local).AddTicks(2784), true, "Overview", new DateTime(2025, 5, 14, 14, 38, 46, 686, DateTimeKind.Local).AddTicks(9810), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "14dc21725cd94d6b85936bd79b8f5f1c", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(654), true, "MembershipExtendDefaults", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(655), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2cc506faa3084a27bef9fe6d7733b53b", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(559), true, "Tag", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(559), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "2f654980583a4971b75192d99e4ab7d1", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(483), true, "AffiliateList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(483), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "511d5b240f1d4a6f9623cf48346cd9d8", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(651), true, "EnableFeatures", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(651), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "518fd7d216bc4e32b18d623fc15062d4", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(563), true, "History", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(563), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "5596c9c7a60b44679746527f8126df1b", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(643), true, "TemplateUidList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(644), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "664cfe60885745ab98cc035a1b9db861", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(504), true, "Category", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(504), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "667b611ea0c94932b92ec834f7cc07d6", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(534), true, "InvoiceTemplate", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(534), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "75d9c11f1e214dd6a6e38339ba8f1032", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(513), true, "ProductProperty", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(513), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "7922926f9495478bba727124868001a3", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(620), true, "History", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(620), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "83729961fb254b2c85a4b6e09edce637", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(664), true, "CustomForm", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(665), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8756d7fded514827b34e6ce3199f1993", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(550), true, "MembershipList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(550), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "93c87391b27b438ebb43329a111d149d", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(516), true, "ProductList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(517), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "9ad17a1668c8448ba452b88d3366aef8", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(627), true, "CampaignList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(628), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "9fe71f506f7f4121bb7414e9d3869c9c", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(539), true, "DiscountList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(539), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "b14ba72c9cdc473f937063e02c5712fd", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(527), true, "BookingList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(527), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "b1740b50787043a79768fa47e7530224", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(530), true, "OrderList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(531), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "b1ae743ca8fd4cd6824bb3eda80a638d", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(611), true, "SurveyList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(612), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "bb662e0088fd42249bc952abfe388662", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(658), true, "ShippingFeeConfig", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(658), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "bc1e1dcd981e44c28b77c0d07944239a", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(523), true, "BookingItem", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(523), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "c5b6742f3d454e41bc73a9a44d2c6130", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(508), true, "Brand", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(509), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c835bf7cae3b40bdadc2ec483f2d0e19", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(640), true, "EventTemplateHistory", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(640), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "cbe4e961b93344d18c432faaaf299479", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(632), true, "CampaignHistory", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(633), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d29c5d96d2b24e8aa9ccacb148ba3643", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(647), true, "GeneralSetting", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(647), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "db3527865c8f40939e408cc83cf13f3f", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(476), true, "BranchList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(477), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "e35e41342eb7434dac2068350b9a6493", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(487), true, "ArticleCategory", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(487), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "f5ee9443dd11469fb43f71c16665413f", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(636), true, "EventTemplateList", new DateTime(2025, 5, 14, 14, 38, 46, 687, DateTimeKind.Local).AddTicks(636), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" }
                });
        }
    }
}
