﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Models.DTOs.Surveys;

namespace MiniAppCore.Services.Surveys.Answers
{
    public interface ISurveyAnswerService : IService<SurveyAnswer>
    {
        Task<int> DeleteAnswersByQuestionIdsAsync(List<string> questionIds);
        Task<int> CreateOrUpdateAnswersAsync(List<SurveyAnswerDTO> answers);
        Task<int> CreateOrUpdateAnswersAsync(Dictionary<string, List<SurveyAnswerDTO>> answers); // questionId và answers

        Task<List<T>> GetAnswersResponseByQuestionIds<T>(List<string> questionIds);
    }
}
