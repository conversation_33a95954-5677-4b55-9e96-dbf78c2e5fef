﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Products;

namespace MiniAppCore.Services.RecommendServices
{
    public class RecommendService(IUnitOfWork unitOfWork) : IRecommendService
    {
        public async Task<List<Product>> GetRecommendProducts(string? criteria, string? criteriaValue, string? phoneNumber, int top = 5, double confidence = 0.3)
        {
            List<Product> products = new List<Product>();
            //_repo.top = top;
            //_repo.THRESHOLD = confidence;

            //switch (criteria)
            //{
            //    case "sanpham-sanpham":
            //        products = await _repo.GetRecommendedProducts(criteriaValue, top, "sanpham");
            //        break;

            //    case "sanpham-dichvu":
            //        products = await _repo.GetRecommendedProducts(criteriaValue, top, "dichvu");
            //        break;

            //    case "dichvu-dichvu":
            //        products = await _repo.GetRecommendedProducts(criteriaValue, top, "dichvu");
            //        break;

            //    case "dichvu-sanpham":
            //        products = await _repo.GetRecommendedProducts(criteriaValue, top, "sanpham");
            //        break;

            //    default:
            //        products = await _repo.GetRecommendByDefault(criteria, criteriaValue, top, phoneNumber);
            //        break;
            //}

            //if (products.Count() == 0)
            //{
            //    products = await _repo.GetServicesOrProductMostUsed("sanpham", top);
            //}

            return products;
        }
    }
}
