﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddCommissionRateAndCategoryChild : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "5cda10eb8a3b414a8f8d13fc242fbb90");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "048f25d4b83342cbae3593616d2def50");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "11e9adfefdd843bc9eafe8245f22e63c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "137846f0f22344f69086bcb67a7c9838");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "13e7d53296874a4995c62680d7fc4ffb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1845b3432ced4461a18d98b3ceb412ff");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1a6a47a882454b428e952d4498fb4b67");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "20489cc9d1b5429e846344aa2b5fb75e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "20617f181e6b40b5b66955b447fad7bd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "233b2a9ac10d48459f8adc22a322ba05");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "40d87bcc8b83491a81a539da4430a68a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "432281fd7da84d029f08e926983232a3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4e9773b349d24170a33fcd2c5a4f5b86");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "515e34a4a3874cadbb4e1a5db9501ba9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "52398028e57544b0936c866ffc8b827d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "528a875690de42b5829a78aef50c215b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "593ff3958f64491186a0324f53efe34c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "61f3e0049d9a4b678bbd726066fb16b8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "67d308c4e9bd49d0827e0d3e28e9ddac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6fdbdcde35a9404cb012b55ea80c2757");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "73426e57c6224996b67c370e369d03ff");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "73e2350fa27c4a58a0e2d3c4501073c4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7e583a5756414fe5867307b34cefb4f8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8307bccda9d04d1486cc310ab64611a9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "aaf9f5f4c1bd43a5b024371ff40e0588");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b73779b0dce5464785e7e183cda0740f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "be0fa9beb6bb46cdb44678e0447a30ca");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c10c548374f3458399c39d87254dec6d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c4054a3114ff4f0cb864a3fb9059a76f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cac5c4a4dd5a4a42b59d1a9ecbd99697");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d619c649570d46e096a6cc24aca123d7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "da962c5bb54a47a68b163975687ee853");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "de52f5b57cef4ac7b0ce695596fc6c9f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "eb018cdfc8e84e419bdde5abc6867b07");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fd9b6d538b684a45b79ddb318e3139e7");

            migrationBuilder.AddColumn<string>(
                name: "ReferrerId",
                table: "Memberships",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OrderPriority",
                table: "Articles",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateTable(
                name: "AffiliateCommissionRates",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Level = table.Column<int>(type: "int", nullable: false),
                    Rate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    EffectiveDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AffiliateCommissionRates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CategoryChilds",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OrderPriority = table.Column<int>(type: "int", nullable: false),
                    CategoryId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CategoryChilds", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ProductCategoryChilds",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    CategoryChildId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ProductId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProductCategoryChilds", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEO8Wal3g0UsM6GSdmaGtoROVpAS4oV5SNFb3OllSCfzUiNfysmsNoc6fiaW54VPTbw==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "ab67c03ee6c541ae9e6aeaa73e6fbc01", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 28, 16, 58, 19, 250, DateTimeKind.Local).AddTicks(5016), "FeaturesButton", new DateTime(2025, 5, 28, 16, 58, 19, 250, DateTimeKind.Local).AddTicks(5019) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "100b3d77fafc44009b5f3d3bf5ba3743", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7226), true, "MembershipList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7227), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "121e0cd4e9e9421f9235e176f8a7a83b", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7231), true, "Rank", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7231), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "249c38c9bd244bf69f539aaa1b3f4d06", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7245), true, "CampaignList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7245), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "24a22a91fde4415ea73009a6942351f4", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7224), true, "VoucherList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7225), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "2611a053b94a4e36892a07ea47b1e3b6", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7204), true, "ProductProperty", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7204), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "26aa279c4c5b4457bdc5848635c40dac", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7222), true, "Promotion", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7222), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "2b3b6ca3b6d545b38f6ccf71cbb57655", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7187), true, "ArticleCategory", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7188), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "30f6274a8e0144d291a2e7a1164137d1", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7253), true, "EventTemplateHistory", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7253), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "40b0a366eba044dfa9a77ea72d94eddc", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7243), true, "GamePrize", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7243), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "533f8dcafda74fd9aaa99cbab4aff95e", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7248), true, "CampaignHistory", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7249), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "55a8dc5077e94a259ab021c151050e82", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7259), true, "EnableFeatures", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7259), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "62ce06b84bd14cd88e9d919ed4316590", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7237), true, "SurveyList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7237), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "6b554834bf7340469fb2cec30e9f59e4", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7128), true, "BranchList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7130), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "70b320a5a8d948f39905d2031e1abbd8", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7184), true, "AffiliateList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7184), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "7b5d5213709840e29b3032a5d3a26582", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7255), true, "TemplateUidList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7255), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "84e31336f8c84cd59c023b044310c4e8", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7267), true, "CustomForm", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7267), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "8d942308f220407f8c04077f3d5a04bd", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7239), true, "GameList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7239), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "957b7b02256542a89b83c0032569a6db", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7190), true, "ArticleList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7190), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "9946ea89f80e42ee85ea3ddb90a57933", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7263), true, "ShippingFeeConfig", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7263), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "a41a5eeedce0452e84f25725e4c168e1", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7235), true, "History", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7235), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "a861681fdd0e491c9415d7c40251d9bb", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(1023), true, "Overview", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(6386), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "af0a2e91c04f45d9ad158cd32bbff151", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7206), true, "ProductList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7206), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "b8653872590c464cbd575934bfc4dce2", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7216), true, "OrderList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7216), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "c041400916f24ef8847550fe62ae94c5", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7218), true, "InvoiceTemplate", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7218), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "c5c0608ee334485fa9014f0853f439c4", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7211), true, "BookingItem", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7212), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "cadee4e8e7334e0a8ad936381c41fabb", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7214), true, "BookingList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7214), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "cf9c8d49affc40aaa29e4cc015c3f650", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7257), true, "GeneralSetting", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7257), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "d4be24ba33ec4f569cedf6c4a24816d5", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7251), true, "EventTemplateList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7251), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d7caa70563ec4a9f80c992f717073cb3", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7261), true, "MembershipExtendDefaults", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7261), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "dec7fab6ffd04fd288d4dd5e0eb2fd8a", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7241), true, "History", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7241), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "e34be14105df43008684fd875818bf4c", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7202), true, "Brand", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7202), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "e4e89cd1b0424fedb1bebba69dee0c13", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7220), true, "DiscountList", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7220), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "f265367fc04f48ec8874e66860236cdc", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7233), true, "Tag", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7233), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "fc4e9a2546274d12ac00eb0d873d497c", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7199), true, "Category", new DateTime(2025, 5, 28, 16, 58, 19, 249, DateTimeKind.Local).AddTicks(7200), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AffiliateCommissionRates");

            migrationBuilder.DropTable(
                name: "CategoryChilds");

            migrationBuilder.DropTable(
                name: "ProductCategoryChilds");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "ab67c03ee6c541ae9e6aeaa73e6fbc01");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "100b3d77fafc44009b5f3d3bf5ba3743");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "121e0cd4e9e9421f9235e176f8a7a83b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "249c38c9bd244bf69f539aaa1b3f4d06");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "24a22a91fde4415ea73009a6942351f4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2611a053b94a4e36892a07ea47b1e3b6");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "26aa279c4c5b4457bdc5848635c40dac");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2b3b6ca3b6d545b38f6ccf71cbb57655");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "30f6274a8e0144d291a2e7a1164137d1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "40b0a366eba044dfa9a77ea72d94eddc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "533f8dcafda74fd9aaa99cbab4aff95e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "55a8dc5077e94a259ab021c151050e82");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "62ce06b84bd14cd88e9d919ed4316590");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "6b554834bf7340469fb2cec30e9f59e4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "70b320a5a8d948f39905d2031e1abbd8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7b5d5213709840e29b3032a5d3a26582");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "84e31336f8c84cd59c023b044310c4e8");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "8d942308f220407f8c04077f3d5a04bd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "957b7b02256542a89b83c0032569a6db");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9946ea89f80e42ee85ea3ddb90a57933");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a41a5eeedce0452e84f25725e4c168e1");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a861681fdd0e491c9415d7c40251d9bb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "af0a2e91c04f45d9ad158cd32bbff151");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b8653872590c464cbd575934bfc4dce2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c041400916f24ef8847550fe62ae94c5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c5c0608ee334485fa9014f0853f439c4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cadee4e8e7334e0a8ad936381c41fabb");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "cf9c8d49affc40aaa29e4cc015c3f650");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d4be24ba33ec4f569cedf6c4a24816d5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d7caa70563ec4a9f80c992f717073cb3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dec7fab6ffd04fd288d4dd5e0eb2fd8a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e34be14105df43008684fd875818bf4c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e4e89cd1b0424fedb1bebba69dee0c13");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "f265367fc04f48ec8874e66860236cdc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fc4e9a2546274d12ac00eb0d873d497c");

            migrationBuilder.DropColumn(
                name: "ReferrerId",
                table: "Memberships");

            migrationBuilder.DropColumn(
                name: "OrderPriority",
                table: "Articles");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEP2FDbahLwlsorVdiQvjEjvvB0M8jofChs7rBBklQXC6EWlIDtOeleNDk0p4eQoaDA==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "5cda10eb8a3b414a8f8d13fc242fbb90", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 5, 22, 11, 42, 5, 887, DateTimeKind.Local).AddTicks(2670), "FeaturesButton", new DateTime(2025, 5, 22, 11, 42, 5, 887, DateTimeKind.Local).AddTicks(2674) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "048f25d4b83342cbae3593616d2def50", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4124), true, "DiscountList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4125), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "11e9adfefdd843bc9eafe8245f22e63c", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4142), true, "SurveyList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4142), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "137846f0f22344f69086bcb67a7c9838", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4146), true, "History", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4147), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "13e7d53296874a4995c62680d7fc4ffb", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4162), true, "GeneralSetting", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4163), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "1845b3432ced4461a18d98b3ceb412ff", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4223), true, "ShippingFeeConfig", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4224), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "1a6a47a882454b428e952d4498fb4b67", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4228), true, "CustomForm", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4228), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "20489cc9d1b5429e846344aa2b5fb75e", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4075), true, "BranchList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4077), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "20617f181e6b40b5b66955b447fad7bd", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4118), true, "BookingList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4118), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "233b2a9ac10d48459f8adc22a322ba05", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4160), true, "TemplateUidList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4161), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "40d87bcc8b83491a81a539da4430a68a", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4144), true, "GameList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4145), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "432281fd7da84d029f08e926983232a3", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4093), true, "ArticleList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4093), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "4e9773b349d24170a33fcd2c5a4f5b86", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4148), true, "GamePrize", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4149), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "515e34a4a3874cadbb4e1a5db9501ba9", new DateTime(2025, 5, 22, 11, 42, 5, 885, DateTimeKind.Local).AddTicks(7690), true, "Overview", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(3293), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "52398028e57544b0936c866ffc8b827d", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4122), true, "InvoiceTemplate", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4123), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "528a875690de42b5829a78aef50c215b", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4140), true, "History", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4140), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "593ff3958f64491186a0324f53efe34c", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4120), true, "OrderList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4120), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "61f3e0049d9a4b678bbd726066fb16b8", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4113), true, "BookingItem", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4113), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "67d308c4e9bd49d0827e0d3e28e9ddac", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4164), true, "EnableFeatures", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4165), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "6fdbdcde35a9404cb012b55ea80c2757", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4090), true, "ArticleCategory", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4091), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "73426e57c6224996b67c370e369d03ff", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4105), true, "Brand", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4106), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "73e2350fa27c4a58a0e2d3c4501073c4", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4087), true, "AffiliateList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4087), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "7e583a5756414fe5867307b34cefb4f8", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4137), true, "Tag", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4138), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "8307bccda9d04d1486cc310ab64611a9", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4110), true, "ProductList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4110), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "aaf9f5f4c1bd43a5b024371ff40e0588", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4131), true, "MembershipList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4131), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "b73779b0dce5464785e7e183cda0740f", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4129), true, "VoucherList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4129), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "be0fa9beb6bb46cdb44678e0447a30ca", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4158), true, "EventTemplateHistory", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4159), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "c10c548374f3458399c39d87254dec6d", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4151), true, "CampaignList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4151), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "c4054a3114ff4f0cb864a3fb9059a76f", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4167), true, "MembershipExtendDefaults", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4167), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "cac5c4a4dd5a4a42b59d1a9ecbd99697", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4108), true, "ProductProperty", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4108), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "d619c649570d46e096a6cc24aca123d7", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4134), true, "Rank", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4134), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "da962c5bb54a47a68b163975687ee853", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4153), true, "CampaignHistory", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4153), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "de52f5b57cef4ac7b0ce695596fc6c9f", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4127), true, "Promotion", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4127), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "eb018cdfc8e84e419bdde5abc6867b07", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4156), true, "EventTemplateList", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4157), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "fd9b6d538b684a45b79ddb318e3139e7", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4103), true, "Category", new DateTime(2025, 5, 22, 11, 42, 5, 886, DateTimeKind.Local).AddTicks(4104), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" }
                });
        }
    }
}
