﻿namespace MiniAppCore.Models.Requests.Articles
{
    public class ArticleRequest
    {
        public string? Author { get; set; }
        public required string Title { get; set; }
        public required string Content { get; set; }
        public required string CategoryId { get; set; }

        public IFormFile? BannerImage { get; set; }
        public List<IFormFile>? Images { get; set; }
        public short Status { get; set; } = 0;
        public DateTime LastModifiedDate { get; set; } = DateTime.Now;
        public List<string> RemovedOldImages { get; set; } = new();
        public string RemovedOldBanner { get; set; } = string.Empty;

        public int OrderPriority { get; set; }
    }
}
