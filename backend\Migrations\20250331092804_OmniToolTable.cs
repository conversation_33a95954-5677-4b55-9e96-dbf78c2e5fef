﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class OmniToolTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CampaignConfigs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    CampaignId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TemplateCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VariableContent = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TemplateVariable = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TagContent = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TagAttribute = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CampaignConfigs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CampaignCSKHs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Status = table.Column<short>(type: "smallint", nullable: false),
                    RoutingRule = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TemplateCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CampaignCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CampaignName = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    UpdateCount = table.Column<byte>(type: "tinyint", nullable: false),
                    CampaignStatusID = table.Column<byte>(type: "tinyint", nullable: false),
                    Note = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IdJob = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Total = table.Column<int>(type: "int", nullable: false),
                    TotalSuccess = table.Column<int>(type: "int", nullable: false),
                    UpdateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ScheduleTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    OldScheduleTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CampaignCSKHs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CampaignPhoneCSKHs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    CampaignCSKHId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PhoneNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ErrorCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IdOmniMess = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ChannelCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RoutingRule = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TemplateCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ParamContent = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<byte>(type: "tinyint", nullable: false),
                    ReportTimes = table.Column<short>(type: "smallint", nullable: false),
                    UpdateCount = table.Column<short>(type: "smallint", nullable: false),
                    DelayTime = table.Column<int>(type: "int", nullable: false),
                    IsCharged = table.Column<bool>(type: "bit", nullable: false),
                    ChannelID = table.Column<short>(type: "smallint", nullable: false),
                    Duration = table.Column<short>(type: "smallint", nullable: false),
                    MtCount = table.Column<short>(type: "smallint", nullable: false),
                    TelcoID = table.Column<short>(type: "smallint", nullable: false),
                    ExtraDuration = table.Column<int>(type: "int", nullable: false),
                    AccountId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProcessTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IndexCrDateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IndexProcessTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ReportedDateTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CampaignPhoneCSKHs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CampaignPhoneCSKHTemps",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    CampaignCSHKId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Status = table.Column<byte>(type: "tinyint", nullable: false),
                    TelcoID = table.Column<byte>(type: "tinyint", nullable: false),
                    RequestID = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RoutingRule = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TemplateCode = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ParamContent = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    AccountId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CampaignPhoneCSKHTemps", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "WebHookLogs",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    Status = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Channel = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Response = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ErrorCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IdOmniMess = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MtCount = table.Column<int>(type: "int", nullable: false),
                    TelcoId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WebHookLogs", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEDXFrP/DqMkzPMAztAL1V3JAjW0Z1Z3JSwfCB/mcpBRluOQ4Pcpb4dKdy+NAthOUdw==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CampaignConfigs");

            migrationBuilder.DropTable(
                name: "CampaignCSKHs");

            migrationBuilder.DropTable(
                name: "CampaignPhoneCSKHs");

            migrationBuilder.DropTable(
                name: "CampaignPhoneCSKHTemps");

            migrationBuilder.DropTable(
                name: "WebHookLogs");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOQyP53AAax8zL34bFYv2OBGRRgVyn7pcwvRKhd8jTxR9hS3JerKunjaPe7o2yNlNQ==");
        }
    }
}
