﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Surveys;
using MiniAppCore.Models.Responses.Surveys;

namespace MiniAppCore.Services.Surveys.UserSurveys
{
    public interface ISurveySubmissionService : IService<SurveySubmission>
    {
        Task<int> SyncDataFromVisitorIdToUserZaloId(string visitorId, string userZaloId);
        Task<int> SubmitSurveyAsync(string userZaloId, SurveySubmissionRequest surveySubmissionRequest);
        Task<PagedResult<SurveySubmissionResponse>> GetSurveyHistoryAsync(RequestQuery requestQuery, string? surveyId, DateTime? fromDate, DateTime? toDate);

        Task<SurveyResultResponse> GetSurveyResultAsync(string submissionId);
        Task<byte[]> ExportSurveysToExcelAsync(List<string> surveyIds);
    }
}
