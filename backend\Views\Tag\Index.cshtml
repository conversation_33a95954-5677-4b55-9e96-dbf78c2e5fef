﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON>h sách thẻ</h4>
                @* <p class="mb-0"> *@
                @*     Danh sách sản phẩm quyết định cách trình bày sản phẩm một cách hiệu quả và cung cấp không gian<br /> *@
                @*     để liệt kê các sản phẩm và ưu đãi của bạn theo cách hấp dẫn nhất. *@
                @* </p> *@
            </div>
            <button type="button" class="btn btn-primary mt-2" onclick="GetFormArticleCategory()">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--<PERSON><PERSON> lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="tag-table" class="table-responsive rounded mb-3">
            <table id="list-tag" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-tag" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-dialog-centered"></div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            GetListArticleCategory();
            $('#search').on('input', search);
        });

        function GetFormArticleCategory(id) {
            const url = id ? `@Url.Action("Detail", "Tag")/${id}` : "@Url.Action("Create", "Tag")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-tag").modal("toggle");
                }
            })

        }

        function GetListArticleCategory() {
            table = new DataTable("#list-tag", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const status = $("#filter-status").val()

                    $.ajax({
                        url: '@Url.Action("GetPage", "Tags")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            status: status
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: item.name,
                                2: item.description,
                                5: FormatDate(item.createdDate),
                                6: `<div class="d-flex align-items-center justify-content-center list-action">
                                                        <a onclick="GetFormArticleCategory('${item.id}')" class="badge badge-info mx-1" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                            <i class="ri-edit-line fs-6 mr-0"></i>
                                                        </a>

                                                        <a onclick="DeleteArticleCategory('${item.id}')" class="badge bg-warning mx-1" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                            <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                        </a>
                                                    </div>`,
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên thẻ", data: 1 },
                    { title: "Mô tả", data: 2 },
                    { title: "Thao tác", data: 6, className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function DeleteArticleCategory(id) {
            if (id === '') return;
            const url = `/api/Tags/${id}`
            $("#modal-tag").modal("hide");
            DeleteItem(url);
        }

        async function HandleSaveOrUpdate(id) {
            if (!$("#name").val().trim()) {
                AlertResponse("Bạn phải chọn ít nhất một danh mục cho sản phẩm!", 'warning');
                return;
            }

            const url = id === "" ? "/api/Tags" : "/api/Tags/" + id;
            const method = id === "" ? "POST" : "PUT";
            $.ajax({
                url: url,
                type: method,
                contentType: "application/json",
                data: JSON.stringify({
                    name: $("#name").val().trim(),
                    description: $("#description").val().trim(),
                }),
                success: function (response) {
                    if (response.code === 200) {
                        AlertResponse(response.message, 'success')
                        table.ajax.reload();
                    }
                    $("#modal-tag").modal("toggle");
                },
                error: function (err) {
                    AlertResponse("Đã xảy ra lỗi, vui lòng thử lại sau!", 'error')
                }
            });
        }
    </script>
}
