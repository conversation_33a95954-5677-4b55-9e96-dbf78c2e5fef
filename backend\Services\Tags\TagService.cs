﻿using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Services.Tags
{
    public class TagService(IUnitOfWork unitOfWork) : Service<Tag>(unitOfWork), ITagService
    {
        public async Task<int> UpdateAsync(string id, Tag entity)
        {
            var tag = await GetByIdAsync(id);
            if (tag == null)
            {
                return 0;
            }
            tag.Name = entity.Name;
            tag.Description = entity.Description;
            return await base.UpdateAsync(tag);
        }
    }
}
