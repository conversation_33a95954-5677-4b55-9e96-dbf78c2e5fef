﻿@{
    Layout = "/Views/Shared/_Layout_Simple.cshtml";
    ViewData["Title"] = "Đăng nhập";
}
<section class="login-content">
    <div class="container">
        <div class="row align-items-center justify-content-center height-self-center">
            <div class="col-lg-8">
                <div class="card auth-card">
                    <div class="card-body p-0">
                        <div class="d-flex align-items-center auth-content" style="max-height: 400px;">
                            <div class="col-lg-7 align-self-center">
                                <form asp-controller="Home" asp-action="Login">
                                    <div class="p-3">
                                        <h2 class="mb-2">Đăng nhập</h2>
                                        @if (ViewBag.Error != null)
                                        {
                                            <p class="text-danger">@ViewBag.Error</p>
                                        }
                                        <div class="row">
                                            <div class="col-lg-12">
                                                <div class="floating-label form-group">
                                                    @* @Html.TextBoxFor(m => m.Username, new { @class = "floating-input form-control", placeholder = " " }) *@
                                                    <input class="floating-input form-control" name="username"  />
                                                    <label>Tên đăng nhập</label>
                                                </div>
                                            </div>
                                            <div class="col-lg-12">
                                                <div action="POST" class="floating-label form-group">
                                                    @* @Html.PasswordFor(m => m.Password, new { @class = "floating-input form-control", placeholder = " " }) *@
                                                    <input type="password" class="floating-input form-control" name="password" />
                                                    <label>Mật khẩu</label>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="custom-control custom-checkbox mb-3">
                                                    @* @Html.CheckBoxFor(m => m.Remember, new { @class = "custom-control-input", id = "remember" }) *@
                                                    <input type="checkbox" class="custom-control-input" id="remember" />
                                                    <label class="custom-control-label control-label-1" for="remember">Lưu thông tin đăng nhập</label>
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <a href="#" class="text-primary float-right">Quên mật khẩu</a>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary mt-2 w-25">Đăng nhập</button>
                                    </div>
                                </form>
                            </div>
                            <div class="col-lg-5 content-right">
                                <img src="~/images/01.png" class="img-fluid image-right" alt="" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>

</script>