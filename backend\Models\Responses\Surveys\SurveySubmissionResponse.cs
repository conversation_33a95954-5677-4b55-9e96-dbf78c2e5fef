﻿namespace MiniAppCore.Models.Responses.Surveys
{
    public class SurveySubmissionMemberInfo
    {
        public string? UserZaloId { get; set; }
        public string? UserZaloName { get; set; }
        public string? DisplayName { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime SubmissionDate { get; set; }
    }

    public class SurveySubmissionResponse : SurveySubmissionMemberInfo
    {
        // Submission info
        public string? Id { get; set; }
        public string? SurveyId { get; set; }
        public string? SurveyTitle { get; set; }
    }

    public class SurveyResultResponse : SurveySubmissionMemberInfo
    {
        // Survey info
        public string? SurveyId { get; set; }
        public string? Title { get; set; }
        public string? Status { get; set; }
        public bool IsDisplay { get; set; }
        public DateTime StartedDate { get; set; }
        public DateTime EndDate { get; set; }
        public int TotalSection { get; set; }
        public List<SectionResponse> Sections { get; set; } = new List<SectionResponse>();

        public string? SubmissionId { get; set; }
    }
}
