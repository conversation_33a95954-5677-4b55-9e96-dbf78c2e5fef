﻿namespace MiniAppCore.Models.Requests.Orders
{
    public class OrderRequest
    {
        public string? Note { get; set; }
        public string? Address { get; set; }
        public string? BranchId { get; set; }
        public string? VoucherId { get; set; }
        public string? BookingId { get; set; }
        public string? PhoneNumber { get; set; }
        public string? VoucherCode { get; set; }
        public string? MembershipName { get; set; }

        public short OrderStatus { get; set; }
        public short PaymentMethod { get; set; }
        public short PaymentStatus { get; set; }

        public bool? UsePoint { get; set; }
        public bool? IsChangeOrderDetails { get; set; }
        public string? MembershipAddressId { get; set; }
        public string? MembershipVATId { get; set; }
        public List<string> VoucherCodes { get; set; } = new();
        public List<OrderDetailRequest> Products { get; set; } = new();
    }
}
