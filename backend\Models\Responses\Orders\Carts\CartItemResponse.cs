﻿namespace MiniAppCore.Models.Responses.Orders.Carts
{
    public class CartItemResponse
    {
        public long Quantity { get; set; }
        public string? Note { get; set; }
        public string? CartItemId { get; set; }
        public string? ProductId { get; set; }
        public string? ProductName { get; set; }
        public string? VariantId { get; set; }
        public decimal OriginalPrice { get; set; }
        public decimal DiscountPrice { get; set; }
        public bool IsDiscounted => DiscountPrice < OriginalPrice;
        public List<string> Images { get; set; } = new();
        public List<string> PropertyValues { get; set; } = new();
        public List<CartItemGift> Gifts { get; set; } = new();

        // discount
        public short DiscountType { get; set; } // 1. giam %, 2. truc tiep
        public decimal DiscountValue
        {
            get
            {
                if (OriginalPrice <= 0 || DiscountPrice <= 0)
                    return 0;

                return DiscountType switch
                {
                    1 => Math.Round((1 - DiscountPrice / OriginalPrice) * 100, 2), // Giảm theo %
                    2 => Math.Round(OriginalPrice - DiscountPrice, 2), // G<PERSON><PERSON>m trực tiếp
                    _ => 0
                };
            }
        }
    }
}
