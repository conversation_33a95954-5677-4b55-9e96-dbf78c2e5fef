﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Memberships
{
    public class MembershipAddress : BaseEntity
    {
        public required string UserZaloId { get; set; }
        public required string StreetLine { get; set; }
        public required string WardId { get; set; }
        public required string DistrictId { get; set; }
        public required string ProvinceId { get; set; }
        public required string FullAddress { get; set; }
        public bool IsDefault { get; set; } = true;

        public string? Note { get; set; }
        public string? Name { get; set; }
        public string? PhoneNumber { get; set; }
    }
}
