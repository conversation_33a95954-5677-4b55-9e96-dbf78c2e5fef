﻿﻿﻿@{
    ViewData["Title"] = "Lịch sử vòng quay";
}

<style>
    /* Custom styles for this page */
    .border-left-primary {
        border-left: 5px solid #4e73df;
    }

    .border-left-success {
        border-left: 5px solid #1cc88a;
    }

    .border-left-info {
        border-left: 5px solid #36b9cc;
    }

    .border-left-warning {
        border-left: 5px solid #f6c23e;
    }

    .icon-box {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .bg-soft-primary {
        background-color: rgba(78, 115, 223, 0.1);
    }

    .bg-soft-success {
        background-color: rgba(28, 200, 138, 0.1);
    }

    .bg-soft-info {
        background-color: rgba(54, 185, 204, 0.1);
    }

    .bg-soft-warning {
        background-color: rgba(246, 194, 62, 0.1);
    }

    .table-row-odd {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .row-hover {
        background-color: rgba(0, 123, 255, 0.05) !important;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }
</style>

<div class="row">
    <!-- Header Section -->
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-2 text-primary"><i class="ri-history-line me-2"></i>Lịch sử vòng quay</h4>
                <p class="text-muted">Theo dõi chi tiết lịch sử tham gia và kết quả vòng quay may mắn</p>
            </div>
            <div>
                <button class="btn btn-outline-primary" onclick="exportHistory()">
                    <i class="ri-download-2-line me-1"></i> Xuất Excel
                </button>
            </div>
        </div>
    </div>

    <!-- Filter Card -->
    <div class="col-lg-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="ri-filter-3-line me-2"></i>Bộ lọc tìm kiếm</h5>
            </div>
            <div class="card-body">
                <div class="row align-items-end">
                    <!-- Search Input -->
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Tìm kiếm</label>
                        <div class="input-group">
                            <input id="search" type="text" class="form-control" placeholder="Tên, SĐT, Zalo ID..." />
                            <button class="btn btn-primary" id="search-btn">
                                <i class="ri-search-line"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Result Type Filter -->
                    <div class="col-md-2 mb-3">
                        <label class="form-label">Kết quả quay</label>
                        <select id="filter-winner" class="form-select" onchange="table.ajax.reload()">
                            <option value="">Tất cả kết quả</option>
                            <option value="true">Trúng thưởng</option>
                            <option value="false">Không trúng thưởng</option>
                        </select>
                    </div>

                    <!-- Prize Type Filter -->
                    <div class="col-md-2 mb-3">
                        <label class="form-label">Loại phần thưởng</label>
                        <select id="filter-type" class="form-select" onchange="table.ajax.reload()">
                            <option value="">Tất cả loại</option>
                            <option value="0">Không trúng thưởng</option>
                            <option value="1">Voucher</option>
                            <option value="2">Sản phẩm</option>
                            <option value="3">Tiền mặt</option>
                            <option value="4">Điểm</option>
                            <option value="99">Khác</option>
                        </select>
                    </div>

                    <!-- Date Range Filters -->
                    <div class="col-md-2 mb-3">
                        <label class="form-label">Ngày bắt đầu</label>
                        <input type="date" id="start-date" class="form-control" value="@DateTime.Now.AddDays(-DateTime.Now.Day + 1).ToString("yyyy-MM-dd")" />
                    </div>

                    <div class="col-md-2 mb-3">
                        <label class="form-label">Ngày kết thúc</label>
                        <input type="date" id="end-date" class="form-control" value="@DateTime.Now.ToString("yyyy-MM-dd")" />
                    </div>

                    <!-- Apply Filter Button -->
                    <div class="col-md-12 d-flex justify-content-end">
                        <button class="btn btn-primary" id="apply-filter">
                            <i class="ri-filter-line me-1"></i> Lọc
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards Row -->
    <div class="col-12 mb-4" hidden>
        <div class="row">
            <!-- Total Spins Card -->
            <div class="col-md-3 mb-3">
                <div class="card shadow-sm border-left-primary">
                    <div class="card-body d-flex">
                        <div class="icon-box bg-soft-primary text-primary">
                            <i class="ri-refresh-line"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-1">Tổng lượt quay</h6>
                            <h4 class="mb-0" id="total-spins">0</h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Winners Card -->
            <div class="col-md-3 mb-3">
                <div class="card shadow-sm border-left-success">
                    <div class="card-body d-flex">
                        <div class="icon-box bg-soft-success text-success">
                            <i class="ri-trophy-line"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-1">Lượt trúng thưởng</h6>
                            <h4 class="mb-0" id="total-winners">0</h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Unique Players Card -->
            <div class="col-md-3 mb-3">
                <div class="card shadow-sm border-left-info">
                    <div class="card-body d-flex">
                        <div class="icon-box bg-soft-info text-info">
                            <i class="ri-user-line"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-1">Người chơi</h6>
                            <h4 class="mb-0" id="unique-players">0</h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Win Rate Card -->
            <div class="col-md-3 mb-3">
                <div class="card shadow-sm border-left-warning">
                    <div class="card-body d-flex">
                        <div class="icon-box bg-soft-warning text-warning">
                            <i class="ri-percent-line"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-1">Tỷ lệ trúng</h6>
                            <h4 class="mb-0" id="win-rate">0%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Table -->
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-spin" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div class="modal fade" id="historyDetailModal" tabindex="-1" aria-hidden="true">
    <div id="historyDetailContainer">
        <!-- Partial view sẽ được load vào đây -->
    </div>
</div>

@section Scripts {
    <script>
        const statsData = {
            totalSpins: 0,
            winners: 0,
            players: 0
        };

        $(document).ready(function () {
            // Apply filters button
            $('#apply-filter').on('click', function () {
                table.ajax.reload();
                updateStatistics();
            });

            GetListSpinHistory();
        });

        function GetListSpinHistory() {
            // Show spinner, hide table
            $('#spinner').show();

            table = new DataTable("#list-spin", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = Math.floor(data.start / data.length) + 1;
                    const pageSize = data.length;
                    const keyword = $("#search").val().trim();
                    const type = $("#filter-type").val();

                    const winnerVal = $("#filter-winner").val();
                    const winner = winnerVal === "" ? null : (winnerVal === "true");

                    const startDate = $("#start-date").val()
                        ? moment($("#start-date").val(), "YYYY-MM-DD").startOf('day').format("YYYY-MM-DD HH:mm:ss")
                        : null;

                    const endDate = $("#end-date").val()
                        ? moment($("#end-date").val(), "YYYY-MM-DD").endOf('day').format("YYYY-MM-DD HH:mm:ss")
                        : null;

                    // Call API for data
                    // Call API for data
                    $.ajax({
                        url: '@Url.Action("GetHistoryAdmin", "LuckyWheels")',
                        type: 'GET',
                        contentType: "application/json",
                        data: {
                            page: page,
                            pageSize: pageSize,
                            keyword: keyword,
                            type: type,
                            startDate: startDate,
                            endDate: endDate,
                            resultType: winner
                        },
                        success: function (response) {
                            // Update statistics
                            updateStatisticsFromData(response.statistic);

                            const formattedData = response.data.map((item, index) => ({
                                index: data.start + index + 1,
                                userZaloId: item.userZaloId || '-',
                                userZaloName: item.userZaloName || '-',
                                phoneNumber: item.phoneNumber || '-',
                                prizeName: item.prizeName || '-',
                                prizeValue: formatPrizeValue(item.prizeValue) || '-',
                                prizeType: getPrizeTypeLabel(item.type),
                                isWon: item.hasWon
                                    ? `<span class="badge bg-success"><i class="ri-check-line me-1"></i>Trúng thưởng</span>`
                                    : `<span class="badge bg-secondary"><i class="ri-close-line me-1"></i>Không trúng</span>`,
                                claimStatus: getClaimStatusLabel(item.isClaimed, item.requestStatus, item.type),
                                createDate: FormatDateTime(item.createdDate),
                                actions: item.hasWon ? `<button class="btn btn-sm btn-outline-primary" onclick="viewDetails('${item.historyId}')">
                                                <i class="ri-eye-line me-1"></i>Chi tiết
                                            </button>` : ""
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.totalPages * pageSize,
                                    recordsFiltered: response.totalPages * pageSize || 0,
                                    data: formattedData
                                });
                            }, 300);
                        },
                        error: function (xhr, status, error) {
                            console.error("Error fetching data:", error);
                            $("#spinner").html('');
                            AlertResponse("Đã có lỗi xảy ra khi tải dữ liệu", "error");

                            callback({
                                draw: data.draw,
                                recordsTotal: 0,
                                recordsFiltered: 0,
                                data: []
                            });
                        },
                    });
                },
                columns: [
                    { title: "#", data: "index", className: "text-center" },
                    { title: "Zalo ID", data: "userZaloId", className: "text-center col-1" },
                    { title: "Tên người chơi", data: "userZaloName", className: "text-center col-2" },
                    { title: "Số điện thoại", data: "phoneNumber", className: "text-center col-1" },
                    { title: "Kết quả", data: "isWon", className: "text-center col-3" },
                    { title: "Trạng thái nhận thưởng", data: "claimStatus", className: "text-center" },
                    { title: "Thời gian", data: "createDate", className: "text-center" },
                    { title: "", data: "actions", className: "text-center" }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": `<p class="text-center m-0">Không tìm thấy kết quả</p>`,
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "emptyTable": `<p class="text-center m-0">Không có dữ liệu</p>`,
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)",
                }
            });

            $(table.table().header()).addClass('');
        }

        function updateStatisticsFromData(statistic) {
            // Cập nhật giao diện
            $('#win-rate').text(`${statistic?.winRate || 0}%`);
            $('#total-spins').text(statistic?.totalSpins || 0);
            $('#total-winners').text(statistic?.totalWinners || 0);
            $('#unique-players').text(statistic?.uniquePlayers || 0);
        }

        function exportHistory() {
            // Get current filter parameters
            const type = $("#filter-type").val();
            const keyword = $("#search").val().trim();
            const winnerVal = $("#filter-winner").val();
            const winner = winnerVal === "" ? null : (winnerVal === "true");
            const endDate = moment($("#end-date").val()).format();
            const startDate = moment($("#start-date").val()).format();

            // Build export URL
            const url = `@Url.Action("ExportWinnerHistory", "Dashboards")?keyword=${keyword}&type=${type}&resultType=${winner}&startDate=${startDate}&endDate=${endDate}`;

            // Download the file
            window.location.href = url;
        }

        function formatPrizeValue(value) {
            if (!value) return "-";
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(value);
        }

        function getClaimStatusLabel(isClaimed, requestStatus, type) {
            const status = parseInt(requestStatus);

            if(type == 0) {
                return "-";
            }

            if (!isClaimed && status === 0) {
                return `<span class="badge bg-danger">Chưa nhận</span>`
            }

            switch (status) {
                case 0:
                    return `<span class="badge bg-gray">Chờ xác nhận</span>`;
                case 1:
                    return `<span class="badge bg-gray">Chờ xác nhận</span>`;
                case 2:
                    return `<span class="badge bg-info">Đã xác nhận</span>`;
                case 3:
                    return `<span class="badge bg-success">Đã nhận</span>`;
                case 4:
                    return `<span class="badge bg-danger">Từ chối</span>`;
                default:
                    return `<span class="badge bg-info">Chưa nhận</span>`;
            }
        }

        function getPrizeTypeLabel(type) {
            switch (parseInt(type)) {
                case 0:
                    return `<span class="badge bg-secondary">Không trúng</span>`;
                case 1:
                    return `<span class="badge bg-info">Voucher</span>`;
                case 2:
                    return `<span class="badge bg-success">Sản phẩm</span>`;
                case 3:
                    return `<span class="badge bg-warning">Tiền mặt</span>`;
                case 4:
                    return `<span class="badge bg-primary">Điểm</span>`;
                default:
                    return `<span class="badge bg-dark">Khác</span>`;
            }
        }

        function viewDetails(historyId) {
            $.ajax({
                url: '@Url.Action("HistoryDetail", "LuckyWheel")/' + historyId,
                type: 'GET',
                success: function(response) {
                    $('#historyDetailContainer').html(response);
                    $('#historyDetailModal').modal('show');
                },
                error: function() {
                    AlertResponse("Đã có lỗi xảy ra khi tải dữ liệu chi tiết!", "error");
                }
            });
        }

    </script>
}
