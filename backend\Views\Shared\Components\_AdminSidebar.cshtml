﻿@using Microsoft.AspNetCore.Http
@inject IHttpContextAccessor HttpContextAccessor
@{
    string controller = ViewContext.RouteData.Values["controller"]?.ToString() ?? "";
    string action = ViewContext.RouteData.Values["action"]?.ToString() ?? "";

    // Get view permissions from user claims
    var viewPermissions = new List<string>();
    if (User?.Identity?.IsAuthenticated == true)
    {
        viewPermissions = User.Claims
        .Where(c => c.Type == "ViewPermission")
        .Select(c => c.Value)
        .ToList();
    }

    // Hàm kiểm tra quyền truy cập vào một MainView và SubView
    bool HasViewPermission(string mainViewId, string subViewId = null)
    {
        // Nếu không có quyền nào, trả về false
        if (!viewPermissions.Any()) return false;

        // <PERSON>ểm tra từng quyền trong danh sách viewPermissions
        foreach (var permission in viewPermissions)
        {
            var permissionParts = permission.Split(':'); // Tách thành MainView và SubView
            if (permissionParts.Length == 2) // Kiểm tra quyền có 2 phần
            {
                var mainView = permissionParts[0];
                var subView = permissionParts[1];

                // Kiểm tra nếu quyền phù hợp với mainView và subView
                if (mainView == mainViewId && (subViewId == null || subView == subViewId))
                {
                    return true; // Nếu khớp, trả về true
                }
            }
            else
            {
                // Trường hợp không có SubView, chỉ có MainView
                var mainView = permissionParts[0];
                // Kiểm tra nếu quyền phù hợp với MainView mà không có SubView
                if (mainView == mainViewId && subViewId == null)
                {
                    return true; // Nếu khớp, trả về true
                }
            }
        }

        return false; // Nếu không khớp, trả về false
    }
}

<div class="data-scrollbar" data-bs-scrollbar="true" style="overflow: auto; outline: none;" tabindex="-1">
    <nav class="iq-sidebar-menu" style="padding-bottom: 100px">
        <ul id="iq-sidebar-toggle" class="iq-menu">
            @if (HasViewPermission("Dashboard", "Overview"))
            {
                <li class="@(new string[] { "Dashboard" }.Contains(controller) ? "active" : "")">
                    <a asp-controller="Dashboard" asp-action="Index" class="svg-icon">
                        <i class="ri-pie-chart-2-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Thống Kê</span>
                    </a>
                </li>
            }


            @if (HasViewPermission("Branch", "BranchList"))
            {
                <li class="@(new string[] { "Branch" }.Contains(controller) ? "active" : "")">
                    <a asp-controller="Branch" asp-action="Index" class="collapsed @(new string[] { "Branch" }.Contains(controller) ? "active" : "")">
                        <i class="ri-map-pin-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Chi Nhánh</span>
                    </a>
                </li>
            }


            @if (HasViewPermission("Affiliate"))
            {
                <li class="@(new string[] { "Affiliate" }.Contains(controller) ? "active" : "")">
                    <a asp-controller="Affiliate" asp-action="Index" class="collapsed @(new string[] { "Affiliate" }.Contains(controller) ? "active" : "")">
                        <i class="ri-wallet-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Tiếp thị liên kết</span>
                    </a>
                </li>
            }


            @if (HasViewPermission("Article"))
            {
                <li class="@(new string[] { "Article", "ArticleCategory" }.Contains(controller) ? "active" : "")">
                    <a href="#article" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-article-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Tin Tức</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="article" class="iq-submenu collapse @(new string[] { "Article", "ArticleCategory" }.Contains(controller) ? "show" : "") " data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("Article", "ArticleCategory"))
                        {
                            <li class="">
                                <a asp-controller="ArticleCategory" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh Mục Tin Tức</span>
                                </a>
                            </li>
                        }

                        @if (HasViewPermission("Article", "ArticleList"))
                        {
                            <li class="">
                                <a asp-controller="Article" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh Sách Tin Tức</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }


            @if (HasViewPermission("Product"))
            {
                <li class="@(new string[] { "Product", "ProductProperty", "Brand", "Category" }.Contains(controller) ? "active" : "")">
                    <a href="#product" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-shopping-cart-2-line" style="font-size: 20px;"></i>
                        <span class="ml-4">sản phẩm</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="product" class="iq-submenu collapse @(new string[] { "Product", "ProductProperty", "Brand", "Category" }.Contains(controller) ? "show" : "")"
                        data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("Product", "Category"))
                        {
                            <li class="">
                                <a asp-controller="Category" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh mục sản phẩm</span>
                                </a>
                            </li>
                        }

                        @if (HasViewPermission("Product", "Brand"))
                        {
                            <li class="">
                                <a asp-controller="Brand" asp-action="Index" hidden>
                                    <i class="ri-subtract-line"></i><span>Thương Hiệu sản phẩm</span>
                                </a>
                            </li>
                        }

                        @if (HasViewPermission("Product", "ProductProperty"))
                        {
                            <li class="">
                                <a asp-controller="ProductProperty" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách Phân loại</span>
                                </a>
                            </li>
                        }

                        @if (HasViewPermission("Product", "ProductList"))
                        {
                            <li class="">
                                <a asp-controller="Product" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách sản phẩm</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }


            @if (HasViewPermission("Booking"))
            {
                <li class="@(new string[] { "Booking", "BookingItem" }.Contains(controller) ? "active" : "") ">
                    <a href="#booking" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-calendar-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Đặt Lịch </span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="booking" class="iq-submenu collapse @(new string[] { "Booking", "BookingItem" }.Contains(controller) ? "show" : "") " data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("Booking", "BookingItem"))
                        {
                            <li class="">
                                <a asp-controller="BookingItem" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh Sách Dịch Vụ</span>
                                </a>
                            </li>
                        }

                        @if (HasViewPermission("Booking", "BookingList"))
                        {
                            <li class="">
                                <a asp-controller="Booking" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh Sách Đặt Lịch</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }


            @if (HasViewPermission("Order"))
            {
                <li class="@(new string[] { "Order", "InvoiceTemplate" }.Contains(controller) ? "active" : "") ">
                    <a href="#order" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-file-list-3-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Đơn hàng</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="order" class="iq-submenu collapse @(new string[] { "Order", "InvoiceTemplate" }.Contains(controller) ? "show" : "") " data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("Order", "OrderList"))
                        {
                            <li class="">
                                <a asp-controller="Order" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách Đơn hàng</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("Order", "InvoiceTemplate"))
                        {
                            <li class="">
                                <a asp-controller="InvoiceTemplate" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách hóa đơn</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }


            @if (HasViewPermission("Discount"))
            {
                <li class="@(new string[] { "Discount", "Promotion" }.Contains(controller) ? "active" : "")">
                    <a href="#discount" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-price-tag-3-line" style="font-size: 20px;"></i>
                        <span class="ml-4">ưu đãi</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="discount" class="iq-submenu collapse @(new string[] { "Discount", "Promotion" }.Contains(controller) ? "show" : "") " data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("Discount", "DiscountList"))
                        {
                            <li class="">
                                <a asp-controller="Discount" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Chương trình giảm giá</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("Discount", "Promotion"))
                        {
                            <li class="">
                                <a asp-controller="Promotion" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Mua X tặng Y</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }


            @if (HasViewPermission("Voucher"))
            {
                <li class="@(new string[] { "Voucher" }.Contains(controller) ? "active" : "")">
                    <a href="#voucher" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-ticket-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Voucher</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="voucher" class="iq-submenu collapse @(new string[] { "Voucher" }.Contains(controller) ? "show" : "") " data-parent="#iq-sidebar-toggle">
                        <li class="">
                            <a asp-controller="Voucher" asp-action="Index">
                                <i class="ri-subtract-line"></i><span>Danh sách voucher</span>
                            </a>
                        </li>
                    </ul>
                </li>
            }


            @if (HasViewPermission("Membership"))
            {
                <li class="@(new string[] { "Membership", "Tag", "Rank" }.Contains(controller) ? "active" : "")">
                    <a href="#people" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-user-settings-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Khách Hàng</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="people" class="iq-submenu collapse @(new string[] { "Membership", "Tag", "Rank" }.Contains(controller) ? "show" : "") " data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("Membership", "MembershipList"))
                        {
                            <li class="">
                                <a asp-controller="Membership" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách khách hàng</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("Membership", "Rank"))
                        {
                            <li class="">
                                <a asp-controller="Rank" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Hạng Thành viên</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("Membership", "Tag"))
                        {
                            <li class="">
                                <a asp-controller="Tag" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách thẻ</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }


            @if (HasViewPermission("Survey"))
            {
                <li class="@(new string[] { "Survey" }.Contains(controller) ? "active" : "")">
                    <a href="#survey" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-survey-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Khảo Sát</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="survey" class="iq-submenu collapse @(new string[] { "Survey" }.Contains(controller) ? "show" : "") " data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("Survey", "SurveyList"))
                        {
                            <li class="">
                                <a asp-controller="Survey" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách khảo sát</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("Survey", "History"))
                        {
                            <li class="">
                                <a asp-controller="Survey" asp-action="History">
                                    <i class="ri-subtract-line"></i><span>Danh sách phản hồi</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }


            @if (HasViewPermission("LuckyWheel"))
            {
                <li class="@(new string[] { "LuckyWheel", "GamePrize" }.Contains(controller) ? "active" : "")">
                    <a href="#gamification" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-gamepad-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Vòng Quay</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="gamification" class="iq-submenu collapse @(new string[] { "LuckyWheel", "GamePrize" }.Contains(controller) ? "show" : "")" data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("LuckyWheel", "GameList"))
                        {
                            <li class="">
                                <a asp-action="Index" asp-controller="LuckyWheel">
                                    <i class="ri-subtract-line"></i><span>Danh sách vòng quay</span>
                                </a>
                            </li>
                        }

                        @if (HasViewPermission("LuckyWheel", "History"))
                        {
                            <li class="">
                                <a asp-action="History" asp-controller="LuckyWheel">
                                    <i class="ri-subtract-line"></i><span>Danh sách trúng giải</span>
                                </a>
                            </li>
                        }

                        @if (HasViewPermission("LuckyWheel", "GamePrize"))
                        {
                            <li class="">
                                <a asp-action="Index" asp-controller="GamePrize">
                                    <i class="ri-subtract-line"></i><span>Danh sách phần thưởng</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }

            @if (HasViewPermission("Event"))
            {
                <li class="@(new string[] { "Event", "Sponsor", "SponsorshipTier" }.Contains(controller) ? "active" : "")">
                    <a href="#event" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-calendar-event-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Sự kiện</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="event" class="iq-submenu collapse @(new string[] { "Event", "Sponsor", "SponsorshipTier" }.Contains(controller) ? "show" : "") " data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("Event", "EventList"))
                        {
                            <li class="">
                                <a asp-controller="Event" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách sự kiện</span>
                                </a>
                            </li>
                        }

                        @if (HasViewPermission("Event", "Sponsor"))
                        {
                            <li class="">
                                <a asp-controller="Sponsor" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách nhà tài trợ</span>
                                </a>
                            </li>
                               
                        }

                        @if (HasViewPermission("Event", "SponsorshipTier"))
                        {
                            <li class="">
                                <a asp-controller="SponsorshipTier" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Danh sách hạng nhà tài trợ</span>
                                </a>
                            </li> 
                        }
                    </ul>
                </li>
            }


            @if (HasViewPermission("OmniTool"))
            {
                <li class="@(new string[] { "OmniTool", "Campaign", "EventTrigger" }.Contains(controller) ? "active" : "")">
                    <a href="#omniTool" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-magic-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Omni Mini Tool</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="omniTool" class="iq-submenu collapse @(new string[] { "OmniTool", "Campaign", "EventTrigger" }.Contains(controller) ? "show" : "")" data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("OmniTool", "CampaignList"))
                        {
                            <li class="">
                                <a asp-controller="OmniTool" asp-action="IndexCampaign">
                                    <i class="ri-subtract-line"></i><span>Danh sách Campaign</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("OmniTool", "CampaignHistory"))
                        {
                            <li class="">
                                <a asp-controller="OmniTool" asp-action="HistoryCampaign">
                                    <i class="ri-subtract-line"></i><span>Nhật kí gửi tin (Campaign)</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("OmniTool", "EventTemplateList"))
                        {
                            @* <li class="">
                                <a asp-controller="OmniTool" asp-action="IndexEventTemplate">
                                    <i class="ri-subtract-line"></i><span>Event Trigger Messaging</span>
                                </a>
                            </li> *@
                            <li class="">
                                <a asp-controller="EventTrigger" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Event Trigger Messaging</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("OmniTool", "EventTemplateHistory"))
                        {
                            <li class="">
                                <a asp-controller="EventTrigger" asp-action="History">
                                    <i class="ri-subtract-line"></i><span>Nhật kí gửi tin (ETM)</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("OmniTool", "TemplateUidList"))
                        {
                            <li class="">
                                <a asp-controller="OmniTool" asp-action="IndexTemplateUid">
                                    <i class="ri-subtract-line"></i><span>Quản lý mẫu tin UID</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }

            
            @if (HasViewPermission("SystemSetting"))
            {
                <li class="@(new string[] { "SystemSetting" , "CustomForm" }.Contains(controller) ? "active" : "")">
                    <a href="#setting" class="collapsed" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="ri-settings-3-line" style="font-size: 20px;"></i>
                        <span class="ml-4">Cài đặt chung</span>
                        <i class="ri-arrow-right-s-line dropdown-indicator-icon iq-arrow-right arrow-active"></i>
                    </a>
                    <ul id="setting" class="iq-submenu collapse @(new string[] { "SystemSetting" , "CustomForm" }.Contains(controller) ? "show" : "")" data-parent="#iq-sidebar-toggle">
                        @if (HasViewPermission("SystemSetting", "GeneralSetting"))
                        {
                            <li class="">
                                <a asp-controller="SystemSetting" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Thông tin ứng dụng</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("SystemSetting", "EnableFeatures"))
                        {
                            <li class="" hidden>
                                <a asp-controller="SystemSetting" asp-action="EnableFeatures">
                                    <i class="ri-subtract-line"></i><span>Cài đặt Bật/Tắt Chức năng</span>
                                </a>
                            </li>

                            <li class="">
                                <a asp-controller="SystemSetting" asp-action="ActionButtonConfig">
                                    <i class="ri-subtract-line"></i><span>Cài đặt nút điều hướng</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("SystemSetting", "MembershipExtendDefaults"))
                        {
                            <li class="">
                                <a asp-controller="SystemSetting" asp-action="MembershipExtendDefaults">
                                    <i class="ri-subtract-line"></i><span>Cài đặt thông tin khách hàng</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("SystemSetting", "ShippingFeeConfig"))
                        {
                            <li class="">
                                <a asp-controller="SystemSetting" asp-action="ShippingFeeConfig">
                                    <i class="ri-subtract-line"></i><span>Cài đặt phí vận chuyển</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("SystemSetting", "CustomForm"))
                        {
                            <li class="">
                                <a asp-controller="CustomForm" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Cài đặt tùy chỉnh form</span>
                                </a>
                            </li>
                        }
                        @if (HasViewPermission("CustomEntity", "ListCustomEntity"))
                        {
                            <li class="">
                                <a asp-controller="CustomEntity" asp-action="Index">
                                    <i class="ri-subtract-line"></i><span>Cài đặt trường dữ liệu mở rộng</span>
                                </a>
                            </li>
                        }
                    </ul>
                </li>
            }

        </ul>
    </nav>
    <div style="height:40px;"></div>
</div>