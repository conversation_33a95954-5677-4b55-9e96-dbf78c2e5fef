﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;
namespace MiniAppCore.Entities.Bookings
{
    public class Booking : BaseEntity
    {
        public required string UserZaloId { get; set; }
        public string? Name { get; set; }
        public string? Note { get; set; }
        public string? OrderId { get; set; }
        public string? BranchId { get; set; }
        public string? CancelReason { get; set; }
        public EBooking Status { get; set; }
        public DateTime BookingDate { get; set; }
    }
}
