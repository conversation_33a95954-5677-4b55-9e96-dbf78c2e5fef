﻿﻿@using MiniAppCore.Entities.Memberships
@using MiniAppCore.Entities.LuckyWheels;
@using MiniAppCore.Models.Responses.Gamification.LuckyWheels;
@model (Membership membership, SpinHistory spinHistory, ClaimRewardResponse reward);

@{
    var statusDict = new Dictionary<int, (string BadgeClass, string Text)> {
{ 0, ("bg-gray", "Chờ xác nhận") },
{ 1, ("bg-gray", "Chờ xác nhận") },
{ 2, ("bg-info", "Đã xác nhận") },
{ 3, ("bg-success", "Đã nhận") },
{ 4, ("bg-danger", "Từ chối") },
};
}

<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header bg-light">
            <h5 class="modal-title">
                <i class="ri-award-line me-2"></i>
                Chi tiết l<PERSON> quay
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow-sm h-100 border-0 rounded-3 overflow-hidden">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0 d-flex align-items-center">
                                <i class="ri-user-3-line me-2"></i>
                                Thông tin người chơi
                            </h6>
                        </div>
                        <div class="card-body">
                            <!-- Thông tin cơ bản -->
                            <div class="d-flex align-items-center mb-4 pb-3 border-bottom">
                                <div class="me-3">
                                    @if (!string.IsNullOrEmpty(Model.reward.HistoryId))
                                    {
                                        <img src="@Model.membership.Avatar" class="rounded-circle border shadow-sm" alt="Avatar" style="width: 60px; height: 60px; object-fit: cover;" />
                                    }
                                    else
                                    {
                                        <div class="bg-light rounded-circle border d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                            <i class="ri-user-3-line text-primary fs-3"></i>
                                        </div>
                                    }
                                </div>
                                <div>
                                    <h6 class="mb-1 fw-bold">@(string.IsNullOrEmpty(Model.membership.UserZaloName) ? "-" : Model.membership.UserZaloName)</h6>
                                    <div class="d-flex align-items-center text-muted">
                                        <i class="ri-information-line me-1"></i>
                                        <small>ID: @(string.IsNullOrEmpty(Model.membership.UserZaloId) ? "-" : Model.membership.UserZaloId)</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Thông tin chi tiết -->
                            <div class="mb-4 pb-3 border-bottom">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="ri-phone-line text-primary me-2"></i>
                                    <label class="fw-medium mb-0">Số điện thoại</label>
                                </div>
                                <p class="mb-0 ms-4 fw-normal">@(string.IsNullOrEmpty(Model.membership.PhoneNumber) ? "-" : Model.membership.PhoneNumber)</p>
                            </div>

                            <div class="mb-0">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="ri-time-line text-primary me-2"></i>
                                    <label class="fw-medium mb-0">Thời gian quay</label>
                                </div>
                                <p class="mb-0 ms-4 fw-normal">@(DateTime.Parse(Model.spinHistory.CreatedDate.ToString()).ToString("dd/MM/yyyy HH:mm:ss"))</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card shadow-sm h-100 border-0 rounded-3 overflow-hidden">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0 d-flex align-items-center">
                                <i class="ri-award-line me-2"></i>
                                Kết quả vòng quay
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-4 pb-3 border-bottom">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="ri-gift-line text-primary me-2"></i>
                                    <label class="fw-medium mb-0">Giải thưởng</label>
                                </div>
                                <p class="mb-0 ms-4 fw-normal">@(string.IsNullOrEmpty(Model.reward.Name) ? "-" : Model.reward.Name)</p>
                            </div>

                            <div class="mb-4 pb-3 border-bottom">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="ri-wallet-3-line text-primary me-2"></i>
                                    <label class="fw-medium mb-0">Giá trị</label>
                                </div>
                                <p class="mb-0 ms-4 fw-normal">
                                    @if (Model.reward.Value != null)
                                    {
                                        @Model.reward.Value
                                    }
                                    else
                                    {
                                        <span>-</span>
                                    }
                                </p>
                            </div>

                            <div class="mb-4 pb-3 border-bottom">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="ri-medal-line text-primary me-2"></i>
                                    <label class="fw-medium mb-0">Trạng thái</label>
                                </div>
                                <div class="ms-4">
                                    @if (Model.spinHistory.IsWon)
                                    {
                                        <span class="badge bg-success"><i class="ri-check-line me-1"></i>Trúng thưởng</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary"><i class="ri-close-line me-1"></i>Không trúng</span>
                                    }
                                </div>
                            </div>

                            <div class="mb-0">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="ri-exchange-line text-primary me-2"></i>
                                    <label class="fw-medium mb-0">Trạng thái nhận thưởng</label>
                                </div>
                                <div class="ms-4">
                                    @if (Model.reward.Type == 0)
                                    {
                                        <span class="text-muted">-</span>
                                    }
                                    else
                                    {
                                        var status = (short)Model.reward.RequestStatus;
                                        var (BadgeClass, Text) = statusDict.ContainsKey(status)
                                        ? statusDict[status]
                                        : ("bg-secondary", "Chưa xác định");

                                        <span class="badge @BadgeClass">@Text</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            @if (Model.reward.ImageUrl != null)
            {
                <div class="row mt-4" hidden>
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Hình ảnh giải thưởng</h6>
                            </div>
                            <div class="card-body text-center">
                                <img src="@Model.reward.ImageUrl" alt="Prize Image" class="img-fluid rounded" style="max-height: 200px;" />
                            </div>
                        </div>
                    </div>
                </div>
            }

            <div class="row mt-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light d-flex align-items-center justify-content-between">
                            <h6 class="mb-0">Quản lý trạng thái</h6>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-6">
                                    <select id="claim-status-select" class="form-select">
                                        <option value="1" selected="@(Model.reward.RequestStatus == MiniAppCore.Enums.ETransaction.Pending)">Đang chờ</option>
                                        <option value="2" selected="@(Model.reward.RequestStatus == MiniAppCore.Enums.ETransaction.Confirmed)">Xác nhận</option>
                                        <option value="3" selected="@(Model.reward.RequestStatus == MiniAppCore.Enums.ETransaction.Completed)">Đã nhận</option>
                                        <option value="4" selected="@(Model.reward.RequestStatus == MiniAppCore.Enums.ETransaction.Rejected)">Đã từ chối</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <button id="btn-update-status" class="btn btn-primary w-100" onclick="updateClaimStatus('@Model.reward.HistoryId')">
                                        <i class="ri-save-line me-1"></i> Cập nhật trạng thái
                                    </button>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="form-text text-muted">
                                    @* <i class="ri-information-line me-1"></i> Cập nhật trạng thái sẽ gửi thông báo đến khách hàng. *@
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-12">
                    <div class="alert alert-info mb-0">
                        <div class="d-flex align-items-center">
                            <i class="ri-information-line fs-5 me-2"></i>
                            <div>
                                <div>Mã lịch sử: <strong>@Model.spinHistory.Id</strong></div>
                                <div>Mã giải thưởng: <strong>@Model.spinHistory.PrizeId</strong></div>
                                <div>Thời gian tạo: <strong>@(Model.spinHistory.CreatedDate.ToString("dd/MM/yyyy HH:mm:ss"))</strong></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                <i class="ri-close-line me-1"></i> Đóng
            </button>
        </div>
    </div>
</div>

@await Html.PartialAsync("Partials/_HistoryScriptModal.cshtml")