﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Categories
{
    public class Category : BaseEntity
    {
        public string? Type { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public string? HexColor { get; set; } = "#dc3545";
        public string? Images { get; set; }
        public bool IsGlobal { get; set; } = false;
        public int OrderPriority { get; set; } = 1;
    }
}
