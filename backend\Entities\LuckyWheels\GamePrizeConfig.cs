﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.LuckyWheels
{
    public class GamePrizeConfig : BaseEntity
    {
        public float WinRate { get; set; } // Tỉ lệ thắng thực tế của phần thưởng này (%)
        public short Ranking { get; set; }
        public required string GameId { get; set; } // Id của vòng quay
        public required string GamePrizeId { get; set; }

        public int Position { get; set; } // Vị trí trên vòng quay (độ: 0-359)
        public int Quantity { get; set; } // Tổng số lượng phần thưởng có thể phát ra
        public int DailyLimit { get; set; } // Giới hạn số lượng có thể phát ra mỗi ngày (0 = không giới hạn)
    }
}
