﻿@model MiniAppCore.Models.DTOs.Surveys.SurveyQuestionDTO

@{
    var typeLabel = Model.Type switch
    {
        "paragraph" => "Đoạn văn",
        "multiChoice" => "<PERSON>hiều lựa chọn",
        "singleChoice" => "Một lựa chọn",
        "likert" => "Thang đo Likert",
        "dropDown" => "Dropdown",
        "date" => "Ngày",
        "time" => "Thời gian",
        _ => Model.Type
    };

    var typeIcon = Model.Type switch
    {
        "paragraph" => "ri-text",
        "multiChoice" => "ri-checkbox-multiple-line",
        "singleChoice" => "ri-radio-button-line",
        "likert" => "ri-scales-line",
        "dropDown" => "ri-list-settings-line",
        "date" => "ri-calendar-line",
        "time" => "ri-time-line",
        _ => "ri-question-line"
    };
}

<div class="question-item question-card" data-question-id="@Model.Id">
    <div class="d-flex justify-content-between align-items-start">
        <!-- Question Title with Number -->
        <div class="question-header">
            <div class="question-number">@Model.DisplayOrder</div>
            <div class="question-title rich-content">@Html.Raw(Model.QuestionTitle)</div>
        </div>

        <!-- Question Type Badge and Actions -->
        <div class="d-flex align-items-center">
            <!-- Badges Container -->
            <div class="badges me-2 d-flex flex-column gap-1">
                @if (Model.IsRequired)
                {
                    <span class="badge bg-danger question-badge" title="Bắt buộc">
                        <i class="ri-asterisk me-1"></i> Bắt buộc
                    </span>
                }
                <span class="badge bg-info question-badge" title="@typeLabel">
                    <i class="@typeIcon me-1"></i> @typeLabel
                </span>
            </div>

            <!-- Action buttons -->
            <div class="btn-group ms-2">
                <button class="btn btn-sm btn-outline-primary btn-action" onclick="SurveyModule.editQuestion('@Model.Id')">
                    <i class="ri-edit-line fs-6"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger btn-action" onclick="SurveyModule.removeQuestion('@Model.Id')">
                    <i class="ri-delete-bin-line fs-6"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Question Type Specific Content -->
    <div class="question-content mt-3">
        @if (Model.Type == "likert")
        {
            <partial name="_LikertQuestionPartial" model="Model" />
        }
        else if (new[] { "multiChoice", "singleChoice", "dropDown" }.Contains(Model.Type))
        {
            <partial name="_ChoiceQuestionPartial" model="Model" />
        }
    </div>

    <!-- Required Switch at the bottom -->
    <div class="required-checkbox-container">
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="<EMAIL>" @(Model.IsRequired ? "checked" : "") onchange="SurveyModule.toggleRequired('@Model.Id', this.checked)">
            <label class="form-check-label" for="<EMAIL>">
                <i class="ri-asterisk me-1 text-danger"></i> Bắt buộc trả lời
            </label>
        </div>
    </div>
</div>