﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Events;
using MiniAppCore.Entities.Events.Sponsors;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Entities.Products;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events;
using MiniAppCore.Models.Responses.Events;
using MiniAppCore.Services.Events.Sponsors;
using MiniAppCore.Services.Events.SponsorshipTiers;

namespace MiniAppCore.Services.Events
{
    public class EventService(IUnitOfWork unitOfWork, 
                            IWebHostEnvironment env, 
                            IHttpContextAccessor httpContextAccessor,
                            ISponsorService sponsorService,
                            ISponsorshipTierService sponsorshipTierService,
                            IMapper mapper) : Service<Event>(unitOfWork), IEventService
    {
        private readonly IRepository<EventGift> _eventGiftRepo = unitOfWork.GetRepository<EventGift>();
        private readonly IRepository<EventSponsor> _eventSponsorRepo = unitOfWork.GetRepository<EventSponsor>();
        private readonly IRepository<EventRegistration> _registrationRepo = unitOfWork.GetRepository<EventRegistration>();
        private readonly IRepository<Product> _productRepo = unitOfWork.GetRepository<Product>();

        private readonly string _uploadPath = "uploads/images/events";
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public async Task<PagedResult<EventResponse>> GetPaged(RequestQuery query, string userZaloId, string sponsorId, short status, short activeStatus)
        {
            var events = _repository.AsQueryable();

            if (!string.IsNullOrEmpty(query.Keyword))
            {
                events = events.Where(x => x.Title!.Contains(query.Keyword) || x.Content!.Contains(query.Keyword));
            }

            if (!string.IsNullOrEmpty(sponsorId))
            {
                var sponsors = await _eventSponsorRepo.AsQueryable()
                    .Where(x => x.SponsorId == sponsorId)
                    .Select(x => x.EventId).ToListAsync();

                events = events.Where(x => sponsors.Contains(x.Id));
            }

            if (status > 0)
            {
                events = events.Where(x => x.Status == (EEventStatus)status);
            }

            if (!IsAdmin)
            {
                events = events.Where(x => x.IsActive);
            }
            else
            {
                if (activeStatus == 1) events = events.Where(x => x.IsActive);
                else if (activeStatus == 2) events = events.Where(x => !x.IsActive);
            }

            var totalItems = await events.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / query.PageSize);

            var listEvents = await events
                .OrderByDescending(t => t.CreatedDate)
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            foreach (var ev in listEvents)
            {
                ev.Status = CalculateEventStatus(ev.StartTime, ev.EndTime);

                if (!string.IsNullOrEmpty(ev.Banner) && !ev.Banner.StartsWith("http"))
                {
                    ev.Banner = $"{hostUrl}/{_uploadPath}/{ev.Banner}";
                }

                if (!string.IsNullOrEmpty(ev.Images))
                {
                    var imageList = ev.Images
                        .Split(",")
                        .Select(img => img.StartsWith("http") ? img : $"{hostUrl}/{_uploadPath}/{img}");

                    ev.Images = string.Join(",", imageList);
                }
            }

            var responseList = mapper.Map<List<EventResponse>>(listEvents);

            var registrations = await _registrationRepo.AsQueryable()
                .Where(r => r.UserZaloId == userZaloId)
                .ToListAsync();

            var regDict = registrations.ToDictionary(r => r.EventId, r => r);

            foreach (var item in responseList)
            {
                if (regDict.TryGetValue(item.Id, out var reg))
                {
                    item.IsRegister = reg.IsCheckIn == ECheckInStatus.Cancel ? false : true;
                    item.IsCheckIn = reg.IsCheckIn == ECheckInStatus.CheckIn ? true : false;
                    item.CheckInCode = reg.CheckInCode;
                }
            }

            return new PagedResult<EventResponse>
            {
                Data = responseList,
                TotalPages = totalPages
            };
        }

        public async Task<int> CreateEventAsync(EventDTO dto)
        {
            var ev = mapper.Map<Event>(dto);

            ev.Banner = await HandleImageUploadAsync(dto.Banner);
            ev.Images = await HandleMultipleImageUploadAsync(dto.Images);
            ev.Status = CalculateEventStatus(dto.StartTime, dto.EndTime);

            var gifts = dto.Gifts;
            if (gifts != null && gifts.All(g => !string.IsNullOrEmpty(g.ProductId)))
                await SyncEventGifts(ev.Id, gifts);

            var sponsors = dto.Sponsors;
            if (sponsors != null && sponsors.All(s => !string.IsNullOrEmpty(s.SponsorId) && !string.IsNullOrEmpty(s.TierId)))
                await SyncEventSponsors(ev.Id, sponsors);

            _repository.Add(ev);

            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> UpdateEventAsync(string id, EventDTO dto)
        {
            var ev = await _repository.FindByIdAsync(id);
            if (ev == null) throw new CustomException(1, "Sự kiện không khả dụng!");

            mapper.Map(dto, ev);

            ev.Banner = await HandleImageUploadAsync(dto.Banner, ev.Banner);
            ev.Images = await HandleMultipleImageUploadAsync(dto.Images, ev.Images);
            ev.Status = CalculateEventStatus(dto.StartTime, dto.EndTime);

            var gifts = dto.Gifts;
            if (gifts != null && gifts.All(g => !string.IsNullOrEmpty(g.ProductId)))
                await SyncEventGifts(id, gifts);

            var sponsors = dto.Sponsors;
            if (sponsors != null && sponsors.All(s => !string.IsNullOrEmpty(s.SponsorId) && !string.IsNullOrEmpty(s.TierId)))
                await SyncEventSponsors(id, sponsors);

            _repository.Update(ev);

            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> DeleteEventAsync(string id)
        {
            var ev = await _repository.FindByIdAsync(id);
            if (ev == null) throw new CustomException(1, "Sự kiện không tồn tại!");

            if (!string.IsNullOrEmpty(ev.Banner))
                RemoveOldImage(ev.Banner);
            if (!string.IsNullOrEmpty(ev.Images))
                RemoveOldImage(ev.Images);

            var eventGifts = await _eventGiftRepo.AsQueryable()
                .Where(g => g.EventId == id)
                .ToListAsync();
            if (eventGifts.Any())
                _eventGiftRepo.DeleteRange(eventGifts);

            var eventSponsors = await _eventSponsorRepo.AsQueryable()
                .Where(s => s.EventId == id)
                .ToListAsync();
            if (eventSponsors.Any())
                _eventSponsorRepo.DeleteRange(eventSponsors);

            _repository.Delete(ev);

            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<EventDetailResponse?> GetDetailAsync(string id, string userZaloId)
        {
            var ev = await _repository.FindByIdAsync(id);
            if (ev == null) return null;

            var response = mapper.Map<EventDetailResponse>(ev);
            response.Gifts = await GetGifts(id);
            response.Sponsors = await GetSponsors(id);
            response.Tiers = await GetTiers(id);

            response.Banner = Tools.GetImage(hostUrl, ev.Banner, _uploadPath);
            response.Images = Tools.GetImages(hostUrl, ev.Images, _uploadPath);

            var registrations = await _registrationRepo.AsQueryable()
                .Where(r => r.UserZaloId == userZaloId)
                .ToListAsync();

            var regDict = registrations.ToDictionary(r => r.EventId, r => r);

            if (regDict.TryGetValue(response.Id, out var reg))
            {
                response.IsRegister = reg.IsCheckIn == ECheckInStatus.Cancel ? false : true;
                response.IsCheckIn = reg.IsCheckIn == ECheckInStatus.CheckIn ? true : false;
                response.CheckInCode = reg.CheckInCode;
            }

            return response;
        }

        public async Task<List<EventGiftResponse>> GetGifts(string id)
        {
            var giftEntities = await _eventGiftRepo.AsQueryable()
                .Where(g => g.EventId == id)
                .ToListAsync();

            var productIds = giftEntities.Select(g => g.ProductId).Distinct().ToList();

            var products = await _productRepo.AsQueryable()
                .Where(p => productIds.Contains(p.Id))
                .ToDictionaryAsync(p => p.Id);

            return giftEntities
                .Where(g => products.ContainsKey(g.ProductId))
                .Select(g =>
                {
                    var product = products[g.ProductId];
                    var images = (product.Images ?? "")
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(img => img.StartsWith("http", StringComparison.OrdinalIgnoreCase)
                            ? img
                            : $"{hostUrl}/uploads/images/products/{img}")
                        .ToList();

                    return new EventGiftResponse
                    {
                        ProductId = g.ProductId,
                        Name = product.Name,
                        Price = product.Price,
                        Quantity = g.Quantity,
                        Images = images
                    };
                }).ToList();
        }

        public async Task<List<EventSponsorResponse>> GetSponsors(string id)
        {
            var sponsorEntities = await _eventSponsorRepo.AsQueryable()
                .Where(s => s.EventId == id)
                .ToListAsync();

            var sponsorIds = sponsorEntities.Select(s => s.SponsorId).Distinct().ToList();
            var tierIds = sponsorEntities.Where(s => !string.IsNullOrEmpty(s.TierId)).Select(s => s.TierId!).Distinct().ToList();

            var sponsors = await sponsorService.GetListByIdsAsync(sponsorIds);
            var sponsorDict = sponsors.ToDictionary(s => s.Id);

            var tiers = await sponsorshipTierService.GetListByIdsAsync(tierIds);
            var tierDict = tiers.ToDictionary(t => t.Id);

            return sponsorEntities
                .Where(s => sponsorDict.ContainsKey(s.SponsorId))
                .Select(s =>
                {
                    var sponsor = sponsorDict[s.SponsorId];
                    var tier = string.IsNullOrEmpty(s.TierId) ? null : tierDict.GetValueOrDefault(s.TierId);

                    var sponsorImage = string.IsNullOrEmpty(sponsor.Image) ? null :
                        (sponsor.Image.StartsWith("http") ? sponsor.Image : $"{hostUrl}/uploads/images/sponsors/{sponsor.Image}");

                    var tierImage = tier == null || string.IsNullOrEmpty(tier.Image) ? null :
                        (tier.Image.StartsWith("http") ? tier.Image : $"{hostUrl}/uploads/images/sponsorshiptiers/{tier.Image}");

                    return new EventSponsorResponse
                    {
                        SponsorId = sponsor.Id,
                        SponsorName = sponsor.SponsorName,
                        Introduction = sponsor.Introduction,
                        WebsiteURL = sponsor.WebsiteURL,
                        TierId = s.TierId,
                        TierName = tier?.TierName,
                        Image = sponsorImage,
                        TierImage = tierImage
                    };
                }).ToList();
        }

        private async Task<List<EventTierResponse>> GetTiers(string id)
        {
            var sponsorEntities = await _eventSponsorRepo.AsQueryable()
                .Where(s => s.EventId == id)
                .ToListAsync();

            var sponsorIds = sponsorEntities.Select(s => s.SponsorId).Distinct().ToList();
            var tierIds = sponsorEntities.Where(s => !string.IsNullOrEmpty(s.TierId)).Select(s => s.TierId!).Distinct().ToList();

            var sponsors = await sponsorService.GetListByIdsAsync(sponsorIds);
            var sponsorDict = sponsors.ToDictionary(s => s.Id);

            var tiers = await sponsorshipTierService.GetListByIdsAsync(tierIds);
            var tierDict = tiers.ToDictionary(t => t.Id);

            // Nhóm theo TierId
            var groupedByTier = sponsorEntities
                .Where(s => sponsorDict.ContainsKey(s.SponsorId))
                .GroupBy(s => s.TierId ?? string.Empty)
                .ToList();

            var result = new List<EventTierResponse>();

            foreach (var group in groupedByTier)
            {
                var tierId = group.Key;
                var tier = tierDict.GetValueOrDefault(tierId);

                var tierImage = string.IsNullOrEmpty(tier?.Image) ? null :
                    (tier.Image.StartsWith("http") ? tier.Image : $"{hostUrl}/uploads/images/sponsorshiptiers/{tier.Image}");

                var sponsorList = group
                    .Select(s =>
                    {
                        var sponsor = sponsorDict[s.SponsorId];
                        var sponsorImage = string.IsNullOrEmpty(sponsor.Image) ? null :
                            (sponsor.Image.StartsWith("http") ? sponsor.Image : $"{hostUrl}/uploads/images/sponsors/{sponsor.Image}");

                        return new EventSponsorByTierResponse
                        {
                            Id = sponsor.Id,
                            Name = sponsor.SponsorName,
                            Introduction = sponsor.Introduction,
                            WebsiteURL = sponsor.WebsiteURL,
                            Image = sponsorImage
                        };
                    })
                    .ToList();

                result.Add(new EventTierResponse
                {
                    Id = string.IsNullOrEmpty(tierId) ? null : tierId,
                    Name = tier?.TierName ?? "Không phân loại",
                    Image = tierImage,
                    Sponsors = sponsorList
                });
            }

            return result;
        }

        #region Data Process

        private async Task SyncEventGifts(string eventId, List<EventGiftDTO> newGifts)
        {
            var currentGifts = await _eventGiftRepo.AsQueryable()
                .Where(g => g.EventId == eventId)
                .ToListAsync();

            var toAdd = new List<EventGift>();
            var toUpdate = new List<EventGift>();
            var toRemove = new List<EventGift>();

            foreach (var dto in newGifts)
            {
                if (string.IsNullOrEmpty(dto.ProductId)) continue;

                var product = await _productRepo.FindByIdAsync(dto.ProductId);
                if (product == null) continue;

                var existing = currentGifts.FirstOrDefault(g => g.ProductId == dto.ProductId);

                if (existing == null)
                {
                    toAdd.Add(new EventGift
                    {
                        EventId = eventId,
                        ProductId = dto.ProductId,
                        Quantity = dto.Quantity
                    });
                }
                else
                {
                    existing.Quantity = dto.Quantity;
                    toUpdate.Add(existing);
                }
            }

            var newIds = newGifts.Where(x => !string.IsNullOrEmpty(x.ProductId)).Select(x => x.ProductId).ToHashSet();
            toRemove = currentGifts.Where(g => !newIds.Contains(g.ProductId)).ToList();

            if (toAdd.Any()) _eventGiftRepo.AddRange(toAdd);
            if (toUpdate.Any()) _eventGiftRepo.UpdateRange(toUpdate);
            if (toRemove.Any()) _eventGiftRepo.DeleteRange(toRemove);
        }

        private async Task SyncEventSponsors(string eventId, List<EventSponsorDTO> newSponsors)
        {
            var currentSponsors = await _eventSponsorRepo.AsQueryable()
                .Where(s => s.EventId == eventId)
                .ToListAsync();

            var toAdd = new List<EventSponsor>();
            var toUpdate = new List<EventSponsor>();
            var toRemove = new List<EventSponsor>();

            foreach (var dto in newSponsors)
            {
                if (string.IsNullOrEmpty(dto.SponsorId) || string.IsNullOrEmpty(dto.TierId)) continue;

                var sponsor = await sponsorService.GetById(dto.SponsorId);
                var tier = await sponsorshipTierService.GetById(dto.TierId);

                if (sponsor == null || tier == null) continue;
                if (!sponsor.IsActive) continue;

                var existing = currentSponsors.FirstOrDefault(s => s.SponsorId == dto.SponsorId);

                if (existing == null)
                {
                    toAdd.Add(new EventSponsor
                    {
                        EventId = eventId,
                        SponsorId = dto.SponsorId,
                        TierId = dto.TierId
                    });
                }
                else if (existing.TierId != dto.TierId)
                {
                    existing.TierId = dto.TierId;
                    toUpdate.Add(existing);
                }
            }

            var newIds = newSponsors.Where(x => !string.IsNullOrEmpty(x.SponsorId)).Select(x => x.SponsorId).ToHashSet();
            toRemove = currentSponsors.Where(s => !newIds.Contains(s.SponsorId)).ToList();

            if (toAdd.Any()) _eventSponsorRepo.AddRange(toAdd);
            if (toUpdate.Any()) _eventSponsorRepo.UpdateRange(toUpdate);
            if (toRemove.Any()) _eventSponsorRepo.DeleteRange(toRemove);
        }

        private async Task<string?> HandleImageUploadAsync(IFormFile? imageFile, string? oldImage = null)
        {
            if (imageFile == null) return oldImage;

            if (!string.IsNullOrEmpty(oldImage))
                RemoveOldImage(oldImage);

            return await ProcessUpload(new List<IFormFile> { imageFile });
        }

        private async Task<string?> HandleMultipleImageUploadAsync(List<IFormFile>? imageFiles, string? oldImages = null)
        {
            if (imageFiles == null || imageFiles.Count == 0)
                return oldImages;

            if (!string.IsNullOrEmpty(oldImages))
                RemoveOldImage(oldImages);

            return await ProcessUpload(imageFiles);
        }

        private async Task<string> ProcessUpload(List<IFormFile> files)
        {
            if (files == null || files.Count == 0)
                return string.Empty;

            var savePath = Path.Combine(env.WebRootPath, "uploads/images/events");
            var fileResult = await FileHandler.SaveFiles(files, savePath);
            return string.Join(",", fileResult);
        }

        private void RemoveOldImage(string listImage)
        {
            var images = listImage.Split(",")
                .Select(x => Path.Combine(env.WebRootPath, "uploads/images/events", x))
                .ToList();

            FileHandler.RemoveFiles(images);
        }

        private static EEventStatus CalculateEventStatus(DateTime startTime, DateTime endTime)
        {
            var now = DateTime.Now;

            if (now < startTime) return EEventStatus.Upcoming;
            if (now >= startTime && now <= endTime) return EEventStatus.Ongoing;
            return EEventStatus.Ended;
        }

        #endregion
    }
}
