﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Database;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Bookings;
using MiniAppCore.Entities.Branches;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Bookings;
using MiniAppCore.Models.Responses.BookingItem;
using MiniAppCore.Models.Responses.Bookings;
using MiniAppCore.Services.Branches;
using MiniAppCore.Services.Memberships;
using System.Text.RegularExpressions;

namespace MiniAppCore.Services.Bookings
{
    public class BookingService(IUnitOfWork unitOfWork, IMapper mapper, IHttpContextAccessor httpContextAccessor, IMembershipService membershipService,IBranchService branchService) : Service<Booking>(unitOfWork), IBookingService
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";
        private readonly IRepository<BookingItem> _bookingItemRepository = unitOfWork.GetRepository<BookingItem>();
        private readonly IRepository<BookingDetail> _bookingDetailRepository = unitOfWork.GetRepository<BookingDetail>();

        public async Task<PagedResult<BookingResponse>> GetPaged(PurchaseQueryParams queryParams, string? userZaloId = null)
        {
            var bookings = ApplyFilters(_repository.AsQueryable(), queryParams, userZaloId);

            var totalItems = await bookings.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / queryParams.PageSize);

            var bookingList = await bookings
                            .OrderByDescending(x => x.BookingDate)
                            .Skip(queryParams.Skip)
                            .Take(queryParams.PageSize)
                            .ToListAsync();

            var memberships = await membershipService.GetByUserZaloIds(bookingList.Select(x => x.UserZaloId).Distinct().ToList());
            var branches = await branchService.GetBranchesByIds(bookingList.Select(x => x.BranchId).Distinct().ToList());

            var items = bookingList.Select(x =>
            {
                var membership = memberships.FirstOrDefault(m => m.UserZaloId == x.UserZaloId);
                if (membership == null)
                {
                    return null;
                }
                var branch = branches.FirstOrDefault(b => b.Id == x.BranchId);
                return mapper.Map<BookingResponse>((x, membership, branch));
            }).Where(x => x != null).Select(x => x!).ToList();

            return new PagedResult<BookingResponse>
            {
                Data = items,
                TotalPages = totalPages
            };
        }

        private IQueryable<Booking> ApplyFilters(IQueryable<Booking> query, PurchaseQueryParams queryParams, string? userZaloId = null)
        {
            if (!string.IsNullOrEmpty(queryParams.Keyword))
            {
                // Thêm điều kiện tìm kiếm theo từ khóa nếu có
                var keyword = queryParams.Keyword;
                var memberships = unitOfWork.GetRepository<Membership>();
                query = query.Where(x => x.Id == keyword || memberships.AsQueryable()
                                                    .Any(m => m.UserZaloId == x.UserZaloId &&
                                                             (m.UserZaloName.Contains(keyword) || m.PhoneNumber.Contains(keyword))));
            }

            if (!string.IsNullOrEmpty(userZaloId))
            {
                query = query.Where(x => x.UserZaloId == userZaloId);
            }

            if (queryParams.BookingStatus.HasValue && queryParams.BookingStatus.Value != -1)
            {
                query = query.Where(x => queryParams.BookingStatus == (short)x.Status);
            }

            if (queryParams.StartDate.HasValue)
            {
                query = query.Where(x => x.BookingDate >= queryParams.StartDate.Value);
            }

            if (queryParams.EndDate.HasValue)
            {
                query = query.Where(x => x.BookingDate <= queryParams.EndDate.Value);
            }

            if (queryParams.BranchId.Any())
            {
                query = query.Where(x => !string.IsNullOrEmpty(x.BranchId) && queryParams.BranchId.Contains(x.BranchId));
            }

            return query;
        }

        public async Task<BookingDetailResponse> GetBookingDetailResponseAsync(string bookingId)
        {
            var booking = await base.GetByIdAsync(bookingId);
            if (booking == null)
            {
                throw new CustomException(200, "Đặt lịch không tồn tại!");
            }

            var membership = await membershipService.GetByUserZaloId(booking.UserZaloId);
            var bookingDetails = _bookingDetailRepository.AsQueryable().Where(x => x.BookingId == bookingId);
            var bookingItemDetails = await _bookingItemRepository.AsQueryable().Where(x => bookingDetails.Any(d => d.BookingItemId == x.Id)).ToListAsync();

            return new BookingDetailResponse
            {
                Id = booking.Id,
                Note = booking.Note,
                UserZaloId = booking.UserZaloId,
                UserZaloName = booking.Name ?? membership?.UserZaloName,
                Status = (short)booking.Status,
                PhoneNumber = membership?.PhoneNumber,
                BookingDate = booking.BookingDate,
                CreatedDate = booking.CreatedDate,
                BookingItems = bookingItemDetails.Select(item => new BookingItemResponse
                {
                    Id = item.Id,
                    Name = item.Name,
                    OriginalPrice = item.Price,
                    DiscountPrice = item.Price,
                    Description = Regex.Replace(item.Description ?? string.Empty, "<.*?>", ""),
                    Images = string.IsNullOrEmpty(item.Images)
                   ? new List<string>()
                   : item.Images.Split(',').Select(x => $"{hostUrl}/uploads/images/bookingItems/{x}").ToList()
                }).ToList(),
                Branch = string.IsNullOrEmpty(booking.BranchId) ? null: branchService.GetByIdAsync(booking.BranchId).Result
            };
        }

        public async Task<string> CreateAsync(string userZaloId, BookingRequest dto)
        {
            // Ánh xạ BookingRequest -> Booking
            var booking = mapper.Map<Booking>(dto);
            booking.UserZaloId = userZaloId;
            booking.Status = EBooking.Pending; // Mặc định là "Chờ xác nhận"

            if (string.IsNullOrEmpty(dto.MembershipName))
            {
                var membership = await membershipService.GetByUserZaloId(userZaloId);
                dto.MembershipName = membership?.UserZaloName ?? "Khách vãng lai";
            }

            // Thêm vào DB
            _repository.Add(booking);

            // Xử lý danh sách BookingItems -> BookingDetail
            // Lấy danh sách BookingItem từ DB theo danh sách ItemId
            var bookingDetails = new List<BookingDetail>();
            var bookingItems = await _bookingItemRepository.FindByIdsAsync(dto.BookingItems.Select(x => x.ItemId).ToList());
            foreach (var item in dto.BookingItems)
            {
                var bookingItem = bookingItems.FirstOrDefault(x => x.Id == item.ItemId);
                if (bookingItem == null)
                {
                    throw new Exception($"Không tìm thấy dịch vụ với Id: {item.ItemId}");
                }

                var bookingDetail = new BookingDetail
                {
                    BookingId = booking.Id.ToString(),
                    BookingItemId = bookingItem.Id,
                    Quantity = item.Quantity,  // Gán số lượng từ request
                    Note = item.Note,          // Gán ghi chú từ request
                    OrginalPrice = bookingItem.Price,
                    DiscountPrice = bookingItem.Price // Hiện tại chưa có giảm giá
                };

                bookingDetails.Add(bookingDetail);
            }

            // Thêm danh sách BookingDetail vào DB
            _bookingDetailRepository.AddRange(bookingDetails);
            await unitOfWork.SaveChangesAsync();

            return booking.Id;
        }

        public async Task<int> UpdateAsync(string id, BookingRequest dto)
        {
            var booking = await base.GetByIdAsync(id);
            if (booking == null)
            {
                throw new Exception("Đặt lịch không tồn tại!");
            }

            booking.BranchId = dto.BranchId;
            booking.Note = dto.Note;
            booking.Status = (EBooking)dto.Status;
            booking.BookingDate = dto.BookingDate;

            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> UpdateStatusBooking(string id, EBooking newStatus, string? reason = null)
        {
            var booking = await base.GetByIdAsync(id);
            if (booking == null)
            {
                throw new CustomException(200, "Đặt lịch không tồn tại!");
            }

            // Ràng buộc trạng thái
            switch (newStatus)
            {
                case EBooking.CheckIn:
                    if (booking.Status != EBooking.Confirmed)
                    {
                        throw new CustomException(200, "Chỉ có thể check-in khi đặt lịch đã được xác nhận.");
                    }
                    break;

                case EBooking.Completed:
                    if (booking.Status != EBooking.CheckIn && booking.Status != EBooking.InProgress)
                    {
                        throw new CustomException(200, "Chỉ có thể hoàn thành khi khách đã check-in hoặc đang trong quá trình sử dụng dịch vụ.");
                    }
                    break;

                case EBooking.Canceled:
                    if (booking.Status == EBooking.Canceled)
                    {
                        throw new CustomException(200, "Đặt lịch đã hủy không thể thực hiện hủy nữa!");
                    }
                    break;
                case EBooking.Rejected:
                    if (booking.Status == EBooking.CheckIn || booking.Status == EBooking.Completed)
                    {
                        throw new CustomException(200, "Không thể hủy hoặc từ chối khi đặt lịch đã check-in hoặc đã hoàn thành.");
                    }
                    break;
                default:
                    break;
            }

            if (!string.IsNullOrEmpty(reason))
            {
                booking.CancelReason = reason;
            }

            // Cập nhật trạng thái nếu hợp lệ
            booking.Status = newStatus;
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<byte[]> ExportBookings(DateTime? startDate, DateTime? endDate, short? bookingStatus)
        {
            var query = new PurchaseQueryParams()
            {
                StartDate = startDate,
                EndDate = endDate,
                BookingStatus = bookingStatus
            };

            var orders = ApplyFilters(_repository.AsQueryable(), query, null);

            var listOrder = await orders.OrderByDescending(x => x.CreatedDate).ToListAsync();
            var memberships = await membershipService.GetByUserZaloIds(listOrder.Select(x => x.UserZaloId).ToList());

            var items = listOrder.Select(x =>
            {
                var membership = memberships.FirstOrDefault(m => m.UserZaloId == x.UserZaloId);
                return mapper.Map<BookingResponse>((x, membership));
            });

            if (!items.Any())
            {
                throw new CustomException(400, "Không có dữ liệu nào");
            }

            // Dictionary ánh xạ tiêu đề tiếng Anh sang tiếng Việt
            var mappingDictHeaders = new Dictionary<string, string>
            {
                { "Id", "Mã đơn hàng" },
                { "Status", "Trạng thái đơn hàng" },
                { "UserZaloName", "Tên khách hàng" },
                { "PhoneNumber", "Số điện thoại" },
                { "BookingDate", "Ngày đặt chỗ" },
                { "CreatedDate", "Ngày tạo đơn hàng" },
                { "Note", "Ghi chú" }
            };

            // Tạo một dictionary để lưu trữ dữ liệu theo cột
            var data = new Dictionary<string, List<string>>();
            foreach (var key in mappingDictHeaders.Keys)
            {
                data[key] = new List<string>();
            }

            var bookingStatusDict = new Dictionary<int, string>()
            {
                {(int)EBooking.Pending, "Đang chờ xác nhận" },       // Pending
                {(int)EBooking.Confirmed, "Đã xác nhận" },     // Confirmed
                {(int)EBooking.CheckIn, "Khách đã check-in" },       // CheckIn
                {(int)EBooking.InProgress, "Dịch vụ đang diễn ra" },    // InProgress
                {(int)EBooking.Completed, "Đặt lịch đã hoàn thành" },     // Completed
                {(int)EBooking.Canceled, "Đặt lịch đã bị hủy" },      // Canceled
                {(int)EBooking.Rejected, "Đặt lịch đã bị từ chối" }        // Rejected
            };

            foreach (var item in items)
            {
                // Ánh xạ từ EBooking - Booking Status
                data["Status"].Add(bookingStatusDict.ContainsKey(item.Status ?? 0)
                    ? bookingStatusDict[item.Status.Value]
                    : "Không xác định");

                // Ánh xạ các thuộc tính khác của BookingResponse vào dữ liệu
                data["Id"].Add(item.Id ?? string.Empty);
                data["UserZaloName"].Add(item.UserZaloName ?? string.Empty);
                data["PhoneNumber"].Add(item.PhoneNumber ?? string.Empty);
                data["Note"].Add(item.Note ?? string.Empty);
                data["BookingDate"].Add(item.BookingDate.ToString("yyyy-MM-dd"));
                data["CreatedDate"].Add(item.CreatedDate.ToString("yyyy-MM-dd"));
            }

            // Ánh xạ tiêu đề tiếng Anh sang tiếng Việt
            var mappedData = new Dictionary<string, List<string>>();
            foreach (var key in data.Keys)
            {
                if (mappingDictHeaders.ContainsKey(key))
                {
                    var mappedHeader = mappingDictHeaders[key];
                    mappedData[mappedHeader] = data[key];
                }
            }

            return await ExportHandler.ExportData("Đặt lịch", mappedData);
        }
    }
}
