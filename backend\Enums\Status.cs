﻿namespace MiniAppCore.Enums
{
    public enum Status
    {
        BOOKING_WAITING = 1,
        BOOKING_REJECTED = 2,
        BOOKING_CONFIRMED = 3,
        BOOKING_CANCELLED = 4,
        BOOKING_COMPLETED = 5,
        BOOKING_CHECKEDIN = 6,
        BOOKING_NOTCOME = 7,

        ORDER_WAITING = 1,
        ORDER_REJECTED = 2,
        ORDER_CONFIRMED = 3,
        ORDER_CANCELLED = 4,
        ORDER_COMPLETED = 5,

        DELIVERY_WAITING = 0,
        DELIVERY_PREPARING = 1,
        DELIVERY_CONFIRMED = 2,
        DELIVERY_COMPLETE = 3,

        PAYMENT_UNPAID = 0,
        PAYMENT_PAID = 1,

        VOUCHER_AVAILABLE = 1,
        VOUCHER_USED = 2,
        VOUCHER_EXPIRED = 3,

        WITHDRAW_WAITING = 1,
        WITHDRAW_REJECTED = 2,
        WITHDRAW_CONFIRMED = 3,
        WITHDRAW_CANCELLED = 4,
        WITHDRAW_COMPLETED = 5,

        ARTICLE_PRIVATE = 0,
        ARTICLE_PUBLIC = 1,
    }

    public enum TransactionStatus
    {
        NO_STATUS, // Chờ xác nhận
        PENDING, // Chờ xác nhận
        CONFIRMED, // Đã xác nhận
        COMPLETE, // Hoàn thành
        REJECT, // Từ chối
        CANCEL, // Huỷ

        PAID, // Đã thanh toán
        UNPAID, // Chưa thanh toán
    }

    public enum DiscountType
    {
        PERCENTAGE = 0,
        FLAT = 1
    }

    public enum StockStatus
    {
        IN_STOCK = 0,
        OUT_OF_STOCK = 1,
        NO_LONGER_AVAILABLE = 2
    }

    public enum VoucherExchangeType
    {
        POINT_EXCHANGE = 0,
        DIRECT_EXCHANGE = 1
    }
}
