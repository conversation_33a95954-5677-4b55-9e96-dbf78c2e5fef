﻿@model MiniAppCore.Entities.Products.Brands.Brand;
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator">
                <input type="hidden" value="@Model.Id" />
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Tên thương hiệu <span style="color:red">*</span></label>
                            <input id="name" type="text" class="form-control" value="@Model.Name" placeholder="Tên sản phẩm... " data-errors="Vui lòng tên sản phẩm." required>
                            <div class="help-block with-errors"></div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="descr">Mô tả thương hiệu</label>
                            <textarea id="descr" class="form-control" rows="4">@Model.Description</textarea>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-group">
                            <label>Ảnh<span style=" color:red">*</span></label>
                            <input type="file" id="pics" class="form-control image-file" name="pic" accept="image/*" onchange="ShowPreview(event)" multiple>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div id="preview" class="row d-flex flex-nowrap" style="overflow-x: scroll; min-height: 250px;">
                            @if (!string.IsNullOrEmpty(Model.Images))
                            {
                                var images = Model.Images.Split(",").Select(x => $"/images/brands/{x}");

                                foreach (var item in images)
                                {
                                    <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                        <img src="@item" class="card-img-top h-100" alt="Alternate Text" style="object-fit:contain" />
                                        <span class="btn-preview-remove" data-url="@item">x</span>
                                    </div>
                                }
                            }
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        @if (User.IsInRole("ADMIN"))
        {
            <button type="button" class="btn btn-secondary" onclick="ClearForm()">Làm mới</button>
            <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
        }
    </div>
</div>

<script>
    $(document).ready(() => {
        currentImages = $("#preview .image-preview img").length;
    });
</script>