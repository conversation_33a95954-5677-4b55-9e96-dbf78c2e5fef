﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Models.Common;

namespace MiniAppCore.Models.Queries
{
    public class PurchaseQueryParams : RequestQuery, IRequestQuery
    {
        public DateTime? EndDate { get; set; }
        public DateTime? StartDate { get; set; }

        private short? _orderStatus;
        public short? OrderStatus
        {
            get => _orderStatus;
            set => _orderStatus = (value == -1) ? null : value;
        }

        public short? PaymentStatus { get; set; }

        private short? _bookingStatus;
        public short? BookingStatus
        {
            get => _bookingStatus;
            set => _bookingStatus = (value == -1) ? null : value;
        }

        public List<string> BranchId { get; set; } = new();
    }
}
