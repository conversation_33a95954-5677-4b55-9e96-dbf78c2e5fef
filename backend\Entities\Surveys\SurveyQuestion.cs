﻿using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Surveys
{
    public class SurveyQuestion : BaseEntity
    {
        [MaxLength(36)]
        public required string SurveySectionId { get; set; }

        [MaxLength(50)]
        public string? Type { get; set; }

        public string? QuestionTitle { get; set; }

        public bool IsRequired { get; set; }

        public short DisplayOrder { get; set; }
    }
}
