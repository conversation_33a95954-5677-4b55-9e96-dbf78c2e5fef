﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.SystemSettings;
using MiniAppCore.Models.Requests.ActionButtons;
using MiniAppCore.Services.OmniTool.TokenManager;
using MiniAppCore.Services.Orders.ShippingFees;
using MiniAppCore.Services.SystemSettings;
using MiniAppCore.Services.SystemSettings.ActionButtonConfigs;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "ADMIN")]
    public class SystemSettingsController(ILogger<SystemSettingsController> logger,
                                          ISystemSettingService systemSettingService,
                                          IActionButtonService actionButtonService,
                                          ITokenManagerService tokenManagerService,
                                          IShippingFeeService shippingFeeService) : ControllerBase
    {
        #region "Omni Account"

        [HttpPost("OmniAccount")]
        public async Task<IActionResult> CreateOrUpdateOmniAccount([FromBody] OmniAccountDTO model)
        {
            try
            {
                await systemSettingService.AddOrUpdateAccountOmniAsync(model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật tài khoản omni thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        #endregion

        #region Category Home

        [AllowAnonymous]
        [HttpGet("CategoryHome")]
        public async Task<IActionResult> GetCategoryHome()
        {
            try
            {
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = await systemSettingService.GetCategoryHomeAsync()
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpPost("CategoryHome")]
        public async Task<IActionResult> CreateOrUpdateCategoryHome([FromBody] List<string> categoryIds)
        {
            try
            {
                await systemSettingService.AddOrUpdateCategoryHomeAsync(categoryIds);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật danh mục trang chủ thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        #endregion

        #region Enterpise Information

        [AllowAnonymous]
        [HttpGet("About")]
        public async Task<IActionResult> GetEnterpiseInformation()
        {
            try
            {
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = await systemSettingService.GetEnterpriseInformationAsync()
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpPost("About")]
        public async Task<IActionResult> CreateOrUpdateEnterpriseInformation([FromBody] EnterpriseInfomationDTO data)
        {
            try
            {
                await systemSettingService.AddOrUpdateEnterpriseInformationAsync(data);
                if (!string.IsNullOrEmpty(data.PaymentInstruction))
                {
                    await systemSettingService.AddOrUpdateLinkPaymentGuideAsync(data.PaymentInstruction);
                }
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật thông tin doanh nghiệp thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        #endregion

        #region Shipping Fee

        [HttpGet("ShippingFee")]
        public async Task<IActionResult> GetPageShippingFeeConfig([FromQuery] RequestQuery query)
        {
            try
            {
                var result = await shippingFeeService.GetPage(query);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpPost("ShippingFee")]
        public async Task<IActionResult> CreateShippingFeeConfig([FromBody] ShippingFeeConfig data)
        {
            try
            {
                await shippingFeeService.CreateAsync(data);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo mới cấu hình phí ship thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpPut("ShippingFee/{id}")]
        public async Task<IActionResult> UpdateShippingFeeConfig(string id, [FromBody] ShippingFeeConfig data)
        {
            try
            {
                await shippingFeeService.UpdateAsync(id, data);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật cấu hình phí ship thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpDelete("ShippingFee/{id}")]
        public async Task<IActionResult> DeleteShippingFeeConfig(string id)
        {
            try
            {
                await shippingFeeService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa cấu hình phí ship thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        #endregion

        #region Enable/Disable features
        [HttpGet("Features")]
        [AllowAnonymous]
        public async Task<IActionResult> GetFeatures()
        {
            try
            {
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = await systemSettingService.GetFeaturesButton()
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        #endregion

        #region Oa token manager

        [HttpPost("TokenOa")]
        public async Task<IActionResult> UpdateToken([FromBody] OaInfoDTO oaInfo)
        {
            try
            {
                await tokenManagerService.UpdateToken(oaInfo.AccessToken, oaInfo.RefreshToken);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thanh cong"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred");
                return StatusCode(500, ex.Message);
            }
        }

        [HttpPost("AppInfo")]
        public async Task<IActionResult> UpdateApp([FromBody] OaInfoDTO oaInfo)
        {
            try
            {
                await tokenManagerService.UpdateAppInfo(oaInfo.AppId, oaInfo.SecretKey);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thanh cong"
                });
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "An error occurred");
                return StatusCode(500, ex.Message);
            }
        }

        #endregion

        #region ActionButtons API

        [HttpGet("ActionButtons/List")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPageActionButtons()
        {
            try
            {
                var result = await actionButtonService.GetActionButtonConfigsAsync();
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpGet("ActionButtons")]
        [AllowAnonymous]
        public async Task<IActionResult> GetPageActionButtons([FromQuery] RequestQuery query, [FromQuery] string? category, [FromQuery] string? type)
        {
            try
            {
                var result = await actionButtonService.GetPage(query, category, type);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpGet("ActionButtons/{id}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetActionButtonById(string id)
        {
            try
            {
                var button = await actionButtonService.GetByIdAsync(id);
                if (button == null)
                {
                    return NotFound(new { Code = 1, Message = "Không tìm thấy nút" });
                }

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = button
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpPost("ActionButtons")]
        public async Task<IActionResult> CreateActionButton([FromForm] ActionButtonRequest model)
        {
            try
            {
                await actionButtonService.CreateAsync(model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo mới nút hành động thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpPut("ActionButtons/{id}")]
        public async Task<IActionResult> UpdateActionButton(string id, [FromForm] ActionButtonRequest model)
        {
            try
            {
                await actionButtonService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật nút hành động thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        [HttpDelete("ActionButtons/{id}")]
        public async Task<IActionResult> DeleteActionButton(string id)
        {
            try
            {
                await actionButtonService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa nút hành động thành công!"
                });
            }
            catch (Exception ex)
            {
                return ServerError(ex);
            }
        }

        #endregion

        private IActionResult ServerError(Exception ex)
        {
            logger.LogDebug(ex.Message);
            return Ok(new
            {
                Code = 1,
                Message = "Internal Server Errors"
            });
        }
    }
}
