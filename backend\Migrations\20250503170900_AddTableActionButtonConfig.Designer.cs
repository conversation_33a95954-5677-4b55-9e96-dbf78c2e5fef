﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using MiniAppCore.Base.Database;

#nullable disable

namespace MiniAppCore.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250503170900_AddTableActionButtonConfig")]
    partial class AddTableActionButtonConfig
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.3")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles", (string)null);

                    b.HasData(
                        new
                        {
                            Id = "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0",
                            ConcurrencyStamp = "1",
                            Name = "ADMIN",
                            NormalizedName = "ADMIN"
                        },
                        new
                        {
                            Id = "12345678-90AB-CDEF-1234-567890ABCDEF",
                            ConcurrencyStamp = "1",
                            Name = "SUPER_ADMIN",
                            NormalizedName = "SUPER_ADMIN"
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);

                    b.HasData(
                        new
                        {
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            RoleId = "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0"
                        },
                        new
                        {
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            RoleId = "12345678-90AB-CDEF-1234-567890ABCDEF"
                        },
                        new
                        {
                            UserId = "ABCDEF12-3456-7890-ABCD-EF1234567890",
                            RoleId = "A1B2C3D4-E5F6-7890-1234-56789ABCDEF0"
                        });
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("MiniAppCore.Entities.Affiliates.CommissionTransaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<decimal>("CommissionRate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsPaid")
                        .HasColumnType("bit");

                    b.Property<DateTime>("OrderDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferredAvatar")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferredName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferredPhone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferredZaloId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferrerCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferrerZaloId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("TotalCommission")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("TotalOrder")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CommissionTransactions");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Articles.Article", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Author")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BannerImage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CategoryId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Images")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("Status")
                        .HasColumnType("smallint");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Articles");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Articles.ArticleCategory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ArticleCategories");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Bookings.Booking", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("BookingDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CancelReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Bookings");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Bookings.BookingDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("BookingId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BookingItemId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("DiscountPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("OrginalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("Quantity")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("BookingDetails");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Bookings.BookingItem", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<int>("BookedCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Images")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsGlobalProduct")
                        .HasColumnType("bit");

                    b.Property<int>("LikeCount")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ReviewPoint")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int");

                    b.Property<bool>("isGift")
                        .HasColumnType("bit");

                    b.HasKey("Id");

                    b.ToTable("BookingItems");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Branches.Branch", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("ClosingTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DistrictId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DistrictName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GoogleMapURL")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOpenHoliday")
                        .HasColumnType("bit");

                    b.Property<string>("Latitude")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Longitude")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("OpeningTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProvinceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProvinceName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<string>("StreetLine")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("WardId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WardName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Branches");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Categories.Category", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("HexColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Images")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsGlobal")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OrderPriority")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Categories");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Commons.ActionButtonConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ActionButtonConfigs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Commons.ApplicationUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers", (string)null);

                    b.HasData(
                        new
                        {
                            Id = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            AccessFailedCount = 0,
                            ConcurrencyStamp = "afb6dce9-cebd-45fd-ab66-e2453e85c71a",
                            Email = "<EMAIL>",
                            EmailConfirmed = false,
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "ICSG",
                            PasswordHash = "AQAAAAIAAYagAAAAEEcPQmzFlWDpwrqX6lEhyOMoXtIf4+AS0K8oO20Uufiznuvnjo05H+eFiIxb5Nq+gg==",
                            PhoneNumber = "0000000000",
                            PhoneNumberConfirmed = false,
                            SecurityStamp = "d928884b-3833-4d98-9fc1-46f334c5771e",
                            TwoFactorEnabled = false,
                            UserName = "IncomSaiGon"
                        },
                        new
                        {
                            Id = "ABCDEF12-3456-7890-ABCD-EF1234567890",
                            AccessFailedCount = 0,
                            ConcurrencyStamp = "0d7f89f6-30af-4a75-9c35-a5a58e2f829a",
                            Email = "<EMAIL>",
                            EmailConfirmed = false,
                            LockoutEnabled = false,
                            NormalizedEmail = "<EMAIL>",
                            NormalizedUserName = "MiniAppCore",
                            PasswordHash = "AQAAAAIAAYagAAAAEEtf/LLPy9rCLenxmCFlA5KzwWZgG1teOs++SBOrwRXkKm2JTdAb9V4LupFMQHhvSw==",
                            PhoneNumber = "0000000000",
                            PhoneNumberConfirmed = false,
                            SecurityStamp = "07599e3c-66ae-4283-9ee9-763775792ddf",
                            TwoFactorEnabled = false,
                            UserName = "MiniAppCore"
                        });
                });

            modelBuilder.Entity("MiniAppCore.Entities.Commons.Common", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Commons");

                    b.HasData(
                        new
                        {
                            Id = "7669df895d73456a8bf134cc0a900349",
                            Content = "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 490, DateTimeKind.Local).AddTicks(4396),
                            Name = "FeaturesButton",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 490, DateTimeKind.Local).AddTicks(4408)
                        });
                });

            modelBuilder.Entity("MiniAppCore.Entities.Commons.Tag", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Tags");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Commons.ViewPermission", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("SubViewId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ViewId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("ViewPermissions");

                    b.HasData(
                        new
                        {
                            Id = "0599752851f84af29f30a8e6b11c4fce",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(1013),
                            IsActive = true,
                            SubViewId = "Overview",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5216),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Dashboard"
                        },
                        new
                        {
                            Id = "935826b1c670497680801c440b2e0e64",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5915),
                            IsActive = true,
                            SubViewId = "BranchList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5917),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Branch"
                        },
                        new
                        {
                            Id = "d4e9267c5086404dbd8dd64e7628a1b9",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5921),
                            IsActive = true,
                            SubViewId = "AffiliateList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5922),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Affiliate"
                        },
                        new
                        {
                            Id = "f3fcc5063ed9409f83d9be0a78276404",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5924),
                            IsActive = true,
                            SubViewId = "ArticleCategory",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5924),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Article"
                        },
                        new
                        {
                            Id = "d8856b7e15cb4ab0a52c64bff8b314ad",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5926),
                            IsActive = true,
                            SubViewId = "ArticleList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5926),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Article"
                        },
                        new
                        {
                            Id = "71a30ffa4fed47349497c24436869e45",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5937),
                            IsActive = true,
                            SubViewId = "Category",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5937),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Product"
                        },
                        new
                        {
                            Id = "1a5759ac54ad495595699824c8b6bc6a",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5939),
                            IsActive = true,
                            SubViewId = "Brand",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5940),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Product"
                        },
                        new
                        {
                            Id = "ca0479e1920c47399bfdb5d4f64eda60",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5941),
                            IsActive = true,
                            SubViewId = "ProductProperty",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5942),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Product"
                        },
                        new
                        {
                            Id = "b11b5701cde1431198be3c65ff83d601",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5943),
                            IsActive = true,
                            SubViewId = "ProductList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5944),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Product"
                        },
                        new
                        {
                            Id = "bb03f9cf14114ce9a9e62024ed628460",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5947),
                            IsActive = true,
                            SubViewId = "BookingItem",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5947),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Booking"
                        },
                        new
                        {
                            Id = "d7c083b5bb1f4b22b3efbcb59db6bded",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5949),
                            IsActive = true,
                            SubViewId = "BookingList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5949),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Booking"
                        },
                        new
                        {
                            Id = "c385950b121b4a9cb6f5ac890492876a",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5951),
                            IsActive = true,
                            SubViewId = "OrderList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5951),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Order"
                        },
                        new
                        {
                            Id = "4b64cdd8f5d74173864fbef4daaf9a0e",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5953),
                            IsActive = true,
                            SubViewId = "InvoiceTemplate",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5954),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Order"
                        },
                        new
                        {
                            Id = "37764e17602e41d6a57c4b35abf3fd32",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5958),
                            IsActive = true,
                            SubViewId = "DiscountList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5958),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Discount"
                        },
                        new
                        {
                            Id = "d8028fdc47a64a9298a37fa493a94073",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5960),
                            IsActive = true,
                            SubViewId = "Promotion",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5960),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Discount"
                        },
                        new
                        {
                            Id = "5b03d93687ec4a6f8bbf1afcb167da35",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5962),
                            IsActive = true,
                            SubViewId = "VoucherList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5962),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Voucher"
                        },
                        new
                        {
                            Id = "1f603528c4944ce288efb2ce7610520c",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5964),
                            IsActive = true,
                            SubViewId = "MembershipList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5965),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Membership"
                        },
                        new
                        {
                            Id = "70e5b6c7d49c4bac99e48c50a3a1010d",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5967),
                            IsActive = true,
                            SubViewId = "Rank",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5968),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Membership"
                        },
                        new
                        {
                            Id = "33e89d68c08449c4826177e9b52bf83f",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5970),
                            IsActive = true,
                            SubViewId = "Tag",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5970),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Membership"
                        },
                        new
                        {
                            Id = "193952b325b84fbc9f9374dd825981a1",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5972),
                            IsActive = true,
                            SubViewId = "History",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5972),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Survey"
                        },
                        new
                        {
                            Id = "2e0067561ec545078867d553bcdc85c3",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5974),
                            IsActive = true,
                            SubViewId = "SurveyList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5974),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "Survey"
                        },
                        new
                        {
                            Id = "1f67946195b44a96a8c564565a3cab9b",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5977),
                            IsActive = true,
                            SubViewId = "GameList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(5978),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "LuckyWheel"
                        },
                        new
                        {
                            Id = "1807f86b67be40d6b7d46d24e900192f",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6034),
                            IsActive = true,
                            SubViewId = "History",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6034),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "LuckyWheel"
                        },
                        new
                        {
                            Id = "65c2d9d8a4994ed29b8f233ff493f7fc",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6037),
                            IsActive = true,
                            SubViewId = "GamePrize",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6037),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "LuckyWheel"
                        },
                        new
                        {
                            Id = "3db5a4f9e19c4cc4be3b6a7ce7317a3d",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6039),
                            IsActive = true,
                            SubViewId = "CampaignList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6039),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "OmniTool"
                        },
                        new
                        {
                            Id = "1f6862a8d2914eb3b74cab5d60ce2a20",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6041),
                            IsActive = true,
                            SubViewId = "CampaignHistory",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6041),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "OmniTool"
                        },
                        new
                        {
                            Id = "9ae8946f0b3849adbb354a52ec7a59ee",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6043),
                            IsActive = true,
                            SubViewId = "EventTemplateList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6043),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "OmniTool"
                        },
                        new
                        {
                            Id = "2cc201acd93c4e349b4f9464b59bbca6",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6045),
                            IsActive = true,
                            SubViewId = "EventTemplateHistory",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6046),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "OmniTool"
                        },
                        new
                        {
                            Id = "30170463c05e452dbea50566e8eb06f0",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6047),
                            IsActive = true,
                            SubViewId = "TemplateUidList",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6048),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "OmniTool"
                        },
                        new
                        {
                            Id = "455579a5c01b44cf8ab8936df9167f81",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6051),
                            IsActive = true,
                            SubViewId = "GeneralSetting",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6051),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "SystemSetting"
                        },
                        new
                        {
                            Id = "aea3de85262847258417fff19d68b632",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6053),
                            IsActive = true,
                            SubViewId = "EnableFeatures",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6054),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "SystemSetting"
                        },
                        new
                        {
                            Id = "3d917cd4d5a54c71a9b62fc632d951ac",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6055),
                            IsActive = true,
                            SubViewId = "MembershipExtendDefaults",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6056),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "SystemSetting"
                        },
                        new
                        {
                            Id = "6371743da95b42dc888d0c3acb922203",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6058),
                            IsActive = true,
                            SubViewId = "ShippingFeeConfig",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6058),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "SystemSetting"
                        },
                        new
                        {
                            Id = "f53477a08c1144bebae4415036cfda73",
                            CreatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6061),
                            IsActive = true,
                            SubViewId = "CustomForm",
                            UpdatedDate = new DateTime(2025, 5, 4, 0, 8, 58, 393, DateTimeKind.Local).AddTicks(6062),
                            UserId = "09876543-21AB-CDEF-5678-90ABCDEF1234",
                            ViewId = "SystemSetting"
                        });
                });

            modelBuilder.Entity("MiniAppCore.Entities.Dashboards.StatisticBooking", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("BookingCount")
                        .HasColumnType("bigint");

                    b.Property<string>("BookingStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateOnly>("UpdatedDate")
                        .HasColumnType("date");

                    b.HasKey("Id");

                    b.ToTable("StatisticBookings");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Dashboards.StatisticMembership", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("JoinCount")
                        .HasColumnType("bigint");

                    b.Property<DateOnly>("UpdatedDate")
                        .HasColumnType("date");

                    b.HasKey("Id");

                    b.ToTable("StatisticMemberships");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Dashboards.StatisticOrder", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("OrderCount")
                        .HasColumnType("bigint");

                    b.Property<string>("OrderStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Revenue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateOnly>("UpdatedDate")
                        .HasColumnType("date");

                    b.HasKey("Id");

                    b.ToTable("StatisticOrders");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Dashboards.StatisticTopBookingItem", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("BookingItemName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Revenue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<long>("Sold")
                        .HasColumnType("bigint");

                    b.Property<DateOnly>("UpdatedDate")
                        .HasColumnType("date");

                    b.HasKey("Id");

                    b.ToTable("StatisticTopBookingItems");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Dashboards.StatisticTopProducts", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ProductName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Revenue")
                        .HasColumnType("bigint");

                    b.Property<long>("Sold")
                        .HasColumnType("bigint");

                    b.Property<DateOnly>("UpdatedDate")
                        .HasColumnType("date");

                    b.HasKey("Id");

                    b.ToTable("StatisticTopProducts");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Dashboards.StatisticVoucher", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<long>("ExchangeCount")
                        .HasColumnType("bigint");

                    b.Property<DateOnly>("UpdatedDate")
                        .HasColumnType("date");

                    b.Property<long>("UsedCount")
                        .HasColumnType("bigint");

                    b.Property<string>("VoucherName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("StatisticVouchers");
                });

            modelBuilder.Entity("MiniAppCore.Entities.ETM.CampaignItem", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CampaignKey")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("bit");

                    b.Property<string>("Key")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VoucherCode")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CampaignItems");
                });

            modelBuilder.Entity("MiniAppCore.Entities.ETM.EventLog", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IdOMniMess")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParamsContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TelcoId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("EventLogs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.ETM.EventTemplate", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Conditions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EventName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEnable")
                        .HasColumnType("bit");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoutingRule")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateMapping")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("EventTemplates");
                });

            modelBuilder.Entity("MiniAppCore.Entities.ETM.EventTemplateLog", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Message")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Metadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Recipient")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RequestBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResponseBody")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResultCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("EventTemplateLogs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.ETM.OmniTemplateConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoutingRule")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateMapping")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("OmniTemplateConfigs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.ETM.ZaloTemplateConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Recipients")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateMapping")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ZaloTemplateConfigs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.ETM.ZaloTemplateUid", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ListParams")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ZaloTemplateUids");
                });

            modelBuilder.Entity("MiniAppCore.Entities.FormCustoms.CustomForm", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("BackgroundColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ButtonColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ButtonText")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CampaignName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TextColor")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CustomForms");
                });

            modelBuilder.Entity("MiniAppCore.Entities.FormCustoms.CustomFormAttribute", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Attribute")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AttributeLabel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AttributeValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomFormId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("DislayOrder")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("Max")
                        .HasColumnType("bigint");

                    b.Property<long>("Min")
                        .HasColumnType("bigint");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CustomFormAttributes");
                });

            modelBuilder.Entity("MiniAppCore.Entities.LuckyWheels.BlackList", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("BlackLists");
                });

            modelBuilder.Entity("MiniAppCore.Entities.LuckyWheels.GamePrize", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Metadata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReferenceId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("GamePrizes");
                });

            modelBuilder.Entity("MiniAppCore.Entities.LuckyWheels.GamePrizeConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DailyLimit")
                        .HasColumnType("int");

                    b.Property<string>("GameId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GamePrizeId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Position")
                        .HasColumnType("int");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<short>("Ranking")
                        .HasColumnType("smallint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<float>("WinRate")
                        .HasColumnType("real");

                    b.HasKey("Id");

                    b.ToTable("GamePrizeConfigs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.LuckyWheels.LuckyWheel", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("RequiredPoints")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("LuckyWheels");
                });

            modelBuilder.Entity("MiniAppCore.Entities.LuckyWheels.SpinHistory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime?>("ClaimDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ClaimStatatus")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsWon")
                        .HasColumnType("bit");

                    b.Property<string>("LuckyWheelId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrizeId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PrizeType")
                        .HasColumnType("int");

                    b.Property<string>("RecipientAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RecipientName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RewardId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SpinHistories");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Memberships.Cart", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("BranchId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Quantity")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VariantId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Carts");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Memberships.Membership", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Avatar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateOfBirth")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Job")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MiniAppId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RankingId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("RankingPoint")
                        .HasColumnType("bigint");

                    b.Property<string>("ReferralCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Source")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("SpinPoint")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserZaloIdByOA")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserZaloName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("UsingPoint")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("Memberships");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Memberships.MembershipAddress", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DistrictId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FullAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProvinceId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StreetLine")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WardId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("MembershipAddresses");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Memberships.MembershipExtend", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Attribute")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CustomFormId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("MembershipExtends");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Memberships.MembershipExtendDefault", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Attribute")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AttributeName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DefaultContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("Max")
                        .HasColumnType("bigint");

                    b.Property<long>("Min")
                        .HasColumnType("bigint");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("MembershipExtendDefaults");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Memberships.MembershipTag", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TagId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("MembershipTags");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Memberships.MembershipVoucher", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("ExpiryDateTimeStamps")
                        .HasColumnType("bigint");

                    b.Property<string>("MembershipId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Quantity")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VoucherId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("MembershipVouchers");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Offers.Discounts.Discount", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<decimal>("MaxDiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<short>("Type")
                        .HasColumnType("smallint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Discounts");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Offers.Discounts.DiscountItem", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DiscountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("DiscountItems");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Offers.Promotion", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Promotions");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Offers.PromotionItem", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsGift")
                        .HasColumnType("bit");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PromotionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("PromotionItems");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Offers.Vouchers.Voucher", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DiscountType")
                        .HasColumnType("int");

                    b.Property<decimal>("DiscountValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("ExchangeTimes")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<long>("ExpiryDateTimeStamps")
                        .HasColumnType("bigint");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsAllProducts")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExchange")
                        .HasColumnType("bit");

                    b.Property<decimal>("MaxDiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MinimumOrderValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("PointRequired")
                        .HasColumnType("bigint");

                    b.Property<long>("Quantity")
                        .HasColumnType("bigint");

                    b.Property<long>("RankingPoint")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("VoucherType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Vouchers");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Offers.Vouchers.VoucherProduct", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VoucherId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("VoucherProducts");
                });

            modelBuilder.Entity("MiniAppCore.Entities.OmniTool.CampaignCSKH", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("AccountCmsId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CampaignCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CampaignName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte>("CampaignStatusID")
                        .HasColumnType("tinyint");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("IdJob")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("OldScheduleTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("RoutingRule")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ScheduleTime")
                        .HasColumnType("datetime2");

                    b.Property<short>("Status")
                        .HasColumnType("smallint");

                    b.Property<string>("TemplateCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Total")
                        .HasColumnType("int");

                    b.Property<int>("TotalSuccess")
                        .HasColumnType("int");

                    b.Property<byte>("UpdateCount")
                        .HasColumnType("tinyint");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CampaignCSKHs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.OmniTool.CampaignConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CampaignId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TagAttribute")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TagContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TemplateVariable")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VariableContent")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CampaignConfigs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.OmniTool.CampaignPhoneCSKH", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("AccountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CampaignCSKHId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChannelCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("ChannelID")
                        .HasColumnType("smallint");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("DelayTime")
                        .HasColumnType("int");

                    b.Property<short>("Duration")
                        .HasColumnType("smallint");

                    b.Property<string>("ErrorCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ExtraDuration")
                        .HasColumnType("int");

                    b.Property<string>("IdOmniMess")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("IndexCrDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("IndexProcessTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsCharged")
                        .HasColumnType("bit");

                    b.Property<short>("MtCount")
                        .HasColumnType("smallint");

                    b.Property<string>("ParamContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("ProcessTime")
                        .HasColumnType("datetime2");

                    b.Property<short>("ReportTimes")
                        .HasColumnType("smallint");

                    b.Property<DateTime?>("ReportedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("RoutingRule")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("Status")
                        .HasColumnType("smallint");

                    b.Property<byte>("TelcoID")
                        .HasColumnType("tinyint");

                    b.Property<string>("TemplateCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("UpdateCount")
                        .HasColumnType("smallint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CampaignPhoneCSKHs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.OmniTool.CampaignPhoneCSKHTemp", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("AccountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CampaignCSHKId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ParamContent")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RequestID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoutingRule")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<short>("Status")
                        .HasColumnType("smallint");

                    b.Property<byte>("TelcoID")
                        .HasColumnType("tinyint");

                    b.Property<string>("TemplateCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CampaignPhoneCSKHTemps");
                });

            modelBuilder.Entity("MiniAppCore.Entities.OmniTool.CampaignTag", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CampaignId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("TagID")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CampaignTags");
                });

            modelBuilder.Entity("MiniAppCore.Entities.OmniTool.WebHookLogs", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Channel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("IdOmniMess")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MtCount")
                        .HasColumnType("int");

                    b.Property<string>("Response")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("TelcoId")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("WebHookLogs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Orders.InvoiceTemplate", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("InvoiceTemplates");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Orders.Order", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("BookingId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("BranchId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CancelReason")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeliveryAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("DeliveryStatus")
                        .HasColumnType("int");

                    b.Property<decimal>("DiscountAmount")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("OrderStatus")
                        .HasColumnType("int");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("int");

                    b.Property<long>("PointUsage")
                        .HasColumnType("bigint");

                    b.Property<decimal>("ShippingFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("Total")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VoucherCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ZaloOrderId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Orders");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Orders.OrderDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("DiscountPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<bool>("IsUseForCommission")
                        .HasColumnType("bit");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("OriginalPrice")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Quantity")
                        .HasColumnType("bigint");

                    b.Property<string>("RefCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VariantId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("OrderDetails");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Orders.OrderDetailGift", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OrderDetailId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Quantity")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("OrderDetailGifts");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Orders.OrderDetailReview", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Images")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OrderDetailId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProductId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ReviewContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float>("ReviewPoint")
                        .HasColumnType("real");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Videos")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("OrderDetailReviews");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Orders.OrderVoucher", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("OrderId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VoucherCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VoucherId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VoucherName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("OrderVouchers");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Orders.ShippingFeeConfig", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("MaxOrderValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("MinOrderValue")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ShippingFee")
                        .HasColumnType("decimal(18,2)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ShippingFeeConfigs");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Products.Product", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<int>("BoughtCount")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Images")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsGift")
                        .HasColumnType("bit");

                    b.Property<bool>("IsGlobalProduct")
                        .HasColumnType("bit");

                    b.Property<int>("LikeCount")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<int>("ReviewCount")
                        .HasColumnType("int");

                    b.Property<float>("ReviewPoint")
                        .HasColumnType("real");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("ViewCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Products");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Products.ProductInfo.ProductCategory", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("CategoryId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ProductCategories");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Products.ProductStock", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("BranchId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Quantity")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("ProductStocks");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Products.Variants.Property", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsMultipleChoice")
                        .HasColumnType("bit");

                    b.Property<int>("MaxSelection")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Properties");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Products.Variants.PropertyValue", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PropertyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("PropertyValues");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Products.Variants.Variant", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DiscountId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("ProductId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PromotionId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Quantity")
                        .HasColumnType("bigint");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Variants");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Products.Variants.VariantValue", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("PropertyId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyValueId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("VariantId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("VariantValues");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Settings.Rank", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<float>("ConvertRate")
                        .HasColumnType("real");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Image")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("RankingPoint")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Ranks");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Settings.RecommendSetting", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Criteria")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DefaultData")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsEnable")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("ResultQuantity")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("RecommendSettings");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Settings.RewardPoint", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<decimal>("CommissionPercentage")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal>("ConversionRatio")
                        .HasPrecision(18, 5)
                        .HasColumnType("decimal(18,5)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("MaxPointPerTxn")
                        .HasColumnType("int");

                    b.Property<int>("MemberShipRankId")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("RewardPoints");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Surveys.Survey", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<short>("DisplayOrder")
                        .HasColumnType("smallint");

                    b.Property<DateTime>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDisplay")
                        .HasColumnType("bit");

                    b.Property<DateTime>("StartedDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Surveys");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Surveys.SurveyAnswer", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<short>("DisplayOrder")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsInput")
                        .HasColumnType("bit");

                    b.Property<string>("Key")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("QuestionId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SurveyAnswers");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Surveys.SurveyLikertQuestion", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<short>("DisplayOrder")
                        .HasColumnType("smallint");

                    b.Property<short>("OptionCount")
                        .HasColumnType("smallint");

                    b.Property<string>("QuestionId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("QuestionLikertTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("SurveyLikertQuestions");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Surveys.SurveyQuestion", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<short>("DisplayOrder")
                        .HasColumnType("smallint");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("bit");

                    b.Property<string>("QuestionTitle")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SectionId")
                        .IsRequired()
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("SurveyQuestions");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Surveys.SurveySection", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<byte>("DisplayOrder")
                        .HasColumnType("tinyint");

                    b.Property<string>("SurveyId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("TitleSection")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("SurveyId");

                    b.ToTable("SurveySections");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Surveys.SurveySubmission", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("SurveyId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserZaloId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("SurveySubmissions");
                });

            modelBuilder.Entity("MiniAppCore.Entities.Surveys.SurveySubmissionDetail", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)");

                    b.Property<string>("AnswerId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("InputValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LikertId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("LikertQuestionId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<string>("QuestionId")
                        .HasMaxLength(36)
                        .HasColumnType("nvarchar(36)");

                    b.Property<DateTime>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("SurveySubmissionDetails");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("MiniAppCore.Entities.Commons.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("MiniAppCore.Entities.Commons.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("MiniAppCore.Entities.Commons.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("MiniAppCore.Entities.Commons.ApplicationUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });
#pragma warning restore 612, 618
        }
    }
}
