﻿@model IEnumerable<MiniAppCore.Models.MappingParams>
<div class="table-responsive">
    <table id="table-params" class="table table-hover table-striped">
        <thead class="bg-light">
            <tr>
                <th style="width: 15%"><PERSON><PERSON> số</th>
                <th class="d-none" style="width: 25%">Nguồn dữ liệu</th>
                <th style="width: 30%">Trường dữ liệu</th>
                <th style="width: 30%">Giá trị mặc định</th>
            </tr>
        </thead>
        <tbody>
            @if (Model.Any())
            {
                foreach (var item in Model)
                {
                    <tr>
                        <td data-param="@item.ParamName">
                            <span class="badge bg-primary">@item.ParamName</span>
                        </td>
                        <td class="d-none">
                            <select class="form-select data-source-select" data-table="@item.ParamName" aria-label="Chọn nguồn dữ liệu">
                                <option value="" disabled selected>-- Chọn nguồn dữ liệu --</option>
                            </select>
                        </td>
                        <td>
                            <input class="form-control field-input"
                                   name="<EMAIL>"
                                   data-param="@item.ParamName"
                                   data-source="@item.MappingTableName"
                                   list="<EMAIL>"
                                   value="@item.MappingColumnName"
                                   placeholder="Nhập hoặc chọn trường dữ liệu..." />

                            <datalist id="<EMAIL>">
                                @*TO DO: Danh sách data có thể mapping*@
                            </datalist>
                        </td>
                        <td>
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Giá trị mặc định (tùy chọn)" value="@item.DefaultValue" data-param="@item.ParamName" />
                            </div>
                        </td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td colspan="4" class="text-center py-4">
                        <div class="alert alert-info mb-0">
                            <i class="ri-information-line me-2"></i> Không có tham số nào được tìm thấy cho template này
                        </div>
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>

<script>
    function validateParamMappings() {
        let isValid = true;

        $('.data-source-select').each(function () {
            const dataSource = $(this).val();
            const row = $(this).closest('tr');
            const fieldInput = row.find('.field-input');
            const fieldValue = fieldInput.val();

            if (dataSource && !fieldValue) {
                isValid = false;
                fieldInput.addClass('is-invalid');
            } else {
                fieldInput.removeClass('is-invalid');
            }
        });

        return isValid;
    }
</script>
