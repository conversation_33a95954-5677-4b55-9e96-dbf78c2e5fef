﻿using MiniAppCore.Enums;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Memberships
{
    public class WithDrawRequest
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString("N").ToString().ToUpper();
        public required decimal Amount { get; set; }
        public required string UserZaloId { get; set; }
        public required string CusPhoneNumber { get; set; }
        public required string CusName { get; set; }
        public required string AccountDetail { get; set; }
        public short RequestStatus { get; set; } = (short)Status.WITHDRAW_WAITING;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
