﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class SeedBaseSettingData : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAELxMgkgVaFTMIgUEnjdibXyCAH2R7bHIeLB+lVLeG7POV+exosuexMvnV2r9Q4BHRQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "eed9801da536467d9e6949c2a0f4fd2c", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 14, 17, 29, 10, 825, DateTimeKind.Local).AddTicks(823), "FeaturesButton", new DateTime(2025, 4, 14, 17, 29, 10, 826, DateTimeKind.Local).AddTicks(274) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "eed9801da536467d9e6949c2a0f4fd2c");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAENZN4Yq8N0GfK4wYASGk7eDniL/17X9ZcZQpiClLCZJYiMMq2ZTiCdS+DeIC2jkk8w==");
        }
    }
}
