﻿namespace MiniAppCore.Models.DTOs.Employees
{
    public class EmployeeDTO
    {
        public required string Password { get; set; }
        public required string DisplayName { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumber { get; set; }
        public required string RoleName { get; set; }
        public bool IsChangePassword { get; set; }

        public List<string> BranchIds { get; set; } = new List<string>();
        public List<string> RoleIds { get; set; } = new List<string>();
    }
}
