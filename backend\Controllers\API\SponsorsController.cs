﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Events.Sponsors;
using MiniAppCore.Models.Responses.Surveys;
using MiniAppCore.Services.Events.Sponsors;

namespace MiniAppCore.Controllers.API
{
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [Route("api/[controller]")]
    [ApiController]
    public class SponsorsController(ILogger<SponsorsController> logger, ISponsorService tierService) : ControllerBase
    {
        [AllowAnonymous]
        [HttpGet]
        public async Task<IActionResult> GetAll([FromQuery] RequestQuery query, [FromQuery] short activeStatus)
        {
            try
            {
                tierService.IsAdmin = User?.IsInRole("ADMIN") ?? false;

                var result = await tierService.GetPaged(query, activeStatus);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            try
            {
                tierService.IsAdmin = User?.IsInRole("ADMIN") ?? false;

                var data = await tierService.GetById(id);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        #region ADMIN FUNCTIONS

        [HttpPost]
        public async Task<IActionResult> Create([FromForm] SponsorDTO request)
        {
            try
            {
                await tierService.CreateSponsorAsync(request);

                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo nhà tài trợ thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Create(string id, [FromForm] SponsorDTO request)
        {
            try
            {
                await tierService.UpdateSponsorAsync(id, request);

                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật nhà tài trợ thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                await tierService.DeleteSponsorAsync(id);

                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa nhà tài trợ thành công!",
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        #endregion
    }

}
