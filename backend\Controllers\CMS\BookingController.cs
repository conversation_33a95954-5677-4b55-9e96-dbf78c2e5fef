﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Services.Bookings;
using MiniAppCore.Services.Branches;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class BookingController(IBookingService bookingService, IBranchService branchService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("Booking/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            var bookingDetail = await bookingService.GetBookingDetailResponseAsync(id);
            ViewBag.Branches = await branchService.GetAllAsync();
            ViewBag.Title = "Chi tiết đặt lịch";
            ViewBag.Button = "Cập nhật";
            return PartialView("_Booking", bookingDetail);
        }
    }
}
