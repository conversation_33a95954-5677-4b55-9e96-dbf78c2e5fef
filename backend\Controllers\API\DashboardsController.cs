﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Services.Dashboards;

namespace MiniAppCore.Controllers.API
{
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    [ApiController()]
    [Route("api/[controller]")]
    public class DashboardsController : ControllerBase
    {
        private readonly ILogger<DashboardsController> _logger;
        private readonly IDashboardService _dashboardService;

        public DashboardsController(ILogger<DashboardsController> logger, IDashboardService dashboardService)
        {
            _logger = logger;
            _dashboardService = dashboardService;
        }

        #region Old Statistics API (Deprecated)

        /// <summary>
        /// Lấy số lượng đơn hàng và doanh thu trong khoảng thời gian
        /// </summary>
        [HttpGet("orders-revenue")]
        public async Task<IActionResult> GetOrdersAndRevenue([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.GetOrdersAndRevenue(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting orders and revenue");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy số lượng đơn đặt lịch trong khoảng thời gian
        /// </summary>
        [HttpGet("bookings-count")]
        public async Task<IActionResult> GetBookingsCount([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.GetBookingsCount(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting bookings count");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy số lượng khách hàng mới trong khoảng thời gian
        /// </summary>
        [HttpGet("new-customers")]
        public async Task<IActionResult> GetNewCustomersCount([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.GetNewCustomersCount(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting new customers count");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy thông tin số lần đặt của từng dịch vụ trong khoảng thời gian
        /// </summary>
        [HttpGet("service-bookings")]
        public async Task<IActionResult> GetServiceBookingStats([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.GetServiceBookingStats(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting service booking statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy thông tin số lượng và doanh thu của từng sản phẩm trong khoảng thời gian
        /// </summary>
        [HttpGet("product-sales")]
        public async Task<IActionResult> GetProductSalesStats([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.GetProductSalesStats(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product sales statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy biểu đồ doanh thu theo ngày trong khoảng thời gian
        /// </summary>
        [HttpGet("daily-revenue-chart")]
        public async Task<IActionResult> GetDailyRevenueChart([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.GetDailyRevenueChart(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily revenue chart");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy biểu đồ số lượng đơn hàng theo ngày trong khoảng thời gian
        /// </summary>
        [HttpGet("daily-orders-chart")]
        public async Task<IActionResult> GetDailyOrdersChart([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.GetDailyOrdersChart(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily orders chart");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy biểu đồ số lượng đặt lịch theo ngày trong khoảng thời gian
        /// </summary>
        [HttpGet("daily-bookings-chart")]
        public async Task<IActionResult> GetDailyBookingsChart([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.GetDailyBookingsChart(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily bookings chart");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy tổng số đơn hàng trong khoảng thời gian
        /// </summary>
        [HttpGet("total-orders")]
        public async Task<IActionResult> GetTotalOrders([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.TotalOrdersInRange(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total orders");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy tổng doanh thu trong khoảng thời gian
        /// </summary>
        [HttpGet("total-revenue")]
        public async Task<IActionResult> GetTotalRevenue([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.TotalRevenueInRange(startDate, endDate, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total revenue");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy dữ liệu biểu đồ doanh thu trong khoảng thời gian
        /// </summary>
        [HttpGet("chart-revenue")]
        public async Task<IActionResult> GetChartRevenue([FromQuery] DateTime startDate, [FromQuery] DateTime endDate, [FromQuery] string type = "daily", [FromQuery] string branchId = null)
        {
            try
            {
                var result = await _dashboardService.ChartRevenueInRange(startDate, endDate, type, branchId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting chart revenue");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy dữ liệu thống kê tổng quan cho dashboard
        /// </summary>
        [HttpGet("summary")]
        public async Task<IActionResult> GetDashboardSummary([FromQuery] DateTime date)
        {
            try
            {
                // Lấy dữ liệu cho ngày hiện tại
                var startDate = date.Date;
                var endDate = startDate.AddDays(1).AddSeconds(-1);

                var ordersRevenue = await _dashboardService.GetOrdersAndRevenue(startDate, endDate);
                var bookingsCount = await _dashboardService.GetBookingsCount(startDate, endDate);
                var newCustomersCount = await _dashboardService.GetNewCustomersCount(startDate, endDate);

                return Ok(new
                {
                    OrdersCount = ordersRevenue.OrderCount,
                    Revenue = ordersRevenue.Revenue,
                    BookingsCount = bookingsCount,
                    NewCustomersCount = newCustomersCount
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard summary");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy dữ liệu thống kê chi tiết cho ngày được chọn
        /// </summary>
        [HttpGet("daily-stats")]
        public async Task<IActionResult> GetDailyStats([FromQuery] DateTime date)
        {
            try
            {
                var startDate = date.Date;
                var endDate = startDate.AddDays(1).AddSeconds(-1);

                var ordersRevenue = await _dashboardService.GetOrdersAndRevenue(startDate, endDate);
                var bookingsCount = await _dashboardService.GetBookingsCount(startDate, endDate);
                var newCustomersCount = await _dashboardService.GetNewCustomersCount(startDate, endDate);
                var serviceBookings = await _dashboardService.GetServiceBookingStats(startDate, endDate);
                var productSales = await _dashboardService.GetProductSalesStats(startDate, endDate);

                return Ok(new
                {
                    Date = date.ToString("dd/MM/yyyy"),
                    OrdersCount = ordersRevenue.OrderCount,
                    Revenue = ordersRevenue.Revenue,
                    BookingsCount = bookingsCount,
                    NewCustomersCount = newCustomersCount,
                    ServiceBookings = serviceBookings,
                    ProductSales = productSales
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting daily statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        #endregion

        #region New Statistics API

        /// <summary>
        /// Lấy thống kê dashboard tổng hợp theo khoảng thời gian
        /// </summary>
        [HttpGet("stats/summary")]
        public async Task<IActionResult> GetDashboardSummaryStats([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate)
        {
            try
            {
                var result = await _dashboardService.GetDashboardSummaryAsync(startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard summary statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy thống kê đơn booking theo khoảng thời gian
        /// </summary>
        [HttpGet("stats/bookings")]
        public async Task<IActionResult> GetBookingStats([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate)
        {
            try
            {
                var result = await _dashboardService.GetBookingStatisticsAsync(startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting booking statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy thống kê đơn booking theo trạng thái và khoảng thời gian
        /// </summary>
        [HttpGet("stats/bookings/status")]
        public async Task<IActionResult> GetBookingStatsByStatus([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate, [FromQuery] string status)
        {
            try
            {
                var result = await _dashboardService.GetBookingStatisticsByStatusAsync(startDate, endDate, status);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting booking statistics by status");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy thống kê membership theo khoảng thời gian
        /// </summary>
        [HttpGet("stats/memberships")]
        public async Task<IActionResult> GetMembershipStats([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate)
        {
            try
            {
                var result = await _dashboardService.GetMembershipStatisticsAsync(startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting membership statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy thống kê đơn hàng theo khoảng thời gian
        /// </summary>
        [HttpGet("stats/orders")]
        public async Task<IActionResult> GetOrderStats([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate)
        {
            try
            {
                var result = await _dashboardService.GetOrderStatisticsAsync(startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy thống kê đơn hàng theo trạng thái và khoảng thời gian
        /// </summary>
        [HttpGet("stats/orders/status")]
        public async Task<IActionResult> GetOrderStatsByStatus([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate, [FromQuery] string status)
        {
            try
            {
                var result = await _dashboardService.GetOrderStatisticsByStatusAsync(startDate, endDate, status);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting order statistics by status");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy tổng doanh thu từ thống kê theo khoảng thời gian
        /// </summary>
        [HttpGet("stats/total-revenue")]
        public async Task<IActionResult> GetTotalRevenueFromStats([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate)
        {
            try
            {
                var result = await _dashboardService.GetTotalRevenueFromStatisticsAsync(startDate, endDate);
                return Ok(new { TotalRevenue = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total revenue from statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy top sản phẩm bán chạy theo khoảng thời gian
        /// </summary>
        [HttpGet("stats/top-products")]
        public async Task<IActionResult> GetTopProductsStats([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate, [FromQuery] int limit = 5)
        {
            try
            {
                var result = await _dashboardService.GetTopProductsStatisticsAsync(startDate, endDate, limit);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top products statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy top booking items bán chạy theo khoảng thời gian
        /// </summary>
        [HttpGet("stats/top-booking-items")]
        public async Task<IActionResult> GetTopBookingItemsStats([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate, [FromQuery] int limit = 5)
        {
            try
            {
                var result = await _dashboardService.GetTopBookingItemsStatisticsAsync(startDate, endDate, limit);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top booking items statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Lấy thống kê voucher theo khoảng thời gian
        /// </summary>
        [HttpGet("stats/vouchers")]
        public async Task<IActionResult> GetVoucherStats([FromQuery] DateOnly startDate, [FromQuery] DateOnly endDate)
        {
            try
            {
                var result = await _dashboardService.GetVoucherStatisticsAsync(startDate, endDate);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting voucher statistics");
                return StatusCode(500, "Internal server error");
            }
        }

        #endregion
    }
}
