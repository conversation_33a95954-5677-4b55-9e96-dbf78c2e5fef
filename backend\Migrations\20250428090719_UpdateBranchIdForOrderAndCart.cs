﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateBranchIdForOrderAndCart : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "cb34d4acb3204360bbc74348a7a7cf8b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "054dce5b09434525827fbe2f9420a7ba");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "09f0951e93154299b476919079f7b22d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "1419ddc1f8054473ae97a9c8c29c7ffe");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "290254bccc3d4e56b2234841e64b2674");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "297609da074a45bb8cd3968f300e4322");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2ce6dcdfe7724536b11e6e817f7b3c50");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "345a9db27e8a4e399242682aebf7f813");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "411e0d12b6414191b88fdfe5f6e22a6d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "46fc240b4316437d9d41788cfc8d4bab");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4fef5958c0344a2da2ed90d2fd3f9c14");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "51cf85cab43b4985ab3005de14f957de");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "526e84e3e8c14efc9a1497821205af77");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "52acda108c2b4fd99057630f74aff450");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5989602aa7824e1a9cbd7fd8b29a0b13");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "5b4c31399d9a46f283748dd375bd0a1b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "60a911dbcfcc4fa99eb5f4fbc0cb4368");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7932b0e58e1e4a06ad5fbf1a05bc6ad3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "825674952682438da2ceae5029bd47e3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "9629fd142d95499897e2564098741eaa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "983d41643d054e779487e9980428db9f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "abda13dc06aa456d9aedfb03d24e3898");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ad211b915cb14054833dcd1223e77e6e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "adda35c5a1c64fc78936e0f61b19eb0b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "af8de9c520524a0482b697f3562db579");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c1662c365e374a659a41c1e94a1d759a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c25823b327d0480a9e57a16862c157d9");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c90355425ca042269b2dcad14b8143dc");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d074441882ae42d58f34d4e65d0c456b");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dbfe84e07df74e62a2df43e641482bd0");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dd883a71c00846c3ad82a167c5c5872f");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "df0326c805344153a375f5a76ed2c8aa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e5ddaebc2d5d424ba611f2b8b2532c79");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fce7b7a0e8204b218eef5588d4d34ea4");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fd29d91bc5ae4255b317d9fbf968b7e8");

            migrationBuilder.AddColumn<string>(
                name: "BranchId",
                table: "Orders",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BranchId",
                table: "Carts",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEFnD0yM5e66kPysRwAlpjx/ZHAIShPCRw96+LpNa9vxlo5AqclqDRQ3mfQL9YArwNw==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "61d24dfb7f7744e6b8549fab15b21fb5", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 28, 16, 7, 18, 66, DateTimeKind.Local).AddTicks(9154), "FeaturesButton", new DateTime(2025, 4, 28, 16, 7, 18, 66, DateTimeKind.Local).AddTicks(9157) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "0cca2b4a3fce47d6afa05c7675a103cf", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9952), true, "CustomForm", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9952), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2907b32a65304330a0bbc1196c260c2e", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9941), true, "GeneralSetting", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9941), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "29b5c200b76640bba81e76c6045c78d2", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9923), true, "GameList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9923), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "325c7f3117d048139f407e658e9cca45", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9918), true, "History", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9919), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "38b4b2bbe61d49178064b4bfc6dceab3", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9921), true, "SurveyList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9921), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "3b5510a94fd249718c4a281c2920bce2", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9883), true, "Brand", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9884), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "3f36583eba0847939435881ad90bf5df", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9896), true, "BookingList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9896), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "47f80fb69bcf468dbf9043d0a9fc0506", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9914), true, "Rank", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9914), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "48600721a1ff4f19859cfdc06a072129", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9929), true, "CampaignList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9929), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "4a53bbc292a14c96b510e37277fc6626", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9907), true, "VoucherList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9907), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "507fe437720647f493a01d45f7e02f85", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9869), true, "ArticleCategory", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9870), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "54ca1114c4024dd0bfdf399fe7e95a8a", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9933), true, "CampaignHistory", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9933), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "66e702f285314cf386ce3822b01edfe5", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(4916), true, "Overview", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9092), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "702521c8146140a19b26ccc6f09f3852", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9893), true, "BookingItem", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9894), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "74f24a5675564e759072073b1c6b6852", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9935), true, "EventTemplateList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9935), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "7cb4295f7f44427fa592645ffe641937", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9948), true, "ShippingFeeConfig", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9948), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "99abbfcea5d1433bbd2f5300ec614006", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9814), true, "AffiliateList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9815), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "a7cd3d664d9747369aae3981f3a92b32", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9888), true, "ProductList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9888), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "a8646f84fd7a40259db6f9c99cf62372", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9939), true, "TemplateUidList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9939), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "aba359bb53d840b19ae19106dbed45ea", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9945), true, "MembershipExtendDefaults", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9946), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "ad37da3de60f4596a4b58e3c5abeebb7", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9927), true, "GamePrize", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9927), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "b217b5a5c3394bb0a8989c357aa0e312", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9937), true, "EventTemplateHistory", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9937), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "bdd7d66dcfb64ac99a07db063158ae9e", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9898), true, "OrderList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9898), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "bf2e331792154a36a9f38388a8cd1702", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9925), true, "History", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9925), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "c0a35ef04243413dbc63a3432a4d99fd", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9905), true, "Promotion", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9905), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "c21819588a2049e09818da2c7d5529ee", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9809), true, "BranchList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9811), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "c21b88ad05b34a01995f3f04be65b7cf", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9900), true, "InvoiceTemplate", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9901), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "ce9e432031e94604905d2b3c9a60733e", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9943), true, "EnableFeatures", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9944), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "d8a8dcf3d8f44242916604a96fe8154c", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9903), true, "DiscountList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9903), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "dc38bfbbd7fd4e4b8e22e318cc89f043", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9916), true, "Tag", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9917), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "e217d285d9ec46a9a1a5512148b0a6fa", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9909), true, "MembershipList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9910), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "e9f6faa165664d26845166072371d38a", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9885), true, "ProductProperty", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9886), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "ed965853cd3d47b9b015b0915af2dd3d", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9872), true, "ArticleList", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9872), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "fa7cbd3db1b74e4ea41133b4d5f5f543", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9881), true, "Category", new DateTime(2025, 4, 28, 16, 7, 18, 65, DateTimeKind.Local).AddTicks(9881), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "61d24dfb7f7744e6b8549fab15b21fb5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "0cca2b4a3fce47d6afa05c7675a103cf");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "2907b32a65304330a0bbc1196c260c2e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "29b5c200b76640bba81e76c6045c78d2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "325c7f3117d048139f407e658e9cca45");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "38b4b2bbe61d49178064b4bfc6dceab3");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3b5510a94fd249718c4a281c2920bce2");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "3f36583eba0847939435881ad90bf5df");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "47f80fb69bcf468dbf9043d0a9fc0506");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "48600721a1ff4f19859cfdc06a072129");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "4a53bbc292a14c96b510e37277fc6626");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "507fe437720647f493a01d45f7e02f85");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "54ca1114c4024dd0bfdf399fe7e95a8a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "66e702f285314cf386ce3822b01edfe5");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "702521c8146140a19b26ccc6f09f3852");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "74f24a5675564e759072073b1c6b6852");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "7cb4295f7f44427fa592645ffe641937");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "99abbfcea5d1433bbd2f5300ec614006");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a7cd3d664d9747369aae3981f3a92b32");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "a8646f84fd7a40259db6f9c99cf62372");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "aba359bb53d840b19ae19106dbed45ea");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ad37da3de60f4596a4b58e3c5abeebb7");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "b217b5a5c3394bb0a8989c357aa0e312");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bdd7d66dcfb64ac99a07db063158ae9e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "bf2e331792154a36a9f38388a8cd1702");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c0a35ef04243413dbc63a3432a4d99fd");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c21819588a2049e09818da2c7d5529ee");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "c21b88ad05b34a01995f3f04be65b7cf");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ce9e432031e94604905d2b3c9a60733e");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "d8a8dcf3d8f44242916604a96fe8154c");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "dc38bfbbd7fd4e4b8e22e318cc89f043");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e217d285d9ec46a9a1a5512148b0a6fa");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "e9f6faa165664d26845166072371d38a");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "ed965853cd3d47b9b015b0915af2dd3d");

            migrationBuilder.DeleteData(
                table: "ViewPermissions",
                keyColumn: "Id",
                keyValue: "fa7cbd3db1b74e4ea41133b4d5f5f543");

            migrationBuilder.DropColumn(
                name: "BranchId",
                table: "Orders");

            migrationBuilder.DropColumn(
                name: "BranchId",
                table: "Carts");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOyIbIPUQi4ofLCpzPNJnheUu5Krd/V6o5Wu9bMPyzOaa8CvyS7OkDtxTXD0xT3qTQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "cb34d4acb3204360bbc74348a7a7cf8b", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 25, 11, 56, 37, 767, DateTimeKind.Local).AddTicks(5891), "FeaturesButton", new DateTime(2025, 4, 25, 11, 56, 37, 767, DateTimeKind.Local).AddTicks(5894) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "054dce5b09434525827fbe2f9420a7ba", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5300), true, "Rank", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5300), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "09f0951e93154299b476919079f7b22d", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5340), true, "MembershipExtendDefaults", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5341), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "1419ddc1f8054473ae97a9c8c29c7ffe", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5292), true, "Promotion", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5292), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "290254bccc3d4e56b2234841e64b2674", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5294), true, "VoucherList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5294), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "297609da074a45bb8cd3968f300e4322", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5342), true, "ShippingFeeConfig", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5343), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "2ce6dcdfe7724536b11e6e817f7b3c50", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5209), true, "Category", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5209), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "345a9db27e8a4e399242682aebf7f813", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5296), true, "MembershipList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5297), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "411e0d12b6414191b88fdfe5f6e22a6d", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5325), true, "EventTemplateHistory", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5325), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "46fc240b4316437d9d41788cfc8d4bab", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5229), true, "InvoiceTemplate", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5230), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "4fef5958c0344a2da2ed90d2fd3f9c14", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5314), true, "History", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5314), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "51cf85cab43b4985ab3005de14f957de", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5327), true, "TemplateUidList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5327), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "526e84e3e8c14efc9a1497821205af77", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5332), true, "EnableFeatures", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5333), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "52acda108c2b4fd99057630f74aff450", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5318), true, "CampaignList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5318), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "5989602aa7824e1a9cbd7fd8b29a0b13", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5304), true, "History", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5304), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "5b4c31399d9a46f283748dd375bd0a1b", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5227), true, "OrderList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5228), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "60a911dbcfcc4fa99eb5f4fbc0cb4368", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5196), true, "AffiliateList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5196), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "7932b0e58e1e4a06ad5fbf1a05bc6ad3", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5302), true, "Tag", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5302), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "825674952682438da2ceae5029bd47e3", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5346), true, "CustomForm", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5346), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "9629fd142d95499897e2564098741eaa", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5199), true, "ArticleCategory", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5199), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "983d41643d054e779487e9980428db9f", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5306), true, "SurveyList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5307), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Survey" },
                    { "abda13dc06aa456d9aedfb03d24e3898", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5316), true, "GamePrize", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5316), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "ad211b915cb14054833dcd1223e77e6e", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(187), true, "Overview", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(4491), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "adda35c5a1c64fc78936e0f61b19eb0b", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5223), true, "BookingItem", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5224), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "af8de9c520524a0482b697f3562db579", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5225), true, "BookingList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5226), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "c1662c365e374a659a41c1e94a1d759a", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5218), true, "ProductProperty", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5218), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "c25823b327d0480a9e57a16862c157d9", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5308), true, "GameList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5309), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "c90355425ca042269b2dcad14b8143dc", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5201), true, "ArticleList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5201), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "d074441882ae42d58f34d4e65d0c456b", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5216), true, "Brand", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5216), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "dbfe84e07df74e62a2df43e641482bd0", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5321), true, "CampaignHistory", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5321), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "dd883a71c00846c3ad82a167c5c5872f", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5329), true, "GeneralSetting", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5329), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "df0326c805344153a375f5a76ed2c8aa", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5323), true, "EventTemplateList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5323), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "e5ddaebc2d5d424ba611f2b8b2532c79", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5220), true, "ProductList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5220), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "fce7b7a0e8204b218eef5588d4d34ea4", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5287), true, "DiscountList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5287), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "fd29d91bc5ae4255b317d9fbf968b7e8", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5191), true, "BranchList", new DateTime(2025, 4, 25, 11, 56, 37, 766, DateTimeKind.Local).AddTicks(5192), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" }
                });
        }
    }
}
