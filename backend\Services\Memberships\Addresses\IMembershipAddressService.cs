﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Requests.Memberships;

namespace MiniAppCore.Services.Memberships.Addresses
{
    public interface IMembershipAddressService : IService<MembershipAddress>
    {
        Task<int> UpdateAsync(string id, MembershipAddressRequest entity);
        Task<int> CreateAsync(string userZaloId, MembershipAddressRequest entity);

        Task<MembershipAddress?> GetDefaultAddress(string userZaloId);
        Task<PagedResult<MembershipAddress>> GetAddressesByUserZaloId(string userUserZaloId, RequestQuery query);
    }
}