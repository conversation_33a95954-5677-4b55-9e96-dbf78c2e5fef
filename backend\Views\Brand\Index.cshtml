﻿<style>
    .spinner {
        z-index: 0 !important;
    }
</style>

<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> sách thương hiệu</h4>
                <p class="mb-0">
                    Sử dụng danh sách thương hiệu để mô tả nguồn gốc, xu<PERSON>t xứ của sản phẩm
                </p>
            </div>
            <button onclick="GetFormBrand('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-brand">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center mb-4">
            <div class="col-3">
                <input id="search" type="text" class="form-control" placeholder="Tìm kiếm" />
                <button class="btn btn-search">
                    <i class="ri-search-line"></i>
                </button>
            </div>
            @* <div class="col-2">
                <select id="filter-type" class="selectpicker form-control">
                    <option value="" selected>Tất cả</option>
                    <option value="dichvu">Tư vấn sản phẩm</option>
                    <option value="sanpham">Sản phẩm</option>
                </select>
            </div> *@
        </div>
    </div>
    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0; z-index: -1;"></div>
        <div class="table-responsive rounded mb-3">
            <table id="list-brands" class="data-table table mb-0 tbl-server-info"> </table>
        </div>
    </div>
</div>

<div id="modal-brand" class="modal fade" tabindex="-1" aria-modal="true" role="dialog">
    <div id="modal-content" class="modal-dialog modal-xl"></div>
</div>

@section Scripts {
    <script>
        let removedOldImages = [];
        let newImages = [];
        let currentImages = 0;

        $(document).ready(function () {
            GetListBrand();

            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $('#search').on('input', search);
        });

        function ClearForm() {
            $("form").trigger("reset");
            ShowPreview();
        }

        function ShowPreview(event) {
            if (!event) return;

            const files = event.target.files;

            // Kiểm tra tất cả file phải là ảnh
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (!file.type.startsWith("image/")) {
                    AlertResponse("Chỉ được upload ảnh!", "warning");
                    event.target.value = ""; // Reset input
                    return;
                }
            }

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.readAsDataURL(file);
                    reader.onload = (e) => {
                        const previewItem = $(`
                                            <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height: 170px;">
                                                        <img src="${e.target.result}" class="card-img-top h-100" alt="Hình ảnh" style="object-fit:contain" />
                                                        <span class="btn-preview-remove">x</span>
                                                    </div>`);

                        previewItem.find('.btn-preview-remove').click(function () {
                            $(this).parent().remove();
                            const currentFiles = Array.from(event.target.files);
                            const newFiles = currentFiles.filter(f => f !== file);
                        });

                        newImages.push(file);
                        $("#preview").append(previewItem);
                    };
                }
            }
        }

        $(document).on("click", ".btn-preview-remove", function () {
            const fullImageUrl = $(this).data("url");
            const imageUrl = fullImageUrl.split("/").pop();

            $(this).parent().remove();

            if (!removedOldImages.includes(imageUrl)) {
                removedOldImages.push(imageUrl);
            }
        });

        function GetListBrand() {
            table = new DataTable("#list-brands", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    // const type = $("#filter-type").val();
                    const keyword = $("#search").val();

                    $.ajax({
                        url: '@Url.Action("GetAll", "brands")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            sort: data.order[0]?.dir || 'asc',
                            keyword: keyword,
                            type: "sanpham"
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: `<div class="d-flex align-items-center">
                                                            <img src="${item.images ? item.images[0] : "/images/product/01.png"}" class="img-fluid rounded avatar-50 mr-3" alt="image" />
                                                            <div>
                                                                ${item.name}
                                                            </div>
                                                        </div>`,
                                2: item.description,
                                3: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                                            <a onclick="GetFormBrand('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                                                <i class="ri-edit-line fs-6 mr-0"></i>
                                                                            </a>
                                                                            <a onclick="DeleteBrand('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                                                <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                                            </a>
                                                                        </div>`
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400)
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tên", data: 1 },
                    { title: "Mô tả", data: 2 },
                    { title: "Thao tác", data: 3 },
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetFormBrand(id) {
            const url = id ? `@Url.Action("Detail", "Brand")/${id}` : "@Url.Action("Create", "Brand")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-brand").modal("toggle");

                    $("#type").select2();

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function DeleteBrand(id) {
            const url = `/api/brands/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const formData = new FormData();

            formData.append("name", $("#name").val()?.trim());
            formData.append("description", $("#descr").val()?.trim());

            const files = $('#pics')[0].files;

            console.log(currentImages);
            console.log(files.length);
            console.log(removedOldImages.length);

            if (removedOldImages.length === currentImages) {
                if (files.length === 0) {
                    $.alert({
                        title: 'Thông báo',
                        content: 'Bạn cần ít nhất một hình ảnh!',
                        buttons: {
                            ok: {
                                text: 'Đóng',
                                btnClass: 'btn-primary'
                            }
                        }
                    });
                    return;
                }
            }

            if (id === '0') {

                if (files.length == 0) {
                    $.alert({
                        title: 'Thông báo',
                        content: 'Bạn vui lòng tải hình ảnh lên!',
                        buttons: {
                            ok: {
                                text: 'Đóng',
                                btnClass: 'btn-primary'
                            }
                        }
                    });
                    return;
                }

            }

            for (let i = 0; i < files.length; i++) {
                formData.append('files', files[i]);
            }

            if (removedOldImages.length > 0) {
                for (const removedImage of removedOldImages) {
                    formData.append('removedOldImages', removedImage);
                }
            }

            const url = id !== '0' ? `/api/brands/${id}` : '/api/brands';
            const method = id !== '0' ? 'PUT' : 'POST'

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        removedOldImages = [];
                        newImages = [];
                        currentImages = 0;
                        AlertResponse(response.message, 'success')
                        table.ajax.reload(null, false);
                    }
                    $("#modal-brand").modal("toggle");
                },
                error: function (err) {
                    AlertResponse('Thêm mới danh mục thất bại!', 'error')
                }
            });
        }
    </script>
}
