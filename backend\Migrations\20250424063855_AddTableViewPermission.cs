﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddTableViewPermission : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "caea4945a94a479385f7fd6441d10ea8");

            migrationBuilder.CreateTable(
                name: "ViewPermissions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    ViewId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    SubViewId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    UserId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ViewPermissions", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEN8bO8/olGX6e2WFSs8WuqNS6s588x1kGhvfGdkqwrto3Du6hT3ckLX1xzhNMPHziQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "874f4774f21d4922a12961678b1f1f02", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 24, 13, 38, 53, 607, DateTimeKind.Local).AddTicks(8641), "FeaturesButton", new DateTime(2025, 4, 24, 13, 38, 53, 607, DateTimeKind.Local).AddTicks(8659) });

            migrationBuilder.InsertData(
                table: "ViewPermissions",
                columns: new[] { "Id", "CreatedDate", "IsActive", "SubViewId", "UpdatedDate", "UserId", "ViewId" },
                values: new object[,]
                {
                    { "03c36eee653341b284e49d8acbdb3b41", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8317), true, "BranchList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8319), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Branch" },
                    { "061671a5dec74fada354c71824fd359c", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8423), true, "BookingList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8423), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "063d060bfac1458db5ec51c5f41a2061", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8352), true, "ProductList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8352), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "18c4ae2535d14b81b71212bdae43bf12", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8420), true, "BookingItem", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8420), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Booking" },
                    { "1a29f76567584c1e92f0e1261382601c", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8347), true, "Brand", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8348), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "237eb5c9fd1c417f9d7008138ff91217", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8350), true, "ProductProperty", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8350), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "2ae0bade39e149fab1177195cdab1496", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8335), true, "ArticleList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8335), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "30ee0d6781074db483f96e996108384d", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8434), true, "Promotion", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8434), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "3ed91c1e4d4d4ed3aeaa3bf9695dd900", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8473), true, "ShippingFeeConfig", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8473), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "480c81fd007345d0beb755698c1355cf", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8425), true, "OrderList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8425), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "55600ef3adb04cfea21a5ea7c9a768c5", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8345), true, "Category", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8345), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Product" },
                    { "5653565d8b044d45893d57af12f79ab3", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8462), true, "TemplateUidList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8463), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "5a591da9b40a4fb99fa61d8a42b38dbd", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8450), true, "History", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8450), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "5a75f814f1a0479c8be404248bbd70d1", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(2535), true, "Overview", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(7603), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Dashboard" },
                    { "5b49105184624f83924bba5fd91008e3", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8430), true, "InvoiceTemplate", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8430), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Order" },
                    { "6173466f23444035a33a282baaea18d6", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8442), true, "Rank", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8442), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "655c2407e1504eb1b9cd19fdaf68f5ac", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8432), true, "DiscountList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8432), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Discount" },
                    { "6822b352cfff4663b13e22c47d20cafc", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8458), true, "EventTemplateList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8458), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "799be08d2060403cb3cb42faca19e69b", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8322), true, "AffiliateList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8322), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Affiliate" },
                    { "7a50f8dca7da4418b3efb6ec398debcc", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8452), true, "GamePrize", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8452), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "8aa6ed54a7644a9bbbc1cf32dda8cac0", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8460), true, "EventTemplateHistory", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8461), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "92b8bd26485341b282c8805e26655659", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8475), true, "CustomForm", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8475), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "9d8f64861206470c810002f2d8c12ff6", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8454), true, "CampaignList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8454), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "a95cef0986494fa6aa0d1438347e6eaa", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8456), true, "CampaignHistory", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8456), "09876543-21AB-CDEF-5678-90ABCDEF1234", "OmniTool" },
                    { "d0bfcf7b9ede40f0bfe1f510eb87a181", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8444), true, "Tag", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8445), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "db81730f672b4f14ba195230706bd9e1", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8436), true, "VoucherList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8437), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Voucher" },
                    { "dc97579455684ccda91150a0d87af9c3", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8468), true, "EnableFeatures", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8469), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "e25961ff3ab5406ca8fc080bdfeb5061", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8439), true, "MembershipList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8439), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Membership" },
                    { "e2f498f035a041da8d8b03cf64ef88dc", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8446), true, "GameList", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8447), "09876543-21AB-CDEF-5678-90ABCDEF1234", "LuckyWheel" },
                    { "ee85841d527d4b18a8bba316364239ab", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8325), true, "ArticleCategory", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8325), "09876543-21AB-CDEF-5678-90ABCDEF1234", "Article" },
                    { "ef051d958ef24398b416ee67e5553b6a", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8471), true, "MembershipExtendDefaults", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8471), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" },
                    { "f3667d7d449b4d368a699756bd004f2f", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8465), true, "GeneralSetting", new DateTime(2025, 4, 24, 13, 38, 53, 516, DateTimeKind.Local).AddTicks(8465), "09876543-21AB-CDEF-5678-90ABCDEF1234", "SystemSetting" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ViewPermissions");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "874f4774f21d4922a12961678b1f1f02");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEMKVEsY9kaIbft3zM3Ug5VmMyuY/ENoHllA0NxNwHJr6sqjMcirdR8nJiVgpTnGOmg==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "caea4945a94a479385f7fd6441d10ea8", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true},{\"key\":\"BookingButton\",\"value\":true},{\"key\":\"SurveyButton\",\"value\":false},{\"key\":\"RattingButton\",\"value\":false}]", new DateTime(2025, 4, 22, 18, 9, 38, 201, DateTimeKind.Local).AddTicks(7953), "FeaturesButton", new DateTime(2025, 4, 22, 18, 9, 38, 202, DateTimeKind.Local).AddTicks(3392) });
        }
    }
}
