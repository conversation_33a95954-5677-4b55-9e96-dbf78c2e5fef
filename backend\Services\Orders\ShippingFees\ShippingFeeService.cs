﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Orders;

namespace MiniAppCore.Services.Orders.ShippingFees
{
    public class ShippingFeeService(IUnitOfWork unitOfWork) : Service<ShippingFeeConfig>(unitOfWork), IShippingFeeService
    {
        public async Task<int> UpdateAsync(string id, ShippingFeeConfig shippingFeeConfig)
        {
            var existing = await base.GetByIdAsync(id);
            if (existing == null)
            {
                return 0; // or throw an exception
            }
            existing.ShippingFee = shippingFeeConfig.ShippingFee;
            existing.MinOrderValue = shippingFeeConfig.MinOrderValue;
            existing.MaxOrderValue = shippingFeeConfig.MaxOrderValue;
            return await base.UpdateAsync(existing);
        }

        public async Task<decimal> GetShippingFeeByTotalOrder(decimal totalOrder)
        {
            var config = await _repository.AsQueryable()
                        .FirstOrDefaultAsync(x => x.MinOrderValue <= totalOrder &&
                                                  x.MaxOrderValue >= totalOrder);
            return config?.ShippingFee ?? 0;
        }
    }
}
