﻿namespace MiniAppCore.Models.Responses.Memberships
{
    public class FormCustomResponse
    {
        public string? FormId { get; set; } // ID của form
        public string? FormName { get; set; } // Tên của form
        public string? FormTitle { get; set; } // Tiêu đề hiển thị của form
        public string? CampaignName { get; set; } // Tên chiến dịch liên quan

        public string? CheckboxTextColor { get; set; } // Màu chữ của checkbox
        public string? SubmitButtonText { get; set; } // Tên nút submit
        public string? SubmitButtonColor { get; set; } // <PERSON><PERSON><PERSON> nút submit
        public string? FormBackgroundColor { get; set; } // Màu nền của form
        public bool IsActive { get; set; } // Trạng thái hoạt động của form
        public List<ExtendInfoFormResponse> InputFields { get; set; } = new(); // Danh sách các trường nhập liệu
    }
}
