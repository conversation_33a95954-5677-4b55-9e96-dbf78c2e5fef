﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Bookings;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.BookingItems;
using MiniAppCore.Models.Responses.BookingItem;
namespace MiniAppCore.Services.Bookings
{
    public class BookingItemService(IUnitOfWork unitOfWork, IHttpContextAccessor httpContextAccessor, IWebHostEnvironment env, IMapper mapper) : OrderedEntityService<BookingItem>(unitOfWork), IBookingItemService
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";
        protected override string OrderColumnName => "DisplayOrder";

        public async Task<PagedResult<BookingItemResponse>> GetPage(ProductQueryParams query)
        {
            var bookingItems = _repository.AsQueryable().AsNoTracking();
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                bookingItems = bookingItems.Where(p => p.Name.Contains(query.Keyword));
            }

            // filter min price
            if (query.MinPrice.HasValue)
            {
                bookingItems = bookingItems.Where(p => p.Price >= query.MinPrice.Value);
            }

            // filter max price
            if (query.MaxPrice.HasValue)
            {
                bookingItems = bookingItems.Where(p => p.Price <= query.MaxPrice.Value);
            }

            if (query.StockStatus.Any())
            {
                bookingItems = bookingItems.Where(p => query.StockStatus.Contains(p.Status));
            }

            var totalItems = await bookingItems.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)query.PageSize);

            var orderedList = query.IsOrderByCreatedDate
                        ? bookingItems.OrderBy(x => x.CreatedDate)
                        : bookingItems.OrderBy(x => x.DisplayOrder).ThenBy(x => x.CreatedDate);

            // ✅ Chuyển dữ liệu vào bộ nhớ trước khi xử lý
            var bookingItemList = await orderedList.Skip(query.Skip)
                                                .Take(query.PageSize)
                                                .ToListAsync();
            // Ánh xạ dữ liệu và xử lý Images
            var items = bookingItemList.Select(p =>
            {
                var productResponse = mapper.Map<BookingItemResponse>(p);
                productResponse.Images = string.IsNullOrEmpty(p.Images)
                   ? new List<string>()
                   : p.Images.Split(',').Select(x => $"{hostUrl}/uploads/images/bookingItems/{x}").ToList();
                return productResponse;
            }).ToList();

            return new PagedResult<BookingItemResponse>
            {
                Data = items,
                TotalPages = totalPages
            };
        }

        public async Task<BookingItemResponse> GetDetailResponseByIdAsync(string id)
        {
            var bookingItem = await GetByIdAsync(id);
            if (bookingItem == null)
            {
                throw new CustomException(200, "Product not found!");
            }

            var productResponse = mapper.Map<BookingItemResponse>(bookingItem);

            productResponse.Images = bookingItem?.Images?.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Select(x => $"{hostUrl}/uploads/images/bookingItems/{x}").ToList() ?? new List<string>();
            return productResponse;
        }


        public async Task<int> CreateAsync(BookingItemRequest dto)
        {
            await unitOfWork.BeginTransactionAsync();
            try
            {
                var bookingItem = mapper.Map<BookingItem>(dto);
                bookingItem.Description = dto.Description ?? string.Empty;

                if (dto.Images.Any())
                {
                    bookingItem.Images = await ProcessUpload(dto.Images);
                }

                await base.PrepareInsertFirstAsync(bookingItem);
                //await base.CreateAsync(bookingItem);
                _repository.Add(bookingItem);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }


        public async Task<int> UpdateAsync(string id, BookingItemRequest dto)
        {
            var existingItem = await base.GetByIdAsync(id);
            if (existingItem == null)
            {
                throw new CustomException("Không tìm thấy dịch vụ!");
            }

            await unitOfWork.BeginTransactionAsync();
            try
            {
                int currentOrder = existingItem.DisplayOrder;

                mapper.Map(dto, existingItem);

                await base.ReorderAsync(existingItem, currentOrder, existingItem.DisplayOrder);

                if (dto.Images.Any())
                {
                    string newFiles = await ProcessUpload(dto.Images);
                    if (!string.IsNullOrEmpty(newFiles))
                    {
                        var existingImages = (existingItem.Images ?? "").Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
                        var newImageFiles = newFiles.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();

                        existingImages.AddRange(newImageFiles);
                        existingItem.Images = string.Join(",", existingImages.Distinct());
                    }
                }

                if (dto.RemovedOldImages.Any())
                {
                    RemoveOldImage(string.Join(",", dto.RemovedOldImages));
                    var remainingImages = existingItem.Images?.Split(",", StringSplitOptions.RemoveEmptyEntries)
                        .Where(image => !dto.RemovedOldImages.Contains(image.Trim()))
                        .ToList();
                    existingItem.Images = string.Join(",", remainingImages ?? new List<string>());
                }

                //await base.UpdateAsync(existingItem);
                _repository.Update(existingItem);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            await unitOfWork.BeginTransactionAsync();
            try
            {
                var bookingItem = await GetByIdAsync(id);
                if (bookingItem == null)
                {
                    throw new CustomException(404, "Product not found!");
                }

                RemoveOldImage(bookingItem.Images ?? "");

                await base.ReorderAfterDeleteAsync(bookingItem.DisplayOrder);

                _repository.Delete(bookingItem);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        #region Hanlde Image

        private async Task<string> ProcessUpload(List<IFormFile>? files)
        {
            var stringFiles = string.Empty;
            if (files != null)
            {
                var savePath = Path.Combine(env.WebRootPath, "uploads/images/bookingItems");
                var fileResult = await FileHandler.SaveFiles(files, savePath);
                stringFiles = string.Join(",", fileResult);
            }
            return stringFiles;
        }

        private void RemoveOldImage(string listImage)
        {
            var images = listImage.Split(",").Select(x => Path.Combine(env.WebRootPath, "uploads/images/bookingItems", x)).ToList();
            FileHandler.RemoveFiles(images);
        }

        #endregion
    }
}
