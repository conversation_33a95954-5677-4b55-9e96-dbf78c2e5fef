﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateTableVariant : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Stock",
                table: "Variants");

            migrationBuilder.RenameColumn(
                name: "RouteRule",
                table: "EventTemplates",
                newName: "RoutingRule");

            migrationBuilder.AddColumn<long>(
                name: "Quantity",
                table: "Variants",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "Variants",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "********-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAELl7p/9mRA/Kn0t+Ye0Qn6HlORfkz6r8bdOMZWcRKEKGd3BiLpHGGX1GlWmmNXnNSg==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Quantity",
                table: "Variants");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "Variants");

            migrationBuilder.RenameColumn(
                name: "RoutingRule",
                table: "EventTemplates",
                newName: "RouteRule");

            migrationBuilder.AddColumn<short>(
                name: "Stock",
                table: "Variants",
                type: "smallint",
                nullable: false,
                defaultValue: (short)0);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "********-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAENrrHY5NhXlzZbZoqEBtsvsaTemdET4uT123Rf9Wm9j7SL40dlq/ngI7G7hjOHxZiQ==");
        }
    }
}
