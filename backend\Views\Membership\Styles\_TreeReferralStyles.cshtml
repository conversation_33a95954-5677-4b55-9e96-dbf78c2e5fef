<style>
    /* Modal Override */
    .modal-dialog,
    .modal-dialog.modal-sm,
    .modal-dialog.modal-lg,
    .modal-dialog.modal-xl {
        max-width: none !important;
        width: 90vw !important;
        height: 90vh !important;
        margin: 2vh auto !important;
    }

    .modal-content {
        height: 100% !important;
        resize: both !important;
        min-width: 800px !important;
        min-height: 600px !important;
        max-width: none !important;
        max-height: none !important;
    }

    .modal-body {
        height: calc(100% - 120px) !important;
        overflow: hidden !important;
        padding: 0 !important;
    }

    /* Container Layout */
    .tree-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #f8f9fa;
        border-radius: 12px;
        padding: 20px;
        margin: 10px 0;
        display: flex;
        flex-direction: row;
        gap: 20px;
        height: 100%;
    }

    .statistics-section {
        flex: 0 0 350px;
        min-width: 300px;
    }

    .tree-section {
        flex: 1;
        min-width: 0;
    }

    .tree-scroll-wrapper {
        height: calc(100% - 20px);
        overflow: auto;
        padding: 10px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: white;
    }

    /* Tree Nodes */
    .tree-node {
        position: relative;
        padding: 12px 16px;
        margin: 8px 0;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        border-left: 4px solid #007bff;
        min-width: 300px;
    }

    .tree-node.level-0 {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
    }

    .tree-node.level-1 {
        border-left-color: #007bff;
        background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
    }

    .tree-node.level-2 {
        border-left-color: #ffc107;
        background: linear-gradient(135deg, #fffdf8 0%, #fff8e1 100%);
    }

    .tree-node.level-3 {
        border-left-color: #dc3545;
        background: linear-gradient(135deg, #fff8f8 0%, #ffebee 100%);
    }

    .tree-node.level-4 {
        border-left-color: #6f42c1;
        background: linear-gradient(135deg, #faf8ff 0%, #f3e5f5 100%);
    }

    .tree-node.level-5 {
        border-left-color: #fd7e14;
        background: linear-gradient(135deg, #fff9f6 0%, #ffeaa7 100%);
    }

    /* Member Info */
    .member-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #e9ecef;
        flex-shrink: 0;
    }

    .member-info {
        flex-grow: 1;
        margin-left: 12px;
        min-width: 0;
    }

    .member-name {
        font-weight: 600;
        color: #343a40;
        margin-bottom: 3px;
        font-size: 0.95em;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .member-details {
        color: #6c757d;
        font-size: 0.8em;
        line-height: 1.3;
    }

    .member-stats {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-top: 8px;
    }

    .stat-badge {
        background: #e9ecef;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.7em;
        color: #495057;
        white-space: nowrap;
    }

    .stat-badge.commission {
        background: #d4edda;
        color: #155724;
    }

    .stat-badge.primary {
        background: #cce7ff;
        color: #0056b3;
    }

    /* Tree Structure */
    .tree-children {
        margin-left: 20px;
        border-left: 2px dashed #dee2e6;
        padding-left: 15px;
        margin-top: 10px;
    }

    .expand-toggle {
        cursor: pointer;
        color: #007bff;
        font-size: 0.8em;
        margin-top: 5px;
        padding: 3px 8px;
        border-radius: 12px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        display: inline-block;
    }

    .expand-toggle:hover {
        background: #e9ecef;
        color: #0056b3;
    }

    /* Statistics */
    .statistics-summary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        height: fit-content;
        position: sticky;
        top: 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin-top: 15px;
    }

    .stat-item {
        text-align: center;
        background: rgba(255, 255, 255, 0.1);
        padding: 12px 8px;
        border-radius: 8px;
        backdrop-filter: blur(10px);
    }

    .stat-value {
        font-size: 1.1em;
        font-weight: bold;
        display: block;
        margin-bottom: 5px;
        word-break: break-all;
    }

    .stat-label {
        font-size: 0.8em;
        opacity: 0.9;
        line-height: 1.2;
    }

    /* Utilities */
    .tree-node-header {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .no-referrals {
        text-align: center;
        color: #6c757d;
        font-style: italic;
        padding: 40px 20px;
    }

    /* Fullscreen Button */
    .modal-fullscreen-btn {
        position: absolute;
        top: 10px;
        right: 50px;
        background: rgba(0, 0, 0, 0.1);
        border: none;
        border-radius: 4px;
        padding: 5px 8px;
        cursor: pointer;
        z-index: 1060;
        color: #666;
        font-size: 14px;
        display: none;
    }

    .modal .modal-fullscreen-btn {
        display: block !important;
    }

    .modal-fullscreen-btn:hover {
        background: rgba(0, 0, 0, 0.2);
        color: #333;
    }

    /* Fullscreen Mode */
    .modal.fullscreen .modal-dialog {
        width: 100vw !important;
        height: 100vh !important;
        margin: 0 !important;
        max-width: none !important;
    }

    .modal.fullscreen .modal-content {
        height: 100vh !important;
        border-radius: 0 !important;
        border: none !important;
    }

    .modal.fullscreen .modal-body {
        height: calc(100vh - 60px) !important;
    }

    /* Modal Tree Overrides */
    .modal .tree-container {
        max-height: none !important;
        height: 100% !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }

    .modal .tree-scroll-wrapper {
        max-height: none !important;
        height: calc(100% - 20px) !important;
    }

    /* Responsive */
    @@media (max-width: 1200px) {
        .tree-container {
            flex-direction: column;
        }

        .statistics-section {
            flex: none;
            min-width: auto;
        }

        .tree-section {
            flex: none;
        }

        .stats-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @@media (max-width: 768px) {
        .tree-container {
            padding: 15px;
            flex-direction: column;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .tree-node {
            min-width: 250px;
        }

        .stat-value {
            font-size: 1em;
        }

        .stat-label {
            font-size: 0.75em;
        }
    }
</style>
