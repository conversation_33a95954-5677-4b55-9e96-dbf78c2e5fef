﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Affiliates;

namespace MiniAppCore.Services.Affiliates.CommissionRates
{
    public interface ICommissionRateService : IService<AffiliateCommissionRate>
    {
        #region "Old Commission Rate Config"

        Task<decimal> GetCommissionConfigAsync();
        Task<int> AddOrUpdateCommissionRateAsync(decimal percentage);

        #endregion

        Task<List<AffiliateCommissionRate>> GetActiveRatesAsync();
        Task<List<AffiliateNode>> GetUserAffiliateLevelAsync(string sourceUserZaloId, int maxLevel = 10);
    }
}
