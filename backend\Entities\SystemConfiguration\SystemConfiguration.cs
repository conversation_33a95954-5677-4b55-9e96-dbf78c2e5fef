﻿using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.SystemConfiguration
{
    public class SystemConfiguration
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public required string Key { get; set; }
        public required string Value { get; set; }
        public required string Description { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
