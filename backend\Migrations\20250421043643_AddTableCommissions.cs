﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class AddTableCommissions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BranchCategories");

            migrationBuilder.DropTable(
                name: "Branch<PERSON>");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "9d3ece5a3ed541ebab2c192e20c4b9b6");

            migrationBuilder.CreateTable(
                name: "CommissionTransactions",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: false),
                    ReferrerZaloId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReferrerCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReferredZaloId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReferredName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReferredAvatar = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ReferredPhone = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OrderId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TotalOrder = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CommissionRate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    TotalCommission = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    OrderDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsPaid = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CommissionTransactions", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEIkXhpKeGOQwfq0GVkotqA9Qj2DJm25pGw8bNdAjuXnVdTl709Um1+HJaEfh+b0tqw==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "196ebddc34f24e72a52682289fbe509d", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 21, 11, 36, 42, 36, DateTimeKind.Local).AddTicks(956), "FeaturesButton", new DateTime(2025, 4, 21, 11, 36, 42, 36, DateTimeKind.Local).AddTicks(5341) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CommissionTransactions");

            migrationBuilder.DeleteData(
                table: "Commons",
                keyColumn: "Id",
                keyValue: "196ebddc34f24e72a52682289fbe509d");

            migrationBuilder.CreateTable(
                name: "BranchCategories",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    BranchId = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CategoryId = table.Column<int>(type: "int", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdateDate = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BranchCategories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Branches",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Avatar = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    City = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ClosingTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    District = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    GoogleMapURL = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Image = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsDefault = table.Column<bool>(type: "bit", nullable: false),
                    IsOpenHoliday = table.Column<bool>(type: "bit", nullable: false),
                    Latitude = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Longitude = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    OpeningTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Representor = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ServiceRadius = table.Column<long>(type: "bigint", nullable: false),
                    Status = table.Column<bool>(type: "bit", nullable: false),
                    StreetLine = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Type = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Ward = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Branches", x => x.Id);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEBljDL0tIlpD+zVnINjFKkspALlRYFJdPmfN4S9MXvZO/Py+zS/9mYm8cAXALdGuSQ==");

            migrationBuilder.InsertData(
                table: "Commons",
                columns: new[] { "Id", "Content", "CreatedDate", "Name", "UpdatedDate" },
                values: new object[] { "9d3ece5a3ed541ebab2c192e20c4b9b6", "[{\"key\":\"ChatOAButton\",\"value\":true},{\"key\":\"BranchButton\",\"value\":true},{\"key\":\"HotLineButton\",\"value\":true},{\"key\":\"LuckyWheelButton\",\"value\":true},{\"key\":\"MembershipRankButton\",\"value\":true}]", new DateTime(2025, 4, 17, 14, 54, 38, 584, DateTimeKind.Local).AddTicks(2678), "FeaturesButton", new DateTime(2025, 4, 17, 14, 54, 38, 584, DateTimeKind.Local).AddTicks(7109) });
        }
    }
}
