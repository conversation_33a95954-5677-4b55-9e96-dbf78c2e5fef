﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Articles
{
    public class Article : BaseEntity
    {
        public required string BannerImage { get; set; }
        public required string Title { get; set; }
        public required string Author { get; set; }
        public required string Content { get; set; }
        public short Status { get; set; } = 0;
        public string? Images { get; set; }
        public string? CategoryId { get; set; }
        public int OrderPriority { get; set; } = 1;
    }
}
