﻿namespace MiniAppCore.Models.Requests.Memberships
{
    public class RegisterMember
    {
        private string phoneNumber = string.Empty;

        public required string UserZaloId { get; set; }
        public required string PhoneNumber
        {
            get => phoneNumber;
            set => phoneNumber = value; // Regex.Replace(value, "^84", "0");
        }

        public required string UserZaloName { get; set; }
        public required string Avatar { get; set; }
        public required string MiniAppId { get; set; }

        public string? Referral { get; set; }
        public string? DisplayName { get; set; }
        public string? UserZaloIdByOA { get; set; }

        // trường mở rộng cho form đăng ký
        public string? FormId { get; set; }
        public List<RegisterExtendInfo> ExtendInfo { get; set; } = new List<RegisterExtendInfo>(); // chứa các thông tin mở rộng của cấu hình
    }

    public class RegisterExtendInfo
    {
        public string? Id { get; set; }
        public string? Content { get; set; } // chứa giá trị của cấu hình mở rộng
    }
}
