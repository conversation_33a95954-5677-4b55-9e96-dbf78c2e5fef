﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Events.Sponsors;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Services.Events.Sponsors;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class SponsorController(IHttpContextAccessor httpContextAccessor, ISponsorService sponsorService) : Controller
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Create()
        {
            var tier = new Sponsor()
            {
                Id = string.Empty,
                SponsorName = string.Empty,
            };

            ViewBag.Title = "Thêm mới nhà tài trợ";
            ViewBag.Button = "Lưu";

            return PartialView("_Sponsor", tier);
        }

        public async Task<IActionResult> Detail(string id)
        {
            var result = await sponsorService.GetById(id);
            if (result == null) throw new CustomException(1, "Nhà tài trợ không khả dụng");

            ViewBag.Image = Tools.GetImage(hostUrl, result.Image, "uploads/images/sponsors");

            ViewBag.Title = "Cập nhật nhà tài trợ";
            ViewBag.Button = "Cập nhật";
            return PartialView("_Sponsor", result);
        }
    }
}
