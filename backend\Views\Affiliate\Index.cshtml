﻿@{
    ViewData["Title"] = "Tiế<PERSON> thị liên kết";
}

<div class="row">
    <div class="col-lg-12 d-flex">
        <div class="card card-transparent card-block card-stretch card-height border-none">
            <div class="card-body p-0 mt-lg-2 mt-0 ">
                <h3 class="mb-3"><PERSON>h sách cộng tác viên</h3>
            </div>
        </div>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-commission">
                T<PERSON>h hoa hồng theo khoảng thời gian
            </button>

            <button type="button" class="btn btn-primary" onclick="ProcessPayment()">Tính hoa hồng</button>

            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#modal-excel-export">
                Xuất Excel
            </button>

            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modal-config">
                <i class="ri-settings-line mr-1"></i> Cài đặt tỷ lệ hoa hồng
            </button>
        </div>
    </div>

    <div class="col-lg-12">
        <div class="card mb-4">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên/số điện thoại">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label for="filter-status" class="form-label">Trạng thái thanh toán</label>
                        <select id="filter-status" class="form-select select2" onchange="table.ajax.reload()">
                            <option value="">Tất cả</option>
                            <option value="false">Chưa thanh toán</option>
                            <option value="true">Đã thanh toán</option>
                        </select>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Thời gian</label>
                        <div class="d-flex align-items-center">
                            <div class="input-group">
                                <input id="filterStart" type="date" class="form-control">
                                <span class="input-group-text bg-light border-0">đến</span>
                                <input id="filterEnd" type="date" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 d-flex justify-content-end mt-3">
                        <button class="btn btn-primary" onclick="table.ajax.reload()">
                            <i class="ri-filter-line me-1"></i> Lọc
                        </button>
                    </div>
                </div>

                <div class="row mt-1">
                    <div class="col-12">
                        <div class="filter-tags d-flex flex-wrap align-items-center">
                            <span id="active-filters" class="text-muted small"></span>
                            <a href="javascript:void(0)" onclick="clearAllFilters()" class="ms-2 small text-primary d-none" id="clear-filters">
                                <i class="ri-close-circle-line"></i> Xóa bộ lọc
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="transaction-table" class="table-responsive rounded mb-3">
            <table id="list-transaction" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="modal-commission" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tính hoa hồng theo khoảng thời gian</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Ngày bắt đầu</label>
                    <input id="start" class="form-control" type="date" min="2000-01-01" />
                </div>
                <div class="form-group">
                    <label>Ngày kết thúc</label>
                    <input id="end" class="form-control" type="date" min="2000-01-01" />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="ProcessPayment(true)">Xử lí</button>
            </div>
        </div>
    </div>
</div>

<!-- Export Excel Modal -->
<div class="modal fade" id="modal-excel-export" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xuất báo cáo cộng tác viên</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Ngày bắt đầu</label>
                    <input id="startExcel" class="form-control" type="date" />
                </div>
                <div class="form-group">
                    <label>Ngày kết thúc</label>
                    <input id="endExcel" class="form-control" type="date" />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="ProcessExportExcel()">Xuất</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Chi tiết giao dịch -->
<div class="modal fade" id="modal-transaction-details" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chi tiết giao dịch hoa hồng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="transaction-details-container" class="table-responsive rounded mb-3">
                    <table id="details-table" class="data-table table mb-0 tbl-server-info"></table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal Cài đặt 
<div class="modal fade" id="modal-config" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cài đặt tỷ lệ hoa hồng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="ri-information-line mr-2"></i>
                    Tỷ lệ hoa hồng sẽ được áp dụng cho tất cả các đơn hàng mới
                </div>

                <div class="form-group">
                    <label for="commissionRate">Tỷ lệ hoa hồng (%)</label>
                    <div class="input-group">
                        <input type="number" class="form-control" id="commissionRate" step="0.1" min="0" max="100" value="@(ViewBag.CommissionRate * 100)" required>
                        <div class="input-group-append">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <small class="form-text text-muted">Nhập tỷ lệ hoa hồng từ 0 đến 100%</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="UpdateCommissionRate()">Lưu cài đặt</button>
            </div>
        </div>
    </div>
</div>
-->

<partial name="Partials/_CommissionRateModal"></partial>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Thiết lập ngày mặc định
            $("#start").val(moment().startOf('month').format('YYYY-MM-DD'));
            $("#end").val(moment().endOf('month').format('YYYY-MM-DD'));

            $("#startExcel").val(moment(new Date()).format('YYYY-MM-DD'));
            $("#endExcel").val(moment(new Date()).add(30, 'days').format('YYYY-MM-DD'));

            $("#filterStart").val(moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD'));
            $("#filterEnd").val(moment(new Date()).format('YYYY-MM-DD'));

            $("#filter-status").select2();

            GetListCommissionRecipients();

            $('#search').on('input', function () {
                table.ajax.reload();
            });
        })

        function ProcessPayment(byTime = false) {
            let startDate, endDate;

            if (byTime) {
                // Use dates from the modal when calculating by specific time range
                startDate = moment($("#start").val()).format('YYYY-MM-DDT00:00');
                endDate = moment($("#end").val()).format('YYYY-MM-DDT00:00');
            } else {
                // Use current month when calculating without time parameters
                const now = moment();
                startDate = moment(now).startOf('month').format('YYYY-MM-DDT00:00');
                endDate = moment(now).endOf('month').format('YYYY-MM-DDT23:59');
            }

            const formData = new FormData();
            formData.append('startDate', startDate);
            formData.append('endDate', endDate);

            $.ajax({
                url: '/api/Commissions',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, "success");
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, "error");
                    }

                    if (byTime) {
                        $("#modal-commission").modal("toggle");
                    }
                },
                error: function (err) {
                    console.log(err)
                    AlertResponse("Đã xảy ra lỗi khi tính hoa hồng", "error");
                }
            });
        }

        function ProcessExportExcel() {
            const startDate = moment($("#startExcel").val()).format('YYYY-MM-DDT00:00');
            const endDate = moment($("#endExcel").val()).format('YYYY-MM-DDT00:00');

            const formData = new FormData();
            formData.append('startDate', startDate);
            formData.append('endDate', endDate);

            $.ajax({
                url: '/api/Commissions/export-commission',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                xhrFields: {
                    responseType: 'blob'
                },
                success: function (response) {
                    const url = window.URL.createObjectURL(new Blob([response]));
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', 'nguoi_nhan_hoa_hong.xlsx');
                    document.body.appendChild(link);
                    link.click();
                    link.remove();

                    AlertResponse('Xuất file Excel thành công!', 'success');
                    $("#modal-excel-export").modal("toggle");
                },
                error: function (err) {
                    AlertResponse('Không thể xuất dữ liệu.', 'error');
                    console.error(err);
                }
            });
        }

        function GetListCommissionRecipients() {
            table = new DataTable("#list-transaction", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const status = $("#filter-status").val();
                    const keyword = $("#search").val();
                    const startDate = moment($("#filterStart").val()).format('YYYY-MM-DDT00:00');
                    const endDate = moment($("#filterEnd").val()).format('YYYY-MM-DDT23:59');

                    $.ajax({
                        url: '/api/commissions/recipients',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            status: status,
                            startDate: startDate,
                            endDate: endDate
                        },
                        success: function (response) {
                            const formattedData = [];

                            response.data.map((item, index) => {
                                formattedData.push({
                                    0: data.start + index + 1,
                                    1: `<div class="d-flex align-items-center">
                                        <div>
                                            ${item.displayName ?? 'Không khả dụng'}
                                            <p class="mb-0"><small>${item.phoneNumber ?? 'Không khả dụng'}</small></p>
                                        </div>
                                    </div>`,
                                    2: item.totalCommission.toLocaleString('vi-VN') + ' đ',
                                    3: item.pendingCommission.toLocaleString('vi-VN') + ' đ',
                                    4: item.paidCommission.toLocaleString('vi-VN') + ' đ',
                                    5: item.referralCount + ' người',
                                    6: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                        <a onclick="ViewTransactionDetails('${item.referrerZaloId}')" class="badge bg-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                            <i class="ri-eye-line fs-6"></i>
                                        </a>
                                        <a onclick="PayCommission('${item.referrerZaloId}')" class="badge badge-success" data-toggle="tooltip" data-placement="top" title="Thanh toán hoa hồng">
                                            <i class="ri-money-dollar-circle-line fs-6"></i>
                                        </a>
                                    </div>`,
                                });
                            });

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.count ?? 0,
                                    data: formattedData
                                });
                            }, 400)
                        },
                        error: function (err) {
                            console.error(err);
                            AlertResponse("Không thể tải dữ liệu cộng tác viên", "error");
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center', width: '5%' },
                    { title: "Cộng tác viên", data: 1, width: '25%' },
                    { title: "Tổng hoa hồng", data: 2, className: 'text-center', width: '15%' },
                    { title: "Chưa thanh toán", data: 3, className: 'text-center', width: '15%' },
                    { title: "Đã thanh toán", data: 4, className: 'text-center', width: '15%' },
                    { title: "Số người giới thiệu", data: 5, className: 'text-center', width: '15%' },
                    { title: "Thao tác", data: 6, className: 'text-center', width: '10%' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)",
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function ViewTransactionDetails(referrerZaloId) {
            const startDate = moment($("#filterStart").val()).format('YYYY-MM-DDT00:00');
            const endDate = moment($("#filterEnd").val()).format('YYYY-MM-DDT23:59');

            if (detailsTable) {
                detailsTable.destroy();
            }

            detailsTable = new DataTable("#details-table", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 5,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;

                    $.ajax({
                        url: '/api/commissions/transactions',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            referrerZaloId: referrerZaloId,
                            startDate: startDate,
                            endDate: endDate
                        },
                        success: function (response) {
                            const formattedData = [];

                            response.data.map((item, index) => {
                                formattedData.push({
                                    stt: data.start + index + 1,
                                    orderId: item.orderId ? item.orderId : 'Không khả dụng',
                                    referredInfo: `<div class="d-flex align-items-center">
                                        <div>
                                            ${item.referredName ?? 'Không khả dụng'}
                                            <p class="mb-0"><small>${item.referredPhone ?? 'Không khả dụng'}</small></p>
                                        </div>
                                    </div>`,
                                    orderDate: FormatDateTime(item.orderDate),
                                    totalOrder: item.totalOrder.toLocaleString('vi-VN') + ' đ',
                                    commissionRate: (item.commissionRate).toFixed(1) + '%',
                                    totalCommission: item.totalCommission.toLocaleString('vi-VN') + ' đ',
                                    statusDisplay: item.isPaid ?
                                        `<div class="badge badge-success">Đã thanh toán</div>` :
                                        `<div class="badge badge-danger">Chưa thanh toán</div>`,
                                    actions: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                        <a onclick="UpdateStatus('${item.id}', true, ${item.isPaid})" class="badge badge-success" data-toggle="tooltip" data-placement="top" title="Thanh toán">
                                            <i class="ri-check-line"></i>
                                        </a>
                                        <a onclick="UpdateStatus('${item.id}', false, ${item.isPaid})" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Hủy thanh toán">
                                            <i class="ri-close-line"></i>
                                        </a>
                                    </div>`,
                                    id: item.id,
                                    isPaid: item.isPaid
                                });
                            });

                            callback({
                                draw: data.draw,
                                recordsTotal: response.data.length,
                                recordsFiltered: response.count ?? 0,
                                data: formattedData
                            });
                        },
                        error: function (err) {
                            console.error(err);
                            AlertResponse("Không thể tải chi tiết giao dịch", "error");
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "stt", className: 'text-center', width: '5%' },
                    { title: "Mã đơn hàng", data: "orderId", width: '10%' },
                    { title: "Người được giới thiệu", data: "referredInfo", width: '20%' },
                    { title: "Ngày đặt hàng", data: "orderDate", className: 'text-center', width: '10%' },
                    { title: "Giá trị đơn hàng", data: "totalOrder", className: 'text-center', width: '15%' },
                    { title: "Tỷ lệ hoa hồng", data: "commissionRate", className: 'text-center', width: '10%' },
                    { title: "Hoa hồng", data: "totalCommission", className: 'text-center', width: '15%' },
                    { title: "Trạng thái", data: "statusDisplay", className: 'text-center', width: '10%' },
                    { title: "Thao tác", data: "actions", className: 'text-center', width: '5%' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $("#modal-transaction-details").modal("show");
        }

        function PayCommission(referrerZaloId) {
            swal({
                title: "Xác nhận thanh toán",
                text: "Bạn có chắc chắn muốn thanh toán tất cả hoa hồng cho người này?",
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Hủy",
                        value: null,
                        visible: true,
                        className: "",
                        closeModal: true,
                    },
                    confirm: {
                        text: "Xác nhận thanh toán",
                        value: true,
                        visible: true,
                        className: "",
                        closeModal: true,
                    },
                },
            }).then((result) => {
                if (result) {
                    $.ajax({
                        url: `/api/Commissions/pay-all/${referrerZaloId}`,
                        method: "POST",
                        contentType: 'application/json',
                        success: function (response) {
                            if (response.code === 0) {
                                AlertResponse(response.message ?? "Thanh toán hoa hồng thành công!", "success");
                                table.ajax.reload(null, false);
                            } else {
                                AlertResponse(response.message ?? "Đã xảy ra lỗi!", "error");
                            }
                        },
                        error: function (error) {
                            AlertResponse("Lỗi khi thực hiện thanh toán!", "error");
                        },
                    });
                }
            });
        }

        function UpdateStatus(id, status, currentStatus) {
            if (currentStatus == status) {
                $.toast({
                    heading: 'Thông báo',
                    text: "Không cần thực hiện thao tác này!",
                    hideAfter: 3000,
                    allowToastClose: false,
                    showHideTransition: 'fade',
                    icon: 'info',
                    position: 'bottom-right',
                });
                return;
            }

            const data = {
                status: status
            };

            swal({
                title: "Xác nhận",
                text: status ? "Xác nhận thanh toán!" : "Xác nhận hủy thanh toán!",
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Hủy",
                        value: null,
                        visible: true,
                        className: "",
                        closeModal: true,
                    },
                    confirm: {
                        text: "Xác nhận",
                        value: true,
                        visible: true,
                        className: "",
                        closeModal: true,
                    },
                },
            }).then((result) => {
                if (result) {
                    $.ajax({
                        url: `/api/Commissions/${id}`,
                        method: "PUT",
                        data: JSON.stringify(data),
                        contentType: 'application/json',
                        success: function (response) {
                            if (response.code === 0) {
                                AlertResponse(response.message ?? "Thành công!", "success");
                                if (detailsTable) detailsTable.ajax.reload(null, false);
                                table.ajax.reload(null, false);
                            } else {
                                AlertResponse(response.message ?? "Đã xảy ra lỗi!", "error");
                            }
                        },
                        error: function (error) {
                            AlertResponse("Lỗi khi thực hiện yêu cầu!", "error");
                        },
                    });
                }
            });
        }

        function UpdateCommissionRate() {
            var ratePercent = parseFloat($("#commissionRate").val());
            if (isNaN(ratePercent) || ratePercent < 0 || ratePercent > 100) {
                AlertResponse("Vui lòng nhập tỷ lệ hợp lệ từ 0 đến 100%", "error");
                return;
            }

            // Chuyển từ phần trăm sang phân số
            var rateFraction = ratePercent / 100;

            $.ajax({
                url: '/api/Commissions/config',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify({
                    rate: rateFraction
                }),
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse("Cập nhật tỷ lệ hoa hồng thành công", "success");
                        $("#modal-config").modal("hide");
                    } else {
                        AlertResponse(response.message || "Không thể cập nhật cài đặt hoa hồng", "error");
                    }
                },
                error: function (err) {
                    console.error(err);
                    AlertResponse("Lỗi khi cập nhật cài đặt hoa hồng", "error");
                }
            });
        }

        let detailsTable = null;
    </script>

    <script>
            // Add these to your existing script section
        let commissionRates = [];

        $(document).ready(function () {
            // Add this to your existing ready function
            $("#isActive").select2({
                dropdownParent: $("#modal-commission-rate-form")
            });

            // Initialize the effective date with current datetime
            $("#effectiveDate").val(moment().format('YYYY-MM-DDTHH:mm'));

            // Load commission rates when modal is opened
            $('#modal-config').on('shown.bs.modal', function () {
                loadCommissionRates();
            });
        });

        function loadCommissionRates() {
            $.ajax({
                url: '/api/affiliates/commission-rates',
                type: 'GET',
                data: {
                    pagesize: 100 // Load a large number of rates
                },
                success: function (response) {
                    commissionRates = response.data || [];
                    renderCommissionRates();
                },
                error: function (err) {
                    console.error(err);
                    AlertResponse("Không thể tải dữ liệu tỷ lệ hoa hồng", "error");
                }
            });
        }

        function renderCommissionRates() {
            const tableBody = $("#commission-rates-body");
            tableBody.empty();

            if (commissionRates.length === 0) {
                tableBody.html('<tr><td colspan="5" class="text-center">Chưa có dữ liệu tỷ lệ hoa hồng</td></tr>');
                return;
            }

            // Sort by level
            commissionRates.sort((a, b) => a.level - b.level);

            commissionRates.forEach(rate => {
                tableBody.append(`
                    <tr>
                        <td class="text-center">${rate.level}</td>
                        <td class="text-center">${rate.rate}%</td>
                        <td class="text-center">
                            <span class="badge ${rate.isActive ? 'bg-success' : 'bg-danger'}">
                                ${rate.isActive ? 'Hoạt động' : 'Không hoạt động'}
                            </span>
                        </td>
                        <td class="text-center">${FormatDateTime(rate.effectiveDate)}</td>
                        <td class="text-center">
                            <button onclick="editCommissionRate('${rate.id}')" class="btn btn-info mx-1" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                <i class="ri-edit-line mx-0"></i>
                            </button>
                            <button onclick="deleteCommissionRate('${rate.id}')" class="btn btn-warning mx-1" data-toggle="tooltip" data-placement="top" title="Xóa">
                                <i class="ri-delete-bin-line mx-0"></i>
                            </button>
                        </td>
                    </tr>
                `);
            });
        }

        function openCommissionRateForm(id = null) {
            // Reset form
            $("#commission-rate-form")[0].reset();
            $("#rate-id").val('');
            $("#level").val('');
            $("#rate").val('');
            $("#isActive").val('true').trigger('change');
            $("#effectiveDate").val(moment().format('YYYY-MM-DDTHH:mm'));

            if (id) {
                // Edit mode
                const rate = commissionRates.find(r => r.id === id);
                if (rate) {
                    $("#rate-form-title").text("Chỉnh sửa tỷ lệ hoa hồng");
                    $("#rate-id").val(rate.id);
                    $("#level").val(rate.level);
                    $("#rate").val(rate.rate);
                    $("#isActive").val(rate.isActive.toString()).trigger('change');
                    $("#effectiveDate").val(moment(rate.effectiveDate).format('YYYY-MM-DDTHH:mm'));
                }
            } else {
                // Add mode
                $("#rate-form-title").text("Thêm tỷ lệ hoa hồng");
            }

            $("#modal-commission-rate-form").modal('show');
        }

        function editCommissionRate(id) {
            openCommissionRateForm(id);
        }

        function saveCommissionRate() {
            const id = $("#rate-id").val();
            const level = parseInt($("#level").val());
            const rate = parseFloat($("#rate").val());
            const isActive = $("#isActive").val() === 'true';
            const effectiveDate = $("#effectiveDate").val();

            // Validate
            if (!level || level < 1) {
                AlertResponse("Vui lòng nhập cấp hoa hồng hợp lệ (lớn hơn 0)", "warning");
                return;
            }

            if (!rate || rate < 0 || rate > 100) {
                AlertResponse("Vui lòng nhập tỷ lệ hoa hồng hợp lệ (0-100%)", "warning");
                return;
            }

            if (!effectiveDate) {
                AlertResponse("Vui lòng chọn ngày hiệu lực", "warning");
                return;
            }

            const data = {
                id: id || undefined,
                level: level,
                rate: rate,
                isActive: isActive,
                effectiveDate: effectiveDate
            };

            const url = id ? `/api/affiliates/commission-rates/${id}` : '/api/affiliates/commission-rates';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code === 0) {
                        AlertResponse(id ? "Cập nhật tỷ lệ hoa hồng thành công" : "Thêm tỷ lệ hoa hồng thành công", "success");
                        $("#modal-commission-rate-form").modal('hide');
                        loadCommissionRates();
                    } else {
                        AlertResponse(response.message || "Có lỗi xảy ra", "error");
                    }
                },
                error: function(err) {
                    console.error(err);
                    AlertResponse("Có lỗi xảy ra khi lưu dữ liệu", "error");
                }
            });
        }

        function deleteCommissionRate(id) {
            swal({
                title: "Xác nhận xóa",
                text: "Bạn có chắc chắn muốn xóa tỷ lệ hoa hồng này?",
                icon: "warning",
                buttons: {
                    cancel: {
                        text: "Hủy",
                        value: null,
                        visible: true,
                        className: "",
                        closeModal: true,
                    },
                    confirm: {
                        text: "Xác nhận",
                        value: true,
                        visible: true,
                        className: "",
                        closeModal: true,
                    },
                },
            }).then((result) => {
                if (result) {
                    $.ajax({
                        url: `/api/affiliates/commission-rates/${id}`,
                        type: 'DELETE',
                        success: function(response) {
                            if (response.code === 0) {
                                AlertResponse("Xóa tỷ lệ hoa hồng thành công", "success");
                                loadCommissionRates();
                            } else {
                                AlertResponse(response.message || "Có lỗi xảy ra", "error");
                            }
                        },
                        error: function(err) {
                            console.error(err);
                            AlertResponse("Có lỗi xảy ra khi xóa dữ liệu", "error");
                        }
                    });
                }
            });
        }
    </script>
}