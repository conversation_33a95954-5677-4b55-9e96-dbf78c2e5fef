﻿@model MiniAppCore.Models.Responses.Surveys.SectionResponse

<div class="accordion" id="sectionAccordion">
    <div class="accordion-item mb-4">
        <h2 class="accordion-header" id="<EMAIL>">
            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#<EMAIL>">
                @Model.TitleSection
            </button>
        </h2>
        <div id="<EMAIL>" class="accordion-collapse collapse show">
            <div class="accordion-body">
                @foreach (var question in Model.ListQuestion)
                {
                    @switch (question?.Type?.ToLower())
                    {
                        case "paragraph":
                        case "date":
                        case "time":
                            @await Html.PartialAsync("SurveySubmission/_AnswerResultPartial", question)
                            break;
                        case "singlechoice":
                        case "multichoice":
                        case "dropdown":
                            @await Html.PartialAsync("SurveySubmission/_ChoiceResultPartial", question)
                            break;
                        case "likert":
                            @await Html.PartialAsync("SurveySubmission/_LikertResultPartial", question)
                            break;
                    }
                }
            </div>
        </div>
    </div>
</div>
