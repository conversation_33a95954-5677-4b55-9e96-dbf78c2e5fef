﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Surveys
{
    [Index(nameof(SurveyId))]
    public class SurveySection : BaseEntity
    {
        [MaxLength(36)]
        public string? SurveyId { get; set; }
        public string? TitleSection { get; set; }

        public byte DisplayOrder { get; set; }
    }
}
