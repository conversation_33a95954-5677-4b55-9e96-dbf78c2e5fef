﻿<div id="modal-import" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title"><i class="ri-file-excel-line me-2 text-success"></i>Import sản phẩm từ Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-3">
                <div class="container-fluid px-0">
                    <!-- Template download section -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="alert alert-primary border-0 d-flex align-items-center py-3">
                                <i class="ri-information-line me-3 fs-5"></i>
                                <div>
                                    <p class="mb-1"><PERSON><PERSON> import sản phẩm, vui lòng sử dụng file Excel có định dạng như mẫu.</p>
                                    <div class="d-flex align-items-center flex-wrap">
                                        <a href="/api/products/TemplateImport" target="_blank" class="btn btn-primary me-3 mb-2 mb-md-0">
                                            <i class="ri-download-line me-1"></i>Tải mẫu file Excel
                                        </a>
                                        <span class="badge bg-danger me-2">Lưu ý:</span>
                                        <span>Chỉ import tối đa 100 sản phẩm cơ bản mỗi lần (không giới hạn số lượng biến thể)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File input section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-0 bg-light p-3">
                                <label for="import-file" class="form-label fw-bold mb-2">Chọn file Excel (.xlsx, .xls)</label>
                                <div class="input-group">
                                    <input type="file" class="form-control" id="import-file" accept=".xlsx, .xls">
                                    <button class="btn btn-outline-secondary" type="button" id="clear-file">
                                        <i class="ri-close-line"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback" id="file-feedback">Vui lòng chọn file Excel (.xlsx, .xls)</div>
                                <div class="form-text mt-2">Đảm bảo file tuân thủ định dạng mẫu đã cung cấp</div>
                            </div>
                        </div>
                    </div>

                    <!-- Property limitation notice -->
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="alert alert-info d-flex align-items-center">
                                <i class="ri-information-line me-2 fs-5 flex-shrink-0"></i>
                                <div>
                                    <strong>Giới hạn thuộc tính:</strong> Hệ thống hỗ trợ tối đa 3 thuộc tính cho mỗi biến thể sản phẩm
                                    (ví dụ: màu sắc, kích cỡ, chất liệu). Giới hạn này được thiết kế để đảm bảo tính đơn giản và hiệu quả
                                    trong việc quản lý sản phẩm, đồng thời phù hợp với hầu hết nhu cầu kinh doanh.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Guidance tabs -->
                    <div class="row">
                        <div class="col-12">
                            <ul class="nav nav-tabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="structure-tab" data-bs-toggle="tab" data-bs-target="#structure" type="button" role="tab" aria-selected="true">
                                        <i class="ri-file-list-line me-1"></i> Cấu trúc file
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="example-tab" data-bs-toggle="tab" data-bs-target="#example" type="button" role="tab" aria-selected="false">
                                        <i class="ri-lightbulb-line me-1"></i> Ví dụ
                                    </button>
                                </li>
                            </ul>

                            <div class="tab-content">
                                <!-- Structure tab -->
                                <div class="tab-pane fade show active p-3 border border-top-0 rounded-bottom" id="structure" role="tabpanel">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th class="text-nowrap">Cột</th>
                                                    <th>Mô tả</th>
                                                    <th class="text-center">Yêu cầu</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="text-nowrap fw-bold">Tên sản phẩm</td>
                                                    <td>Tên sản phẩm (để trống ở dòng tiếp theo nếu là biến thể cùng SP)</td>
                                                    <td class="text-center"><span class="badge bg-danger">Bắt buộc</span></td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Tên thuộc tính 1</td>
                                                    <td>Tên thuộc tính đầu tiên (VD: Màu sắc)</td>
                                                    <td class="text-center" rowspan="6">
                                                        <span class="badge bg-info mb-2">Tùy chọn</span><br>
                                                        <small class="text-muted">Cần để tạo biến thể</small>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Giá trị thuộc tính 1</td>
                                                    <td>Giá trị của thuộc tính 1 (VD: Đỏ, Xanh)</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Tên thuộc tính 2</td>
                                                    <td>Tên thuộc tính thứ hai (VD: Kích cỡ)</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Giá trị thuộc tính 2</td>
                                                    <td>Giá trị của thuộc tính 2 (VD: S, M, L)</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Tên thuộc tính 3</td>
                                                    <td>Tên thuộc tính thứ ba (VD: Chất liệu)</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Giá trị thuộc tính 3</td>
                                                    <td>Giá trị của thuộc tính 3 (VD: Cotton, Polyester)</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Giá</td>
                                                    <td>Giá sản phẩm/biến thể (VD: 150000)</td>
                                                    <td class="text-center"><span class="badge bg-secondary">Mặc định: 0</span></td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Tồn kho</td>
                                                    <td>Số lượng tồn kho (VD: 100)</td>
                                                    <td class="text-center"><span class="badge bg-secondary">Mặc định: 0</span></td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Ảnh</td>
                                                    <td>URL các hình ảnh, phân cách bằng dấu phẩy</td>
                                                    <td class="text-center"><span class="badge bg-info">Tùy chọn</span></td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Danh mục</td>
                                                    <td>Tên danh mục, phân cách bằng dấu phẩy (VD: Áo thun, Thời trang nam)</td>
                                                    <td class="text-center"><span class="badge bg-info">Tùy chọn</span></td>
                                                </tr>
                                                <tr>
                                                    <td class="text-nowrap">Mô tả</td>
                                                    <td>Mô tả chi tiết sản phẩm</td>
                                                    <td class="text-center"><span class="badge bg-info">Tùy chọn</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="mt-3 ps-2">
                                        <p class="mb-0 text-muted small">
                                            <i class="ri-question-line me-1"></i> <strong>Tại sao chỉ hỗ trợ 3 thuộc tính?</strong>
                                            Sau nghiên cứu thống kê, hầu hết các sản phẩm thương mại điện tử chỉ sử dụng tối đa 3 thuộc tính
                                            để phân biệt biến thể (thường là màu sắc, kích cỡ và một thuộc tính khác).
                                            Giới hạn này giúp tối ưu hóa hiệu suất hệ thống, dễ dàng quản lý tồn kho và cung cấp trải nghiệm
                                            mua sắm đơn giản cho người dùng.
                                        </p>
                                    </div>
                                </div>

                                <!-- Example tab -->
                                <div class="tab-pane fade p-3 border border-top-0 rounded-bottom" id="example" role="tabpanel">
                                    <div class="alert alert-warning mb-3">
                                        <i class="ri-information-line me-2"></i>
                                        <strong>Mẹo:</strong> Một sản phẩm có thể có nhiều biến thể. Chỉ điền tên sản phẩm ở dòng đầu tiên, các biến thể của sản phẩm để trống tên sản phẩm.
                                    </div>

                                    <div class="alert alert-secondary mb-3 d-flex">
                                        <div class="me-2"><i class="ri-error-warning-line"></i></div>
                                        <div>
                                            <strong>Lưu ý về giới hạn thuộc tính:</strong>
                                            Hệ thống hỗ trợ tối đa 3 thuộc tính/biến thể. Nếu sản phẩm của bạn có nhiều hơn 3 thuộc tính,
                                            hãy cân nhắc gộp hoặc sắp xếp lại thuộc tính để phù hợp với giới hạn này.
                                        </div>
                                    </div>

                                    <h6 class="mb-3"><i class="ri-store-2-line me-2 text-primary"></i>Ví dụ: "Áo thun nam" với các biến thể</h6>

                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped border">
                                            <thead class="table-primary">
                                                <tr>
                                                    <th>Tên SP</th>
                                                    <th>Tên TT 1</th>
                                                    <th>Giá trị TT 1</th>
                                                    <th>Tên TT 2</th>
                                                    <th>Giá trị TT 2</th>
                                                    <th>Tên TT 3</th>
                                                    <th>Giá trị TT 3</th>
                                                    <th>Giá</th>
                                                    <th>Tồn kho</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="fw-bold">Áo thun nam</td>
                                                    <td>Màu</td>
                                                    <td>Đen</td>
                                                    <td>Size</td>
                                                    <td>L</td>
                                                    <td>Chất liệu</td>
                                                    <td>Cotton</td>
                                                    <td>150000</td>
                                                    <td>50</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-muted fst-italic">(để trống)</td>
                                                    <td>Màu</td>
                                                    <td>Trắng</td>
                                                    <td>Size</td>
                                                    <td>L</td>
                                                    <td>Chất liệu</td>
                                                    <td>Cotton</td>
                                                    <td>150000</td>
                                                    <td>30</td>
                                                </tr>
                                                <tr>
                                                    <td class="text-muted fst-italic">(để trống)</td>
                                                    <td>Màu</td>
                                                    <td>Đen</td>
                                                    <td>Size</td>
                                                    <td>XL</td>
                                                    <td>Chất liệu</td>
                                                    <td>Cotton</td>
                                                    <td>170000</td>
                                                    <td>20</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer bg-light">
                <div id="import-result" class="me-auto"></div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="btn-import" onclick="importProducts()">
                    <span class="d-flex align-items-center">
                        <span class="spinner-border spinner-border-sm d-none me-2" role="status" aria-hidden="true" id="import-spinner"></span>
                        <i class="ri-upload-2-line me-1"></i> Import
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>