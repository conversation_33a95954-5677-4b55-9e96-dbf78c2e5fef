﻿using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Database;
using MiniAppCore.Models.Responses.Affiliates;

namespace MiniAppCore.Services.Affiliates.Referrals
{
    public class ReferralService : IReferralService
    {
        private readonly ApplicationDbContext _context;

        public ReferralService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ReferralTreeResponse?> GetReferralTreeAsync(string userZaloId, int maxDepth = 5)
        {
            var rootMember = await _context.Memberships
                .FirstOrDefaultAsync(m => m.UserZaloId == userZaloId);

            if (rootMember == null)
                return null;

            var commissionTotal = await GetMemberCommissionAsync(userZaloId);

            var rootNode = new ReferralTreeResponse
            {
                UserZaloId = rootMember.UserZaloId,
                UserZaloName = rootMember.UserZaloName,
                Avatar = rootMember.Avatar,
                PhoneNumber = rootMember.PhoneNumber,
                ReferralCode = rootMember.ReferralCode ?? "",
                JoinDate = rootMember.CreatedDate,
                Level = 0,
                TotalCommission = commissionTotal,
                Children = new List<ReferralTreeResponse>()
            };

            await BuildReferralTreeRecursive(rootNode, 1, maxDepth);
            CalculateReferralCounts(rootNode);

            return rootNode;
        }

        public async Task<ReferralStatisticsResponse?> GetReferralStatisticsAsync(string userZaloId)
        {
            var member = await _context.Memberships
                .FirstOrDefaultAsync(m => m.UserZaloId == userZaloId);

            if (member == null)
                return null;

            var tree = await GetReferralTreeAsync(userZaloId, 10); // Lấy cây với độ sâu lớn để thống kê

            if (tree == null)
                return null;

            var statistics = new ReferralStatisticsResponse
            {
                UserZaloId = member.UserZaloId,
                UserZaloName = member.UserZaloName,
                Avatar = member.Avatar,
                PhoneNumber = member.PhoneNumber
            };

            var allNodes = GetAllNodesFromTree(tree);

            // Tính toán thống kê
            statistics.TotalDirectReferrals = tree.Children.Count;
            statistics.TotalIndirectReferrals = allNodes.Count - 1 - statistics.TotalDirectReferrals;
            statistics.MaxDepth = GetMaxDepth(tree);

            // Thống kê theo level
            foreach (var node in allNodes.Skip(1)) // Bỏ qua root node
            {
                if (statistics.ReferralsByLevel.ContainsKey(node.Level))
                    statistics.ReferralsByLevel[node.Level]++;
                else
                    statistics.ReferralsByLevel[node.Level] = 1;
            }

            // Tính toán commission
            var commissionTransactions = await _context.CommissionTransactions
                .Where(ct => ct.ReferrerZaloId == userZaloId)
                .ToListAsync();

            statistics.TotalCommissionEarned = commissionTransactions.Sum(ct => ct.TotalCommission);
            statistics.TotalCommissionPaid = commissionTransactions.Where(ct => ct.IsPaid).Sum(ct => ct.TotalCommission);

            return statistics;
        }

        public async Task<List<ReferralTreeResponse>> GetDirectReferralsAsync(string userZaloId)
        {
            var directReferrals = await _context.Memberships
                .Where(m => m.ReferrerId == userZaloId)
                .ToListAsync();

            var result = new List<ReferralTreeResponse>();

            foreach (var member in directReferrals)
            {
                var commissionTotal = await GetMemberCommissionAsync(member.UserZaloId);
                var childrenCount = await _context.Memberships.CountAsync(m => m.ReferrerId == member.UserZaloId);

                result.Add(new ReferralTreeResponse
                {
                    UserZaloId = member.UserZaloId,
                    UserZaloName = member.UserZaloName,
                    Avatar = member.Avatar,
                    PhoneNumber = member.PhoneNumber,
                    ReferralCode = member.ReferralCode ?? "",
                    JoinDate = member.CreatedDate,
                    Level = 1,
                    TotalCommission = commissionTotal,
                    DirectReferrals = childrenCount
                });
            }

            return result.OrderBy(r => r.JoinDate).ToList();
        }

        public async Task<List<ReferralTreeResponse>> GetReferralPathAsync(string userZaloId)
        {
            var path = new List<ReferralTreeResponse>();
            var currentMember = await _context.Memberships
                .FirstOrDefaultAsync(m => m.UserZaloId == userZaloId);

            if (currentMember == null)
                return path;

            // Đi ngược lên từ member hiện tại đến root
            var level = 0;
            while (currentMember != null)
            {
                var commissionTotal = await GetMemberCommissionAsync(currentMember.UserZaloId);

                path.Insert(0, new ReferralTreeResponse
                {
                    UserZaloId = currentMember.UserZaloId,
                    UserZaloName = currentMember.UserZaloName,
                    Avatar = currentMember.Avatar,
                    PhoneNumber = currentMember.PhoneNumber,
                    ReferralCode = currentMember.ReferralCode ?? "",
                    JoinDate = currentMember.CreatedDate,
                    Level = level,
                    TotalCommission = commissionTotal
                });

                if (string.IsNullOrEmpty(currentMember.ReferrerId))
                    break;

                currentMember = await _context.Memberships
                    .FirstOrDefaultAsync(m => m.UserZaloId == currentMember.ReferrerId);
                level++;
            }

            // Đảo ngược level để root có level 0
            for (int i = 0; i < path.Count; i++)
            {
                path[i].Level = path.Count - 1 - i;
            }

            return path;
        }

        #region Private Methods

        private async Task BuildReferralTreeRecursive(ReferralTreeResponse parentNode, int currentLevel, int maxDepth)
        {
            if (currentLevel > maxDepth)
                return;

            var children = await _context.Memberships
                .Where(m => m.ReferrerId == parentNode.UserZaloId)
                .ToListAsync();

            foreach (var child in children)
            {
                var commissionTotal = await GetMemberCommissionAsync(child.UserZaloId);

                var childNode = new ReferralTreeResponse
                {
                    UserZaloId = child.UserZaloId,
                    UserZaloName = child.UserZaloName,
                    Avatar = child.Avatar,
                    PhoneNumber = child.PhoneNumber,
                    ReferralCode = child.ReferralCode ?? "",
                    JoinDate = child.CreatedDate,
                    Level = currentLevel,
                    TotalCommission = commissionTotal,
                    Children = new List<ReferralTreeResponse>()
                };

                parentNode.Children.Add(childNode);
                await BuildReferralTreeRecursive(childNode, currentLevel + 1, maxDepth);
            }

            // Sắp xếp children theo ngày tham gia
            parentNode.Children = parentNode.Children.OrderBy(c => c.JoinDate).ToList();
        }

        private void CalculateReferralCounts(ReferralTreeResponse node)
        {
            var allDescendants = GetAllNodesFromTree(node).Skip(1).ToList(); // Bỏ qua chính node đó

            node.TotalReferrals = allDescendants.Count;
            node.DirectReferrals = node.Children.Count;

            foreach (var child in node.Children)
            {
                CalculateReferralCounts(child);
            }
        }

        private List<ReferralTreeResponse> GetAllNodesFromTree(ReferralTreeResponse root)
        {
            var allNodes = new List<ReferralTreeResponse> { root };

            foreach (var child in root.Children)
            {
                allNodes.AddRange(GetAllNodesFromTree(child));
            }

            return allNodes;
        }

        private int GetMaxDepth(ReferralTreeResponse node)
        {
            if (node.Children.Count == 0)
                return node.Level;

            return node.Children.Max(child => GetMaxDepth(child));
        }

        private async Task<decimal> GetMemberCommissionAsync(string userZaloId)
        {
            var commissionTransactions = await _context.CommissionTransactions
                .Where(ct => ct.ReferrerZaloId == userZaloId)
                .ToListAsync();

            return commissionTransactions.Sum(ct => ct.TotalCommission);
        }

        #endregion
    }
}
