﻿﻿﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON><PERSON> s<PERSON>ch sự kiện</h4>
            </div>
            <button onclick="GetFormEvent('')" type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#modal-event">
                <i class="las la-plus mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">T<PERSON><PERSON> kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-3">
                        <label for="filter-status">Trạng thái sự kiện</label>
                        <select id="filter-status" class="form-control">
                            <option value="0">Tất cả</option>
                            <option value="1">Sắp diễn ra</option>
                            <option value="2">Đang diễn ra</option>
                            <option value="3">Đã kết thúc</option>
                        </select>
                    </div>
                    <div class="col-3">
                        <label for="filter-active-status">Trạng thái hoạt động</label>
                        <select id="filter-active-status" class="form-control">
                            <option value="0">Tất cả</option>
                            <option value="1">Đang hoạt động</option>
                            <option value="2">Ngưng hoạt động</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="event-table" class="table-responsive rounded mb-3">
            <table id="list-event" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-event" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade" style="overflow-y:scroll;">
    <div id="modal-content" class="modal-dialog modal-xl" style="width: 90vw"></div>
</div>

@section Scripts {
    <script src="~/js/event.js"></script>
    <script>
        const handler = new EventFormHandler();

        $(document).ready(function () {
            GetListEvent();

            $('#search').on('input', () => {
                table.ajax.reload();
            });
            $('#filter-status').on('change', function () {
                table.ajax.reload();
            });
            $('#filter-active-status').on('change', function () {
                table.ajax.reload();
            });
        });

        function GetListEvent() {
            table = new DataTable("#list-event", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const keyword = $("#search").val();
                    const status = $("#filter-status").val();
                    const activeStatus = $("#filter-active-status").val();

                    $.ajax({
                        url: '@Url.Action("GetAll", "Events")',
                        type: 'GET',
                        data: {
                            page: page,
                            pageSize: data.length,
                            keyword: keyword,
                            status: status || 0,
                            activeStatus: activeStatus || 0
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                rowNum: data.start + index + 1,
                                title: item.title,
                                content: item.content || '',
                                time: item.startTime && item.endTime ? `${FormatDateTime(item.startTime)} - ${FormatDateTime(item.endTime)}` : '',
                                status: GetStatusBadge(item.status),
                                // active: GetActiveBadge(item.isActive),
                                createdDate: FormatDate(item.createdDate || item.startTime),
                                actions: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                <a onclick="GetFormEvent('${item.id}')" class="badge badge-info mx-1" data-toggle="tooltip" data-placement="top" title="Chi tiết">
                                                    <i class="ri-pencil-line fs-6 mr-0"></i>
                                                </a>
                                                <a href="@Url.Action("Participants", "Event")/${item.id}" class="badge badge-success mx-1" data-toggle="tooltip" data-placement="top" title="Danh sách người tham gia">
                                                    <i class="ri-group-line fs-6 mr-0"></i>
                                                </a>
                                                <a onclick="DeleteEvent('${item.id}')" class="badge bg-warning mx-1" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                    <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                </a>
                                            </div>`,
                                banner: item.banner
                                    ? `<img src="${item.banner}" style="height:50px; object-fit:cover; border-radius:6px;" alt="banner">`
                                    : '<span class="text-muted">Không có</span>',
                                active: item.isActive ?
                                    `<div class="m-auto bg-success circle-active" title="Đang hoạt động"></div>` :
                                    `<div class="m-auto circle-inactive" title="Ngưng hoạt động"></div>`
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: "rowNum", className: 'text-center' },
                    { title: "Tiêu đề", data: "title" },
                    { title: "Hình ảnh", data: "banner", className: 'text-center' },
                    { title: "Thời gian", data: "time", className: 'text-center' },
                    { title: "Trạng thái sự kiện", data: "status", className: 'text-center' },
                    { title: "Trạng thái", data: "active", className: 'text-center' },
                    { title: "Thao tác", data: "actions", className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function GetStatusBadge(status) {
            switch (status) {
                case 1: // Upcoming
                    return '<span class="badge bg-info">Sắp diễn ra</span>';
                case 2: // Ongoing
                    return '<span class="badge bg-success">Đang diễn ra</span>';
                case 3: // Ended
                    return '<span class="badge bg-secondary">Đã kết thúc</span>';
                default:
                    return '<span class="badge bg-dark">Không xác định</span>';
            }
        }

        function GetActiveBadge(status) {
            if (status === true) {
                return '<span class="badge bg-success">Đang hoạt động</span>';
            } else {
                return '<span class="badge bg-secondary">Không hoạt động</span>';
            }
        }

        function GetFormEvent(id) {
            const url = id ? `@Url.Action("Detail", "Event")/${id}` : "@Url.Action("Create", "Event")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-event").modal("toggle");

                    if (!id) {
                        InitValidator();
                    }
                }
            })
        }

        function DeleteEvent(id) {
            if (id === '') return;
            const url = `/api/Events/${id}`
            DeleteItem(url);
        }

        function HandleSaveOrUpdate(id) {
            const formData = new FormData();
            const title = $('#title').val()?.trim();
            const content = window.editor.root.innerHTML;
            const type = parseInt($('#type').val());
            const googleMapURL = $('#googleMapURL').val()?.trim();
            const meetingLink = $('#meetingLink').val()?.trim();
            const startDate = $('#startDate').val();
            const expiryDate = $('#expiryDate').val();
            const address = $('#streetLine').val()?.trim();

            // Validate thông tin cơ bản
            if (!title || !startDate || !expiryDate || !address) {
                AlertResponse('Vui lòng điền đầy đủ thông tin bắt buộc.', 'warning');
                return;
            }

            if ((type === 1 && !meetingLink) && (type === 2 && !googleMapURL)) {
                AlertResponse('Vui lòng nhập đường dẫn tham dự nếu sự kiện Online hoặc đường dẫn Google Map nếu sự kiện Offline.', 'warning');
                return;
            }
            else if (type === 1 && !meetingLink) {
                AlertResponse('Vui lòng nhập đường dẫn tham dự nếu sự kiện Online.', 'warning');
                return;
            }
            else if (type === 2 && !googleMapURL) {
                AlertResponse('Vui lòng nhập đường dẫn Google Map nếu sự kiện Offline.', 'warning');
                return;
            }

            if (new Date(startDate) >= new Date(expiryDate)) {
                AlertResponse('Thời gian kết thúc phải sau thời gian bắt đầu.', 'warning');
                return;
            }

            // Validate ảnh (images)
            const oldImages = $('#preview-upload-images .image-preview img').map(function () {
                return $(this).attr('src');
            }).get();

            const newImages = $('#images')[0]?.files;
            if (oldImages.length === 0 && (!newImages || newImages.length === 0)) {
                AlertResponse('Vui lòng tải lên ít nhất một ảnh sự kiện.', 'warning');
                return;
            }

            // Xác định banner nếu không có => dùng ảnh đầu tiên
            let bannerFile = $('#bannerImage')[0]?.files[0];
            const hasBanner = $('#banner-preview-img').length && $('#banner-preview-img').attr('src')?.indexOf('default') === -1;

            if (!bannerFile && !hasBanner) {
                if (newImages?.length > 0) {
                    formData.append('Banner', newImages[0]);
                } else if (oldImages.length > 0) {
                    // Sử dụng ảnh cũ đầu tiên làm banner nếu đang cập nhật
                    formData.append('BannerUrl', oldImages[0]);
                } else {
                    AlertResponse('Không tìm thấy ảnh banner hợp lệ.', 'warning');
                    return;
                }
            } else if (bannerFile) {
                formData.append('Banner', bannerFile);
            }

            // Quà tặng
            const gifts = [];
            let hasInvalidQuantity = false;
            $('.gift-quantity').each(function () {
                const quantity = parseInt($(this).val());
                const productId = $(this).data('id');
                if (!quantity || quantity <= 0) {
                    hasInvalidQuantity = true;
                } else {
                    gifts.push({ productId, quantity });
                }
            });

            // Nhà tài trợ
            const sponsors = [];
            $('.sponsor-tier-select').each(function () {
                const sponsorId = $(this).data('sponsor-id');
                const tierId = $(this).val();
                if (sponsorId && tierId) {
                    sponsors.push({ sponsorId, tierId });
                }
            });
            if (sponsors.length === 0) {
                AlertResponse('Phải chọn ít nhất một nhà tài trợ và chọn hạng tài trợ tương ứng.', 'warning');
                return;
            }

            // Add fields
            formData.append('Id', id || '');
            formData.append('Title', title);
            formData.append('Content', content);
            formData.append('Type', type);
            formData.append('GoogleMapURL', googleMapURL);
            formData.append('MeetingLink', meetingLink);
            formData.append('StartTime', startDate);
            formData.append('EndTime', expiryDate);
            formData.append('Address', address);
            formData.append('IsActive', true);
            formData.append('GiftData', JSON.stringify(gifts));
            formData.append('SponsorData', JSON.stringify(sponsors));

            // Add images
            if (newImages?.length) {
                for (let i = 0; i < newImages.length; i++) {
                    formData.append('Images', newImages[i]);
                }
            }

            const url = id ? `/api/events/${id}` : '/api/events';
            const method = id ? 'PUT' : 'POST';

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success');
                        table.ajax.reload(null, false);
                    } else {
                        AlertResponse(response.message, 'warning');
                    }
                    $('#modal-event').modal('toggle');
                },
                error: function () {
                    AlertResponse('Lỗi máy chủ, vui lòng thử lại sau!', 'error');
                }
            });
        }
    </script>
}

