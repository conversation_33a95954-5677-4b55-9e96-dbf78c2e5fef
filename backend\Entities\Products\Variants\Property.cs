﻿namespace MiniAppCore.Entities.Products.Variants
{
    public class Property
    {
        public string Id { get; set; } = Guid.NewGuid().ToString("N");
        public required string Name { get; set; }
        public string? Description { get; set; }
        public bool IsMultipleChoice { get; set; } = false;
        public int MaxSelection { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}
