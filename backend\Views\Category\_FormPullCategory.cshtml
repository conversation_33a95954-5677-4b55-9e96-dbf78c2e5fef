﻿<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">Thê<PERSON> sản phẩm v<PERSON>o kho</h5>
        <button type="button" class="close" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <div class="col-md-12">
                <div class="form-group">
                    <label>Chọn danh mục</label>
                    <select id="categories" class="form-control" required multiple></select>
                    <div class="help-block with-errors"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdateBranchCategories()"><PERSON><PERSON><PERSON></button>
    </div>
</div>
<script>
    $(document).ready(function() {
        $("#categories").select2({
            placeholder: 'Chọn sản phẩm',
            allowClear: false,
            minimumInputLength:0 ,
            dropdownParent: $("#modal-pullCategory"),
            ajax: {
                url: '@Url.Action("GetAll", "Categories")' + "?isGetAll=false&excludeAlreadyItems=true",
                dataType: 'json',
                delay: 250, // Thời gian trễ khi gõ tìm kiếm
                data: function (params) {
                    return {
                        keyword: params.term, // Keyword tìm kiếm
                        page: params.page || 1,
                        pageSize: 10
                    };
                },
                processResults: function (data, params) {
                    params.page = params?.page || 1;
                    // Map data
                    const mappedData = data.data.map(function (item) {
                        return {
                            id: item['id'],
                            text: item['name']
                        };
                    });

                    return {
                        results: mappedData,
                        pagination: {
                            more: (params.page * (10)) < (data.totalItems || 0)
                        }
                    };
                },
                cache: true,
            }
        });
    });
</script>