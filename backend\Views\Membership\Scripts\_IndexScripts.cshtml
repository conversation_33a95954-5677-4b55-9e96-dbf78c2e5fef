@using Newtonsoft.Json

<script>
    let ranks = @Html.Raw(JsonConvert.SerializeObject(Model)) || [];

    $(document).ready(function () {
        $("#filterStart").val(moment().startOf('year').format('YYYY-MM-DD'));
        $("#filterEnd").val(moment().format('YYYY-MM-DD'));

        $("#startExcel").val(moment(new Date()).format('YYYY-MM-DD'));
        $("#endExcel").val(moment(new Date()).add(30, 'days').format('YYYY-MM-DD'));

        GetListMembership();
        $('#filter-type').on('change', function () {
            table.ajax.reload(null, false);
        });
        $("#tagsFilter").select2();
        $('#search').on('input', search);
    });

    function GetListMembership() {
        table = new DataTable("#list-membership", {
            searching: false,
            sort: false,
            responsive: true,
            pageLength: 10,
            serverSide: true,
            ajax: function (data, callback, settings) {
                const page = (data.start / data.length) + 1;
                const type = $("#filter-type").val();
                const keyword = $("#search").val();
                const startDate = moment($("#filterStart").val()).format('YYYY-MM-DDT00:00');
                const endDate = moment($("#filterEnd").val()).format('YYYY-MM-DDT23:59');

                $.ajax({
                    url: '@Url.Action("GetPage", "Memberships")',
                    type: 'GET',
                    data: {
                        page: page,
                        pagesize: data.length,
                        sort: data.order[0]?.dir || 'asc',
                        keyword: keyword,
                        startDate,
                        endDate
                    },
                    success: function (response) {
                        const formattedData = response.data.map((item, index) => {
                            const rankLabel = ranks?.find(rank => rank.Id === item.rankingId)?.Name || "Không có dữ liệu";

                            return {
                                stt: data.start + index + 1, // Số thứ tự (STT)
                                userInfo: `<div class="d-flex align-items-center">
                                                                    <img src="${item.avatar || '/images/no-image-1.png'}" class="img-fluid rounded avatar-50 mr-3" alt="image" />
                                                                    <div>
                                                                        ${item.userZaloName || 'Chưa cập nhật'}
                                                                        <p class="mb-0"><small>${item.phoneNumber || 'Chưa cập nhật'}</small></p>
                                                                    </div>
                                                                </div>`, // Thông tin thành viên
                                dob: item.dateOfBirth ? FormatDate(item.dateOfBirth) : 'Chưa cập nhật', // Ngày sinh
                                points: item.rankingPoint || 0, // Điểm
                                rank: rankLabel, // Tên hạng thành viên (label rank)
                                source: item.source, // Tên hạng thành viên (label rank)
                                actions: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                                    <a onclick="GetFormMembership('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                                        <i class="ri-edit-line fs-6 mr-0"></i>
                                                                    </a>
                                                                    <a onclick="ViewReferralTree('${item.userZaloId}','${item.userZaloName}','${item.phoneNumber}')" class="badge bg-primary" data-toggle="tooltip" data-placement="top" title="Xem cây đa cấp">
                                                                        <i class="ri-node-tree fs-6 mr-0"></i>
                                                                    </a>
                                                                    <a class="d-none" onclick="ViewRefV2('${item.userZaloId}','${item.userZaloName}','${item.phoneNumber}')" class="badge bg-success" data-toggle="tooltip" data-placement="top" title="Xem người giới thiệu">
                                                                        <i class="ri-team-line fs-6 mr-0"></i>
                                                                    </a>
                                                                    <a onclick="DeleteMembership('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                                        <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                                    </a>
                                                                    <a onclick="ViewOrders('${item.id}', '${item.phoneNumber || ''}')" class="badge bg-danger" data-toggle="tooltip" data-placement="top" title="Lịch sử mua hàng">
                                                                        <i class="ri-shopping-cart-line fs-6 mr-0"></i>
                                                                    </a>
                                                                </div>` // Thao tác
                            };
                        });

                        setTimeout(() => {
                            callback({
                                draw: data.draw,
                                recordsTotal: response.data.length,
                                recordsFiltered: response.totalPages * data.length || 1,
                                data: formattedData
                            });
                        }, 400)
                    }
                });
            },
            columns: [
                { title: "STT", data: "stt", className: 'text-center' },
                { title: "Thông tin khách hàng", data: "userInfo" },
                { title: "Sinh nhật", data: "dob", className: 'text-center' },
                { title: "Nguồn", data: "source", className: 'text-center' },
                { title: "Điểm", data: "points", className: 'text-center' },
                { title: "Hạng", data: "rank", className: 'text-center' },
                { title: "Thao tác", data: "actions" }
            ],
            language: {
                "lengthMenu": "Hiển thị _MENU_ khách hàng mỗi trang",
                "zeroRecords": "Không tìm thấy khách hàng nào",
                "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ khách hàng",
                "infoEmpty": "Không có khách hàng nào",
                "infoFiltered": "(lọc từ tổng số _MAX_ khách hàng)"
            }
        });

        $(table.table().header()).addClass('ligth ligth-data');
    }

    function GetFormMembership(id) {
        if (!id) {
            AlertResponse("Thao tác này chưa hỗ trợ", 'info');
            return;
        }

        const url = `/Membership/${id}`;
        $.ajax({
            url: url,
            type: 'GET',
            success: function (data) {
                $("#modal-content").html(data);
                $("#modal-membership").modal("toggle");
            }
        })
    }

    function DeleteMembership(id) {
        const url = `/api/memberships/${id}`
        DeleteItem(url);
    }

    function HandleSaveOrUpdate(id) {
        var listMembershipExtend = [];
        if (lstMbEx.length > 0) {
            listMembershipExtend = lstMbEx.map(item => {
                var obj = {
                    Id: $(`#Id_${item.Id}`).val(),
                    Attribute: $(`#Attribute_${item.Id}`).val(),
                    Content: $(`#Content_${item.Id}`).val(),
                    UserZaloId: $(`#UserZaloId_${item.Id}`).val(),
                    CreatedDate: $(`#CreatedDate_${item.Id}`).val(),
                    UpdatedDate: $(`#UpdatedDate_${item.Id}`).val(),
                }
                return obj;
            });
            var checkNull = listMembershipExtend.findIndex(item => item.Attribute == "" || item.Content == "")

            if (checkNull >= 0) {
                AlertResponse("Với các thông tin thêm bạn vui lòng nhập đầy đủ thông tin", 'error')
                return;
            }
        }

        const rankingPoint = InputValidator.parseCurrency($("#rankingPoint").val());
        const usingPoint = InputValidator.parseCurrency($("#usingPoint").val());
        const spinPoint = InputValidator.parseCurrency($("#spinPoint").val());

        const data = {
            UserZaloId: $("#UserZaloId").val()?.trim(),
            phoneNumber: $("#phoneNumber").val()?.trim(),
            displayName: $("#displayName").val()?.trim(),
            dateOfBirth: $("#dob").val() || null,
            job: $("#job").val()?.trim(),
            rankId: $("#rank").val()?.trim() || "",
            rankingPoint: rankingPoint,
            usingPoint: usingPoint,
            spinPoint: spinPoint,
            // rankingPoint: $("#rankingPoint").val()?.trim(),
            notes: $("#note").val()?.trim(),
            address: $("#address").val()?.trim(),
            listMembershipExtend: listMembershipExtend,
            tagIds: $("#tags").val(),
        };

        const url = id ? `/api/memberships/${id}` : '/api/memberships';
        const method = id ? 'PUT' : 'POST';

        $.ajax({
            url: url,
            type: method,
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function (response) {
                if (response.code === 0) {
                    AlertResponse(response.message, 'success')
                    table.ajax.reload(null, false);
                }
                $("#modal-membership").modal("toggle");
            },
            error: function (error) {
                AlertResponse("Đã xảy ra lỗi khi cập nhật!", 'error')
            }
        });
    }

    function ViewRef(id, maxDepth = 2) {
        $.ajax({
            url: `/api/memberships/tree/${id}`,
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                $("#tree-container").html(createTree(response.data, maxDepth));
                $("#modal-ref").modal('toggle');
            }
        });

        function createTree(data, maxDepth) {
            console.log(data)
            if (!data) return '';

            const hasChildren = data.refChild && data.refChild.length > 0;
            const collapseId = `collapse-${data.parentId}`;

            let html = `<li class="border-none" style="list-style:none">
                                                            <a href="#${collapseId}" class="${hasChildren ? 'collapsed' : ''} text-dark" data-bs-toggle="collapse">
                                                            ${hasChildren ? '<span class="fas fa-caret-right dropdown-indicator-icon svg-icon iq-arrow-right arrow-active"></span>' : ''} <span class="ml-4">${data.userZaloName} (${data.phoneNumber})</span></a>`;

            if (hasChildren) {
                html += `<ul id="${collapseId}" class="border-0 collapse" data-bs-parent="#${collapseId}">`;
                for (let child of data.refChild) {
                    html += createTree(child);
                }
                html += '</ul>';
            }

            html += '</li>';

            return html;
        }
    }

    function ViewRefV2(userZaloId, userZaloName, phoneNumber) {
        $.ajax({
            url: `/api/memberships/tree/${userZaloId}`,
            type: 'GET',
            dataType: 'json',
            success: function (response) {
                $("#tree-container").html(createTreeV2(response.data, {
                    userZaloId: userZaloId,
                    userZaloName: userZaloName,
                    phoneNumber: phoneNumber
                }));
                $("#modal-ref").modal('toggle');
            }
        });

        function createTreeV2(data, { userZaloId, userZaloName, phoneNumber }) {
            if (!data) return '';

            const hasChildren = data?.length > 0;
            const collapseId = `collapse-${userZaloId}`;

            let html = `<li class="border-none" style="list-style:none">
                                                    <a href="#${collapseId}" class="${hasChildren ? 'collapsed' : ''} text-dark" data-bs-toggle="collapse">
                                                                ${hasChildren ? '<span class="ri-arrow-right-s-line dropdown-indicator-icon svg-icon iq-arrow-right arrow-active"></span>' : ''}
                                                        <span class="ml-4">${userZaloName} (${phoneNumber})</span>
                                                    </a>`;
            if (hasChildren) {
                html += `<ul id="${collapseId}" class="border-0 collapse" data-bs-parent="#${collapseId}">`;
                for (const child of data) {
                    html += createTreeV2(child, {
                        userZaloId: child?.userZaloId,
                        userZaloName: child?.userZaloName,
                        phoneNumber: child?.phoneNumber
                    });
                }
                html += '</ul>';
            }

            html += '</li>';

            return html;
        }
    }
</script>
