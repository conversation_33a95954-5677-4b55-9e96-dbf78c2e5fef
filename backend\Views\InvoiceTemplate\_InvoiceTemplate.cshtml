﻿@model MiniAppCore.Entities.Orders.InvoiceTemplate;

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-md-8 border-right">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label>Tên <span class="text-danger">*</span></label>
                            <input id="name" value="@Model.Name" class="form-control" data-type="title" />
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Mẫu mặc định</label>
                            <select id="isDefault" class="form-control">
                                <option value="true" selected="@(Model.IsDefault)">Có</option>
                                <option value="false" selected="@(!Model.IsDefault)">Không</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>Nội dung <span class="text-danger">*</span></label>
                    <tinymce-editor id="content" style="min-height: 250px">@Html.Raw(Model.Content)</tinymce-editor>
                </div>

                <div class="form-group">
                    <label class="font-weight-bold">Hướng dẫn sử dụng biến động:</label>
                    <div class="border rounded p-3 bg-white" style="font-size: 14px;">
                        <p class="mb-2">
                            Bạn có thể sử dụng các biến bên dưới để hệ thống tự động thay thế bằng dữ liệu thực tế từ đơn hàng.
                        </p>

                        <strong class="d-block mb-1">Biến động từ đơn hàng:</strong>
                        <ul class="mb-3">
                            <li><code>{OrderId}</code> – Mã đơn hàng</li>
                            <li><code>{ReceiverName}</code> – Tên người nhận hàng </li>
                            <li><code>{ReceiverPhoneNumber}</code> – Số điện thoại người nhận hàng</li>
                            <li><code>{MembershipName}</code> – Tên người đặt hàng </li>
                            <li><code>{PhoneNumber}</code> – Số điện thoại người đặt hàng</li>
                            <li><code>{DeliveryAddress}</code> – Địa chỉ giao hàng</li>
                            <li><code>{Notes}</code> – Ghi chú</li>
                            <li><code>{DiscountAmount}</code> – Giảm giá</li>
                            <li><code>{ShippingFee}</code> – Phí vận chuyển</li>
                            <li><code>{Total}</code> – Tổng tiền</li>
                            <li><code>{CreatedDate}</code> – Ngày tạo đơn (dd/MM/yyyy HH:mm)</li>
                        </ul>

                        <strong class="d-block mb-1">Biến động trong bảng sản phẩm:</strong>
                        <p class="mb-1">Trong phần <code>&lt;tbody&gt;</code> của bảng sản phẩm, bạn có thể dùng:</p>
                        <ul class="mb-3">
                            <li><code>{ProductName}</code> – Tên sản phẩm</li>
                            <li><code>{ProductPrice}</code> – Đơn giá</li>
                            <li><code>{ProductQuantity}</code> – Số lượng</li>
                            <li><code>{ProductSubtotal}</code> – Thành tiền</li>
                        </ul>
                        <p class="mb-0">Hệ thống sẽ tự động lặp dòng đó với từng sản phẩm trong đơn.</p>
                    </div>
                </div>

            </div>

            <div class="col-md-4">
                <div id="preview" class="border p-3 rounded bg-white shadow" style="height: 100%; min-height: 400px; overflow-y: auto;">
                    @(!string.IsNullOrEmpty(Model.Content) ? Html.Raw(Model.Content) : "Mẫu hóa đơn")
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="submit" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        $('.carousel').carousel({
            interval: 2000
        });
        InitialEditor();
    })
</script>