﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Orders
{
    public class OrderDetailReview : BaseEntity
    {
        public required string UserZaloId { get; set; }
        public required string OrderDetailId { get; set; }
        public float ReviewPoint { get; set; }
        public string? Images { get; set; }
        public string? Videos { get; set; }
        public string? ProductId { get; set; }
        public string? ReviewContent { get; set; }
        public bool? IsShow { get; set; } = true;
    }
}
