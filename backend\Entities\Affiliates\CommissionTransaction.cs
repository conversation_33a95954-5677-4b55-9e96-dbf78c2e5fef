using MiniAppCore.Entities.Commons;
using System.ComponentModel.DataAnnotations.Schema;

namespace MiniAppCore.Entities.Affiliates
{
    public class CommissionTransaction : BaseEntity
    {
        public string? ReferrerZaloId { get; set; } // Người giới thiệu
        public string? ReferrerCode { get; set; } // Mã giới thiệu

        public string? ReferredZaloId { get; set; } // Người được giới thiệu (người mua)
        public string? ReferredName { get; set; }
        public string? ReferredAvatar { get; set; }
        public string? ReferredPhone { get; set; }

        public string? OrderId { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal TotalOrder { get; set; }

        [Column(TypeName = "decimal(18,6)")]
        public decimal CommissionRate { get; set; }

        [Column(TypeName = "decimal(18,4)")]
        public decimal TotalCommission { get; set; }

        public DateTime OrderDate { get; set; }
        public bool IsPaid { get; set; } = false; // Tr<PERSON>ng thái thanh toán hoa hồng
    }
}