﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.InvoiceTemplates;
using MiniAppCore.Services.InvoiceTemplates;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class InvoiceTemplatesController(ILogger<InvoiceTemplatesController> logger, IInvoiceTemplateService invoiceTemplateService) : ControllerBase
    {
        [HttpGet]
        public async Task<IActionResult> GetPage([FromQuery] RequestQuery query)
        {
            try
            {
                var templates = await invoiceTemplateService.GetPage(query);

                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    templates.Data,
                    templates.TotalPages
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }

        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] InvoiceTemplateDTO model)
        {
            try
            {
                var result = await invoiceTemplateService.CreateAsync(model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo mẫu hóa đơn thành công!",
                    Data = result
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetById(string id)
        {
            try
            {
                var template = await invoiceTemplateService.GetByIdAsync(id);
                if (template == null)
                {
                    return NotFound();
                }
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = template
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] InvoiceTemplateDTO model)
        {
            try
            {
                await invoiceTemplateService.UpdateAsync(id, model);

                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật mẫu hóa đơn thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                await invoiceTemplateService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa mẫu hóa đơn thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error!"
                });
            }
        }
    }
}
