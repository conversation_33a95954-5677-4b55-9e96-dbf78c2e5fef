﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Services.Affiliates.Commissions;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class AffiliateController(ICommissionService commissionService) : Controller
    {
        public async Task<IActionResult> Index()
        {
            var percentage = await commissionService.GetCommissionConfigAsync();
            ViewBag.CommissionRate = percentage;
            return View();
        }
    }
}
