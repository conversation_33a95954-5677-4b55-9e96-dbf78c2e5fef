﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using MiniAppCore.Entities.Affiliates;
using MiniAppCore.Entities.Articles;
using MiniAppCore.Entities.Bookings;
using MiniAppCore.Entities.Branches;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Entities.Dashboards;
using MiniAppCore.Entities.ETM;
using MiniAppCore.Entities.Events;
using MiniAppCore.Entities.Events.Sponsors;
using MiniAppCore.Entities.Events.SponsorshipTiers;
using MiniAppCore.Entities.FormCustoms;
using MiniAppCore.Entities.LuckyWheels;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Notifications;
using MiniAppCore.Entities.Offers;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Entities.Offers.Vouchers;
using MiniAppCore.Entities.OmniTool;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Entities.Products;
using MiniAppCore.Entities.Products.ProductInfo;
using MiniAppCore.Entities.Products.Variants;
using MiniAppCore.Entities.Settings;
using MiniAppCore.Entities.Surveys;

namespace MiniAppCore.Base.Database
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {

        public virtual DbSet<Entities.Commons.Common> Commons { get; set; }
        public virtual DbSet<ActionButtonConfig> ActionButtonConfigs { get; set; }

        public virtual DbSet<ViewPermission> ViewPermissions { get; set; }
        public virtual DbSet<RecommendSetting> RecommendSettings { get; set; }

        public virtual DbSet<CustomField> CustomFields { get; set; }
        public virtual DbSet<CustomFieldValue> CustomFieldValues { get; set; }


        #region Offers 

        public virtual DbSet<Voucher> Vouchers { get; set; }
        public virtual DbSet<VoucherProduct> VoucherProducts { get; set; }

        public virtual DbSet<Discount> Discounts { get; set; }
        public virtual DbSet<DiscountItem> DiscountItems { get; set; }

        public virtual DbSet<Promotion> Promotions { get; set; }
        public virtual DbSet<PromotionItem> PromotionItems { get; set; }

        #endregion

        #region Branches 

        public virtual DbSet<Branch> Branches { get; set; }
        //public virtual DbSet<BranchCategory> BranchCategories { get; set; }

        #endregion

        #region Articles

        public virtual DbSet<Article> Articles { get; set; }
        public virtual DbSet<ArticleCategory> ArticleCategories { get; set; }

        #endregion

        #region Affiliates

        public virtual DbSet<CommissionTransaction> CommissionTransactions { get; set; }
        public virtual DbSet<AffiliateCommissionRate> AffiliateCommissionRates { get; set; }

        #endregion

        #region Memberships

        public virtual DbSet<Tag> Tags { get; set; }
        public virtual DbSet<Rank> Ranks { get; set; }
        public virtual DbSet<PointHistory> PointHistories { get; set; }

        public virtual DbSet<Membership> Memberships { get; set; }
        public virtual DbSet<MembershipTag> MembershipTags { get; set; }
        public virtual DbSet<MembershipExtend> MembershipExtends { get; set; }
        public virtual DbSet<MembershipVoucher> MembershipVouchers { get; set; }
        public virtual DbSet<MembershipAddress> MembershipAddresses { get; set; }
        public virtual DbSet<MembershipExtendDefault> MembershipExtendDefaults { get; set; }
        public virtual DbSet<MembershipVAT> MembershipVATs { get; set; }

        // Form Custom
        public virtual DbSet<CustomForm> CustomForms { get; set; }
        public virtual DbSet<CustomFormAttribute> CustomFormAttributes { get; set; }

        #endregion

        #region Products & Categories

        public virtual DbSet<Product> Products { get; set; }
        public virtual DbSet<Category> Categories { get; set; }
        public virtual DbSet<CategoryChild> CategoryChilds { get; set; }
        public virtual DbSet<ProductStock> ProductStocks { get; set; }
        public virtual DbSet<ProductCategory> ProductCategories { get; set; }
        public virtual DbSet<ProductCategoryChild> ProductCategoryChilds { get; set; }

        // Product variants
        public virtual DbSet<Property> Properties { get; set; }
        public virtual DbSet<PropertyValue> PropertyValues { get; set; }

        public virtual DbSet<Variant> Variants { get; set; }
        public virtual DbSet<VariantValue> VariantValues { get; set; }

        #endregion

        #region Carts & Orders

        public virtual DbSet<Cart> Carts { get; set; }
        public virtual DbSet<Order> Orders { get; set; }
        public virtual DbSet<OrderDetail> OrderDetails { get; set; }
        public virtual DbSet<OrderVoucher> OrderVouchers { get; set; }
        public virtual DbSet<InvoiceTemplate> InvoiceTemplates { get; set; }
        public virtual DbSet<OrderDetailGift> OrderDetailGifts { get; set; }
        public virtual DbSet<OrderDetailReview> OrderDetailReviews { get; set; }
        public virtual DbSet<ShippingFeeConfig> ShippingFeeConfigs { get; set; }

        #endregion

        #region Bookings & Booking Items

        public virtual DbSet<Booking> Bookings { get; set; }
        public virtual DbSet<BookingItem> BookingItems { get; set; }
        public virtual DbSet<BookingDetail> BookingDetails { get; set; }

        #endregion

        #region Lucky Wheel & Spin History

        public virtual DbSet<BlackList> BlackLists { get; set; }
        public virtual DbSet<GamePrize> GamePrizes { get; set; }
        public virtual DbSet<LuckyWheel> LuckyWheels { get; set; }
        public virtual DbSet<SpinHistory> SpinHistories { get; set; }
        public virtual DbSet<GamePrizeConfig> GamePrizeConfigs { get; set; }

        #endregion

        #region Statistic Table

        public virtual DbSet<StatisticOrder> StatisticOrders { get; set; }
        public virtual DbSet<StatisticBooking> StatisticBookings { get; set; }
        public virtual DbSet<StatisticVoucher> StatisticVouchers { get; set; }
        public virtual DbSet<StatisticMembership> StatisticMemberships { get; set; }
        public virtual DbSet<StatisticTopProducts> StatisticTopProducts { get; set; }
        public virtual DbSet<StatisticTopBookingItem> StatisticTopBookingItems { get; set; }

        #endregion

        #region Omni Mini Tool & Even Trigger Messaging

        public virtual DbSet<WebHookLogs> WebHookLogs { get; set; }
        public virtual DbSet<CampaignTag> CampaignTags { get; set; }
        public virtual DbSet<CampaignCSKH> CampaignCSKHs { get; set; }
        public virtual DbSet<CampaignConfig> CampaignConfigs { get; set; }
        public virtual DbSet<CampaignPhoneCSKH> CampaignPhoneCSKHs { get; set; }
        public virtual DbSet<CampaignPhoneCSKHTemp> CampaignPhoneCSKHTemps { get; set; }

        public virtual DbSet<EventLog> EventLogs { get; set; }
        public virtual DbSet<EventTemplate> EventTemplates { get; set; } // lưu cấu hình chung của thằng Event Template
        public virtual DbSet<EventTemplateLog> EventTemplateLogs { get; set; } // Log event template đã gửi đi

        public virtual DbSet<OmniTemplateConfig> OmniTemplateConfigs { get; set; } // config riêng dành cho Omni
        public virtual DbSet<ZaloTemplateConfig> ZaloTemplateConfigs { get; set; } // config riêng dành cho UID

        public virtual DbSet<ZaloTemplateUid> ZaloTemplateUids { get; set; } // Lưu mẫu template dành cho UID

        public virtual DbSet<CampaignItem> CampaignItems { get; set; } // Voucher Code

        public DbSet<EventTriggerLog> EventTriggerLogs { get; set; }
        public DbSet<EventTriggerSetting> EventTriggerSettings { get; set; }
        public DbSet<Entities.Notifications.Templates.OmniTemplate> OmniTemplates { get; set; }

        #endregion

        #region Survey

        public virtual DbSet<Survey> Surveys { get; set; }
        public virtual DbSet<SurveySection> SurveySections { get; set; }

        public virtual DbSet<SurveyAnswer> SurveyAnswers { get; set; }
        public virtual DbSet<SurveyQuestion> SurveyQuestions { get; set; }
        public virtual DbSet<SurveyLikertQuestion> SurveyLikertQuestions { get; set; }


        // response người dùng
        public virtual DbSet<SurveySubmission> SurveySubmissions { get; set; }
        public virtual DbSet<SurveySubmissionDetail> SurveySubmissionDetails { get; set; }

        #endregion

        #region Event

        public virtual DbSet<Event> Events { get; set; }
        public virtual DbSet<EventGift> EventGifts { get; set; }
        public virtual DbSet<EventRegistration> EventRegistrations { get; set; }
        public virtual DbSet<EventSponsor> EventSponsors { get; set; }
        public virtual DbSet<Sponsor> Sponsors { get; set; }
        public virtual DbSet<SponsorshipTier> SponsorshipTiers { get; set; }

        #endregion

        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {

        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            optionsBuilder.ConfigureWarnings(warnings => warnings.Ignore(RelationalEventId.PendingModelChangesWarning));
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            //modelBuilder.Entity<IdentityUserRole<string>>().HasKey(iur => new { iur.UserId, iur.RoleId });
            //UserAdmin.Seed(modelBuilder);
            //BaseSettings.Seed(modelBuilder);
        }
    }
}
