@using Newtonsoft.Json

<script>
    // =====================MODULE IMPORT========================
    function GetFormImportMembership() {
        $("#modal-import-membership").modal("show");
    }

    let membershipsList = [];

    function HandleUpload() {
        const fileInput = document.getElementById('import-membership-input');
        const file = fileInput.files[0];

        if (!file) {
            AlertResponse("Vui lòng chọn file Excel để tải lên!", "error");
            return;
        }

        const formData = new FormData();
        formData.append('file', file);

        // Show loading
        LoadingSpinner(true, "Đang xử lý file...");

        $.ajax({
            url: '/api/memberships/import',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function (response) {
                LoadingSpinner(false);
                if (response.success) {
                    membershipsList = response.data;
                    RenderMembershipTable(membershipsList);
                    AlertResponse("Tải file thành công! H<PERSON>y kiểm tra dữ liệu trước khi nhập.", "success");
                } else {
                    AlertResponse(response.message || "Đã xảy ra lỗi khi xử lý file!", "error");
                }
            },
            error: function (xhr) {
                LoadingSpinner(false);
                const errorResponse = xhr.responseJSON;
                if (errorResponse && errorResponse.errors) {
                    // Display validation errors
                    let errorHtml = "<ul>";
                    errorResponse.errors.forEach(error => {
                        errorHtml += `<li>${error}</li>`;
                    });
                    errorHtml += "</ul>";
                    $("#error-messages").html(errorHtml);
                    $("#error-modal").modal("show");
                } else {
                    AlertResponse("Đã xảy ra lỗi khi tải lên file!", "error");
                }
            }
        });
    }

    function RenderMembershipTable(data) {
        if (!data || data.length === 0) {
            $("#import-preview").html('<div class="alert alert-warning">Không có dữ liệu để hiển thị</div>');
            return;
        }

        let tableHtml = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>STT</th>
                                <th>Tên</th>
                                <th>Số điện thoại</th>
                                <th>Sinh nhật</th>
                                <th>Nghề nghiệp</th>
                                <th>Hạng</th>
                                <th>Điểm</th>
                                <th>Địa chỉ</th>
                                <th>Ghi chú</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

        data.forEach((member, index) => {
            tableHtml += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${member.displayName || ''}</td>
                        <td>${member.phoneNumber || ''}</td>
                        <td>${member.dateOfBirth ? FormatDate(member.dateOfBirth) : ''}</td>
                        <td>${member.job || ''}</td>
                        <td>${member.rank || ''}</td>
                        <td>${member.points || 0}</td>
                        <td>${member.address || ''}</td>
                        <td>${member.notes || ''}</td>
                    </tr>
                `;
        });

        tableHtml += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="ImportMemberships()">
                        Xác nhận nhập ${data.length} thành viên
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                </div>
            `;

        $("#import-preview").html(tableHtml);
    }

    function ImportMemberships() {
        if (!membershipsList || membershipsList.length === 0) {
            AlertResponse("Không có dữ liệu để nhập!", "error");
            return;
        }

        LoadingSpinner(true, "Đang nhập dữ liệu...");

        $.ajax({
            url: '/api/memberships/import-confirm',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(membershipsList),
            success: function (response) {
                LoadingSpinner(false);
                if (response.success) {
                    AlertResponse(`Nhập thành công ${response.successCount} thành viên!`, "success");
                    $("#modal-import-membership").modal("hide");
                    table.ajax.reload(null, false);
                    membershipsList = [];
                } else {
                    AlertResponse(response.message || "Đã xảy ra lỗi khi nhập dữ liệu!", "error");
                }
            },
            error: function (xhr) {
                LoadingSpinner(false);
                AlertResponse("Đã xảy ra lỗi khi nhập dữ liệu!", "error");
            }
        });
    }

    function HandleExportData() {
        const startDate = $("#startExcel").val();
        const endDate = $("#endExcel").val();

        if (!startDate || !endDate) {
            AlertResponse("Vui lòng chọn ngày bắt đầu và ngày kết thúc", "error");
            return;
        }

        if (moment(startDate).isAfter(moment(endDate))) {
            AlertResponse("Ngày bắt đầu phải nhỏ hơn ngày kết thúc", "error");
            return;
        }

        const data = {
            startDate: moment(startDate).format('YYYY-MM-DDTHH:mm:ss'),
            endDate: moment(endDate).format('YYYY-MM-DDTHH:mm:ss')
        };

        $.ajax({
            url: '/api/memberships/export',
            contentType: 'application/json',
            type: 'POST',
            data: JSON.stringify(data),
            xhrFields: {
                responseType: 'blob' // Để nhận file từ server
            },
            success: function (response, status, xhr) {
                // Lấy tên file từ header response
                const timestamp = moment().format("YYYYMMDD_HHmmss");
                const fileName = `Thanhvien_${timestamp}.xlsx`;
                // Tạo đường dẫn tải file
                const url = window.URL.createObjectURL(new Blob([response]));
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                a.remove();
                AlertResponse("Xuất file thành công!", "success");
                $("#modal-excel-export").modal("toggle");
            },
            error: function (xhr) {
                console.log(xhr)
                AlertResponse(xhr.responseJSON?.message || "Đã xảy ra lỗi. Vui lòng thử lại sau.", "error");
            },
            complete: function () {
                $("#modal-export").modal("toggle");
            }
        });
    }
</script>
