﻿using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.SystemConfiguration
{
    public class EmployeeBranch
    {
        [Key]
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public required string BranchId { get; set; }
        public required string EmployeeId { get; set; }
        public required string ViewPermissions { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime UpdateDate { get; set; } = DateTime.Now;
    }
}
