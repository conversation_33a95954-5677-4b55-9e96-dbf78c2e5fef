{"name": "egalink-toasty.js", "title": "Toasty.js", "description": "A minimal JavaScript notification plugin that provides a simple way to display customizable toast messages on the web page with CSS3 transition effects.", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "http://jakim.me/"}, "homepage": "https://jakim.me/Toasty.js/", "version": "1.5.5", "main": "dist/toasty.js", "devDependencies": {"grunt": "^1.0.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-uglify": "^2.0.0", "grunt-contrib-watch": "^1.0.0", "grunt-cssnano": "^2.1.0", "grunt-postcss": "^0.8.0", "grunt-replace": "^1.0.1", "postcss-cssnext": "^2.8.0", "postcss-remove-root": "0.0.2", "stylefmt": "^4.3.1"}, "repository": {"type": "git", "url": "git+https://github.com/egalink/Toasty.js.git"}, "keywords": ["minimal", "javascript", "plugin", "simple", "way", "display", "customizable", "toast", "messages", "alerts"], "license": "MIT", "bugs": {"url": "https://github.com/egalink/Toasty.js/issues"}}