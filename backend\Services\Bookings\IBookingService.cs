﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Bookings;
using MiniAppCore.Enums;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Bookings;
using MiniAppCore.Models.Responses.Bookings;

namespace MiniAppCore.Services.Bookings
{
    public interface IBookingService : IService<Booking>
    {
        Task<int> UpdateAsync(string id, BookingRequest dto);
        Task<int> UpdateStatusBooking(string id, EBooking newStatus, string? reason = null);
        Task<string> CreateAsync(string userZaloId, BookingRequest dto);

        Task<BookingDetailResponse> GetBookingDetailResponseAsync(string bookingId);
        Task<PagedResult<BookingResponse>> GetPaged(PurchaseQueryParams queryParams, string? userZaloId = null);

        Task<byte[]> ExportBookings(DateTime? startDate, DateTime? endDate, short? bookingStatus);
    }
}