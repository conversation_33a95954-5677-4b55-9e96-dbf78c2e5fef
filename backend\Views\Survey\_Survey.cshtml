@{
    ViewData["Title"] = Model != null ? "Cập nhật khảo sát" : "Thêm mới khảo sát";
}
@model MiniAppCore.Models.DTOs.Surveys.SurveyDTO;

<link href="~/css/survey.css" rel="stylesheet" />

<div class="container-fluid survey-form-container">
    <div class="bg-white shadow rounded">
        <div class="p-3 border-bottom bg-light">
            <div class="d-flex flex-wrap align-items-center justify-content-between">
                <h4 class="mb-0"><i class="ri-survey-line me-2 text-primary"></i>@ViewData["Title"]</h4>
                <a href="@Url.Action("Index", "Survey")" class="btn btn-outline-secondary">
                    <i class="ri-arrow-left-line me-1"></i> Quay lại danh sách
                </a>
            </div>
        </div>
        <div class="p-4">
            <!-- Survey general information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="mb-0 text-primary"><i class="ri-information-line me-2"></i>Thông tin khảo sát</h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold">Tiêu đề khảo sát <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" placeholder="Nhập tiêu đề khảo sát" value="@Model.Title" />
                            </div>
                        </div>

                        <div class="col-12" hidden>
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold">Mô tả</label>
                                <textarea class="form-control" id="description" rows="3" placeholder="Nhập mô tả khảo sát"></textarea>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold"><i class="ri-calendar-check-line me-1 text-success"></i>Ngày bắt đầu</label>
                                <input type="date" class="form-control" id="startDate" value="@Model.StartedDate.ToString("yyyy-MM-dd")" />
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold"><i class="ri-calendar-close-line me-1 text-danger"></i>Ngày kết thúc</label>
                                <input type="date" class="form-control" id="endDate" value="@Model.EndDate.ToString("yyyy-MM-dd")" />
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold"><i class="ri-sort-asc me-1 text-info"></i>Thứ tự hiển thị</label>
                                <input type="number" class="form-control" id="displayOrder" value="@Model.DisplayOrder" min="1" />
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label fw-semibold"><i class="ri-toggle-line me-1 text-primary"></i>Trạng thái</label>
                                <select class="form-select" id="status">
                                    <option value="1">Đang hoạt động</option>
                                    <option value="3">Không hoạt động</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sections Container -->
            <div class="mb-4">
                <h5 class="border-bottom pb-2 mb-3 text-primary"><i class="ri-file-list-line me-1"></i>Danh sách phần</h5>

                <div id="sections-container">
                    @if (Model != null && Model.Sections.Any())
                    {
                        @foreach (var section in Model.Sections.OrderBy(s => s.DisplayOrder))
                        {
                            <partial name="_SectionPartial" model="section" />
                        }
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="ri-information-line me-1"></i> Vui lòng thêm phần cho khảo sát.
                        </div>
                    }
                </div>

                <!-- Form thêm mới Section -->
                <div class="add-container mt-4">
                    <h6 class="mb-3"><i class="ri-add-circle-line me-2"></i>Thêm phần mới</h6>
                    <div class="row g-3">
                        <div class="col-md-10">
                            <input type="text" class="form-control" id="new-section-title" placeholder="Tiêu đề phần" />
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-primary w-100" onclick="SurveyModule.addSectionDirectly()">
                                <i class="ri-add-line me-1"></i> Thêm phần
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4 d-flex gap-2 justify-content-end">
                <a href="@Url.Action("Index", "Survey")" class="btn btn-secondary">
                    <i class="ri-close-line me-1"></i> Hủy
                </a>
                <button type="button" class="btn btn-primary" onclick="SurveyModule.handleSave()">
                    <i class="ri-save-line me-1"></i> Lưu khảo sát
                </button>
            </div>
        </div>
    </div>
</div>


<!-- Loading Overlay -->
<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(0,0,0,0.5); z-index: 9999;">
    <div class="d-flex justify-content-center align-items-center h-100">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
</div>

<!-- Loading Overlay - Thêm vào cuối file -->
<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-none" style="background: rgba(0,0,0,0.65); backdrop-filter: blur(2px); z-index: 9999;">
    <div class="d-flex flex-column justify-content-center align-items-center h-100">
        <div class="spinner-grow text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="text-white fs-5 fw-light">Đang lưu khảo sát...</div>
    </div>
</div>

@section Scripts {
    <script src="~/js/survey.js"></script>
    <script>
        $(document).ready(function () {
            // Đảm bảo khởi tạo sau khi DOM đã sẵn sàng
            setTimeout(() => {
                SurveyModule.init('@Model.Id');
            }, 100);
        });
    </script>
}
