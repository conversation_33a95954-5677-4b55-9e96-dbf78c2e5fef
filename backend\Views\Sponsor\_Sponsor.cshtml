﻿@model MiniAppCore.Entities.Events.Sponsors.Sponsor;

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
    </div>

    <div class="modal-body">
        <input type="hidden" id="id" value="@Model.Id" />

        <div class="row g-3">
            <div class="col-md-8">
                <label class="form-label fw-semibold">Tên nhà tài trợ <span class="text-danger">*</span></label>
                <input type="text" id="sponsorName" class="form-control" value="@Model.SponsorName" placeholder="Nhập tên nhà tài trợ" />
            </div>

            <div class="col-md-4">
                <label class="form-label fw-semibold">Trạng thái</label>
                <select id="isActive" class="selectpicker form-control" required>
                    @{
                        if (Model.IsActive == true)
                        {
                            <option value="true" selected>Đang hoạt động</option>
                            <option value="false">Ngưng hoạt động</option>
                        }
                        else if (Model.IsActive == false)
                        {
                            <option value="true">Đang hoạt động</option>
                            <option value="false" selected>Ngưng hoạt động</option>
                        }
                    }
                </select>
            </div>

            <div class="col-md-12">
                <label class="form-label fw-semibold">Website</label>
                <input type="text" id="websiteUrl" class="form-control" value="@Model.WebsiteURL" placeholder="https://example.com" />
            </div>

            <div class="col-md-12">
                <div class="form-group">
                    <label>Giới thiệu</label>
                    <div id="introduction" class="rounded-bottom" style="height: 200px" data-type="content">@Html.Raw(Model.Introduction)</div>
                </div>
            </div>

            <div class="col-md-12">
                <label class="form-label fw-semibold">Hình ảnh <span class="text-danger">*</span></label>
                <input type="file" id="image" class="form-control" accept="image/*" onchange="previewImage(this)" />
                <div class="mt-3" id="preview-image">
                    @if (!string.IsNullOrEmpty(ViewBag.Image as string))
                    {
                        <div class="image-preview position-relative d-inline-block">
                            <span class="btn-preview-remove" onclick="removePreviewImage()">x</span>
                            <img src="@ViewBag.Image" class="rounded border" style="height: 120px; width: 120px; object-fit: cover;" />
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer mt-3">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="button" class="btn btn-primary" onclick="HandleSaveOrUpdate($('#id').val())">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        InitialEditor('#introduction');
    });
</script>
