﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MiniAppCore.Migrations
{
    /// <inheritdoc />
    public partial class UpdateOmniToolTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEC/wF3Q+9+H9XtmZofzgTE5j5iqQXwdWAbW467fNkWQQFeTFzcUz+ZKlA8MDmgsYZA==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "09876543-21AB-CDEF-5678-90ABCDEF1234",
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEI5PO5rashkeADIlCMALUeFbwzArtDHw4T85Z0cJAipsHMZZgtW9EaRtv2XdaiZuJA==");
        }
    }
}
