/*! Toasty.js - v1.5.0 - 2018-05-04
* https://jakim.me/Toasty.js/
* Copyright (c) 2015-2018 <PERSON><PERSON><PERSON>; Licensed MIT */
.toast-container {
  position: fixed;
  z-index: 999999;
  pointer-events: none;
  line-height: normal;
  line-height: initial;
  -webkit-transition: all 1s ease-in-out;
  -o-transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
}
/* the wrapper where the toast messages appends: */
.toast-container .toast-wrapper {
  position: relative;
  padding: 0.5em;
}
/* the class that is assigned to the sound player. */
/* normally, this is a hidden wildcard: */
.toast-container .toast-soundplayer {
  display: none;
  visibility: hidden;
}

/**
 * Toast messages styles:
 * -------------------------------------------------- */

/* each toast message gets this style: */
.toast {
  position: relative;
  padding: 16px;
  border: 1px solid;
  margin: 0.5em 0em;
  border-left: 6px solid transparent;
  border-radius: 2px;
  pointer-events: none;
  -webkit-transition: all 0.32s ease-in-out;
  -o-transition: all 0.32s ease-in-out;
  transition: all 0.32s ease-in-out;
  -webkit-box-shadow: 0px 0px 9px rgba(0, 0, 0, .25);
  box-shadow: 0px 0px 9px rgba(0, 0, 0, .25);
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

/* informational toast class: */
.toast--info {
  color: #31708f;
  background-color: #eaf2fa;
  border-color: #91c5f2;
}

/* successful toast class: */
.toast--success {
  color: #3c763d;
  background-color: #e9ffd9;
  border-color: #a6ca8a;
}

/* warning toast class: */
.toast--warning {
  color: #8a6d3b;
  background-color: #fff8c4;
  border-color: #f2c779;
}

/* error toast class: */
.toast--error {
  color: #a94442;
  background-color: #ffecec;
  border-color: #f5aca6;
}

/* this class is assigned to each toast message when autoClose
 * plugin option is set to BOOLEAN false. */
/* Normally, this is a pointer events handler:*/
.toast.close-on-click {
  cursor: pointer;
  pointer-events: auto;
}

/**
 * Progress bar styles:
 * ------------------------------------------------------------------------- */

/* each progress bar gets this style: */
.toast-progressbar {
  position: absolute;
  height: 4px;
  width: 0%;
  left: 0px;
  bottom: 0px;
  opacity: 0.5;
  -webkit-transition: width 0s ease;
  -o-transition: width 0s ease;
  transition: width 0s ease;
}

/* progress bar color for each toast type: */
.toast-progressbar--info {
  background-color: #91c5f2;
}

.toast-progressbar--success {
  background-color: #a6ca8a;
}

.toast-progressbar--warning {
  background-color: #f2c779;
}

.toast-progressbar--error {
  background-color: #f5aca6;
}

/**
 * Available toast transitions:
 * ------------------------------------------------------------------------- */

/* TRANSITION 01 - a [fade] transition (DEFAULT TRANSITION): */
.toast-container--fade {
  right: 0;
  bottom: 0;
}

.toast-container--fade .toast-wrapper {
  display: inline-block;
}

.toast.fade-init {
  opacity: 0;
}

.toast.fade-show {
  opacity: 1;
}

.toast.fade-hide {
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 02 - a [slideLeftFade] transition: */
.toast-container--slideLeftFade {
  right: 0;
  bottom: 0;
}

.toast-container--slideLeftFade .toast-wrapper {
  display: inline-block;
}

.toast.slideLeftFade-init {
  right: -100%;
  opacity: 0;
}

.toast.slideLeftFade-show {
  right: 0%;
  opacity: 1;
}

.toast.slideLeftFade-hide {
  right: 100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 03 - a [slideLeftRightFade] transition: */
.toast-container--slideLeftRightFade {
  right: 0;
  bottom: 0;
}

.toast-container--slideLeftRightFade .toast-wrapper {
  display: inline-block;
}

.toast.slideLeftRightFade-init {
  right: -100%;
  opacity: 0;
}

.toast.slideLeftRightFade-show {
  right: 0%;
  opacity: 1;
}

.toast.slideLeftRightFade-hide {
  right: -100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 04 - a [slideRightFade] transition: */
.toast-container--slideRightFade {
  right: 0;
  bottom: 0;
}

.toast-container--slideRightFade .toast-wrapper {
  display: inline-block;
}

.toast.slideRightFade-init {
  left: -100%;
  opacity: 0;
}

.toast.slideRightFade-show {
  left: 0%;
  opacity: 1;
}

.toast.slideRightFade-hide {
  left: 100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 05 - a [slideRightLeftFade] transition: */
.toast-container--slideRightLeftFade {
  right: 0;
  bottom: 0;
}

.toast-container--slideRightLeftFade .toast-wrapper {
  display: inline-block;
}

.toast.slideRightLeftFade-init {
  left: -100%;
  opacity: 0;
}

.toast.slideRightLeftFade-show {
  left: 0%;
  opacity: 1;
}

.toast.slideRightLeftFade-hide {
  left: -100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 06 - a [slideUpFade] transition: */
.toast-container--slideUpFade {
  top: 0;
  right: 0;
  bottom: 0;
}

.toast-container--slideUpFade .toast-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.toast.slideUpFade-init {
  bottom: -100%;
  opacity: 0;
}

.toast.slideUpFade-show {
  bottom: 0%;
  opacity: 1;
}

.toast.slideUpFade-hide {
  bottom: 100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 07 - a [slideUpDownFade] transition: */
.toast-container--slideUpDownFade {
  top: 0;
  right: 0;
  bottom: 0;
}

.toast-container--slideUpDownFade .toast-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.toast.slideUpDownFade-init {
  bottom: -100%;
  opacity: 0;
}

.toast.slideUpDownFade-show {
  bottom: 0%;
  opacity: 1;
}

.toast.slideUpDownFade-hide {
  bottom: -100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 08 - a [slideDownFade] transition: */
.toast-container--slideDownFade {
  top: 0;
  right: 0;
  bottom: 0;
}

.toast-container--slideDownFade .toast-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.toast.slideDownFade-init {
  top: -100%;
  opacity: 0;
}

.toast.slideDownFade-show {
  top: 0%;
  opacity: 1;
}

.toast.slideDownFade-hide {
  top: 100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 09 - a [slideDownUpFade] transition: */
.toast-container--slideDownUpFade {
  top: 0;
  right: 0;
  bottom: 0;
}

.toast-container--slideDownUpFade .toast-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.toast.slideDownUpFade-init {
  top: -100%;
  opacity: 0;
}

.toast.slideDownUpFade-show {
  top: 0%;
  opacity: 1;
}

.toast.slideDownUpFade-hide {
  top: -100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 10 - a [pinItUp] transition: */
.toast-container--pinItUp {
  top: 0;
  right: 0;
  bottom: 0;
}

.toast-container--pinItUp .toast-wrapper {
  display: inline-block;
  height: 25px;
  float: right;
}

.toast.pinItUp-init {
  bottom: -100%;
  opacity: 0;
}

.toast.pinItUp-show {
  bottom: 0%;
  opacity: 1;
}

.toast.pinItUp-hide {
  bottom: 100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */

/* TRANSITION 11 - a [pinItDown] transition: */
.toast-container--pinItDown {
  top: 0;
  right: 0;
  bottom: 0;
}

.toast-container--pinItDown .toast-wrapper {
  display: inline-block;
  height: 25px;
  float: right;
}

.toast.pinItDown-init {
  top: -100%;
  opacity: 0;
}

.toast.pinItDown-show {
  top: 0%;
  opacity: 1;
}

.toast.pinItDown-hide {
  top: 100%;
  opacity: 0;
}
/* ------------------------------------------------------------------------- */