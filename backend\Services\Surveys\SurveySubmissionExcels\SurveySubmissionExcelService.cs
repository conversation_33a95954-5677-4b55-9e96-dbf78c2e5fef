﻿using MiniAppCore.Models.Responses.Surveys;
using OfficeOpenXml;
using System.Text.RegularExpressions;

namespace MiniAppCore.Services.Surveys.SurveySubmissionExcels
{
    public class SurveySubmissionExcelService : ISurveySubmissionExcelService
    {
        #region Export Excel

        private record ExportRow(
            string SurveyId, string SurveyTitle, string SectionTitle,
            string QuestionId, string QuestionTitle, string QuestionType,
            bool IsRequired, string UserZaloId, string UserZaloName, string PhoneNumber, string SubmissionId,
            string AnswerValue, int? LikertOptionCount, int? DisplayOrder
        );

        public byte[] ExportToExcel(List<SurveyResultResponse> surveys)
        {
            if (surveys == null || !surveys.Any())
                throw new ArgumentException("Dữ liệu khảo sát không được để trống.", nameof(surveys));

            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            using var package = new ExcelPackage();
            var sheet = package.Workbook.Worksheets.Add("Kết quả khảo sát");

            // Ghi header
            var headers = new[]
            {
                "SurveyId", "SurveyTitle", "SectionTitle", "QuestionId", "QuestionTitle", "QuestionType",
                "IsRequired", "UserZaloId", "UserZaloName", "PhoneNumber", "SubmissionId",
                "AnswerValue", "LikertOptionCount", "DisplayOrder"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                sheet.Cells[1, i + 1].Value = headers[i];
                sheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            var rows = BuildExportRows(surveys);

            int rowIndex = 2;
            foreach (var row in rows)
            {
                sheet.Cells[rowIndex, 1].Value = row.SurveyId;
                sheet.Cells[rowIndex, 2].Value = row.SurveyTitle;
                sheet.Cells[rowIndex, 3].Value = row.SectionTitle;
                sheet.Cells[rowIndex, 4].Value = row.QuestionId;
                sheet.Cells[rowIndex, 5].Value = row.QuestionTitle;
                sheet.Cells[rowIndex, 6].Value = row.QuestionType;
                sheet.Cells[rowIndex, 7].Value = row.IsRequired;
                sheet.Cells[rowIndex, 8].Value = row.UserZaloId;
                sheet.Cells[rowIndex, 9].Value = row.UserZaloName;
                sheet.Cells[rowIndex, 10].Value = row.PhoneNumber;
                sheet.Cells[rowIndex, 11].Value = row.SubmissionId;
                sheet.Cells[rowIndex, 12].Value = row.AnswerValue;
                sheet.Cells[rowIndex, 13].Value = row.LikertOptionCount?.ToString() ?? "";
                sheet.Cells[rowIndex, 14].Value = row.DisplayOrder?.ToString() ?? "";
                rowIndex++;
            }

            sheet.Cells[sheet.Dimension.Address].AutoFitColumns();
            return package.GetAsByteArray();
        }

        private List<ExportRow> BuildExportRows(List<SurveyResultResponse> surveys)
        {
            var rows = new List<ExportRow>();

            foreach (var survey in surveys)
            {
                var userId = survey.UserZaloId ?? "";
                var userName = survey.UserZaloName ?? "";
                var phone = survey.PhoneNumber ?? "";
                var submissionId = survey.SubmissionId ?? "";
                var surveyTitle = survey.Title ?? "";
                var surveyId = survey.SurveyId ?? "";

                if (survey.Sections == null) continue;

                foreach (var section in survey.Sections.OrderBy(s => s.DisplayOrder))
                {
                    var sectionTitle = section.TitleSection ?? "";

                    if (section.ListQuestion == null) continue;

                    foreach (var question in section.ListQuestion.OrderBy(q => q.DisplayOrder))
                    {
                        var questionType = question.Type ?? "";
                        var questionTitle = RemoveHtml(question.QuestionTitle);
                        var questionId = question.QuestionId ?? "";
                        var isRequired = question.IsRequied;

                        if (questionType.Equals("likert", StringComparison.OrdinalIgnoreCase)
                            && question.ListQuestionLikert?.Any() == true)
                        {
                            foreach (var likert in question.ListQuestionLikert.OrderBy(l => l.DisplayOrder))
                            {
                                var likertId = likert.QuestionLikertId ?? "";
                                var likertTitle = likert.QuestionLikertTitle ?? "";
                                var displayTitle = $"{questionTitle} - {likertTitle}";
                                var response = question.UserResponses?.FirstOrDefault(r => r.LikertQuestionId == likert.QuestionLikertId);
                                var value = question.ListOption?.FirstOrDefault(r => r.AnswerId == response?.AnswerId);

                                rows.Add(new ExportRow(
                                    SurveyId: surveyId,
                                    SurveyTitle: surveyTitle,
                                    SectionTitle: sectionTitle,
                                    QuestionId: likertId,
                                    QuestionTitle: displayTitle,
                                    QuestionType: questionType,
                                    IsRequired: isRequired,
                                    UserZaloId: userId,
                                    UserZaloName: userName,
                                    PhoneNumber: phone,
                                    SubmissionId: submissionId,
                                    AnswerValue: value?.Key ?? "",
                                    LikertOptionCount: likert.OptionCount,
                                    DisplayOrder: likert.DisplayOrder
                                ));
                            }
                        }
                        else
                        {
                            rows.Add(new ExportRow(
                                SurveyId: surveyId,
                                SurveyTitle: surveyTitle,
                                SectionTitle: sectionTitle,
                                QuestionId: questionId,
                                QuestionTitle: questionTitle,
                                QuestionType: questionType,
                                IsRequired: isRequired,
                                UserZaloId: userId,
                                UserZaloName: userName,
                                PhoneNumber: phone,
                                SubmissionId: submissionId,
                                AnswerValue: GetAnswerValue(question),
                                LikertOptionCount: null,
                                DisplayOrder: question.DisplayOrder
                            ));
                        }
                    }
                }
            }

            return rows;
        }

        public string GenerateFileName() => $"survey_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

        private string RemoveHtml(string? input)
        {
            return string.IsNullOrWhiteSpace(input)
                ? ""
                : Regex.Replace(input, "<[^>]*>", "").Trim();
        }

        private string GetAnswerValue(QuestionResponse question)
        {
            if (question.UserResponses == null || !question.UserResponses.Any())
                return "";

            string type = question.Type?.ToLower() ?? "";

            // Các loại câu hỏi đơn giản: paragraph, date, time...
            if (type is "paragraph" or "date" or "time")
            {
                // Nếu có inputValue, trả về; nếu không, trả về AnswerId
                var response = question.UserResponses.FirstOrDefault();
                return response?.InputValue ?? response?.AnswerId ?? "";
            }

            // SingleChoice và Dropdown: chỉ có một lựa chọn duy nhất
            if (type == "singlechoice" || type == "dropdown")
            {
                var response = question.UserResponses.FirstOrDefault();
                var option = question.ListOption?.FirstOrDefault(o => o.AnswerId == response?.AnswerId || o.Key == response?.AnswerId);
                return option?.Key ?? "";
            }

            // MultiChoice: nếu có nhiều lựa chọn, gom lại các giá trị trả về
            if (type == "multichoice")
            {
                return string.Join(", ", question.UserResponses.Select(r =>
                {
                    var opt = question.ListOption?.FirstOrDefault(o => o.AnswerId == r.AnswerId || o.Key == r.AnswerId);
                    if (opt == null)
                        return ""; // Không có option thì trả về rỗng

                    // Nếu option có Input và người dùng nhập giá trị vào
                    if (opt.IsInput && !string.IsNullOrEmpty(r.InputValue))
                        return $"{opt.Key} ({r.InputValue})";

                    // Nếu không có Input, chỉ trả về giá trị của option
                    return !string.IsNullOrEmpty(opt.Key)
                        ? opt.Key
                        : r.AnswerId ?? ""; // Nếu không có giá trị option, trả về AnswerId làm fallback
                }).Where(v => !string.IsNullOrWhiteSpace(v))); // Loại bỏ các giá trị trống
            }

            return "";
        }

        #endregion
    }
}
