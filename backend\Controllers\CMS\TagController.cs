﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Commons;
using MiniAppCore.Services.Tags;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class TagController(ITagService tagService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("Tag/Create")]
        public async Task<IActionResult> Create()
        {
            var article = new Tag()
            {
                Id = string.Empty,
                Name = string.Empty,
                Description = string.Empty
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới tag";
            return PartialView("_Tag", article);
        }

        [HttpGet("Tag/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật danh mục tin tức";
            var result = await tagService.GetByIdAsync(id);

            if (result == null)
            {
                return RedirectToAction("Create");
            }
            return PartialView("_Tag", result);
        }
    }
}
