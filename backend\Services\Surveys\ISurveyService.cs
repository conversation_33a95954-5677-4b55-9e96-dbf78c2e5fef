﻿using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Surveys;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Surveys;
using MiniAppCore.Models.Responses.Surveys;

namespace MiniAppCore.Services.Surveys
{
    public interface ISurveyService : IService<Survey>
    {
        Task<int> CreateSurveyAsync(SurveyDTO request);
        Task<int> UpdateSurveyAsync(string id, SurveyDTO request);

        Task<SurveyDTO?> GetSurveyDTOByIdAsync(string id);
        Task<SurveyResponse> GetSurveyByIdAsync(string id);
        Task<PagedResult<Survey>> GetPage(IRequestQuery query, string? vistorId, short? status);
    }
}
