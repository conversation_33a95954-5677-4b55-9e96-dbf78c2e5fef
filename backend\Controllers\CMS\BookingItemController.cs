﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Models.Responses.BookingItem;
using MiniAppCore.Services.Bookings;

namespace MiniAppCore.Controllers.CMS
{
    [Authorize(AuthenticationSchemes = CookieAuthenticationDefaults.AuthenticationScheme)]
    public class BookingItemController(IBookingItemService bookingItemService) : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("BookingItem/Create")]
        public IActionResult Create()
        {
            var product = new BookingItemResponse()
            {
                Id = string.Empty,
                Name = string.Empty,
                Description = string.Empty,
                OriginalPrice = 0,
                DisplayOrder = 1
            };
            ViewBag.Button = "Lưu";
            ViewBag.Title = "Thêm mới dịch vụ";
            return PartialView("_BookingItem", product);
        }

        [HttpGet("BookingItem/Detail/{id}")]
        public async Task<IActionResult> Detail(string id)
        {
            ViewBag.Button = "Cập nhật";
            ViewBag.Title = "Cập nhật dịch vụ";
            var bookingItem = await bookingItemService.GetDetailResponseByIdAsync(id);
            return PartialView("_BookingItem", bookingItem);
        }
    }
}
