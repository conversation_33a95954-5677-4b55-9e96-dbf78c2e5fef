﻿using MiniAppCore.Entities.Commons;
using MiniAppCore.Enums;

namespace MiniAppCore.Entities.LuckyWheels
{
    public class SpinHistory : BaseEntity
    {
        public required string UserZaloId { get; set; }
        public required string LuckyWheelId { get; set; }

        public bool IsWon { get; set; }
        public string? PrizeId { get; set; }
        public string? RewardId { get; set; }
        public EPrizeType PrizeType { get; set; }

        public string? Note { get; set; }
        public string? PhoneNumber { get; set; }
        public string? RecipientName { get; set; }
        public string? RecipientAddress { get; set; }

        public DateTime? ClaimDate { get; set; }
        public ETransaction ClaimStatatus { get; set; }
    }
}
