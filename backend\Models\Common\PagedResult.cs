﻿namespace MiniAppCore.Models.Common
{
    public class PagedResult<T>
    {
        public IEnumerable<T> Data { get; set; } = new List<T>();
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
        public int TotalCount { get; set; }
        public int TotalPages { get; set; }

        // Additional helper properties
        public bool HasPreviousPage => CurrentPage > 1;
        public bool HasNextPage => CurrentPage < TotalPages;
        public int? PreviousPage => HasPreviousPage ? CurrentPage - 1 : null;
        public int? NextPage => HasNextPage ? CurrentPage + 1 : null;

        // Metadata for sorting and filtering
        public string? SortBy { get; set; }
        public string? SortDirection { get; set; }
        public string? Keyword { get; set; }

        // Static factory method for easy creation
        public static PagedResult<T> Create(
            IEnumerable<T> data,
            int totalCount,
            int currentPage,
            int pageSize,
            string? sortBy = null,
            string? sortDirection = null,
            string? keyword = null)
        {
            return new PagedResult<T>
            {
                Data = data,
                CurrentPage = currentPage,
                PageSize = pageSize,
                TotalCount = totalCount,
                TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                SortBy = sortBy,
                SortDirection = sortDirection,
                Keyword = keyword
            };
        }
    }
}
