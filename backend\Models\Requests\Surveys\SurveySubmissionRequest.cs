﻿namespace MiniAppCore.Models.Requests.Surveys
{
    public class SurveySubmissionRequest
    {
        public string? VisitorId { get; set; }
        public string? SurveyId { get; set; }
        public List<AnswerSubmission> Answers { get; set; } = new List<AnswerSubmission>();
    }

    public class AnswerSubmission
    {
        public string? QuestionId { get; set; }
        public string? InputValue { get; set; }                                  // Cho câu hỏi là text/paragraph
        public List<OptionInputSubmission> OptionInputs { get; set; } = new List<OptionInputSubmission>(); // cho câu hỏi là single choice/ multiple choice
        public List<LikertSubmission> LikertAnswers { get; set; } = new List<LikertSubmission>(); // cho câu hỏi dạng likert
    }

    public class OptionInputSubmission
    {
        public string? AnswerId { get; set; }  // ID của option
        public string? InputValue { get; set; } // Giá trị input kèm theo option
    }

    public class LikertSubmission
    {
        public string? AnswerId { get; set; } // ID của câu trả lời trong bảng Answer
        public string? LikertValue { get; set; } // Key của câu trả lời trong bảng Answer
        public string? LikertQuestionId { get; set; } // ID của câu hỏi con trong Likert
    }
}
