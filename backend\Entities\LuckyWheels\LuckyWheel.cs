﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.LuckyWheels
{
    public class LuckyWheel : BaseEntity
    {
        public bool IsActive { get; set; }
        public string? Name { get; set; } // Tên vòng quay
        public string? Image { get; set; } // Ảnh vòng quay
        public string? Description { get; set; } // Thể lệ vòng quay

        public long RequiredPoints { get; set; } // Điểm cần thiết để quay

        public DateTime StartDate { get; set; } // Thời gian bắt đầu
        public DateTime ExpiryDate { get; set; } // Thời gian kết thúc

        // Auto add spin points configuration
        public bool EnableAutoSpinPoints { get; set; } = false; // Có tự động cộng lượt chơi không
        public long SpinPointsToAdd { get; set; } = 0; // Số điểm spin sẽ cộng cho mỗi membership
    }
}
