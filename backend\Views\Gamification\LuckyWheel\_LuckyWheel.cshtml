﻿@model MiniAppCore.Models.DTOs.LuckyWheels.LuckyWheelDTO;

<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <div class="card-body">
            <form data-toggle="validator" id="luckyWheelForm">
                <ul class="nav nav-tabs" role="tablist">
                    <li class="nav-item">
                        <a id="basic-tab" class="nav-link active" data-bs-toggle="tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">Thông tin cơ bản</a>
                    </li>
                    <li class="nav-item">
                        <a id="prizes-tab" class="nav-link" data-bs-toggle="tab" href="#prizes" role="tab" aria-controls="prizes" aria-selected="false">Phần thưởng</a>
                    </li>
                </ul>

                <div class="tab-content mt-3">
                    <!-- Tab thông tin cơ bản -->
                    <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Tên vòng quay <span class="text-danger">*</span></label>
                                    <input id="name" type="text" class="form-control" value="@Model.Name" placeholder="Tên vòng quay..." required>
                                    <div class="invalid-feedback">Vui lòng nhập tên vòng quay</div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Spin points yêu cầu <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input id="requiredPoints" type="number" min="0" class="form-control" value="@Model.RequiredPoints" placeholder="Nhập số điểm yêu cầu." required>
                                    </div>
                                    <div class="invalid-feedback">Vui lòng nhập số điểm yêu cầu</div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Trạng thái hoạt động <span class="text-danger">*</span></label>
                                    <select id="status" class="form-control">
                                        <option value="true" selected="@(Model.IsActive)">Hoạt động</option>
                                        <option value="false" selected="@(!Model.IsActive)">Ngừng hoạt động</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Ngày bắt đầu <span class="text-danger">*</span></label>
                                    <input id="startDate" type="date" class="form-control" value="@Model.StartDate.ToString("yyyy-MM-dd")" required>
                                    <div class="invalid-feedback">Vui lòng chọn ngày bắt đầu</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Ngày hết hạn <span class="text-danger">*</span></label>
                                    <input id="expiryDate" type="date" class="form-control" value="@Model.ExpiryDate.ToString("yyyy-MM-dd")" required>
                                    <div class="invalid-feedback">Vui lòng chọn ngày hết hạn</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Tự động cộng lượt chơi</label>
                                    <select id="enableAutoSpinPoints" class="form-control">
                                        @if (Model.EnableAutoSpinPoints)
                                        {
                                            <option value="true" selected>Bật</option>
                                            <option value="false">Tắt</option>
                                        }
                                        else
                                        {
                                            <option value="true">Bật</option>
                                            <option value="false" selected>Tắt</option>
                                        }
                                    </select>
                                    <small class="form-text text-muted">Tự động cộng lượt chơi cho tất cả thành viên</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Số điểm cộng mỗi lần</label>
                                    <input type="number" id="spinPointsToAdd" class="form-control" value="@Model.SpinPointsToAdd" min="0" step="1" />
                                    <small class="form-text text-muted">Số điểm spin sẽ cộng cho mỗi membership</small>
                                </div>
                            </div>

                            <div class="col-md-12 mt-3">
                                <div class="form-group">
                                    <label>Mô tả vòng quay</label>
                                    <div id="editor" class="" style="height: 150px" data-type="content">@Html.Raw(Model.Description)</div>
                                </div>
                            </div>

                            <div class="col-md-12 mt-3">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Ảnh vòng quay <span class="text-danger">*</span></label>
                                        <input type="file" id="pics" class="form-control image-file" name="pic" accept="image/*" onchange="ShowPreview(this)">
                                        <small class="form-text text-muted">Kích thước đề xuất: 600x600 px. Định dạng: JPG, PNG, GIF.</small>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div id="preview" class="row d-flex flex-nowrap" style="overflow-x: scroll; min-height: 250px;">
                                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                                        {
                                            <div class="image-preview mx-2 card position-relative" style="max-width: 250px; height:170px">
                                                <img src="@Model.ImageUrl" class="card-img-top h-100" alt="Alternate Text" style="object-fit:contain" />
                                                @* <span class="btn-preview-remove" data-url="@Model.ImageUrl">x</span> *@
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab cấu hình phần thưởng -->
                    <div class="tab-pane fade" id="prizes" role="tabpanel" aria-labelledby="prizes-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="mb-0">Danh sách phần thưởng</h5>
                            <button type="button" class="btn btn-primary" onclick="addPrizeRow()">
                                <i class="fa fa-plus"></i> Thêm phần thưởng
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-bordered" id="prizesTable">
                                <thead class="thead-light">
                                    <tr>
                                        <th class="col-3 text-center">Phần thưởng</th>
                                        <th class="col-1 text-center">Vị trí (độ)</th>
                                        <th class="col-1 text-center">Thứ tự</th>
                                        <th class="col-1 text-center">Tỷ lệ (%)</th>
                                        <th class="col-1 text-center">Giới hạn/ngày</th>
                                        <th class="col-1 text-center">Tổng số lượng</th>
                                        <th class="col-1 text-center">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.GamePrizes != null && Model.GamePrizes.Any())
                                    {
                                        @foreach (var prize in Model.GamePrizes.OrderBy(x => x.Ranking))
                                        {
                                            <tr class="prize-row">
                                                <td>
                                                    <select class="form-control select2-prizes" data-prize-id="@prize.GamePrizeId" selected>
                                                        <option value="@prize.GamePrizeId" selected>@prize.GamePrizeName</option>
                                                    </select>
                                                </td>
                                                <td><input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/g, '')" min="0" max="359" class="form-control position-input"
                                                        value="@prize.Position" /></td>
                                                <td><input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/g, '')" min="1" class="form-control ranking-input"
                                                        value="@prize.Ranking" /></td>
                                                <td><input type="number" oninput="validateReviewPoint(this, 0, 100)" step="0.01" min="0" max="100" class="form-control winrate-input" value="@prize.WinRate" />
                                                </td>
                                                <td><input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/g, '')" min="0" class="form-control daily-limit-input"
                                                        value="@prize.DailyLimit" /></td>
                                                <td><input type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/^0+(?=\d)/g, '')" min="0" class="form-control total-quantity-input"
                                                        value="@prize.Quantity" /></td>
                                                <td class="text-center">
                                                    <button type="button" class="btn btn-sm btn-danger remove-prize-btn">
                                                        <i class="ri-delete-bin-line fs-6 mr-0"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div class="alert alert-info mt-3" role="alert">
                            <i class="fa fa-info-circle"></i> Lưu ý: Tổng tỷ lệ trúng thưởng không được vượt quá 100%.
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fa fa-times"></i> Đóng
        </button>
        <button type="button" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">
            <i class="fa fa-save"></i> @ViewBag.Button
        </button>
    </div>
</div>

@await Html.PartialAsync("Partials/_LuckyWheelScriptModal.cshtml")
