﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Exceptions;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.DTOs.Properties;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Services.Categories;
using MiniAppCore.Services.Products;
using MiniAppCore.Services.Products.ExportImports;
using MiniAppCore.Services.Products.Variants;
using Newtonsoft.Json;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme, Roles = "ADMIN")]
    public class ProductsController(ILogger<ProductsController> logger, IProductService productService, IProductPropertyService productPropertyService, IExportImportProductService exportImportProductService, ICategoryService categoryService) : ControllerBase
    {
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetPage([FromQuery] ProductQueryParams queryParams, [FromQuery] bool? isGift)
        {
            try
            {
                if (!User.IsInRole("ADMIN"))
                {
                    queryParams.StockStatus = new List<Enums.EProduct>() { Enums.EProduct.InStock, Enums.EProduct.OutOfStock };
                    //queryParams.IsOrderByCreatedDate = false;
                }

                var result = await productService.GetPage(queryParams);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<IActionResult> GetProductById(string id)
        {
            try
            {
                var product = await productService.GetProductDetailAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    Data = product
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        #region Admin API

        [HttpPost]
        public async Task<IActionResult> CreateProduct([FromForm] ProductRequest dto)
        {
            try
            {
                if (!string.IsNullOrEmpty(dto.VariantsJson))
                {
                    dto.Variants = JsonConvert.DeserializeObject<List<ProductVariantRequest>>(dto.VariantsJson) ?? new List<ProductVariantRequest>();
                }
                await productService.CreateAsync(dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateProduct(string id, [FromForm] ProductRequest model)
        {
            try
            {
                if (!string.IsNullOrEmpty(model.VariantsJson))
                {
                    model.Variants = JsonConvert.DeserializeObject<List<ProductVariantRequest>>(model.VariantsJson) ?? new List<ProductVariantRequest>();
                }
                await productService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật sản phẩm thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteProduct(string id)
        {
            try
            {
                await productService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        // Product Property
        [HttpGet("Properties")]
        public async Task<IActionResult> GetPageProperty([FromQuery] RequestQuery queryParams, [FromQuery] bool isGetAll = false)
        {
            try
            {
                var result = await productPropertyService.GetPage(queryParams);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    result.Data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpGet("Property/{id}")]
        public async Task<IActionResult> GetProperty(string id)
        {
            try
            {
                var data = await productPropertyService.GetDetailProperty(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    data
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPost("Properties")]
        public async Task<IActionResult> CreateProductProperty([FromBody] PropertyDTO dto)
        {
            try
            {
                await productPropertyService.CreateAsync(dto);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpPut("Properties/{id}")]
        public async Task<IActionResult> UpdateProductProperty(string id, [FromBody] PropertyDTO model)
        {

            try
            {
                await productPropertyService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật sản phẩm thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpDelete("Properties/{id}")]
        public async Task<IActionResult> DeleteProductProperty(string id)
        {
            try
            {
                await productPropertyService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpGet("TemplateImport")]
        [AllowAnonymous]
        public IActionResult DownloadImportTemplate()
        {
            using var package = new OfficeOpenXml.ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Products");

            // Add headers
            worksheet.Cells[1, 1].Value = "Tên sản phẩm";
            worksheet.Cells[1, 2].Value = "Tên thuộc tính 1";
            worksheet.Cells[1, 3].Value = "Giá trị thuộc tính 1";
            worksheet.Cells[1, 4].Value = "Tên thuộc tính 2";
            worksheet.Cells[1, 5].Value = "Giá trị thuộc tính 2";
            worksheet.Cells[1, 6].Value = "Tên thuộc tính 3";
            worksheet.Cells[1, 7].Value = "Giá trị thuộc tính 3";
            worksheet.Cells[1, 8].Value = "Giá";
            worksheet.Cells[1, 9].Value = "Tồn kho";
            worksheet.Cells[1, 10].Value = "Ảnh (URL, phân cách bằng dấu phẩy)";
            worksheet.Cells[1, 11].Value = "Danh mục (phân cách bằng dấu phẩy)";
            worksheet.Cells[1, 12].Value = "Mô tả";

            // Format headers
            using (var range = worksheet.Cells[1, 1, 1, 12])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            // Sample data
            worksheet.Cells[2, 1].Value = "Áo thun nam";
            worksheet.Cells[2, 2].Value = "Màu";
            worksheet.Cells[2, 3].Value = "Đen";
            worksheet.Cells[2, 4].Value = "Size";
            worksheet.Cells[2, 5].Value = "L";
            worksheet.Cells[2, 6].Value = "Chất liệu";
            worksheet.Cells[2, 7].Value = "Cotton";
            worksheet.Cells[2, 8].Value = 150000;
            worksheet.Cells[2, 9].Value = 20;
            worksheet.Cells[2, 10].Value = "https://example.com/aothun1.jpg";
            worksheet.Cells[2, 11].Value = "Thời trang nam";
            worksheet.Cells[2, 12].Value = "Áo thun cotton thoáng mát";

            worksheet.Cells[3, 1].Value = ""; // cùng sản phẩm
            worksheet.Cells[3, 2].Value = "Màu";
            worksheet.Cells[3, 3].Value = "Trắng";
            worksheet.Cells[3, 4].Value = "Size";
            worksheet.Cells[3, 5].Value = "XL";
            worksheet.Cells[3, 6].Value = "Chất liệu";
            worksheet.Cells[3, 7].Value = "Polyester";
            worksheet.Cells[3, 8].Value = 155000;
            worksheet.Cells[3, 9].Value = 15;
            worksheet.Cells[3, 10].Value = "https://example.com/aothun2.jpg";
            worksheet.Cells[3, 11].Value = "Thời trang nam";
            worksheet.Cells[3, 12].Value = "";

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            var fileBytes = package.GetAsByteArray();
            var fileName = "ImportProductTemplate_Simple.xlsx";
            return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }

        [HttpPost("Import")]
        public async Task<IActionResult> ImportProduct([FromForm] IFormFile file)
        {
            try
            {
                var importCount = await exportImportProductService.ImportProductsAsync(file);
                return Ok(new
                {
                    Code = 0,
                    Message = $"Đã import thành công {importCount} sản phẩm!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        [HttpGet("Export")]
        public async Task<IActionResult> ExportProducts([FromQuery] ProductQueryParams queryParams)
        {
            try
            {
                var fileBytes = await exportImportProductService.ExportProductsAsync(queryParams);
                return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    $"Products_Export_{DateTime.Now:yyyyMMdd}.xlsx");
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogError("Error occurred while exporting products: {0}", ex.Message);
                return StatusCode(500, new
                {
                    Code = 1,
                    Message = "Có lỗi xảy ra khi xuất dữ liệu sản phẩm."
                });
            }
        }

        #region Category child

        [HttpGet("Category/{id}")]
        public async Task<IActionResult> GetListCategoryChild(string id)
        {
            try
            {
                var data = await categoryService.GetListCategoryChild(id);
                var category = await categoryService.GetByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công!",
                    category,
                    data,

                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(500);
            }
        }

        #endregion

        #endregion
    }
}
