﻿using AutoMapper;
using AutoMapper.Execution;
using Hangfire;
using MediatR;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Helpers;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Memberships;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Entities.Offers.Vouchers;
using MiniAppCore.Entities.Orders;
using MiniAppCore.Entities.Products;
using MiniAppCore.Entities.Settings;
using MiniAppCore.Enums;
using MiniAppCore.Exceptions;
using MiniAppCore.Features.EventTrigger;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Carts;
using MiniAppCore.Models.Requests.Orders;
using MiniAppCore.Models.Responses.Orders;
using MiniAppCore.Models.Responses.Orders.Carts;
using MiniAppCore.Services.Affiliates.Commissions;
using MiniAppCore.Services.Memberships;
using MiniAppCore.Services.Offers.Discounts;
using MiniAppCore.Services.Offers.Promotions;
using MiniAppCore.Services.Offers.Vouchers;
using MiniAppCore.Services.OmniTool.Notifications;
using MiniAppCore.Services.Orders.Carts;
using MiniAppCore.Services.Orders.ShippingFees;
using MiniAppCore.Services.Products;
using MiniAppCore.Services.Products.Variants;
using MiniAppCore.Features.EventTrigger;
using MediatR;
using System.Data;

namespace MiniAppCore.Services.Orders
{
    public class OrderService(IUnitOfWork unitOfWork,
                              IMapper mapper,
                              IConfiguration configuration,
                              IHttpContextAccessor httpContextAccessor,
                              ICartService cartService,
                              IProductService productService,
                              IVoucherService voucherService,
                              IDiscountService discountService,
                              IPromotionService promotionService,
                              IShippingFeeService shippingFeeService,
                              IMembershipService membershipService,
                              IProductPropertyService productPropertyService,
                              IMediator mediator) : Service<Order>(unitOfWork), IOrderService
    {
        private readonly IMediator _mediator = mediator;
        private readonly IRepository<PointHistory> _pointHistory = unitOfWork.GetRepository<PointHistory>();
        private readonly IRepository<OrderDetail> _orderDetailRepo = unitOfWork.GetRepository<OrderDetail>();
        private readonly IRepository<OrderVoucher> _orderVoucherRepo = unitOfWork.GetRepository<OrderVoucher>();
        private readonly IRepository<OrderDetailGift> _orderDetailGiftRepo = unitOfWork.GetRepository<OrderDetailGift>();
        private readonly IRepository<OrderDetailReview> _orderDetailReviewRepo = unitOfWork.GetRepository<OrderDetailReview>();

        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";

        #region "Get Order"

        public async Task<PagedResult<OrderResponse>> GetPage(PurchaseQueryParams queryParams, string? userZaloId)
        {
            var orders = ApplyFilters(_repository.AsQueryable(), queryParams, userZaloId);

            var totalItems = await orders.CountAsync();
            var totalPages = (int)Math.Ceiling((double)totalItems / queryParams.PageSize);

            var listOrder = await orders.OrderByDescending(x => x.CreatedDate).Skip(queryParams.Skip).Take(queryParams.PageSize).ToListAsync();
            var memberships = await membershipService.GetByUserZaloIds(listOrder.Select(x => x.UserZaloId).ToList());

            var items = listOrder.Select(x =>
            {
                var membership = memberships.FirstOrDefault(m => m.UserZaloId == x.UserZaloId || m.Id == x.UserZaloId);
                if (membership == null) return null;
                return mapper.Map<OrderResponse>((x, membership));
            }).Where(x => x != null).Select(x => x!).ToList();

            return new PagedResult<OrderResponse>
            {
                Data = items,
                TotalPages = totalPages
            };
        }

        private IQueryable<Order> ApplyFilters(IQueryable<Order> orders, PurchaseQueryParams queryParams, string? userZaloId)
        {
            if (!string.IsNullOrEmpty(queryParams.Keyword))
            {
                // Thêm điều kiện tìm kiếm theo từ khóa nếu có
                var keyword = queryParams.Keyword;
                var memberships = unitOfWork.GetRepository<Membership>();
                orders = orders.Where(x => x.Id.Contains(keyword) || memberships.AsQueryable()
                                                      .Any(m => m.UserZaloId == x.UserZaloId &&
                                                               (m.UserZaloName.Contains(keyword) || m.PhoneNumber.Contains(keyword))));
            }

            if (!string.IsNullOrEmpty(userZaloId))
            {
                orders = orders.Where(p => p.UserZaloId == userZaloId);
            }

            if (queryParams.BranchId.Any() && !queryParams.BranchId.Any(string.IsNullOrEmpty))
            {
                orders = orders.Where(p => !string.IsNullOrEmpty(p.BranchId) && queryParams.BranchId.Contains(p.BranchId));
            }

            if (queryParams.OrderStatus.HasValue)
            {
                orders = orders.Where(p => queryParams.OrderStatus == (short)p.OrderStatus);
            }

            if (queryParams.PaymentStatus.HasValue)
            {
                orders = orders.Where(p => queryParams.PaymentStatus == (short)p.PaymentStatus);
            }

            if (queryParams.StartDate.HasValue)
            {
                orders = orders.Where(p => p.CreatedDate >= queryParams.StartDate.Value);
            }

            if (queryParams.EndDate.HasValue)
            {
                orders = orders.Where(p => p.CreatedDate <= queryParams.EndDate.Value);
            }

            return orders;
        }

        public async Task<OrderDetailResponse> GetOrderByIdAsync(string orderId)
        {
            var order = await GetByIdAsync(orderId);
            if (order == null)
            {
                throw new CustomException(200, "Không tìm thấy đơn hàng này");
            }
            var membership = await membershipService.GetByUserZaloId(order.UserZaloId) ?? await membershipService.GetByIdAsync(order.UserZaloId);

            var addressRep = unitOfWork.GetRepository<MembershipAddress>();
            var vatRep = unitOfWork.GetRepository<MembershipVAT>();

            var addressInfo = await addressRep.FindByIdAsync(order?.MembershipAddressId ?? "");


            // Mapping order detail trả về
            var orderDetailResponse = mapper.Map<OrderDetailResponse>((order, membership));

            // Lấy danh sách sản phẩm trong đơn hàng
            var orderItems = await GetOrderItemsByOrderId(orderId);
            var orderItemsIds = orderItems.Select(x => x.Id).ToList();

            // Lấy danh sách quà tặng liên quan đến các sản phẩm trong đơn hàng
            var orderItemGifts = _orderDetailGiftRepo.AsQueryable()
                                    .Where(x => orderItemsIds.Contains(x.OrderDetailId))
                                    .ToList();

            // Tập hợp tất cả productId từ sản phẩm chính và quà tặng
            var productsInOrder = orderItems.Select(x => x.ProductId)
                                            .Concat(orderItemGifts.Select(x => x.ProductId!))
                                            .Distinct()
                                            .ToList();

            var allProducts = await productService.GetAllAsync();
            var products = await productService.GetByIdsAsync(productsInOrder);


            var productProperties = await productPropertyService.GetPropertyValuesByVariantIds(orderItems.Select(x => x.VariantId!));

            //Thêm địa chỉ người nhận và sđt người nhận
            orderDetailResponse.NameReciver = addressInfo?.Name;
            orderDetailResponse.PhoneReciver = addressInfo?.PhoneNumber;

            //Thông tin VAT
            if (!string.IsNullOrEmpty(order.MembershipVATId))
            {
                orderDetailResponse.Vat = await vatRep.FindByIdAsync(order.MembershipVATId);
            }


            // Mapping sản phẩm vào OrderDetailResponse
            orderDetailResponse.OrderDetails = orderItems.Select(orderItem =>
            {
                var product = products.FirstOrDefault(p => p.Id == orderItem.ProductId);
                if (product == null)
                {
                    return null;
                }

                var propertyValues = new List<string>();
                if (!string.IsNullOrEmpty(orderItem.VariantId))
                {
                    productProperties.TryGetValue(orderItem.VariantId, out propertyValues);
                }

                return new OrderItemResponse
                {
                    Note = orderItem.Note,
                    ProductId = product?.Id,
                    ProductName = product?.Name ?? "Sản phẩm không tồn tại",
                    DiscountPrice = orderItem.DiscountPrice, // Giá sau giảm
                    OriginalPrice = orderItem.OriginalPrice, // Giá gốc
                    Quantity = orderItem.Quantity,
                    PropertyValues = propertyValues ?? new List<string>(),
                    Gifts = orderItemGifts.Where(g => g.OrderDetailId == orderItem.Id)
                                          .Select(g =>
                                          {
                                              var gift = allProducts.FirstOrDefault(x => x.Id == g.ProductId);
                                              if (gift == null)
                                              {
                                                  return null;
                                              }
                                              // tạo biến để tránh warning
                                              var result = new OrderItemResponse
                                              {
                                                  ProductId = gift.Id,
                                                  ProductName = gift.Name,
                                                  OriginalPrice = gift.Price,
                                                  DiscountPrice = 0,
                                                  Quantity = g.Quantity,
                                                  Images = GetProductImages(gift)
                                              };
                                              return result;
                                          })
                                          .Where(x => x != null)
                                          .Select(x => x!)
                                          .ToList(),
                    Images = string.IsNullOrEmpty(product?.Images)
                   ? new List<string>()
                   : product.Images.Split(',').Select(x =>
                   {
                       var trimmed = x.Trim();
                       if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                           return trimmed;
                       else
                           return $"{hostUrl}/uploads/images/products/{trimmed}";
                   }).ToList()
                };
            }).Select(x => x!).ToList();

            orderDetailResponse.Vouchers = (await GetOrderVoucherByOrderId(orderId)).Select(x => new OrderVoucherRepsone()
            {
                VoucherId = x.VoucherId,
                VoucherCode = x.VoucherCode,
                VoucherName = x.VoucherName
            }).ToList();

            // check thử xem đơn hàng đã dánh giá hay chưa
            var isRating = await _orderDetailReviewRepo.AsQueryable().AnyAsync(x => orderItemsIds.Contains(x.OrderDetailId));
            orderDetailResponse.IsRating = isRating;

            return orderDetailResponse;
        }

        public async Task<IEnumerable<OrderVoucher>> GetOrderVoucherByOrderId(string orderId)
        {
            var orderVoucher = unitOfWork.GetRepository<OrderVoucher>();
            return await orderVoucher.AsQueryable().Where(x => x.OrderId == orderId).ToListAsync();
        }

        public async Task<IEnumerable<OrderDetail>> GetOrderItemsByOrderId(string orderId)
        {
            var orderDetails = unitOfWork.GetRepository<OrderDetail>();
            return await _orderDetailRepo.AsQueryable().AsNoTracking().Where(x => x.OrderId == orderId).ToListAsync();
        }

        public async Task<IEnumerable<CartItemResponse>> GetBuyProductInfo(List<CartItemRequest> cartItemRequest)
        {
            if (cartItemRequest == null || !cartItemRequest.Any()) return Enumerable.Empty<CartItemResponse>();

            var cartItemResponses = cartItemRequest.Select(x => new Cart()
            {
                Quantity = x.Quantity,
                ProductId = x.ProductId,
                VariantId = x.VariantId,
                UserZaloId = string.Empty,
                //BranchId = x.BranchId,
                Note = x.Note,
            }).ToList();

            return await cartService.GetCartItemsResponse(cartItemResponses);
        }

        private List<string> GetProductImages(Product product)
        {
            return string.IsNullOrEmpty(product.Images)
                ? new List<string>()
                : product.Images.Split(",").Select(x =>
                    {
                        var trimmed = x.Trim();
                        if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                            return trimmed;
                        else
                            return $"{hostUrl}/uploads/images/products/{trimmed}";
                    }).ToList();
        }

        #endregion

        #region "Place order"

        public async Task<OrderResponse> CreateAsync(AdminCreateOrderRequset dto, bool? isSave)
        {
            var membership = await membershipService.GetByPhoneNumber(dto.PhoneNumber);
            if (membership == null)
            {
                membership = new Membership()
                {
                    Avatar = string.Empty,
                    MiniAppId = string.Empty,
                    UserZaloId = string.Empty,
                    UserZaloName = dto.MembershipName,
                    DisplayName = dto.MembershipName,
                    Address = dto.Address,
                    PhoneNumber = !string.IsNullOrEmpty(dto.PhoneNumber) ? dto.PhoneNumber : string.Empty,
                };
                await membershipService.CreateAsync(membership);
            }
            if (!dto.Products.Any())
            {
                throw new CustomException(200, "Không có sản phẩm nào trong đơn hàng!");
            }

            var order = mapper.Map<Order>(dto);
            order.UserZaloId = membership.Id;
            order.OrderStatus = EOrder.Pending;
            order.Total = 0;
            order.ShippingFee = 0;

            #region "Process voucher comment"
            // Lấy danh sách voucher nếu có, Gộp và merge danh sách voucher codes
            //var allVoucherCodes = (dto.VoucherCode?.Split(',').Select(x => x.Trim()) ?? Enumerable.Empty<string>())
            //    .Concat(dto?.VoucherCodes ?? Enumerable.Empty<string>())
            //    .Where(x => !string.IsNullOrWhiteSpace(x))
            //    .Distinct()
            //    .ToList();

            // Gọi dịch vụ lấy danh sách voucher nếu có mã
            //var (productVouchers, shippingVouchers, orderVouchers) = allVoucherCodes.Any()
            //    ? await voucherService.CategorizeVouchers(allVoucherCodes, membership.Id)
            //    : (new(), new(), new());

            //// Lấy danh sách tất cả mã hợp lệ từ kết quả trả về
            //var validVoucherCodes = productVouchers
            //    .Concat(shippingVouchers)
            //    .Concat(orderVouchers)
            //    .Select(v => v.Code)
            //    .ToHashSet(StringComparer.OrdinalIgnoreCase); // tránh phân biệt hoa thường

            //// Tìm các mã không hợp lệ (mã đầu vào nhưng không xuất hiện trong kết quả)
            //var invalidCodes = allVoucherCodes
            //    .Where(code => !validVoucherCodes.Contains(code))
            //    .ToList();

            //// Nếu có mã không hợp lệ thì ném exception
            //if (invalidCodes.Any())
            //{
            //    var message = $"Các voucher sau không hợp lệ: {string.Join(", ", invalidCodes)}";
            //    throw new CustomException(4, message);
            //}
            #endregion

            var listAplliedVoucher = new List<OrderVoucher>();

            // lấy danh sách discount cho sản phẩm này, xong rồi discountDict
            var discount = await discountService.GetDiscountsByProductIdsAsync(dto.Products.Select(x => x.ProductId).ToList());
            var discountDict = discount.Item1; // Danh sách Discount
            var discountItemsDict = discount.Item2; // Danh sách DiscountItem

            var variantIds = dto.Products.Where(x => !string.IsNullOrEmpty(x.VariantId)).Select(x => x.VariantId!).ToList();
            var variantsDict = await productPropertyService.GetPriceByVariantIds(variantIds);

            // Xử lý sản phẩm và áp dụng voucher
            var processingOrderResult = await ProcessOrderDetails(isSave, order, dto.Products, new(), listAplliedVoucher, discountDict, discountItemsDict, variantsDict);
            order.Total = processingOrderResult.Total;
            order.DiscountAmount = processingOrderResult.DiscountAmount;

            // Áp dụng voucher đơn hàng
            // decimal appliedOrderDiscount = voucherService.ApplyOrderVouchers(order, orderVouchers, listAplliedVoucher);
            // order.Total = appliedOrderDiscount;

            //// Trừ điểm thưởng (nếu có)
            //if (dto.UsePoint.GetValueOrDefault())
            //{
            //    var minusPoint = Math.Min(order.Total, membership.UsingPoint);
            //    order.DiscountAmount += minusPoint;
            //    order.PointUsage += (long)minusPoint;
            //    order.Total = Math.Max(0, order.Total - minusPoint);
            //}

            // Áp dụng voucher phí vận chuyển
            order.ShippingFee = await shippingFeeService.GetShippingFeeByTotalOrder(order.Total);
            // var appliedShippingDiscount = voucherService.ApplyShippingVouchers(order, shippingVouchers, listAplliedVoucher);
            order.ShippingFee = Math.Max(0, 0);

            // Cập nhật tổng tiền cuối cùng
            order.Total += order.ShippingFee;
            order.DiscountAmount = Math.Min(order.DiscountAmount, order.Total);
            order.VoucherCode = string.Join(",", new List<string>());

            // Lưu đơn hàng nếu cần
            if (isSave.HasValue && isSave.Value)
            {
                _repository.Add(order); // lưu order
                _orderVoucherRepo.AddRange(listAplliedVoucher); // save order voucher
                _orderDetailRepo.AddRange(processingOrderResult.OrderDetails); // lưu order detail
                _orderDetailGiftRepo.AddRange(processingOrderResult.OrderDetailGifts); // lưu order detail gift
                var result = await unitOfWork.SaveChangesAsync();
                // await voucherService.UpdateMembershipVoucherByOrderId(order.Id, order.UserZaloId, EOffers.Used); // cập nhật voucher thành viên

                // Gửi thông báo khi tạo đơn hàng thành công
                if (result > 0)
                {
                    // Comment out old notification service
                    // var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 10;
                    // BackgroundJob.Schedule<INotificationService>(
                    //     x => x.ExecuteOrderEvent("Order.Create", order.Id),
                    //     TimeSpan.FromSeconds(delayInSeconds)
                    // );

                    // Use EventTrigger instead
                    var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 10;
                    var payload = CreateOrderPayloadFromData(order, membership);
                    BackgroundJob.Schedule<OrderService>(
                        x => x.TriggerOrderEvent("Order.Create", payload),
                        TimeSpan.FromSeconds(delayInSeconds));
                }
            }

            return mapper.Map<OrderResponse>((order, membership));
        }

        public async Task<OrderResponse> PlacedOrder(string userZaloId, OrderRequest dto, bool? isSave)
        {
            var membership = await membershipService.GetByUserZaloId(userZaloId);
            if (membership == null)
            {
                throw new CustomException(200, $"Không tìm thấy khách hàng với số điện thoại: {dto.PhoneNumber}");
            }
            if (!dto.Products.Any())
            {
                throw new CustomException(200, "Không có sản phẩm nào trong đơn hàng!");
            }

            var order = mapper.Map<Order>(dto);
            order.UserZaloId = userZaloId;
            order.OrderStatus = EOrder.Pending;
            order.Total = 0;
            order.ShippingFee = 0;

            // Lấy danh sách voucher nếu có, Gộp và merge danh sách voucher codes
            var allVoucherCodes = (dto.VoucherCode?.Split(',').Select(x => x.Trim()) ?? Enumerable.Empty<string>())
                .Concat(dto?.VoucherCodes ?? Enumerable.Empty<string>())
                .Where(x => !string.IsNullOrWhiteSpace(x))
                .Distinct()
                .ToList();

            // Gọi dịch vụ lấy danh sách voucher nếu có mã
            var (productVouchers, shippingVouchers, orderVouchers) = allVoucherCodes.Any()
                ? await voucherService.CategorizeVouchers(allVoucherCodes, userZaloId)
                : (new(), new(), new());


            // Lấy danh sách tất cả mã hợp lệ từ kết quả trả về
            var validVoucherCodes = productVouchers
                .Concat(shippingVouchers)
                .Concat(orderVouchers)
                .Select(v => v.Code)
                .ToHashSet(StringComparer.OrdinalIgnoreCase); // tránh phân biệt hoa thường

            // Tìm các mã không hợp lệ (mã đầu vào nhưng không xuất hiện trong kết quả)
            var invalidCodes = allVoucherCodes
                .Where(code => !validVoucherCodes.Contains(code))
                .ToList();

            // Nếu có mã không hợp lệ thì ném exception
            if (invalidCodes.Any())
            {
                var message = $"Các voucher sau không hợp lệ: {string.Join(", ", invalidCodes)}";
                throw new CustomException(4, message);
            }

            var listAplliedVoucher = new List<OrderVoucher>();

            // lấy danh sách discount cho sản phẩm này, xong rồi discountDict
            var discount = await discountService.GetDiscountsByProductIdsAsync(dto.Products.Select(x => x.ProductId).ToList());
            var discountDict = discount.Item1; // Danh sách Discount
            var discountItemsDict = discount.Item2; // Danh sách DiscountItem

            var variantIds = dto.Products.Where(x => !string.IsNullOrEmpty(x.VariantId)).Select(x => x.VariantId!).ToList();
            var variantsDict = await productPropertyService.GetPriceByVariantIds(variantIds);

            // Xử lý sản phẩm và áp dụng voucher
            var processingOrderResult = await ProcessOrderDetails(isSave, order, dto.Products, productVouchers, listAplliedVoucher, discountDict, discountItemsDict, variantsDict);
            order.Total = processingOrderResult.Total;
            order.DiscountAmount = processingOrderResult.DiscountAmount;

            // Áp dụng voucher đơn hàng
            decimal appliedOrderDiscount = voucherService.ApplyOrderVouchers(order, orderVouchers, listAplliedVoucher);
            order.Total = appliedOrderDiscount;

            // Trừ điểm thưởng (nếu có)
            if (dto.UsePoint.GetValueOrDefault())
            {
                var minusPoint = Math.Min(order.Total, membership.UsingPoint);
                order.DiscountAmount += minusPoint;
                order.PointUsage += (long)minusPoint;
                order.Total = Math.Max(0, order.Total - minusPoint);
            }

            // Áp dụng voucher phí vận chuyển
            order.ShippingFee = await shippingFeeService.GetShippingFeeByTotalOrder(order.Total);
            var appliedShippingDiscount = voucherService.ApplyShippingVouchers(order, shippingVouchers, listAplliedVoucher);
            order.ShippingFee = Math.Max(0, appliedShippingDiscount);

            // Cập nhật tổng tiền cuối cùng
            order.Total += order.ShippingFee;
            //order.DiscountAmount = Math.Min(order.DiscountAmount, order.Total);
            order.VoucherCode = string.Join(",", allVoucherCodes);

            // Lưu đơn hàng nếu cần
            if (isSave.HasValue && isSave.Value)
            {
                _repository.Add(order); // lưu order
                _orderVoucherRepo.AddRange(listAplliedVoucher); // save order voucher
                _orderDetailRepo.AddRange(processingOrderResult.OrderDetails); // lưu order detail
                _orderDetailGiftRepo.AddRange(processingOrderResult.OrderDetailGifts); // lưu order detail gift

                _pointHistory.Add(new PointHistory()
                {
                    UserZaloId = userZaloId,
                    Amount = -order.PointUsage,
                    Type = "PLACED_ORDER",
                    ReferenceId = order.Id,
                });

                var result = await unitOfWork.SaveChangesAsync();
                await voucherService.UpdateMembershipVoucherByOrderId(order.Id, order.UserZaloId, EOffers.Used); // cập nhật voucher thành viên

                // Gửi thông báo khi tạo đơn hàng thành công
                if (result > 0)
                {
                    // Comment out old notification service
                    // var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 10;
                    // BackgroundJob.Schedule<INotificationService>(
                    //     x => x.ExecuteOrderEvent("Order.Create", order.Id),
                    //     TimeSpan.FromSeconds(delayInSeconds)
                    // );

                    // Use EventTrigger instead
                    var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 10;
                    var payload = CreateOrderPayloadFromData(order, membership);
                    BackgroundJob.Schedule<OrderService>(
                        x => x.TriggerOrderEvent("Order.Create", payload),
                        TimeSpan.FromSeconds(delayInSeconds));
                }
            }

            return mapper.Map<OrderResponse>((order, membership));
        }

        public async Task<Order> UpdateOrderDetails(string id, OrderRequest dto, bool? isSave)
        {
            var order = await GetByIdAsync(id);
            if (order == null)
            {
                throw new CustomException(2, "Không tìm thấy đơn hàng này!");
            }
            // Chỉ xử lý khi có yêu cầu thay đổi chi tiết đơn hàng
            if (!dto.IsChangeOrderDetails.GetValueOrDefault())
            {
                return order;
            }

            // Reset các giá trị tài chính  
            order.Total = 0;
            order.DiscountAmount = 0;
            order.ShippingFee = 0;

            // Lấy danh sách voucher từ mã nếu có
            var (productVouchers, shippingVouchers, orderVouchers) =
                string.IsNullOrEmpty(dto.VoucherCode)
                    ? (new List<Voucher>(), new List<Voucher>(), new List<Voucher>())
                    : await voucherService.CategorizeVouchers(
                        dto.VoucherCode.Split(',').Select(x => x.Trim()).ToList(),
                        order.UserZaloId);

            var listAppliedVouchers = new List<OrderVoucher>();

            // Lấy danh sách chi tiết đơn hàng hiện tại và chuyển thành dictionary để truy xuất nhanh
            var existingOrderDetails = await GetOrderItemsByOrderId(order.Id);
            var existingOrderDict = existingOrderDetails.ToDictionary(d => d.ProductId);

            // Lấy thông tin sản phẩm cần cập nhật
            var productIds = dto.Products.Select(x => x.ProductId).ToList();
            var products = await productService.GetByIdsAsync(productIds);
            var productDict = products.ToDictionary(p => p.Id);

            var orderDetails = new List<OrderDetail>();
            foreach (var item in dto.Products)
            {
                // Kiểm tra sản phẩm tồn tại
                if (!productDict.TryGetValue(item.ProductId, out var product))
                    throw new CustomException(3, $"Không tìm thấy sản phẩm với ID {item.ProductId}!");

                OrderDetail orderDetail;
                // Kiểm tra và cập nhật sản phẩm hiện có hoặc tạo mới
                if (existingOrderDict.TryGetValue(item.ProductId, out var existingDetail))
                {
                    // Cập nhật chi tiết đơn hàng hiện tại
                    existingDetail.Quantity = item.Quantity;
                    existingDetail.OriginalPrice = product.Price;
                    orderDetail = existingDetail;
                }
                else
                {
                    // Tạo chi tiết đơn hàng mới
                    orderDetail = mapper.Map<OrderDetail>(item);
                    orderDetail.OrderId = order.Id;
                    orderDetail.OriginalPrice = product.Price;
                }

                // Áp dụng voucher sản phẩm nếu có
                orderDetail.DiscountPrice = voucherService.ApplyProductVouchers(
                    orderDetail, productVouchers, listAppliedVouchers);

                orderDetails.Add(orderDetail);
            }

            // Xác định các chi tiết đơn hàng cần xoá (không còn trong yêu cầu cập nhật)
            var removedOrderDetails = existingOrderDetails
                .Where(d => !productIds.Contains(d.ProductId))
                .ToList();

            // Tính toán lại tổng tiền từ chi tiết đơn hàng
            order.Total = orderDetails.Sum(d => d.TotalPrice);
            order.DiscountAmount = orderDetails.Sum(d => d.OriginalPrice * d.Quantity - d.DiscountPrice * d.Quantity);

            // Áp dụng voucher đơn hàng
            order.Total = voucherService.ApplyOrderVouchers(order, orderVouchers, listAppliedVouchers);

            // Áp dụng voucher phí vận chuyển
            order.ShippingFee = await shippingFeeService.GetShippingFeeByTotalOrder(order.Total);
            order.ShippingFee = Math.Max(0, voucherService.ApplyShippingVouchers(order, shippingVouchers, listAppliedVouchers));

            // Cập nhật tổng tiền cuối cùng
            order.Total += order.ShippingFee;

            // Trừ điểm đã sử dụng
            if (order.PointUsage > 0)
                order.Total = Math.Max(0, order.Total - order.PointUsage);

            // Đảm bảo số tiền giảm giá không vượt quá tổng tiền
            order.DiscountAmount = Math.Min(order.DiscountAmount, order.Total);

            // Cập nhật mã voucher
            if (!string.IsNullOrEmpty(dto.VoucherCode))
                order.VoucherCode = dto.VoucherCode;

            // Lưu thay đổi vào database nếu cần
            if (isSave.HasValue && isSave.Value)
            {
                // Lưu voucher đã áp dụng
                if (listAppliedVouchers.Any())
                {
                    var existingVouchers = await GetOrderVoucherByOrderId(order.Id);
                    var vouchersToRemove = existingVouchers.ToList();
                    _orderVoucherRepo.DeleteRange(vouchersToRemove);
                    _orderVoucherRepo.AddRange(listAppliedVouchers);
                }

                // Cập nhật chi tiết đơn hàng
                _orderDetailRepo.UpdateRange(orderDetails);

                // Xoá chi tiết đơn hàng không còn sử dụng
                if (removedOrderDetails.Any())
                    _orderDetailRepo.DeleteRange(removedOrderDetails);

                // Lưu tất cả thay đổi
                var result = await unitOfWork.SaveChangesAsync();

                // Gửi thông báo khi cập nhật đơn hàng thành công
                if (result > 0)
                {
                    // Comment out old notification service
                    // var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 10;
                    // BackgroundJob.Schedule<INotificationService>(
                    //     x => x.ExecuteOrderEvent("Order.Update", order.Id),
                    //     TimeSpan.FromSeconds(delayInSeconds)
                    // );

                    // Use EventTrigger instead
                    var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 10;
                    var payload = await CreateOrderPayload(order.Id, order.UserZaloId);
                    BackgroundJob.Schedule<OrderService>(
                        x => x.TriggerOrderEvent("Order.Update", payload),
                        TimeSpan.FromSeconds(delayInSeconds));
                }
            }

            return order;
        }

        public async Task<int> UpdateZaloOrderId(string id, string zaloOrderId)
        {
            var order = await GetByIdAsync(id);

            if (order == null)
            {
                throw new CustomException(200, "Không tìm thấy đơn hàng này!");
            }
            order.ZaloOrderId = zaloOrderId;
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<Order> UpdateZaloOrderStatus(string zaloOrderId, int method, string channel)
        {
            var order = await _repository.AsQueryable().FirstOrDefaultAsync(x => x.ZaloOrderId == zaloOrderId);

            if (order == null)
            {
                throw new CustomException(200, "Không tìm thấy đơn hàng này!");
            }

            await base.UpdateAsync(order);
            return order;
        }

        public async Task<Order> UpdatePaymentChannel(string id, string channel)
        {
            var order = await GetByIdAsync(id);
            if (order == null)
            {
                throw new NotFoundException(404, "Không tìm thấy đơn hàng này!");
            }
            await unitOfWork.SaveChangesAsync();
            return order;
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var order = await GetByIdAsync(id);
            if (order == null)
            {
                throw new CustomException(200, "Không tìm thấy đơn hàng này.");
            }
            order.OrderStatus = EOrder.Cancelled;
            // hoàn lại voucher
            if (!string.IsNullOrEmpty(order.VoucherCode))
            {

            }
            return await unitOfWork.SaveChangesAsync();
        }

        // xử lý order detail
        private async Task<ProcessingOrderDetailResult> ProcessOrderDetails(bool? isSave, Order order,
                List<OrderDetailRequest> productDtos,
                List<Voucher> productVouchers,
                List<OrderVoucher> appliedVouchers,
                IEnumerable<Discount> discounts,
                IEnumerable<DiscountItem> discountItems,
                Dictionary<string, decimal> variantDicts)
        {
            var orderDetails = new List<OrderDetail>();
            var orderDetailGifts = new List<OrderDetailGift>();

            decimal total = 0, discountAmount = 0;

            var productIds = productDtos.Select(x => x.ProductId).ToList();
            var products = await productService.GetByIdsAsync(productIds);
            var productDict = products.ToDictionary(p => p.Id);

            var voucherProductDict = await voucherService.GetAppliedProductsForVouchersAsync(productVouchers, productIds);

            foreach (var item in productDtos)
            {
                if (!productDict.TryGetValue(item.ProductId, out var product))
                {
                    throw new CustomException(3, $"Không tìm thấy sản phẩm với Id: {item.ProductId}!");
                }

                var detail = mapper.Map<OrderDetail>(item);
                detail.OrderId = order.Id;

                // Ưu tiên dùng giá variant nếu có, ngược lại dùng giá gốc của sản phẩm
                var basePrice = !string.IsNullOrEmpty(detail.VariantId) && variantDicts.TryGetValue(detail.VariantId, out var variantPrice)
                    ? variantPrice
                    : product.Price;

                detail.OriginalPrice = basePrice;
                //detail.DiscountPrice = basePrice;

                long quantity = detail.Quantity;

                // 1. Áp dụng discount trước (trên basePrice)
                decimal priceAfterDiscount = basePrice;
                if (discountItems.Any() && discounts.Any())
                {
                    var (_, discountedPrice) = discountService.CalculateDiscountedPrice(
                        product.Id, basePrice, discountItems, discounts
                    );
                    priceAfterDiscount = discountedPrice;
                }

                // 2. Gán giá đã discount vào detail.OriginalPrice
                detail.OriginalPrice = priceAfterDiscount;

                // 3. Áp dụng voucher (nếu có) trên giá đã discount
                List<Voucher> applicableVouchers = productVouchers
                    .Where(v =>
                        (v.IsAllProducts && v.VoucherType == EVoucherType.Product)
                        || (voucherProductDict.TryGetValue(v.Id, out var appliedProducts)
                            && appliedProducts.Contains(item.ProductId))
                    ).ToList();

                long voucherApplyCount = applicableVouchers.Any() ? 1 : 0; // chỉ 1 sản phẩm được giảm
                long noVoucherCount = quantity - voucherApplyCount;

                decimal priceWithVoucher = priceAfterDiscount;
                if (applicableVouchers.Any())
                {
                    // Nhận giá đã discount trước
                    priceWithVoucher = voucherService.ApplyProductVouchers(detail, applicableVouchers, appliedVouchers);
                }

                // Cập nhật detail với giá cuối cùng (giá đã áp voucher nếu có)
                detail.DiscountPrice = priceWithVoucher;
                detail.OriginalPrice = basePrice; // Set lại giá gốc

                orderDetails.Add(detail);

                // Tổng tiền cuối cùng
                total += (priceWithVoucher * voucherApplyCount) + (priceAfterDiscount * noVoucherCount);

                // Tổng giảm giá (so với giá gốc)
                discountAmount += (basePrice - priceWithVoucher) * voucherApplyCount;
                discountAmount += (basePrice - priceAfterDiscount) * noVoucherCount;

                if (isSave.HasValue && isSave.Value)
                {
                    // thêm quà vào cho thằng orderDetail
                    var gifts = await promotionService.GetProductGiftByProductId(item.ProductId);
                    if (gifts.Any())
                    {
                        orderDetailGifts.AddRange(gifts.Select(x => new OrderDetailGift() { OrderDetailId = detail.Id, ProductId = x.Id }).ToList());
                    }
                }

                //orderDetails.Add(detail);
                //total += detail.DiscountPrice * detail.Quantity;
            }

            return new ProcessingOrderDetailResult(orderDetails, orderDetailGifts, total, discountAmount);
        }

        #endregion

        #region "Update status order"

        public async Task<int> CancelOrder(string id, string? reason)
        {
            var order = await GetByIdAsync(id);
            if (order == null)
            {
                throw new CustomException(200, "Không tìm thấy đơn hàng này!");
            }
            order.CancelReason = reason;
            order.OrderStatus = EOrder.Cancelled;

            // refund vouche
            if (!string.IsNullOrEmpty(order.VoucherCode))
            {

            }
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> UpdateAsync(string id, OrderRequest dto)
        {
            // 1️⃣ Tìm đơn hàng theo ID
            var order = await GetByIdAsync(id);
            if (order == null)
            {
                throw new CustomException(200, "Không tìm thấy đơn hàng này!");
            }

            var currentBranchId = order.BranchId;
            var currentAddressId = order.MembershipAddressId;
            var currentVATId = order.MembershipVATId;

            // 2️⃣ Cập nhật thông tin đơn hàng từ DTO
            mapper.Map(dto, order);

            order.BranchId = currentBranchId;
            order.MembershipAddressId = currentAddressId;
            order.MembershipVATId = currentVATId;

            // 3️⃣ Nếu có thay đổi sản phẩm trong đơn hàng, cập nhật lại chi tiết đơn hàng
            if (dto.IsChangeOrderDetails.HasValue && dto.IsChangeOrderDetails.Value)
            {
                if (!dto.Products.Any())
                {
                    throw new CustomException(200, "Không có sản phẩm nào trong đơn hàng!");
                }

                order = await UpdateOrderDetails(id, dto, true);
            }

            // 4️⃣ Kiểm tra nếu đơn hàng hoàn tất và đã thanh toán, cập nhật trạng thái voucher thành viên
            var isOrderCompletedAndPaid = (EOrder)dto.OrderStatus == EOrder.Completed && (EPayment)dto.PaymentStatus == EPayment.Paid;
            if (isOrderCompletedAndPaid)
            {
                var membership = await membershipService.GetByUserZaloId(order.UserZaloId) ?? await membershipService.GetByIdAsync(order.UserZaloId);
                if (membership != null && !string.IsNullOrEmpty(membership.RankingId))
                {
                    var rankQuery = unitOfWork.GetRepository<Rank>().AsQueryable();
                    var rank = await rankQuery.FirstOrDefaultAsync(x => x.Id == membership.RankingId);
                    if (rank != null)
                    {
                        var amount = (long)(order.Total * (decimal)rank.ConvertRate);
                        membership.RankingPoint += amount;

                        // Tính toán hạng kế tiếp
                        var nextRank = await rankQuery
                            .Where(r => r.RankingPoint <= membership.RankingPoint)
                            .OrderByDescending(r => r.RankingPoint)
                            .FirstOrDefaultAsync();

                        if (nextRank != null && nextRank.Id != membership.RankingId)
                        {
                            membership.RankingId = nextRank.Id;
                        }

                        // check xem đã cộng điểm chưa, cộng rồi thì ko cộng nữa
                        var isPlusPointForThisOrder = _pointHistory.AsQueryable().Any(x => x.ReferenceId == order.Id &&
                                                                                           !string.IsNullOrEmpty(x.Type) &&
                                                                                           x.Type.Contains("PLUS"));
                        if (!isPlusPointForThisOrder)
                        {
                            // luu log
                            _pointHistory.Add(new PointHistory()
                            {
                                UserZaloId = membership.UserZaloId,
                                Amount = amount,
                                Type = "PLUS",
                                ReferenceId = order.Id
                            });

                            await membershipService.UpdateAsync(membership);
                        }
                    }
                }

                // Tính toán và lưu hoa hồng khi đơn hàng hoàn thành
                BackgroundJob.Schedule<ICommissionService>(
                    x => x.CalculateAndSaveCommissionForOrder(order.Id),
                    TimeSpan.FromSeconds(10)
                );
                // await commissionService.CalculateAndSaveCommissionForOrder(order.Id);
            }

            // 5️⃣ Lưu tất cả thay đổi vào database
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> UpdateStatusAsync(string id, string? paymentMethod, EOrder? orderStatus, EPayment? paymentStatus)
        {
            var order = await _repository.AsQueryable().FirstOrDefaultAsync(x => x.ZaloOrderId == id);
            if (order == null)
            {
                throw new CustomException(200, "Không tìm thấy đơn hàng này");
            }

            // trạng thái đơn hàng
            if (orderStatus.HasValue)
            {
                order.OrderStatus = orderStatus.Value;
            }

            // trạng thái thanh toán
            if (paymentStatus.HasValue)
            {
                order.PaymentStatus = paymentStatus.Value;
            }

            // phương thức thanh toán
            if (!string.IsNullOrEmpty(paymentMethod))
            {
                order.PaymentMethod = paymentMethod;
            }

            // cập nhật voucher nếu như đơn hàng thành công
            if (orderStatus.HasValue && orderStatus.Value == EOrder.Completed &&
               paymentStatus.HasValue && paymentStatus.Value == EPayment.Paid &&
               !string.IsNullOrEmpty(order.VoucherCode))
            {
                await voucherService.UpdateMembershipVoucherByOrderId(order.Id, order.UserZaloId, EOffers.Used);
            }

            // Comment out old notification service
            // var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 60;
            // BackgroundJob.Schedule<INotificationService>(
            //     x => x.ExecuteOrderEvent("Order.Update", order.Id),
            //     TimeSpan.FromSeconds(delayInSeconds)
            // );

            // Use EventTrigger instead
            var delayInSeconds = configuration.GetSection("Hangfire:DelayInSeconds").Get<int?>() ?? 60;
            var payload = await CreateOrderPayload(order.Id, order.UserZaloId, orderStatus, paymentStatus);
            BackgroundJob.Schedule<OrderService>(
                x => x.TriggerOrderEvent("Order.StatusUpdate", payload),
                TimeSpan.FromSeconds(delayInSeconds));
            return await base.UpdateAsync(order);
        }

        #endregion

        #region Export Order

        public async Task<byte[]> ExportOrders(DateTime? startDate, DateTime? endDate, short? orderStatus, short? paymentStatus)
        {
            var query = new PurchaseQueryParams()
            {
                StartDate = startDate,
                EndDate = endDate,
                OrderStatus = orderStatus,
                PaymentStatus = paymentStatus,
            };

            var orders = ApplyFilters(_repository.AsQueryable(), query, null);

            var listOrder = await orders.OrderByDescending(x => x.CreatedDate).ToListAsync();
            var memberships = await membershipService.GetByUserZaloIds(listOrder.Select(x => x.UserZaloId).ToList());

            var items = listOrder.Select(x =>
            {
                var membership = memberships.FirstOrDefault(m => m.UserZaloId == x.UserZaloId);
                return mapper.Map<OrderResponse>((x, membership));
            });

            if (!items.Any())
            {
                throw new CustomException(400, "Không có dữ liệu nào");
            }

            // Dictionary ánh xạ tiêu đề tiếng Anh sang tiếng Việt
            var mappingDictHeaders = new Dictionary<string, string>
            {
                { "OrderId", "Mã đơn hàng" },
                { "OrderStatus", "Trạng thái đơn hàng" },
                { "PaymentStatus", "Trạng thái thanh toán" },
                { "TotalAmount", "Tổng tiền" },
                { "ShippingFee", "Phí vận chuyển" },
                { "UserZaloName", "Tên khách hàng" },
                { "PhoneNumber", "Số điện thoại" },
                { "Address", "Địa chỉ" },
                { "Note", "Ghi chú" },
                { "CreatedDate", "Ngày mua hàng" }
            };

            // Tạo một dictionary để lưu trữ dữ liệu theo cột
            var data = new Dictionary<string, List<string>>();
            foreach (var key in mappingDictHeaders.Keys)
            {
                data[key] = new List<string>();
            }

            var paymentStatusDict = new Dictionary<int, string>()
            {
                {0, "Chưa thanh toán" },   // Unpaid
                {1, "Đã thanh toán" },     // Paid
                {2, "Thanh toán thất bại" }, // Failed
                {3, "Đã hoàn tiền" }        // Refunded
            };

            var orderStatusDict = new Dictionary<int, string>()
            {
                {0, "Chờ xác nhận" },        // Pending
                {1, "Đã xác nhận" },         // Confirmed
                {2, "Đã hoàn thành" },       // Completed
                {3, "Đã hủy" }               // Cancelled
            };

            foreach (var item in items)
            {
                // Ánh xạ từ OrderStatusDict
                data["OrderStatus"].Add(orderStatusDict.ContainsKey(item.OrderStatus) ? orderStatusDict[item.OrderStatus] : "Không xác định");

                // Ánh xạ từ PaymentStatusDict
                data["PaymentStatus"].Add(paymentStatusDict.ContainsKey(item.PaymentStatus) ? paymentStatusDict[item.PaymentStatus] : "Không xác định");

                data["OrderId"].Add(item.OrderId);
                data["TotalAmount"].Add(item.TotalAmount.ToString("N0"));
                data["ShippingFee"].Add(item.ShippingFee.ToString("N0"));
                data["UserZaloName"].Add(item.UserZaloName ?? string.Empty);
                data["PhoneNumber"].Add(item.PhoneNumber ?? string.Empty);
                data["Address"].Add(item.Address ?? string.Empty);
                data["Note"].Add(item.Note ?? string.Empty);
                data["CreatedDate"].Add(item.CreatedDate.ToString("yyyy-MM-dd"));
            }

            // Ánh xạ tiêu đề tiếng Anh sang tiếng Việt
            var mappedData = new Dictionary<string, List<string>>();
            foreach (var key in data.Keys)
            {
                if (mappingDictHeaders.ContainsKey(key))
                {
                    var mappedHeader = mappingDictHeaders[key];
                    mappedData[mappedHeader] = data[key];
                }
            }

            return await ExportHandler.ExportData("Đơn hàng", mappedData);
        }

        public async Task<int> QuickUpdate(QuickUpdateRequest model)
        {
            var orders = await _repository.AsQueryable().Where(x => model.OrderIds.Contains(x.Id)).ToListAsync();
            orders.ForEach(x =>
            {
                x.OrderStatus = (EOrder)model.OrderStatus;
                x.PaymentStatus = (EPayment)model.PaymentStatus;
            });

            return await base.UpdateRangeAsync(orders);
        }

        #endregion

        #region EventTrigger Methods

        public async Task TriggerOrderEvent(string eventName, OrderPayload payload)
        {
            await _mediator.Send(new EmitEventArgs
            {
                EventName = eventName,
                TriggerPhoneNumber = string.IsNullOrEmpty(payload.PhoneNumber) ? null : payload.PhoneNumber,
                TriggerZaloIdByOA = string.IsNullOrEmpty(payload.UserZaloIdByOA) ? null : payload.UserZaloIdByOA,
                Payload = payload
            });
        }

        public async Task<OrderPayload> CreateOrderPayload(string orderId, string userZaloId, EOrder? orderStatus = null, EPayment? paymentStatus = null)
        {
            var order = await GetByIdAsync(orderId);
            var membership = await membershipService.GetByUserZaloId(userZaloId);

            var finalOrderStatus = orderStatus ?? order?.OrderStatus ?? EOrder.Pending;
            var finalPaymentStatus = paymentStatus ?? order?.PaymentStatus ?? EPayment.Unpaid;

            return new OrderPayload
            {
                OrderId = orderId,
                UserZaloId = userZaloId,
                UserZaloName = membership?.UserZaloName ?? string.Empty,
                UserZaloIdByOA = membership?.UserZaloIdByOA ?? string.Empty,
                DeliveryAddress = membership?.Address ?? string.Empty,
                PhoneNumber = membership?.PhoneNumber ?? string.Empty,
                Total = order?.Total ?? 0,
                DiscountAmount = order?.DiscountAmount ?? 0,
                OrderStatus = (int)finalOrderStatus,
                PaymentStatus = (int)finalPaymentStatus,
                OrderStatusLabel = GetOrderStatusLabel(finalOrderStatus),
                PaymentStatusLabel = GetPaymentStatusLabel(finalPaymentStatus)
            };
        }

        public OrderPayload CreateOrderPayloadFromData(Order order, Membership membership, EOrder? orderStatus = null, EPayment? paymentStatus = null)
        {
            var finalOrderStatus = orderStatus ?? order.OrderStatus;
            var finalPaymentStatus = paymentStatus ?? order.PaymentStatus;

            return new OrderPayload
            {
                OrderId = order.Id,
                UserZaloId = order.UserZaloId,
                UserZaloName = membership?.UserZaloName ?? string.Empty,
                UserZaloIdByOA = membership?.UserZaloIdByOA ?? string.Empty,
                DeliveryAddress = membership?.Address ?? string.Empty,
                PhoneNumber = membership?.PhoneNumber ?? string.Empty,
                Total = order.Total,
                DiscountAmount = order.DiscountAmount,
                OrderStatus = (int)finalOrderStatus,
                PaymentStatus = (int)finalPaymentStatus,
                OrderStatusLabel = GetOrderStatusLabel(finalOrderStatus),
                PaymentStatusLabel = GetPaymentStatusLabel(finalPaymentStatus)
            };
        }

        private string GetOrderStatusLabel(EOrder orderStatus)
        {
            return orderStatus switch
            {
                EOrder.Pending => "Chờ xác nhận",
                EOrder.Confirmed => "Đã xác nhận",
                EOrder.Completed => "Đã hoàn thành",
                EOrder.Cancelled => "Đã hủy",
                _ => "Không xác định"
            };
        }

        private string GetPaymentStatusLabel(EPayment paymentStatus)
        {
            return paymentStatus switch
            {
                EPayment.Unpaid => "Chưa thanh toán",
                EPayment.Paid => "Đã thanh toán",
                EPayment.Failed => "Thanh toán thất bại",
                EPayment.Refunded => "Đã hoàn tiền",
                _ => "Không xác định"
            };
        }

        #endregion
    }

    public class OrderPayload
    {
        public string? OrderId { get; set; }
        public string? UserZaloId { get; set; }
        public string? UserZaloName { get; set; }
        public string? UserZaloIdByOA { get; set; }
        public string? DeliveryAddress { get; set; }
        public string? PhoneNumber { get; set; }
        public decimal Total { get; set; }
        public decimal DiscountAmount { get; set; }
        public int OrderStatus { get; set; }
        public int PaymentStatus { get; set; }
        public string OrderStatusLabel { get; set; } = string.Empty;
        public string PaymentStatusLabel { get; set; } = string.Empty;
    }

    internal class ProcessingOrderDetailResult
    {
        public decimal Total { get; }
        public decimal DiscountAmount { get; }
        public List<OrderDetail> OrderDetails { get; }
        public List<OrderDetailGift> OrderDetailGifts { get; }

        public ProcessingOrderDetailResult(List<OrderDetail> orderDetails, List<OrderDetailGift> orderDetailGifts, decimal total, decimal discountAmount)
        {
            Total = total;
            DiscountAmount = discountAmount;
            OrderDetails = orderDetails;
            OrderDetailGifts = orderDetailGifts;
        }
    }
}
