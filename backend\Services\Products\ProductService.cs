﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Common;
using MiniAppCore.Base.Interface;
using MiniAppCore.Entities.Categories;
using MiniAppCore.Entities.Offers.Discounts;
using MiniAppCore.Entities.Products;
using MiniAppCore.Entities.Products.ProductInfo;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Common;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Products;
using MiniAppCore.Models.Responses.Categories;
using MiniAppCore.Models.Responses.Products;
using MiniAppCore.Services.Branches;
using MiniAppCore.Services.Categories;
using MiniAppCore.Services.Offers.Discounts;
using MiniAppCore.Services.Offers.Promotions;
using MiniAppCore.Services.Products.Variants;

namespace MiniAppCore.Services.Products
{
    public class ProductService(IUnitOfWork unitOfWork,
                                IHttpContextAccessor httpContextAccessor,
                                IWebHostEnvironment env,
                                IMapper mapper,
                                IBranchService branchService,
                                ICategoryService categoryService,
                                IDiscountService discountService,
                                IPromotionService promotionService,
                                IProductPropertyService productPropertyService) : OrderedEntityService<Product>(unitOfWork), IProductService
    {
        private readonly string hostUrl = $"{httpContextAccessor?.HttpContext?.Request.Scheme}://{httpContextAccessor?.HttpContext?.Request.Host}";
        protected override string OrderColumnName => "DisplayOrder";
        public async Task<PagedResult<ProductResponse>> GetPage(ProductQueryParams query)
        {
            var products = _repository.AsQueryable().AsNoTracking();

            // Join với products để tính giá đã giảm
            var productsWithDiscount = products
                .GroupJoin(
                    unitOfWork.GetRepository<DiscountItem>().AsQueryable()
                        .Join(unitOfWork.GetRepository<Discount>().AsQueryable(),
                            di => di.DiscountId,
                            d => d.Id,
                            (di, d) => new { di.ProductId, d.Type, d.DiscountValue, d.MaxDiscountAmount, d.IsActive, d.StartDate, d.ExpiryDate }),
                    p => p.Id,
                    d => d.ProductId,
                    (p, d) => new { Product = p, Discount = d.FirstOrDefault() })
                .Select(x => new
                {
                    x.Product,
                    FinalPrice = x.Discount != null && x.Discount.IsActive && x.Discount.StartDate <= DateTime.Now && x.Discount.ExpiryDate >= DateTime.Now
                        ? x.Discount.Type == 1
                            ? x.Product.Price - (x.Product.Price * x.Discount.DiscountValue / 100 > x.Discount.MaxDiscountAmount
                                ? x.Discount.MaxDiscountAmount
                                : x.Product.Price * x.Discount.DiscountValue / 100)
                            : x.Product.Price - x.Discount.DiscountValue
                        : x.Product.Price
                });

            // Áp dụng các bộ lọc
            if (!string.IsNullOrEmpty(query.Keyword))
            {
                productsWithDiscount = productsWithDiscount.Where(p => p.Product.Name.Contains(query.Keyword));
            }

            // Lọc theo giá đã giảm
            if (query.MinPrice.HasValue)
            {
                productsWithDiscount = productsWithDiscount.Where(p => p.FinalPrice >= query.MinPrice.Value);
            }

            // filter max price
            if (query.MaxPrice.HasValue)
            {
                productsWithDiscount = productsWithDiscount.Where(p => p.FinalPrice <= query.MaxPrice.Value);
            }

            // Các bộ lọc khác (BranchId, IsGift, StockStatus, CategoryIds, v.v.) giữ nguyên
            if (!string.IsNullOrEmpty(query.BranchId))
            {
                var stockRepo = unitOfWork.GetRepository<ProductStock>().AsQueryable();
                productsWithDiscount = productsWithDiscount.Where(p => p.Product.IsGlobalProduct || stockRepo.Any(st => st.ProductId == p.Product.Id && st.BranchId == query.BranchId));
            }

            // check is gift
            if (query.IsGift.HasValue)
            {
                productsWithDiscount = productsWithDiscount.Where(p => p.Product.IsGift == query.IsGift.Value);
            }

            // check stock status
            if (query.StockStatus.Any())
            {
                productsWithDiscount = productsWithDiscount.Where(p => query.StockStatus.Contains(p.Product.Status));
            }

            if (!string.IsNullOrEmpty(query.CategoryId))
            {
                var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();
                var lstCategory = query.CategoryId.Split(",").ToList();
                productsWithDiscount = productsWithDiscount.Where(p => lstCategory.All(catId =>
                    productCategoryRepo.AsQueryable().Any(pc =>
                        pc.ProductId == p.Product.Id && pc.CategoryId == catId
                    )
                ));
            }

            if (!string.IsNullOrEmpty(query.CategoryChildId))
            {
                var productCategoryChildRepo = unitOfWork.GetRepository<ProductCategoryChild>();
                var lstCategoryChildId = query.CategoryChildId.Split(",").ToList();
                productsWithDiscount = productsWithDiscount.Where(p => lstCategoryChildId.All(catId =>
                    productCategoryChildRepo.AsQueryable().Any(pc =>
                        pc.ProductId == p.Product.Id && pc.CategoryChildId == catId
                    )
                ));
            }

            // query theo category
            if (query.CategoryIds.Any())
            {
                var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();
                // Use a more efficient set-based approach with grouping
                var productsWithAllCategories = productCategoryRepo.AsQueryable()
                    .Where(pc => query.CategoryIds.Contains(pc.CategoryId))
                    .GroupBy(pc => pc.ProductId)
                    .Where(g => g.Select(pc => pc.CategoryId).Distinct().Count() == query.CategoryIds.Count)
                    .Select(g => g.Key);

                productsWithDiscount = productsWithDiscount.Where(p => productsWithAllCategories.Contains(p.Product.Id));
            }

            // query theo branchIds
            if (query.BranchIds.Any())
            {
                var productStockRepo = unitOfWork.GetRepository<ProductStock>();
                var productsInBranches = productStockRepo.AsQueryable()
                        .Where(ps => query.BranchIds.Contains(ps.BranchId))
                        .Select(ps => ps.ProductId)
                        .Distinct();
                productsWithDiscount = productsWithDiscount.Where(p => productsInBranches.Contains(p.Product.Id));
            }

            // Tính tổng số phần tử và phân trang
            var totalItems = await productsWithDiscount.CountAsync();
            var totalPages = (int)Math.Ceiling(totalItems / (double)query.PageSize);

            var orderedList = query.IsOrderByCreatedDate
                ? productsWithDiscount.OrderBy(x => x.Product.CreatedDate)
                : productsWithDiscount.OrderBy(x => x.Product.DisplayOrder).ThenBy(x => x.Product.CreatedDate);

            // Lấy dữ liệu từ database
            var productList = await orderedList
                .Skip(query.Skip)
                .Take(query.PageSize)
                .Select(x => x.Product)
                .ToListAsync();

            // lấy danh sách discount cho sản phẩm này, xong rồi discountDict
            var discount = await discountService.GetDiscountsByProductIdsAsync(productList.Select(x => x.Id).ToList());
            var discountDict = discount.Item1; // Danh sách Discount
            var discountItemsDict = discount.Item2; // Danh sách DiscountItem

            // Ánh xạ dữ liệu và xử lý Images
            var items = productList.Select(p =>
            {
                var productResponse = mapper.Map<ProductResponse>(p);
                productResponse.Images = string.IsNullOrEmpty(p.Images)
                    ? new List<string>()
                    : p.Images.Split(',').Select(x =>
                    {
                        var trimmed = x.Trim();
                        if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                            return trimmed;
                        else
                            return $"{hostUrl}/uploads/images/products/{trimmed}";
                    }).ToList();

                // Tính giá sau khi áp dụng discount
                var (discountType, discountPrice) = discountService.CalculateDiscountedPrice(p, discountItemsDict, discountDict);
                productResponse.DiscountType = discountType;
                productResponse.DiscountPrice = discountPrice;

                return productResponse;
            }).ToList();

            return new PagedResult<ProductResponse>
            {
                Data = items,
                TotalPages = totalPages
            };
        }

        public async Task<ProductDetailResponse> GetProductDetailAsync(string id)
        {
            var product = await GetByIdAsync(id);
            if (product == null)
            {
                throw new CustomException(200, "Product not found!");
            }

            var productResponse = mapper.Map<ProductDetailResponse>(product);

            productResponse.Images = product?.Images?.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Select(x =>
            {
                var trimmed = x.Trim();
                if (trimmed.StartsWith("http://") || trimmed.StartsWith("https://"))
                    return trimmed;
                else
                    return $"{hostUrl}/uploads/images/products/{trimmed}";
            }).ToList() ?? new List<string>();

            var (productProperties, productVariants) = await productPropertyService.GetProductVariants(id);
            productResponse.Variants = productVariants;
            productResponse.Options = productProperties;

            // category
            {
                var categoryRepo = unitOfWork.GetRepository<Category>();
                var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();


                // danh sách category product
                var productCategories = await productCategoryRepo.AsQueryable()
                                                    .Where(x => x.ProductId == product!.Id)
                                                    .Select(x => x.CategoryId)
                                                    .ToListAsync();
                // danh sách category
                var categories = await categoryRepo.AsQueryable()
                                .Where(c => productCategories.Contains(c.Id))
                                .ToListAsync();

                productResponse.Categories = mapper.Map<List<CategoryResponse>>(categories, opt =>
                {
                    opt.Items["HostUrl"] = hostUrl;
                });
            }
            //Danh mục con
            var categoryChildRepo = unitOfWork.GetRepository<CategoryChild>();
            var productCategoryChildRepo = unitOfWork.GetRepository<ProductCategoryChild>();

            productResponse.CategorieChilds = await categoryChildRepo.AsQueryable()
                                .Where(cc => productCategoryChildRepo.AsQueryable()
                                    .Any(pc => pc.ProductId == product!.Id && pc.CategoryChildId == cc.Id))
                                .ToListAsync();

            // lấy danh sách discount, discountItem cho sản phẩm theo Id, xong rồi 
            var discount = await discountService.GetDiscountsByProductIdsAsync(new List<string> { product?.Id ?? string.Empty });
            var discounts = discount.Item1; // Danh sách Discount
            var discountItems = discount.Item2; // Danh sách DiscountItem

            // Tính giảm giá khi chưa có biến thể
            {
                var (discountType, discountPrice) = discountService.CalculateDiscountedPrice(product!, discountItems, discounts);
                productResponse.DiscountType = discountType;
                productResponse.DiscountPrice = discountPrice;
            }

            // tính giảm giá cho các biến thể của sản phẩm này
            if (discountItems.Any() && discounts.Any())
            {
                productResponse.Variants.ForEach(x =>
                {
                    var (discountType, discountPrice) = discountService.CalculateDiscountedPrice(product!.Id, x.OriginalPrice, discountItems, discounts);
                    x.Price = discountPrice;
                    x.DiscountType = discountType;
                    x.DiscountPrice = discountPrice;
                });
            }

            // gift
            productResponse.Gifts = await promotionService.GetProductGiftByProductId(id);

            return productResponse;
        }

        public async Task<int> ImportProduct(IFormFile file)
        {
            // Validate file is not null
            if (file == null || file.Length == 0)
                throw new CustomException(200, "No file was uploaded.");

            // Check file extension (allow only Excel files)
            var fileExt = Path.GetExtension(file.FileName).ToLower();
            if (fileExt != ".xlsx" && fileExt != ".xls")
                throw new CustomException(200, "Only Excel files (.xlsx or .xls) are allowed.");

            var listProduct = new List<Product>();
            var productCategories = new Dictionary<string, List<string>>(); // Product ID -> Category IDs

            // Get all categories for lookup
            var allCategories = await categoryService.GetAllAsync();
            var categoryNameToId = allCategories.ToDictionary(
                c => c.Name.ToLower().Trim(), // Use lowercase to make comparison case-insensitive
                c => c.Id
            );

            using (var stream = new MemoryStream())
            {
                await file.CopyToAsync(stream);
                stream.Position = 0; // Reset position to the beginning of the stream

                using (var package = new OfficeOpenXml.ExcelPackage(stream))
                {
                    var worksheet = package.Workbook.Worksheets.FirstOrDefault();
                    if (worksheet == null)
                        throw new CustomException(400, "The Excel file does not contain any worksheets.");

                    var rowCount = worksheet.Dimension?.Rows ?? 0;
                    if (rowCount <= 1) // Only header row or empty
                        throw new CustomException(400, "The Excel file does not contain any product data.");

                    // Cap at 100 products (plus header row)
                    var maxRows = Math.Min(rowCount, 101);

                    // Start from row 2 (assuming row 1 is the header)
                    for (int row = 2; row <= maxRows; row++)
                    {
                        var name = worksheet.Cells[row, 1].Value?.ToString();
                        var description = worksheet.Cells[row, 2].Value?.ToString();
                        var priceString = worksheet.Cells[row, 3].Value?.ToString();
                        var categoryNames = worksheet.Cells[row, 4].Value?.ToString();
                        var imageLinks = worksheet.Cells[row, 5].Value?.ToString();

                        // Skip row if name is empty (required field)
                        if (string.IsNullOrEmpty(name))
                            continue;

                        // Parse price with culture-invariant format
                        if (!decimal.TryParse(priceString, System.Globalization.NumberStyles.Any,
                            System.Globalization.CultureInfo.InvariantCulture, out decimal price))
                            price = 0; // Default price if parsing fails

                        // Create new product with default values
                        var product = new Product
                        {
                            Name = name,
                            Description = description ?? string.Empty,
                            Price = price,
                            Status = Enums.EProduct.InStock, // Default status is InStock
                            IsGift = false,
                            IsGlobalProduct = false,
                            LikeCount = 0,
                            ViewCount = 0,
                            BoughtCount = 0,
                            ReviewCount = 0,
                            ReviewPoint = 0,
                            Images = !string.IsNullOrWhiteSpace(imageLinks) ? imageLinks : string.Empty
                        };

                        listProduct.Add(product);

                        // Process categories if provided
                        if (!string.IsNullOrWhiteSpace(categoryNames))
                        {
                            var categories = new List<string>();
                            var categoryNameList = categoryNames.Split(',')
                                .Select(c => c.Trim().ToLower())
                                .Where(c => !string.IsNullOrWhiteSpace(c));

                            foreach (var catName in categoryNameList)
                            {
                                if (categoryNameToId.TryGetValue(catName, out var categoryId))
                                {
                                    categories.Add(categoryId);
                                }
                            }

                            if (categories.Any())
                            {
                                productCategories[product.Id] = categories;
                            }
                        }
                    }
                }
            }

            if (!listProduct.Any())
                throw new CustomException("No valid products found in the Excel file.");

            // Then create product-category relationships
            if (productCategories.Any())
            {
                var productCategoryRepo = unitOfWork.GetRepository<ProductCategory>();
                var productCategoryEntities = new List<ProductCategory>();

                foreach (var product in listProduct)
                {
                    if (productCategories.TryGetValue(product.Id, out var categoryIds))
                    {
                        productCategoryEntities.AddRange(categoryIds.Select(catId => new ProductCategory
                        {
                            ProductId = product.Id,
                            CategoryId = catId
                        }));
                    }
                }

                if (productCategoryEntities.Any())
                {
                    productCategoryRepo.AddRange(productCategoryEntities);
                    await unitOfWork.SaveChangesAsync(); // Save the category relationships separately
                }
            }

            _repository.AddRange(listProduct);
            return await unitOfWork.SaveChangesAsync();
        }

        public async Task<int> CreateAsync(ProductRequest dto)
        {
            await unitOfWork.BeginTransactionAsync();

            try
            {
                Product product = mapper.Map<Product>(dto);
                product.Description = dto.Description ?? string.Empty;
                product.Images = await ProcessUpload(dto.Images);

                await base.PrepareInsertFirstAsync(product);

                // add list product-branch
                if (dto.BranchIds.Any())
                {
                    await branchService.AssignProductToBranches(product.Id, dto.BranchIds);
                }

                // add list product-category
                if (dto.CategoryIds.Any())
                {
                    await categoryService.AssignProductToCategories(product.Id, dto.CategoryIds);
                }

                // add list product-category-child
                if (dto.CategoryIds.Any())
                {
                    await categoryService.AddOrUpdateCategoryChildByProduct(product.Id, dto.CategoryChildIds);
                }

                // thêm mới variants cho product
                if (dto.Variants.Any())
                {
                    await productPropertyService.CreateProductVariants(product.Id, dto.Variants);
                }

                _repository.Add(product);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public async Task<int> UpdateAsync(string id, ProductRequest dto)
        {
            var existingProduct = await GetByIdAsync(id);
            if (existingProduct == null)
            {
                throw new CustomException(404, "Product not found!");
            }

            await unitOfWork.BeginTransactionAsync();

            try
            {
                int currentOrder = existingProduct.DisplayOrder;

                mapper.Map(dto, existingProduct);

                await base.ReorderAsync(existingProduct, currentOrder, existingProduct.DisplayOrder);

                // thao tác liên quan tới cập nhật product-branch
                await branchService.AssignProductToBranches(existingProduct.Id, dto.BranchIds);

                // thao tác liên quan tới cập nhật product-category
                await categoryService.AssignProductToCategories(existingProduct.Id, dto.CategoryIds);

                // thao tác liên quan tới cập nhật product-category-child
                await categoryService.AddOrUpdateCategoryChildByProduct(existingProduct.Id, dto.CategoryChildIds);

                // thao tác liên quan tới cập nhật product-variant
                await productPropertyService.UpdateProductVariants(existingProduct.Id, dto.Variants);

                // thao tác liên quan tới cập nhật ảnh
                if (dto.Images.Any())
                {
                    string newFiles = await ProcessUpload(dto.Images);
                    if (!string.IsNullOrEmpty(newFiles))
                    {
                        if (!string.IsNullOrEmpty(existingProduct.Images))
                        {
                            var existingImages = existingProduct.Images.Split(",").ToList();
                            var newImageFiles = newFiles.Split(",").ToList();

                            existingImages.AddRange(newImageFiles);

                            existingProduct.Images = string.Join(",", existingImages.Distinct());
                        }
                        else
                        {
                            existingProduct.Images = newFiles;
                        }
                    }
                    //existingProduct.Images = newFiles;
                }

                // thao tác liên quan tới xóa ảnh cũ
                if (dto.RemovedOldImages.Any())
                {
                    RemoveOldImage(string.Join(",", dto.RemovedOldImages));
                    var remainingImages = existingProduct.Images?.Split(",")
                        .Where(image => !dto.RemovedOldImages.Contains(image.Trim()))
                        .ToList();
                    if (remainingImages != null && remainingImages.Any())
                    {
                        existingProduct.Images = string.Join(",", remainingImages);
                    }
                }

                //return await base.UpdateAsync(existingProduct);

                _repository.Update(existingProduct);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }
        }

        public override async Task<int> DeleteByIdAsync(string id)
        {
            var product = await GetByIdAsync(id);
            if (product == null)
            {
                throw new CustomException(404, "Product not found!");
            }

            await unitOfWork.BeginTransactionAsync();

            try
            {
                // Đưa vào transaction
                await productPropertyService.DeleteProductVariants(id);

                await branchService.RemoveAllBranchesFromProduct(id); // xóa sản phẩm khỏi chi nhánh 
                await categoryService.RemoveAllCategoriesFromProduct(id); // xóa sản phẩm khỏi danh mục

                await categoryService.RemoveCategoryChildFromProduct(id); // xóa sản phẩm khỏi danh mục con

                RemoveOldImage(product.Images ?? "");

                await base.ReorderAfterDeleteAsync(product.DisplayOrder);

                _repository.Delete(product);

                return await unitOfWork.CommitAsync();
            }
            catch
            {
                await unitOfWork.RollbackAsync();
                throw;
            }

        }

        #region Hanlde Image

        private async Task<string> ProcessUpload(List<IFormFile>? files)
        {
            var stringFiles = string.Empty;
            if (files != null)
            {
                var savePath = Path.Combine(env.WebRootPath, "uploads/images/products");
                var fileResult = await FileHandler.SaveFiles(files, savePath);
                stringFiles = string.Join(",", fileResult);
            }
            return stringFiles;
        }

        private void RemoveOldImage(string listImage)
        {
            var images = listImage.Split(",").Select(x => Path.Combine(env.WebRootPath, "uploads/images/products", x)).ToList();
            FileHandler.RemoveFiles(images);
        }

        #endregion
    }
}
