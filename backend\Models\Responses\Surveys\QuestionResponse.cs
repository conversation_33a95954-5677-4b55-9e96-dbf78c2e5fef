﻿namespace MiniAppCore.Models.Responses.Surveys
{
    public class QuestionResponse
    {
        public bool IsRequied { get; set; }
        public string? Type { get; set; }
        public string? QuestionTitle { get; set; }
        public required string QuestionId { get; set; }
        public short DisplayOrder { get; set; }
        public List<AnswerResponse> ListOption { get; set; } = new List<AnswerResponse>();
        public List<QuestionLikertResponse> ListQuestionLikert { get; set; } = new List<QuestionLikertResponse>();
        public List<SurveySubmissionDetailResponse>? UserResponses { get; set; }
    }

    public class SurveySubmissionDetailResponse
    {
        public string? Id { get; set; }
        public string? QuestionId { get; set; }
        public string? LikertQuestionId { get; set; }
        public string? AnswerId { get; set; }
        public string? LikertId { get; set; }
        public string? InputValue { get; set; }
    }
}
