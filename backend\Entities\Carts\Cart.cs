﻿using MiniAppCore.Entities.Commons;

namespace MiniAppCore.Entities.Memberships
{
    public class Cart : BaseEntity
    {
        public required string UserZaloId { get; set; }
        public string? Note { get; set; }
        public long Quantity { get; set; }
        public decimal Price { get; set; }
        public string? VariantId { get; set; }
        public required string ProductId { get; set; }
        public string? BranchId { get; set; }
    }
}
