using Hangfire;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Interface;

namespace MiniAppCore.Services.Gamifications.LuckyWheels
{
    /// <summary>
    /// Service chuyên quản lý Hangfire jobs cho LuckyWheel auto add spin points
    /// Tách biệt chức năng job management khỏi business logic của LuckyWheelService
    /// </summary>
    public interface ILuckyWheelJobManagementService
    {
        /// <summary>
        /// Tạo hoặc xóa job Hangfire cho một LuckyWheel
        /// </summary>
        void ManageAutoSpinPointsJob(string luckyWheelId, bool enable);

        /// <summary>
        /// Khởi tạo tất cả jobs cho các LuckyWheel có EnableAutoSpinPoints = true
        /// </summary>
        Task InitializeAllAutoSpinPointsJobs();

        /// <summary>
        /// Enqueue một background job để cộng điểm cho tất cả users
        /// </summary>
        void EnqueueAutoAddSpinPointsJob(string luckyWheelId);

        /// <summary>
        /// Enqueue một background job để cộng điểm cho user cụ thể
        /// </summary>
        void EnqueueAddSpinPointsToUserJob(string luckyWheelId, string userZaloId);
    }

    public class LuckyWheelJobManagementService(
        IUnitOfWork unitOfWork,
        ILuckyWheelBackgroundJobService backgroundJobService) : ILuckyWheelJobManagementService
    {
        private readonly IRepository<Entities.LuckyWheels.LuckyWheel> _repository = unitOfWork.GetRepository<Entities.LuckyWheels.LuckyWheel>();

        /// <summary>
        /// Thiết lập hoặc xóa job Hangfire cho tự động cộng lượt chơi
        /// </summary>
        /// <param name="luckyWheelId">ID của vòng quay</param>
        /// <param name="enable">Bật hoặc tắt job</param>
        public void ManageAutoSpinPointsJob(string luckyWheelId, bool enable)
        {
            var jobId = $"AutoAddSpinPoints_{luckyWheelId}";

            if (enable)
            {
                // Tạo recurring job chạy hàng ngày lúc 0:00
                var options = new RecurringJobOptions { TimeZone = TimeZoneInfo.Local };
                RecurringJob.AddOrUpdate(jobId,
                    () => backgroundJobService.ProcessAutoAddSpinPointsBatch(luckyWheelId),
                    Cron.Daily,
                    options);
            }
            else
            {
                // Xóa job nếu tắt
                RecurringJob.RemoveIfExists(jobId);
            }
        }

        /// <summary>
        /// Khởi tạo tất cả job Hangfire cho các LuckyWheel có EnableAutoSpinPoints = true
        /// </summary>
        public async Task InitializeAllAutoSpinPointsJobs()
        {
            var activeAutoSpinLuckyWheels = await _repository.AsQueryable()
                .Where(lw => lw.EnableAutoSpinPoints && lw.IsActive)
                .ToListAsync();

            foreach (var luckyWheel in activeAutoSpinLuckyWheels)
            {
                ManageAutoSpinPointsJob(luckyWheel.Id, true);
            }
        }

        /// <summary>
        /// Enqueue một background job ngay lập tức để cộng điểm cho tất cả users
        /// </summary>
        /// <param name="luckyWheelId">ID của vòng quay</param>
        public void EnqueueAutoAddSpinPointsJob(string luckyWheelId)
        {
            BackgroundJob.Enqueue(() => backgroundJobService.ProcessAutoAddSpinPointsBatch(luckyWheelId));
        }

        /// <summary>
        /// Enqueue một background job để cộng điểm cho user cụ thể
        /// </summary>
        /// <param name="luckyWheelId">ID của vòng quay</param>
        /// <param name="userZaloId">ID user cần cộng điểm</param>
        public void EnqueueAddSpinPointsToUserJob(string luckyWheelId, string userZaloId)
        {
            BackgroundJob.Enqueue(() => backgroundJobService.ProcessAddSpinPointsToUser(luckyWheelId, userZaloId));
        }
    }
}
