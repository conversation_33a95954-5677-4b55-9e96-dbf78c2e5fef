﻿using MiniAppCore.Entities.Categories;

namespace MiniAppCore.Models.Requests.Categories
{
    public class CategoryRequest
    {
        public string? Name { get; set; }
        public string? Type { get; set; }
        public int? OrderPriority { get; set; }
        public bool? IsGlobal { get; set; } = false;
        public string? HexColor { get; set; } = "#dc3545";
        public string? Description { get; set; } = string.Empty;

        public List<IFormFile> Images { get; set; } = new();
        public List<string> BranchIds { get; set; } = new();
        public List<string> RemovedOldImages { get; set; } = new();

        public List<CategoryChild> listChild { get; set; } = new List<CategoryChild>();
    }
}
