﻿@model MiniAppCore.Entities.Branches.Branch;

<style>
    .error-message {
        color: red;
        font-size: 12px;
        margin-top: 4px;
    }
</style>

<div class="modal-content modal-xl">
    <div class="modal-header">
        <h5 class="modal-title">@ViewBag.Title</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" data-bs-dismiss="modal">
            <span aria-hidden="true">×</span>
        </button>
    </div>
    <div class="modal-body">
        <form>
            <div class="row">
                <!-- Thông tin cơ bản -->
                <div class="col-12 mb-3">
                    <h6 class="text-primary">THÔNG TIN CƠ BẢN</h6>
                    <hr>
                </div>

                <!-- Branch Name -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Tên chi nh<PERSON>h <span style="color:red">*</span></label>
                        <input id="name" type="text" class="form-control" placeholder="Nhập tên chi nhánh" value="@Model.Name" required />
                        <span class="error-message" id="error-name"></span>
                    </div>
                </div>

                <!-- Phone Number -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Số điện thoại <span style="color:red">*</span></label>
                        <input id="phoneNumber" type="text" class="form-control" oninput="InputValidator.validatePhoneInput(this, {maxLength:13})" placeholder="Nhập số điện thoại" value="@Model.PhoneNumber" required />
                        <span class="error-message" id="error-phoneNumber"></span>
                    </div>
                </div>

                <!-- Thông tin địa chỉ -->
                <div class="col-12 mb-3 mt-3">
                    <h6 class="text-primary">THÔNG TIN ĐỊA CHỈ</h6>
                    <hr>
                </div>

                <!-- Branch Address -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Địa chỉ <span style="color:red">*</span></label>
                        <input id="streetLine" type="text" class="form-control" placeholder="Số nhà, tên đường" value="@Model.StreetLine" required />
                        <span class="error-message" id="error-streetLine"></span>
                    </div>
                </div>

                <!-- Google Maps Link -->
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Link Google Maps <span style="color:red">*</span></label>
                        <input id="googleMapsLink" type="url" class="form-control" placeholder="Dán link Google Maps" onchange="extractLatLong()" value="@Model.GoogleMapURL" required />
                        <small class="text-muted">VD: http://www.google.com/maps/place/10.7769,106.7009</small>
                        <span class="error-message" id="error-googleMapsLink"></span>
                    </div>
                </div>

                <!-- Branch City -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Tỉnh/Thành phố <span style="color:red">*</span></label>
                        <select id="city" class="form-control select2" required>
                            <option value="">Chọn Tỉnh/Thành phố</option>
                        </select>
                        <span class="error-message" id="error-city"></span>
                    </div>
                </div>

                <!-- Branch District -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Quận/Huyện <span style="color:red">*</span></label>
                        <select id="district" class="form-control select2" required>
                            <option value="">Chọn Quận/Huyện</option>
                        </select>
                        <span class="error-message" id="error-district"></span>
                    </div>
                </div>

                <!-- Branch Ward -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label>Xã/Phường <span style="color:red">*</span></label>
                        <select id="ward" class="form-control select2" required>
                            <option value="">Chọn Xã/Phường</option>
                        </select>
                        <span class="error-message" id="error-ward"></span>
                    </div>
                </div>

                <!-- Thời gian hoạt động -->
                <div class="col-12 mb-3 mt-3">
                    <h6 class="text-primary">THỜI GIAN & TRẠNG THÁI HOẠT ĐỘNG</h6>
                    <hr>
                </div>

                <!-- Opening Time -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Giờ mở cửa</label>
                        <input id="openingTime" type="time" class="form-control" value="@Model.OpeningTime.ToString("HH:mm")" />
                    </div>
                </div>

                <!-- Closing Time -->
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Giờ đóng cửa</label>
                        <input id="closingTime" type="time" class="form-control" value="@Model.ClosingTime.ToString("HH:mm")" />
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label>Hoạt động ngày lễ</label>
                        <select id="isOpenHoliday" class="form-control">
                            <option value="true" selected="@Model.IsOpenHoliday">Mở cửa</option>
                            <option value="false" selected="@(!Model.IsOpenHoliday)">Đóng cửa</option>
                        </select>
                    </div>
                </div>

                <div class="col-md-3">
                    <div class="form-group">
                        <label>Trạng thái hoạt động</label>
                        <select id="isEnable" class="form-control">
                            <option value="true" selected="@Model.Status">Hoạt động</option>
                            <option value="false" selected="@(!Model.Status)">Không hoạt động</option>
                        </select>
                    </div>
                </div>

                <!-- Trường ẩn -->
                <div hidden>
                    <input id="latitude" type="text" value="@Model.Latitude" />
                    <input id="longitude" type="text" value="@Model.Longitude" />
                    <select id="isDefault" class="form-control">
                        <option value="true" selected="@Model.IsDefault">Có</option>
                        <option value="false" selected="@(!Model.IsDefault)">Không</option>
                    </select>
                </div>

                <!-- Mô tả & hình ảnh -->
                <div class="col-12 mb-3 mt-3">
                    <h6 class="text-primary">MÔ TẢ & HÌNH ẢNH</h6>
                    <hr>
                </div>

                <!-- Description -->
                <div class="col-md-12">
                    <div class="form-group">
                        <label>Mô tả</label>
                        <div id="description" class="rounded-bottom" style="height: 250px;">@Html.Raw(Model.Description)</div>
                    </div>
                </div>

                <!-- Image -->
                <div class="col-md-12 mt-3">
                    <div class="form-group">
                        <label>Hình ảnh <span style="color:red">*</span></label>
                        <input id="image" type="file" class="form-control" multiple accept="image/*"
                               onchange="handleImagePreview(this, { width: 0, height: 0 }, null, '#preview', {isSingleImage: true, defaultImagePath: '/images/no-image-2.jpg'})" />
                        <small class="text-muted">Hình ảnh tốt nhất có kích thước 800x600px</small>
                    </div>
                </div>

                <div class="col-md-12 mt-2">
                    <div id="preview" class="d-flex flex-wrap justify-content-center" style="min-height: 200px;">
                        @if (!string.IsNullOrEmpty(Model.Image))
                        {
                            <div class="image-preview mx-2 card position-relative" style="max-width: 250px; width: 250px;">
                                @* <span class="btn-preview-remove position-absolute">x</span> *@
                                <img src="@Model.Image" class="card-img-top h-100" alt="Hình ảnh" style="object-fit: contain" />
                            </div>
                        }
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
        <button type="button" class="btn btn-primary" onclick="HandleSaveOrUpdate('@Model.Id')">@ViewBag.Button</button>
    </div>
</div>

<script>
    $(document).ready(function () {
        $('input, select').on('input change', function () {
            const errorId = `error-${this.id}`;
            $(`#${errorId}`).text('');
        });

        InitialEditor();
        InitSelect2('@Html.Raw(Model.ProvinceId)', '@Html.Raw(Model.DistrictId)', '@Html.Raw(Model.WardId)');

        // Xử lý nút xóa ảnh
        $(document).on('click', '.btn-preview-remove', function () {
            $(this).closest('.image-preview').remove();
        });
    });
</script>
