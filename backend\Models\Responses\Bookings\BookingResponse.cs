﻿using MiniAppCore.Entities.Branches;

namespace MiniAppCore.Models.Responses.Bookings
{
    public class BookingResponse
    {
        public string? Id { get; set; }
        public string? Note { get; set; }
        public short? Status { get; set; }
        public string? UserZaloId { get; set; }
        public string? PhoneNumber { get; set; }
        public string? UserZaloName { get; set; }
        public DateTime BookingDate { get; set; }
        public DateTime CreatedDate { get; set; }

        public Branch? Branch { get; set; }
    }
}
