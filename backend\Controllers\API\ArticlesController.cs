﻿using AutoMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniAppCore.Entities.Articles;
using MiniAppCore.Exceptions;
using MiniAppCore.Helpers;
using MiniAppCore.Models.Queries;
using MiniAppCore.Models.Requests.Articles;
using MiniAppCore.Models.Responses.Articles;
using MiniAppCore.Services.Articles;
using MiniAppCore.Services.Articles.ArticleCategories;

namespace MiniAppCore.Controllers.API
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(AuthenticationSchemes = JwtBearerDefaults.AuthenticationScheme)]
    public class ArticlesController(ILogger<ArticlesController> logger, IMapper mapper, IArticleService articleService, IArticleCategoryService articleCategoryService) : ControllerBase
    {
        private ArticleResponse ConvertArticle(Article item, bool summarize = true)
        {
            ArticleResponse article = mapper.Map<ArticleResponse>(item);

            var baseUrl = $"{Request.Scheme}://{Request.Host.Value}";

            // Xử lý Images
            article.Images = !string.IsNullOrEmpty(item.Images)
                ? item.Images.Split(',').Where(x => !string.IsNullOrWhiteSpace(x)).Select(x => $"{baseUrl}/uploads/images/articles/{x}").ToList()
                : new List<string>();

            // Xử lý BannerImage
            article.BannerImage = !string.IsNullOrEmpty(item.BannerImage)
                ? $"{baseUrl}/uploads/images/articles/{item.BannerImage.Split(',').First()}"
                : !string.IsNullOrEmpty(item.Images) ? item.Images.Split(',').Where(x => !string.IsNullOrWhiteSpace(x))
                         .Select(x => $"{baseUrl}/uploads/images/articles/{x}").First()
                : $"{baseUrl}/images/no-image-1.jpg";

            if (string.IsNullOrEmpty(article.BannerImage))
            {
                article.BannerImage = article.Images.FirstOrDefault();
            }

            article.SummarizeContent = Tools.SummarizeHtmlContent(article.Content, 200);
            return article;
        }

        [HttpGet]
        [AllowAnonymous]
        public async Task<ActionResult> GetPage([FromQuery] ArticleQueryParams query)
        {
            try
            {
                if (!User.IsInRole("ADMIN"))
                {
                    query.Status = 1;
                }

                var result = await articleService.GetPage(query);
                var data = result.Data.Select(article => ConvertArticle(article, true));
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = data,
                    result.TotalPages,
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpGet("Category")]
        [AllowAnonymous]
        public async Task<ActionResult> Category()
        {
            try
            {
                var data = await articleCategoryService.GetAllAsync();
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công",
                    Data = data
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [AllowAnonymous]
        [HttpGet("{id}")]
        public async Task<ActionResult> GetById(string id)
        {
            try
            {
                var article = await articleService.GetByIdAsync(id);
                if (article == null)
                {
                    return StatusCode(404, "Không tìm thấy tin tức!");
                }
                return Ok(new
                {
                    Code = 0,
                    Message = "Thành công.",
                    Data = ConvertArticle(article, false)
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPost]
        [Authorize(Roles = "ADMIN")]
        public async Task<ActionResult> CreateArticle([FromForm] ArticleRequest model)
        {
            try
            {
                var result = await articleService.CreateAsync(model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Tạo tin tức thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<ActionResult> UpdateArticle(string id, [FromForm] ArticleRequest model)
        {
            try
            {
                var article = await articleService.UpdateAsync(id, model);
                return Ok(new
                {
                    Code = 0,
                    Message = "Cập nhật tin tức thành công!",
                    Data = article
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpPatch("{id}/Status")]
        [Authorize(Roles = "ADMIN")]
        public async Task<ActionResult> UpdateStatus(string id, short status)
        {
            try
            {
                //var article = await _articleService.UpdateStatus(id, status);
                await Task.CompletedTask;
                return Ok(new
                {
                    Code = 0,
                    Message = $"Cập nhật trạng thái {(status == (short)Enums.Status.ARTICLE_PUBLIC ? "công khai" : "riêng tư")} thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "ADMIN")]
        public async Task<ActionResult> Delete(string id)
        {
            try
            {
                await articleService.DeleteByIdAsync(id);
                return Ok(new
                {
                    Code = 0,
                    Message = "Xóa tin tức thành công!",
                });
            }
            catch (CustomException ex)
            {
                return StatusCode(ex.Code, new
                {
                    Code = 1,
                    ex.Message,
                    ex.Data
                });
            }
            catch (Exception ex)
            {
                logger.LogDebug("Error occurred while retrieving all products: {0}", ex.Message);
                return StatusCode(200, new
                {
                    Code = 1,
                    Message = "Internal Server Error"
                });
            }
        }
    }
}
