﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;

namespace MiniAppCore.Entities.Settings
{
    public class RewardPoint
    {
        [Key]
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;

        [Precision(18, 5)]
        public decimal ConversionRatio { get; set; }
        public int MemberShipRankId { get; set; }
        public int MaxPointPerTxn { get; set; }
        public bool Status { get; set; }

        // public bool IsDefault { get; set; }

        public decimal CommissionPercentage { get; set; }

        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; } = DateTime.Now;
    }
}