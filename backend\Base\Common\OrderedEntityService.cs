﻿using System.Linq.Expressions;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using MiniAppCore.Base.Interface;
using MiniAppCore.Exceptions;

namespace MiniAppCore.Base.Common
{
    /// <summary>
    /// Base service cho Entity có thuộc tính thứ tự.
    /// Service con chỉ cần chỉ rõ tên cột thứ tự.
    /// </summary>
    public abstract class OrderedEntityService<T> : Service<T> where T : class
    {
        protected OrderedEntityService(IUnitOfWork unitOfWork) : base(unitOfWork) { }

        /// <summary>
        /// Service con chỉ rõ tên cột thứ tự
        /// </summary>
        protected abstract string OrderColumnName { get; }

        /// <summary>
        /// Insert mới ở đầu (Order = 1), đ<PERSON>y tất cả xuống +1.
        /// Gọi trước Commit.
        /// </summary>
        public async Task PrepareInsertFirstAsync(T entity)
        {
            await _repository.AsQueryable()
                .ExecuteUpdateAsync(update =>
                    update.SetProperty(
                        e => EF.Property<int>(e, OrderColumnName),
                        e => EF.Property<int>(e, OrderColumnName) + 1));

            SetOrderValue(entity, 1);
            _repository.Add(entity);
        }

        /// <summary>
        /// Di chuyển entity từ currentOrder sang newOrder.
        /// Gọi trước Commit.
        /// </summary>
        public async Task ReorderAsync(T entity, int currentOrder, int newOrder)
        {
            if (entity == null)
                throw new CustomException("Entity truyền vào không được null.");

            if (newOrder < 1) newOrder = 1;

            if (currentOrder < 1)
            {
                // Giống insert đầu
                await _repository.AsQueryable()
                    .ExecuteUpdateAsync(update =>
                        update.SetProperty(
                            e => EF.Property<int>(e, OrderColumnName),
                            e => EF.Property<int>(e, OrderColumnName) + 1));

                SetOrderValue(entity, 1);
                _repository.Add(entity);
            }
            else if (newOrder == currentOrder)
            {
                return;
            }
            else
            {
                if (newOrder < currentOrder)
                {
                    // Move up
                    await _repository.AsQueryable()
                        .Where(e =>
                            EF.Property<int>(e, OrderColumnName) >= newOrder &&
                            EF.Property<int>(e, OrderColumnName) < currentOrder)
                        .ExecuteUpdateAsync(update =>
                            update.SetProperty(
                                e => EF.Property<int>(e, OrderColumnName),
                                e => EF.Property<int>(e, OrderColumnName) + 1));
                }
                else
                {
                    // Move down
                    await _repository.AsQueryable()
                        .Where(e =>
                            EF.Property<int>(e, OrderColumnName) > currentOrder &&
                            EF.Property<int>(e, OrderColumnName) <= newOrder)
                        .ExecuteUpdateAsync(update =>
                            update.SetProperty(
                                e => EF.Property<int>(e, OrderColumnName),
                                e => EF.Property<int>(e, OrderColumnName) - 1));
                }

                SetOrderValue(entity, newOrder);
                _repository.Update(entity);
            }
        }

        /// <summary>
        /// Sau khi xóa 1 item thì dồn lại thứ tự liên tiếp.
        /// </summary>
        protected async Task ReorderAfterDeleteAsync(int deletedOrder)
        {
            await _repository.AsQueryable()
                .Where(e => EF.Property<int>(e, OrderColumnName) > deletedOrder)
                .ExecuteUpdateAsync(update =>
                    update.SetProperty(
                        e => EF.Property<int>(e, OrderColumnName),
                        e => EF.Property<int>(e, OrderColumnName) - 1));
        }

        /// <summary>
        /// Validate: thứ tự phải liên tiếp từ 1..N.
        /// </summary>
        public async Task ValidateOrderSequenceAsync()
        {
            var orders = await _repository.AsQueryable()
                .Select(e => EF.Property<int>(e, OrderColumnName))
                .OrderBy(o => o)
                .ToListAsync();

            for (int i = 0; i < orders.Count; i++)
            {
                if (orders[i] != i + 1)
                    throw new CustomException($"Thứ tự không liên tiếp: vị trí {i + 1}, giá trị tìm thấy = {orders[i]}");
            }
        }

        /// <summary>
        /// Fix thứ tự về chuẩn 1..N.
        /// </summary>
        public async Task FixOrderSequenceAsync()
        {
            var items = await _repository.AsQueryable()
                .OrderBy(e => EF.Property<int>(e, OrderColumnName))
                .ToListAsync();

            for (int i = 0; i < items.Count; i++)
            {
                SetOrderValue(items[i], i + 1);
            }

            _repository.UpdateRange(items);
        }

        /// <summary>
        /// Helper: Set giá trị thứ tự cho entity qua Reflection.
        /// </summary>
        private void SetOrderValue(T entity, int value)
        {
            var propInfo = typeof(T).GetProperty(OrderColumnName);
            if (propInfo == null)
                throw new CustomException($"Entity {typeof(T).Name} không có property {OrderColumnName}");

            if (propInfo.PropertyType == typeof(short) || propInfo.PropertyType == typeof(short?))
                propInfo.SetValue(entity, (short)value);
            else
                propInfo.SetValue(entity, value);
        }
    }
}
