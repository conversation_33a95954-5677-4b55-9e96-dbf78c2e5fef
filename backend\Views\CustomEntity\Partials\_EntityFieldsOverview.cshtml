@model List<MiniAppCore.Entities.Commons.CustomField>
@{
    var entityName = ViewBag.EntityName as string;
}

<div class="card border-success mb-4">
    <div class="card-header bg-success bg-soft">
        <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex align-items-center">
                <div class="bg-success rounded-circle p-2 me-3">
                    <i class="ri-list-check text-white"></i>
                </div>
                <div>
                    <h5 class="mb-0 text-success fw-bold">Bước 2: Custom Fields cho @entityName</h5>
                    <small class="text-muted">Quản lý và cấu hình các trường dữ liệu tùy chỉnh</small>
                </div>
            </div>
            <div class="d-flex gap-2">
                <button onclick="openEntityConfiguration('@entityName')" class="btn btn-success btn-sm">
                    <i class="ri-settings-3-line me-1"></i><PERSON><PERSON><PERSON> hình tất cả
                </button>
                <button onclick="openCreateModal('@entityName')" class="btn btn-outline-success btn-sm">
                    <i class="ri-add-line me-1"></i>Thêm Field
                </button>
            </div>
        </div>
    </div>

    @if (Model != null && Model.Any())
    {
        <div class="card-body p-0">
            <!-- Quick Stats -->
            <div class="row g-0 border-bottom">
                <div class="col-3 p-3 text-center border-end">
                    <div class="text-primary fs-3 fw-bold">@Model.Count</div>
                    <small class="text-muted">Tổng Fields</small>
                </div>
                <div class="col-3 p-3 text-center border-end">
                    <div class="text-warning fs-3 fw-bold">@Model.Count(f => f.IsRequired)</div>
                    <small class="text-muted">Bắt buộc</small>
                </div>
                <div class="col-3 p-3 text-center border-end">
                    <div class="text-success fs-3 fw-bold">@Model.Count(f => !f.IsRequired)</div>
                    <small class="text-muted">Tùy chọn</small>
                </div>
                <div class="col-3 p-3 text-center">
                    <div class="text-info fs-3 fw-bold">@Model.GroupBy(f => f.DataType).Count()</div>
                    <small class="text-muted">Loại dữ liệu</small>
                </div>
            </div>

            <!-- Fields List -->
            <div class="p-3">
                <div class="row g-3">
                    @foreach (var field in Model.OrderBy(f => f.FieldName))
                    {
                        <div class="col-md-6 col-lg-4">
                            <div class="card h-100 border @(field.IsRequired ? "border-warning" : "border-light") field-card">
                                <div class="card-body p-3">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0 fw-bold text-dark">
                                            <i class="ri-input-method-line text-primary me-1"></i>
                                            @field.FieldName
                                        </h6>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-light border-0" data-bs-toggle="dropdown">
                                                <i class="ri-more-2-line"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="editCustomField('@field.Id')">
                                                        <i class="ri-edit-line me-2"></i>Chỉnh sửa
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item text-danger" href="#" onclick="deleteCustomField('@field.Id', '@field.FieldName')">
                                                        <i class="ri-delete-bin-line me-2"></i>Xóa
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <span class="badge bg-@(GetDataTypeBadgeColor(field.DataType)) me-1">
                                            @GetDataTypeIcon(field.DataType) @field.DataType
                                        </span>
                                        @if (field.IsRequired)
                                        {
                                            <span class="badge bg-warning text-dark">
                                                <i class="ri-error-warning-line me-1"></i>Bắt buộc
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="ri-checkbox-line me-1"></i>Tùy chọn
                                            </span>
                                        }
                                    </div>
                                    
                                    <div class="text-muted small">
                                        <i class="ri-calendar-line me-1"></i>
                                        Tạo: @field.CreatedDate.ToString("dd/MM/yyyy HH:mm")
                                        @if (field.UpdatedDate != null)
                                        {
                                            <br><i class="ri-refresh-line me-1"></i>
                                            <text>Cập nhật: @field.UpdatedDate.ToString("dd/MM/yyyy HH:mm")</text>
                                        }
                                    </div>
                                </div>
                                <div class="card-footer bg-light p-2 text-center">
                                    <small class="text-muted">ID: @field.Id</small>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="card-body text-center py-5">
            <div class="mb-4">
                <i class="ri-database-line text-muted" style="font-size: 4rem; opacity: 0.3;"></i>
            </div>
            <h5 class="text-muted mb-2">Chưa có Custom Fields</h5>
            <p class="text-muted mb-4">
                Entity <strong>@entityName</strong> chưa có custom fields nào. 
                <br>Bắt đầu tạo các trường dữ liệu tùy chỉnh để mở rộng thông tin.
            </p>
            <div class="d-flex gap-2 justify-content-center">
                <button onclick="openEntityConfiguration('@entityName')" class="btn btn-success">
                    <i class="ri-settings-3-line me-2"></i>Bắt đầu cấu hình
                </button>
                <button onclick="openCreateModal('@entityName')" class="btn btn-outline-primary">
                    <i class="ri-add-line me-2"></i>Thêm Field đầu tiên
                </button>
            </div>
        </div>
    }
</div>

@functions {
    string GetDataTypeBadgeColor(string dataType)
    {
        return dataType?.ToLower() switch
        {
            "string" or "textarea" => "primary",
            "int" or "decimal" => "info",
            "bool" => "warning",
            "datetime" or "date" => "success",
            "email" or "phone" or "url" => "secondary",
            "file" or "image" => "dark",
            _ => "light"
        };
    }
    
    string GetDataTypeIcon(string dataType)
    {
        return dataType?.ToLower() switch
        {
            "string" => "📝",
            "textarea" => "📄",
            "int" => "🔢",
            "decimal" => "💯",
            "bool" => "☑️",
            "datetime" => "📅",
            "date" => "📆",
            "email" => "📧",
            "phone" => "📞",
            "url" => "🔗",
            "file" => "📎",
            "image" => "🖼️",
            _ => "⚙️"
        };
    }
}
