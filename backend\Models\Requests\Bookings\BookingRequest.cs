﻿namespace MiniAppCore.Models.Requests.Bookings
{
    public class BookingRequest
    {
        public string? Note { get; set; }
        public string? BranchId { get; set; }
        public string? PhoneNumber { get; set; }
        public string? MembershipName { get; set; }
        public short Status { get; set; }
        public DateTime BookingDate { get; set; }
        public List<BookingRequestDetail> BookingItems { get; set; } = new List<BookingRequestDetail>();
    }
}
