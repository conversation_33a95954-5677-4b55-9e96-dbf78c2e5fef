﻿<div class="row">
    <div class="col-lg-12">
        <div class="d-flex flex-wrap flex-wrap align-items-center justify-content-between mb-4">
            <div>
                <h4 class="mb-3"><PERSON>h sách tin tức</h4>
                @* <p class="mb-0"> *@
                @*     Danh sách tin tức quyết định cách trình bày tin tức một cách hiệu quả và cung cấp không gian<br /> *@
                @*     để liệt kê các sản phẩm và ưu đãi của bạn theo cách hấp dẫn nhất. *@
                @* </p> *@
            </div>
            <button type="button" class="btn btn-primary mt-2" onclick="GetFormArticle()">
                <i class="ri-add-line mr-3"></i>Thêm mới
            </button>
        </div>
    </div>

    <!--Bộ lọc-->
    <div class="col-lg-12">
        <div class="card mb-3">
            <div class="card-body pb-0">
                <h6 class="mb-3"><i class="ri-filter-3-line mr-2"></i>Bộ lọc</h6>
                <div class="row align-items-end pb-4">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Tìm kiếm</label>
                        <div class="search-input-group position-relative">
                            <input id="search" type="text" class="form-control" placeholder="Tìm theo tên">
                            <span class="search-icon position-absolute user-select-auto" style="right: 10px; top: 50%; transform: translateY(-50%); cursor:pointer" onclick="table.ajax.reload()">
                                <i class="ri-search-line"></i>
                            </span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <label for="filter-status" class="form-label">Trạng thái</label>
                        <select id="filter-status" class="form-select select2" onchange="table.ajax.reload()">
                            <option value="">Tất cả</option>
                            <option value="0">Riêng tư</option>
                            <option value="1">Công khai</option>
                        </select>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">Thời gian</label>
                        <div class="d-flex align-items-center">
                            <div class="input-group">
                                <input id="filterStart" type="date" class="form-control">
                                <span class="input-group-text bg-light border-0">đến</span>
                                <input id="filterEnd" type="date" class="form-control">
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12 d-flex justify-content-end mt-3">
                        <button class="btn btn-primary" onclick="table.ajax.reload()">
                            <i class="ri-filter-line me-1"></i> Lọc
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div id="spinner" style="margin: 20px 0"></div>
        <div id="article-table" class="table-responsive rounded mb-3">
            <table id="list-article" class="data-table table mb-0 tbl-server-info"></table>
        </div>
    </div>
</div>

<div id="modal-article" data-bs-backdrop="static" data-bs-keyboard="false" class="modal fade">
    <div id="modal-content" class="modal-dialog modal-dialog-centered modal-xl" style="max-width: 90vw;"></div>
</div>

@section Scripts {
    <script>
        let removedOldImages = [];
        let removedOldBanner = "";

        $(document).ready(function () {
            $('#filter-type').on('change', function () {
                table.ajax.reload(null, false);
            });

            $("#filterStart").val(moment(new Date()).subtract(30, 'days').format('YYYY-MM-DD'));
            $("#filterEnd").val(moment(new Date()).format('YYYY-MM-DD'));

            $('#search').on('input', search);

            GetListArticle();
        });

        function GetFormArticle(id) {
            const url = id ? `@Url.Action("Detail", "Article")/${id}` : "@Url.Action("Create", "Article")"
            $.ajax({
                url: url,
                type: 'GET',
                success: function (data) {
                    $("#modal-content").html(data);
                    $("#modal-article").modal("toggle");
                }
            })

        }

        // Hàm hiển thị lại 3 ảnh mặc định
        function showDefaultImages() {
            const defaultImages = [
                "/images/no-image-2.jpg",
                "/images/no-image-2.jpg",
                "/images/no-image-2.jpg"
            ];

            defaultImages.forEach((src, index) => {
                const defaultItem = $(`
                                            <div class="carousel-item ${index === 0 ? 'active' : ''}">
                                                <img class="d-block w-100" src="${src}" alt="Default image">
                                            </div>
                                        `);
                $("#carosuel-preview").append(defaultItem);
            });
        }

        $(document).on("click", ".btn-preview-remove", function () {
            const fullImageUrl = $(this).data("url");
            const imageUrl = fullImageUrl.split("/").pop();
            $(this).parent().remove();
            removedOldImages.push(imageUrl);
        });

        $(document).on("click", ".btn-banner-remove", function () {
            const fullImageUrl = $("#banner-preview-img").attr("src");
            const imageUrl = fullImageUrl.split("/").pop();
            removedOldBanner = imageUrl;
            $("#preview-banner").html(`<img style="object-fit:contain; height:150px;" src="/images/no-image-2.jpg" />`);
        });

        function removeFileFromInput(inputElement, indexToRemove) {
            const dataTransfer = new DataTransfer();
            const currentFiles = Array.from(inputElement.files);

            currentFiles.forEach((file, index) => {
                if (index !== indexToRemove) {
                    dataTransfer.items.add(file);
                }
            });

            inputElement.files = dataTransfer.files;
        }

        function InitialEditor() {
            window.editor = createQuillEditor("#editor", "#preview-content", 5000, "#editor-counter");
        }

        function GetListArticle() {
            table = new DataTable("#list-article", {
                searching: false,
                sort: false,
                responsive: true,
                pageLength: 10,
                serverSide: true,
                ajax: function (data, callback, settings) {
                    const page = (data.start / data.length) + 1;
                    const pageSize = data.length;
                    const keyword = $("#search").val();
                    const status = $("#filter-status").val()

                    const startDate = moment($("#filterStart").val()).format('YYYY-MM-DDT00:00');
                    const endDate = moment($("#filterEnd").val()).format('YYYY-MM-DDT23:59');

                    $.ajax({
                        url: '@Url.Action("GetPage", "Articles")',
                        type: 'GET',
                        data: {
                            page: page,
                            pagesize: data.length,
                            keyword: keyword,
                            status: status,
                            startDate: startDate,
                            endDate: endDate
                        },
                        success: function (response) {
                            const formattedData = response.data.map((item, index) => ({
                                0: data.start + index + 1,
                                1: `<div style="width: 300px" title="${item.title}">
                                            ${item.title}
                                        </div>`,
                                2: item.status == 0 ? "Riêng tư" : "Công khai",
                                5: FormatDate(item.createdDate),
                                6: `<div class="d-flex align-items-center justify-content-evenly list-action">
                                                                <a onclick="GetFormArticle('${item.id}')" class="badge badge-info" data-toggle="tooltip" data-placement="top" title="Xem chi tiết">
                                                                    <i class="ri-edit-line fs-6"></i>
                                                                </a>

                                                                <a onclick="DeleteArticle('${item.id}')" class="badge bg-warning" data-toggle="tooltip" data-placement="top" title="Xóa">
                                                                    <i class="ri-delete-bin-line fs-6"></i>
                                                                </a>
                                                            </div>`,
                                8: `<img style="width: 120px;height: 55px; object-fit: cover;" src="${item.bannerImage ?? item.images[0]}" alt="Ảnh tin tức" >`,
                                9: item.orderPriority
                            }));

                            setTimeout(() => {
                                callback({
                                    draw: data.draw,
                                    recordsTotal: response.data.length,
                                    recordsFiltered: response.totalPages * data.length || 1,
                                    data: formattedData
                                });
                            }, 400);
                        }
                    });
                },
                columns: [
                    { title: "STT", data: 0, className: 'text-center' },
                    { title: "Tiêu đề", data: 1, width: "250px" },
                    { title: "Trạng thái", data: 2 },
                    { title: "Hình ảnh", data: 8, className: 'text-center' },
                    { title: "Thứ tự hiển thị(mini app)", data: 9, className: 'text-center', width: "120px" },
                    { title: "Ngày đăng", data: 5, className: 'text-center' },
                    { title: "Thao tác", data: 6, className: 'text-center' }
                ],
                language: {
                    "lengthMenu": "Hiển thị _MENU_ dòng mỗi trang",
                    "zeroRecords": "Không tìm thấy kết quả",
                    "info": "Hiển thị từ _START_ đến _END_ của _TOTAL_ dòng",
                    "infoEmpty": "Không có dữ liệu",
                    "infoFiltered": "(lọc từ tổng số _MAX_ dòng)"
                }
            });

            $(table.table().header()).addClass('ligth ligth-data');
        }

        function ApproveArticle(id) {
            $.ajax({
                url: `/api/Articles/${id}/Status?status=1`,
                type: 'PATCH',
                success: function (res) {
                    const { code, message } = res;
                    if (code === 0) {
                        AlertResponse(message, "success");
                        table.ajax.reload();
                    } else {
                        AlertResponse(message, "error");
                    }
                },
                error: function (error) {
                    AlertResponse(error.message, "error");
                }
            });
        }

        function DeleteArticle(id) {
            if (id === '') return;
            const url = `/api/articles/${id}`
            $("#modal-article").modal("hide");
            DeleteItem(url);
        }

        function ChangeInput(ele, previewElement) {
            const elementValue = $(ele).val();
            const data = $(ele).data('type')

            let innerContent = "";
            let subContent = "";

            switch (data) {
                case "title":
                    innerContent = elementValue;
                    break;
                case "status":
                    switch (elementValue) {
                        case "0":
                            subContent = "Riêng tư";
                            break;
                        case "1":
                            subContent = "Công khai";
                            break;
                        default:
                            break;
                    }
                    innerContent = "Trạng thái: " + subContent;
                    break;
                default:
                    break;
            }

            $(previewElement).html(innerContent);
        }

        function validateEmail(email) {
            const regex = /^[^\s@@]+@@[^\s@@]+\.[^\s@@]+$/;
            return regex.test(email);
        }

        async function HandleSaveOrUpdate(id) {
            const formData = new FormData();
            const content = window.editor.root.innerHTML;
            const title = $('#title').val()?.trim();
            const author = $('#author').val()?.trim();
            const bannerImageFile = $('#bannerImage')[0].files[0];
            const images = $('#images')[0].files;
            const status = $("#status").val();
            const categoryId = $("#categoryId").val();
            const orderPriority = $('#orderPriority').val();

            if (!title) {
                AlertResponse("Vui lòng nhập tiêu đề.", 'warning')
                return;
            }

            if (!content || content === "<p><br></p>") {
                AlertResponse("Vui lòng nhập nội dung.", 'warning')
                return;
            }

            if (bannerImageFile) {
                /* const isValidSize = await CheckSize(bannerImageFile);
                   if (!isValidSize.valid) {
                       const actualRatio = (isValidSize.actualWidth / isValidSize.actualHeight).toFixed(2); // Làm tròn 2 chữ số
                       AlertResponse(
                           `Ảnh "${bannerImageFile.name}" không đúng tỷ lệ 16:9!` +
                           `\nKích thước hiện tại: ${isValidSize.actualWidth} x ${isValidSize.actualHeight}.` +
                           `\nVui lòng chọn ảnh có tỷ lệ 16:9.`,
                           "warning"
                       );
                       return;
                   }
                   */
            }

            if (!bannerImageFile) {
                // AlertResponse("Vui lòng chọn ảnh cho banner!", 'warning')
                // return;
            }

            if (images.length === 0 && !id) {
                AlertResponse("Vui lòng tải lên ít nhất 1 ảnh cho tin tức!", 'warning')
                return;
            }

            for (let i = 0; i < images.length; i++) {
                // const isValidSize = await CheckSize(images[i], width, height);
                // if (!isValidSize.valid) {
                //     AlertResponse(`Ảnh "${images[i].name}" không đạt tỉ lệ ảnh có kích thước tối thiểu ${width}x${height}!\nKích thước ảnh của bạn là ${isValidSize.actualWidth}x${isValidSize.actualHeight}.`, "warning");
                //     return;
                // }
            }

            // if (!author || author.length > 200) {
            //     AlertResponse("Vui lòng nhập tác giả.", 'warning')
            //     return;
            // }

            formData.append('title', title);
            formData.append('author', author);
            formData.append('status', status);
            formData.append('content', content);
            formData.append('categoryId', categoryId);
            formData.append('orderPriority', orderPriority);

            if (bannerImageFile) {
                formData.append('bannerImage', bannerImageFile);
            } else if (id === "" && images.length > 0) {
                formData.append('bannerImage', images[0]);
            } else if (id !== "" && removedOldBanner !== "") {
                formData.append('removedOldBanner', removedOldBanner);
            }

            for (let i = 0; i < images.length; i++) {
                formData.append('images', images[i]);
            }

            removedOldImages.forEach((url) => formData.append('removedOldImages', url));

            const url = id === "" ? "/api/articles" : "/api/articles/" + id;
            const method = id === "" ? "POST" : "PUT";

            $.ajax({
                url: url,
                type: method,
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.code === 0) {
                        AlertResponse(response.message, 'success')
                        removedOldImages = [];
                        removedOldBanner = "";
                        table.ajax.reload();
                        $("#modal-article").modal("toggle");
                    } else {
                        AlertResponse(response.message, 'warning')
                    }
                },
                error: function (err) {
                    AlertResponse("Đã xảy ra lỗi, vui lòng thử lại sau!", 'error')
                }
            });
        }
    </script>
}
